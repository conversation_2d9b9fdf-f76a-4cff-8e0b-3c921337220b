﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>SEO多语言信息存放表</summary>
public partial interface ISeoInfoLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>SeoId</summary>
    Int32 SId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>SEO标题</summary>
    String? SeoTitle { get; set; }

    /// <summary>SEO关键词</summary>
    String? SeoKeywords { get; set; }

    /// <summary>SEO描述</summary>
    String? SeoDescription { get; set; }

    /// <summary>类型</summary>
    String? SeoType { get; set; }
    #endregion
}
