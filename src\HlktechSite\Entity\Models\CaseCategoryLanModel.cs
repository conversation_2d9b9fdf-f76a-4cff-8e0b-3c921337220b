﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>案例分类翻译</summary>
public partial class CaseCategoryLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>案例分类Id</summary>
    public Int32 CId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>分类名称</summary>
    public String? Name { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ICaseCategoryLan model)
    {
        Id = model.Id;
        CId = model.CId;
        LId = model.LId;
        Name = model.Name;
    }
    #endregion
}
