﻿using DG.Cube.BaseControllers;

using DH.Core.Domain.Localization;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek.Helpers;
using Pek.Models;

using System.Dynamic;

namespace HlktechSite.Areas.Mobile.Controllers;

/// <summary>
/// 移动端新品控制器
/// </summary>
[MobileArea]
public class FProductController : BaseMobileController
{
    /// <summary>新品列表页</summary>
    /// <returns></returns>
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();

        viewModel.List = JumpProduct.GetAll();

        var CurrentId = 0;
        viewModel.CurrentId = CurrentId;

        return View(viewModel);
    }

    /// <summary>
    /// 获取全部新品
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public IActionResult queryModuleInfoByType()
    {
        var res = new DResult();
        res.data = JumpProduct.GetAll();
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 根据产品名称模糊查询
    /// </summary>
    /// <param name="name">产品名称</param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult SearchLike(string name)
    {
        var res = new DResult();
        res.data = JumpProduct.FindAllByName(name);
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 进入文件详页面
    /// </summary>
    /// <param name="Id">文件Id</param>
    /// <returns></returns>
    public IActionResult FDetail(int Id)
    {
        var Model = JumpProduct.FindById(Id);
        if (Model == null)
        {
            return View404();
        }
        Model.Clicks++;
        Model.SaveAsync();

        ViewBag.IsCanBack = !DHWeb.RefererUrl.IsNullOrWhiteSpace();
        ViewBag.Id = Id;


        return View(Model);
    }

    /// <summary>
    /// 推文跳转
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    public IActionResult Tui(Int32 Id)
    {
        ViewBag.Id = Id;

        if (Id <= 0)
        {
            return Content(GetResource("参数有误"));
        }

        var model = JumpProduct.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("内容不存在"));
        }

        var localizationSettings = LocalizationSettings.Current;
        if (!localizationSettings.IsEnable)
        {
            if (!model.InfoUrl.IsNullOrWhiteSpace())
            {
                if (model.InfoUrl.StartsWithIgnoreCase("http:") || model.InfoUrl.StartsWithIgnoreCase("https:"))
                {
                    return Redirect(model.InfoUrl);
                }

                return Redirect($"http://{model.InfoUrl}");
            }
        }
        else
        {
            var modelJumpProductLan = JumpProductLan.FindByJIdAndLId(Id, WorkingLanguage.Id);

            if (!modelJumpProductLan.InfoUrl.IsNullOrWhiteSpace())
            {
                if (modelJumpProductLan.InfoUrl.StartsWithIgnoreCase("http:") || modelJumpProductLan.InfoUrl.StartsWithIgnoreCase("https:"))
                {
                    return Redirect(modelJumpProductLan.InfoUrl);
                }

                return Redirect($"http://{modelJumpProductLan.InfoUrl}");
            }
        }

        return Content(GetResource("暂无内容"));
    }

    public IActionResult App(Int32 Id)
    {
        ViewBag.Id = Id;

        if (Id <= 0)
        {
            return Content(GetResource("参数有误"));
        }

        var model = JumpProduct.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("内容不存在"));
        }

        if (model.AndroidPaths.IsNullOrWhiteSpace() && model.AndroidPaths1.IsNullOrWhiteSpace() && model.IosPaths.IsNullOrWhiteSpace())
        {
            return Content(GetResource("暂无下载数据"));
        }

        return View(model);
    }
}
