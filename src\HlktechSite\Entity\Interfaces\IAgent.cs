﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>代理商申请</summary>
public partial interface IAgent
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>公司名称</summary>
    String? CompanyName { get; set; }

    /// <summary>联系人</summary>
    String? ContactPerson { get; set; }

    /// <summary>联系电话</summary>
    String? Phone { get; set; }

    /// <summary>邮箱</summary>
    String? Email { get; set; }

    /// <summary>联系地址</summary>
    String? ContactAddress { get; set; }

    /// <summary>申请说明</summary>
    String? Summary { get; set; }

    /// <summary>是否通过</summary>
    Boolean IsThrough { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
