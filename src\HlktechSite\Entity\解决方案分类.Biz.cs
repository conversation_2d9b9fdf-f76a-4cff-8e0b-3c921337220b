using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;

using NewLife;
using NewLife.Data;

using Pek;

using XCode;

namespace HlktechSite.Entity {
    /// <summary>解决方案分类</summary>
    public partial class SolutionCategory : CubeEntityBase<SolutionCategory>
    {
        #region 对象操作
        static SolutionCategory()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(ParentId));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化SolutionCategory[解决方案分类]数据……");

        //    var entity = new SolutionCategory();
        //    entity.Id = 0;
        //    entity.Name = "abc";
        //    entity.ParentId = 0;
        //    entity.ParentIdList = "abc";
        //    entity.Level = 0;
        //    entity.DisplayOrder = 0;
        //    entity.CreateUser = "abc";
        //    entity.CreateUserID = 0;
        //    entity.CreateTime = DateTime.Now;
        //    entity.CreateIP = "abc";
        //    entity.UpdateUser = "abc";
        //    entity.UpdateUserID = 0;
        //    entity.UpdateTime = DateTime.Now;
        //    entity.UpdateIP = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化SolutionCategory[解决方案分类]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        ///是否存在子集
        /// </summary>
        [XmlIgnore, ScriptIgnore]
        public bool subset { get; set; } = false;
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static SolutionCategory FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }


        /// <summary>
        /// 查询所有的解决方案分类
        /// </summary>
        /// <returns></returns>
        public static IList<SolutionCategory> GetAll()
        {
            if (Meta.Session.Count < 1000) return FindAllWithCache();

            return FindAll();
        }

        /// <summary>根据所属父级Id查找</summary>
        /// <param name="parentId">所属父级Id</param>
        /// <returns>实体列表</returns>
        public static IList<SolutionCategory> FindAllByParentId(Int32 parentId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ParentId == parentId);

            return FindAll(_.ParentId == parentId);
        }

        /// <summary>根据编号、当前层级查找</summary>
        /// <param name="id">编号</param>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IList<SolutionCategory> FindAllByIdAndLevel(Int32 id, Int32 level)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Id == id && e.Level == level);

            return FindAll(_.Id == id & _.Level == level);
        }

        /// <summary>根据当前层级查找</summary>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IList<SolutionCategory> FindAllByLevel(Int32 level)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Level == level);

            return FindAll(_.Level == level);
        }

        /// <summary>
        /// 根据列表Ids获取列表
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static IList<SolutionCategory> FindByIds(String ids)
        {
            if (ids.IsNullOrWhiteSpace()) return new List<SolutionCategory>();

            ids = ids.Trim(',');

            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => ids.SplitAsInt(",").Contains(x.Id));
            }

            return FindAll(_.Id.In(ids.Split(',')));
        }

        /// <summary>根据名称查找</summary>
        /// <param name="name">设备DeviceName</param>
        /// <returns>实体对象</returns>
        public static SolutionCategory FindByName(String name)
        {
            if (name.IsNullOrWhiteSpace()) return null;

            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

            return Find(_.Name == name);
        }

        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="parentId">所属父级Id</param>
        /// <param name="level">当前层级</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<SolutionCategory> Search(Int32 parentId, Int32 level, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (parentId >= 0) exp &= _.ParentId == parentId;
            if (level >= 0) exp &= _.Level == level;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.ParentIdList.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="name"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        public static IEnumerable<SolutionCategory> Searchs(string name, PageParameter page)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                IEnumerable<SolutionCategory> list = Meta.Cache.Entities;
                if (name.IsNotNullAndWhiteSpace())
                {
                    list = list.Where(e => e.Name.Contains(name, StringComparison.OrdinalIgnoreCase));
                }
                page.TotalCount = list.Count();

                list = list.OrderBy(e => e.DisplayOrder).Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize);
                return list;
            }

            var exp = new WhereExpression();
            if (name.IsNotNullAndWhiteSpace())
            {
                exp &= _.Name.Contains(name);
            }

            page.Sort = _.DisplayOrder;
            page.Desc = false;

            return FindAll(exp, page);
        }

        // Select Count(Id) as Id,Category From SolutionCategory Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<SolutionCategory> _CategoryCache = new FieldCache<SolutionCategory>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}