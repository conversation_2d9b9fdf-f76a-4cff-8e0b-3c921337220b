﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    Html.AppendTitleParts(T("联系我们").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}
<link href="~/css/mobile/contact.css" rel="stylesheet" />
<div class="top">
    <img src="@(CDN.GetCDN())/images/contact.png" />
    <h2>@T("联系我们")</h2>
    <P>
        @T("创造并提供丰富的便民生活服务")
    </P>
</div>

<div class="con">
    <h2>@T("联系我们")</h2>

    @T("深圳市海凌科电子有限公司")

    @T("武汉极思灵创科技有限公司")

    @T("益坤泰实业（东莞）有限公司")

    @T("优秀代理商1")

</div>


<div class="map-nav">
    <div class="btn-group">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
            <b>@T("深圳市海凌科电子有限公司1")</b><span class="caret"></span>
        </button>
        <ul class="dropdown-menu comdropdown">
            <li><a href="javascript:;" data-type="1">@T("深圳市海凌科电子有限公司1")</a></li>
            <li><a href="javascript:;" data-type="2">@T("武汉极思灵创科技有限公司1")</a></li>
            <li><a href="javascript:;" data-type="3">@T("益坤泰实业有限公司1")</a></li>
        </ul>
    </div>
    <img src="@(CDN.GetCDN())/images/place.png" alt="Alternate Text" />
</div>

<div id="allmap">

</div>

<script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=dPDoL4fF3ZGgxy8UBDpGU3684nWezyHi"></script>


<script asp-location="Footer">

    $(".dropdown-toggle").click(function () {
        $(this).next().toggle();
    });
        var map = new BMap.Map("allmap");  // 创建Map实例
        map.centerAndZoom(new BMap.Point(114.064415, 22.609933), 15);
        map.enableScrollWheelZoom();
        var marker = new BMap.Marker(new BMap.Point(114.05408,  22.625068));
        map.addOverlay(marker);//增加点
        var marker = new BMap.Marker(new BMap.Point(114.336432, 30.539215)); // 创建点
        map.addOverlay(marker);//增加点
        var marker = new BMap.Marker(new BMap.Point(113.818237, 23.071273));
        map.addOverlay(marker);//增加点

        $(".map-nav .dropdown-menu li").click(function () {
            $(".map-nav .btn-group button b").html($(this).find("a").html());
            $(this).parent().toggle();
            var type = $(this).find("a").attr("data-type");
            if (type == "1") {
                map.panTo(new BMap.Point(114.05408,  22.625068));
            }
            if (type == "2") {
                map.panTo(new BMap.Point(114.336432, 30.539215));
            }
            if (type == "3") {
                map.panTo(new BMap.Point(113.818237, 23.071273));
            }
        });
    
    $(document).mouseup(function (e) {
        var _con = $('.dropdown-toggle');
        var _con2 = $('.comdropdown li');
        if (!_con.is(e.target) && _con.has(e.target).length === 0 && !_con2.is(e.target) && _con2.has(e.target).length === 0) {
            if ($(".comdropdown").is(':visible')) {
                $(".comdropdown").hide();
            }
        }

    });
</script>