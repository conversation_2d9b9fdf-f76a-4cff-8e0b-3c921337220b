﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>产品常用分类</summary>
public partial class GoodsClassStapleModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>计数器。使用次数计数</summary>
    public Int32 Counter { get; set; }

    /// <summary>常用分类名称</summary>
    public String? Name { get; set; }

    /// <summary>一级分类ID</summary>
    public Int32 Cid1 { get; set; }

    /// <summary>二级分类ID</summary>
    public Int32 Cid2 { get; set; }

    /// <summary>三级分类ID</summary>
    public Int32 Cid3 { get; set; }

    /// <summary>类型Id。产品分类是否联动</summary>
    public Boolean TypeId { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IGoodsClassStaple model)
    {
        Id = model.Id;
        Counter = model.Counter;
        Name = model.Name;
        Cid1 = model.Cid1;
        Cid2 = model.Cid2;
        Cid3 = model.Cid3;
        TypeId = model.TypeId;
    }
    #endregion
}
