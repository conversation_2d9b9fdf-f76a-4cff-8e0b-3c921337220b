﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace AIAddress.Entity;

/// <summary>地址处理</summary>
[Serializable]
[DataObject]
[Description("地址处理")]
[BindIndex("IU_DH_Address_Id_Salesman_Department_Receiving", true, "Id,Salesman,Department,Receiving")]
[BindTable("DH_Address", Description = "地址处理", ConnName = "DH", DbType = DatabaseType.None)]
public partial class Address : IAddress, IEntity<IAddress>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Salesman;
    /// <summary>业务员</summary>
    [DisplayName("业务员")]
    [Description("业务员")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Salesman", "业务员", "")]
    public String? Salesman { get => _Salesman; set { if (OnPropertyChanging("Salesman", value)) { _Salesman = value; OnPropertyChanged("Salesman"); } } }

    private String? _Department;
    /// <summary>部门名称</summary>
    [DisplayName("部门名称")]
    [Description("部门名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Department", "部门名称", "")]
    public String? Department { get => _Department; set { if (OnPropertyChanging("Department", value)) { _Department = value; OnPropertyChanged("Department"); } } }

    private String? _Receiving;
    /// <summary>收货联系人</summary>
    [DisplayName("收货联系人")]
    [Description("收货联系人")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Receiving", "收货联系人", "")]
    public String? Receiving { get => _Receiving; set { if (OnPropertyChanging("Receiving", value)) { _Receiving = value; OnPropertyChanged("Receiving"); } } }

    private String? _Delivery;
    /// <summary>收货地址</summary>
    [DisplayName("收货地址")]
    [Description("收货地址")]
    [DataObjectField(false, false, true, 300)]
    [BindColumn("Delivery", "收货地址", "")]
    public String? Delivery { get => _Delivery; set { if (OnPropertyChanging("Delivery", value)) { _Delivery = value; OnPropertyChanged("Delivery"); } } }

    private Double _Lat;
    /// <summary>Lat</summary>
    [DisplayName("Lat")]
    [Description("Lat")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Lat", "Lat", "")]
    public Double Lat { get => _Lat; set { if (OnPropertyChanging("Lat", value)) { _Lat = value; OnPropertyChanged("Lat"); } } }

    private Double _Lng;
    /// <summary>Lng</summary>
    [DisplayName("Lng")]
    [Description("Lng")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Lng", "Lng", "")]
    public Double Lng { get => _Lng; set { if (OnPropertyChanging("Lng", value)) { _Lng = value; OnPropertyChanged("Lng"); } } }

    private String? _Detail;
    /// <summary>Detail</summary>
    [DisplayName("Detail")]
    [Description("Detail")]
    [DataObjectField(false, false, true, 300)]
    [BindColumn("Detail", "Detail", "")]
    public String? Detail { get => _Detail; set { if (OnPropertyChanging("Detail", value)) { _Detail = value; OnPropertyChanged("Detail"); } } }

    private String? _Town;
    /// <summary>Town</summary>
    [DisplayName("Town")]
    [Description("Town")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Town", "Town", "")]
    public String? Town { get => _Town; set { if (OnPropertyChanging("Town", value)) { _Town = value; OnPropertyChanged("Town"); } } }

    private String? _PhoneNum;
    /// <summary>PhoneNum</summary>
    [DisplayName("PhoneNum")]
    [Description("PhoneNum")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("PhoneNum", "PhoneNum", "")]
    public String? PhoneNum { get => _PhoneNum; set { if (OnPropertyChanging("PhoneNum", value)) { _PhoneNum = value; OnPropertyChanged("PhoneNum"); } } }

    private String? _CityCode;
    /// <summary>CityCode</summary>
    [DisplayName("CityCode")]
    [Description("CityCode")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CityCode", "CityCode", "")]
    public String? CityCode { get => _CityCode; set { if (OnPropertyChanging("CityCode", value)) { _CityCode = value; OnPropertyChanged("CityCode"); } } }

    private String? _Province;
    /// <summary>Province</summary>
    [DisplayName("Province")]
    [Description("Province")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Province", "Province", "")]
    public String? Province { get => _Province; set { if (OnPropertyChanging("Province", value)) { _Province = value; OnPropertyChanged("Province"); } } }

    private String? _Person;
    /// <summary>Person</summary>
    [DisplayName("Person")]
    [Description("Person")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Person", "Person", "")]
    public String? Person { get => _Person; set { if (OnPropertyChanging("Person", value)) { _Person = value; OnPropertyChanged("Person"); } } }

    private String? _ProvinceCode;
    /// <summary>ProvinceCode</summary>
    [DisplayName("ProvinceCode")]
    [Description("ProvinceCode")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ProvinceCode", "ProvinceCode", "")]
    public String? ProvinceCode { get => _ProvinceCode; set { if (OnPropertyChanging("ProvinceCode", value)) { _ProvinceCode = value; OnPropertyChanged("ProvinceCode"); } } }

    private String? _Text;
    /// <summary>Text</summary>
    [DisplayName("Text")]
    [Description("Text")]
    [DataObjectField(false, false, true, 300)]
    [BindColumn("Text", "Text", "")]
    public String? Text { get => _Text; set { if (OnPropertyChanging("Text", value)) { _Text = value; OnPropertyChanged("Text"); } } }

    private String? _County;
    /// <summary>County</summary>
    [DisplayName("County")]
    [Description("County")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("County", "County", "")]
    public String? County { get => _County; set { if (OnPropertyChanging("County", value)) { _County = value; OnPropertyChanged("County"); } } }

    private String? _City;
    /// <summary>City</summary>
    [DisplayName("City")]
    [Description("City")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("City", "City", "")]
    public String? City { get => _City; set { if (OnPropertyChanging("City", value)) { _City = value; OnPropertyChanged("City"); } } }

    private String? _CountyCode;
    /// <summary>CountyCode</summary>
    [DisplayName("CountyCode")]
    [Description("CountyCode")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CountyCode", "CountyCode", "")]
    public String? CountyCode { get => _CountyCode; set { if (OnPropertyChanging("CountyCode", value)) { _CountyCode = value; OnPropertyChanged("CountyCode"); } } }

    private String? _TownCode;
    /// <summary>TownCode</summary>
    [DisplayName("TownCode")]
    [Description("TownCode")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("TownCode", "TownCode", "")]
    public String? TownCode { get => _TownCode; set { if (OnPropertyChanging("TownCode", value)) { _TownCode = value; OnPropertyChanged("TownCode"); } } }

    private Boolean _Process;
    /// <summary>是否请求</summary>
    [DisplayName("是否请求")]
    [Description("是否请求")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Process", "是否请求", "")]
    public Boolean Process { get => _Process; set { if (OnPropertyChanging("Process", value)) { _Process = value; OnPropertyChanged("Process"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAddress model)
    {
        Id = model.Id;
        Salesman = model.Salesman;
        Department = model.Department;
        Receiving = model.Receiving;
        Delivery = model.Delivery;
        Lat = model.Lat;
        Lng = model.Lng;
        Detail = model.Detail;
        Town = model.Town;
        PhoneNum = model.PhoneNum;
        CityCode = model.CityCode;
        Province = model.Province;
        Person = model.Person;
        ProvinceCode = model.ProvinceCode;
        Text = model.Text;
        County = model.County;
        City = model.City;
        CountyCode = model.CountyCode;
        TownCode = model.TownCode;
        Process = model.Process;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Salesman" => _Salesman,
            "Department" => _Department,
            "Receiving" => _Receiving,
            "Delivery" => _Delivery,
            "Lat" => _Lat,
            "Lng" => _Lng,
            "Detail" => _Detail,
            "Town" => _Town,
            "PhoneNum" => _PhoneNum,
            "CityCode" => _CityCode,
            "Province" => _Province,
            "Person" => _Person,
            "ProvinceCode" => _ProvinceCode,
            "Text" => _Text,
            "County" => _County,
            "City" => _City,
            "CountyCode" => _CountyCode,
            "TownCode" => _TownCode,
            "Process" => _Process,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Salesman": _Salesman = Convert.ToString(value); break;
                case "Department": _Department = Convert.ToString(value); break;
                case "Receiving": _Receiving = Convert.ToString(value); break;
                case "Delivery": _Delivery = Convert.ToString(value); break;
                case "Lat": _Lat = value.ToDouble(); break;
                case "Lng": _Lng = value.ToDouble(); break;
                case "Detail": _Detail = Convert.ToString(value); break;
                case "Town": _Town = Convert.ToString(value); break;
                case "PhoneNum": _PhoneNum = Convert.ToString(value); break;
                case "CityCode": _CityCode = Convert.ToString(value); break;
                case "Province": _Province = Convert.ToString(value); break;
                case "Person": _Person = Convert.ToString(value); break;
                case "ProvinceCode": _ProvinceCode = Convert.ToString(value); break;
                case "Text": _Text = Convert.ToString(value); break;
                case "County": _County = Convert.ToString(value); break;
                case "City": _City = Convert.ToString(value); break;
                case "CountyCode": _CountyCode = Convert.ToString(value); break;
                case "TownCode": _TownCode = Convert.ToString(value); break;
                case "Process": _Process = value.ToBoolean(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 字段名
    /// <summary>取得地址处理字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>业务员</summary>
        public static readonly Field Salesman = FindByName("Salesman");

        /// <summary>部门名称</summary>
        public static readonly Field Department = FindByName("Department");

        /// <summary>收货联系人</summary>
        public static readonly Field Receiving = FindByName("Receiving");

        /// <summary>收货地址</summary>
        public static readonly Field Delivery = FindByName("Delivery");

        /// <summary>Lat</summary>
        public static readonly Field Lat = FindByName("Lat");

        /// <summary>Lng</summary>
        public static readonly Field Lng = FindByName("Lng");

        /// <summary>Detail</summary>
        public static readonly Field Detail = FindByName("Detail");

        /// <summary>Town</summary>
        public static readonly Field Town = FindByName("Town");

        /// <summary>PhoneNum</summary>
        public static readonly Field PhoneNum = FindByName("PhoneNum");

        /// <summary>CityCode</summary>
        public static readonly Field CityCode = FindByName("CityCode");

        /// <summary>Province</summary>
        public static readonly Field Province = FindByName("Province");

        /// <summary>Person</summary>
        public static readonly Field Person = FindByName("Person");

        /// <summary>ProvinceCode</summary>
        public static readonly Field ProvinceCode = FindByName("ProvinceCode");

        /// <summary>Text</summary>
        public static readonly Field Text = FindByName("Text");

        /// <summary>County</summary>
        public static readonly Field County = FindByName("County");

        /// <summary>City</summary>
        public static readonly Field City = FindByName("City");

        /// <summary>CountyCode</summary>
        public static readonly Field CountyCode = FindByName("CountyCode");

        /// <summary>TownCode</summary>
        public static readonly Field TownCode = FindByName("TownCode");

        /// <summary>是否请求</summary>
        public static readonly Field Process = FindByName("Process");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得地址处理字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>业务员</summary>
        public const String Salesman = "Salesman";

        /// <summary>部门名称</summary>
        public const String Department = "Department";

        /// <summary>收货联系人</summary>
        public const String Receiving = "Receiving";

        /// <summary>收货地址</summary>
        public const String Delivery = "Delivery";

        /// <summary>Lat</summary>
        public const String Lat = "Lat";

        /// <summary>Lng</summary>
        public const String Lng = "Lng";

        /// <summary>Detail</summary>
        public const String Detail = "Detail";

        /// <summary>Town</summary>
        public const String Town = "Town";

        /// <summary>PhoneNum</summary>
        public const String PhoneNum = "PhoneNum";

        /// <summary>CityCode</summary>
        public const String CityCode = "CityCode";

        /// <summary>Province</summary>
        public const String Province = "Province";

        /// <summary>Person</summary>
        public const String Person = "Person";

        /// <summary>ProvinceCode</summary>
        public const String ProvinceCode = "ProvinceCode";

        /// <summary>Text</summary>
        public const String Text = "Text";

        /// <summary>County</summary>
        public const String County = "County";

        /// <summary>City</summary>
        public const String City = "City";

        /// <summary>CountyCode</summary>
        public const String CountyCode = "CountyCode";

        /// <summary>TownCode</summary>
        public const String TownCode = "TownCode";

        /// <summary>是否请求</summary>
        public const String Process = "Process";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
