﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH;
using DH.SearchEngine;
using DH.SearchEngine.Interfaces;

using HlktechSite.Entity;

using Lucene.Net.Analysis;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.IO;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>产品型号</summary>
[DisplayName("知识库")]
[Description("用于知识库的管理")]
[AdminArea]
[DHMenu(86,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/Knowledge", CurrentMenuName = "KnowledgeList", CurrentIcon = "&#xe745;", LastUpdate = "20240125")]
public class KnowledgeController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 86;

    private readonly ISearchEngine _searchEngine;

    private readonly ILuceneIndexer _luceneIndexer;

    public KnowledgeController(ISearchEngine searchEngine, Lucene.Net.Store.Directory directory, Analyzer analyzer)
    {
        _searchEngine = searchEngine;
        _luceneIndexer = new LuceneIndexer(directory, analyzer);
    }

    /// <summary>
    /// 产品型号列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("知识库列表")]
    public IActionResult Index(string name, int select, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime",
            Desc = true
        };
        name = name.SafeString().Trim();
        var list = Knowledge.Searchs(name, select, pages).Select(x => new { x.Name, x.Id, x.HelpFuls, x.Clicks, x.Status, Pname = x.ProductModel?.Name });
        viewModel.list = list.ToDynamicList();
        viewModel.page = page;
        viewModel.name = name;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name }, { "select", select.ToString() } });

        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id, selected = x.Id == select });

        ViewBag.List = ProducList.ToJson();

        return View(viewModel);
    }

    /// <summary>
    /// 知识库数据删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("知识库数据删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();

        Ids = Ids.Trim(',');

        var Dellist = Knowledge.FindByIds(Ids);
        _luceneIndexer.Delete(Dellist);

        Knowledge.DelByIds(Ids);
        SearchInfo.DelByOtherIdAndSrtpe(Ids, 1);

        Loger.UserLog("删除", $"删除知识库数据：{Ids}");

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload.Length;
        fileModel.FileType = 8;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"knowledge/{filename}");
        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath.Replace("\\", "/");
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = Pek.Helpers.DHWeb.GetSiteUrl() + "/" + filepath.Replace("\\", "/") });
    }

    /// <summary>
    /// 图片删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片删除")]
    public IActionResult DeleteImg(Int32 Id)
    {
        var model = UploadInfo.FindById(Id);

        if (model != null)
        {
            DHSetting.Current.WebRootPath.GetFullPath().CombinePath(model.FileUrl).AsFile().Delete();
            model.Delete();
        }

        return Ok("true");
    }

    /// <summary>
    /// 知识库修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("知识库修改页面")]
    public IActionResult EditKnowledge(Int32 Id)
    {
        dynamic viewModel = new ExpandoObject();

        var Model = Knowledge.FindById(Id);

        if (Model == null)
        {
            return MessageTip("数据不存在或已被删除！");
        }

        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(Id, 8);

        ViewBag.Name = "";
        ViewBag.MID = 0;
        var pModel = ProductModel.FindById(Model.MId);
        if (pModel != null)
        {
            ViewBag.Name = pModel.Name;
            ViewBag.MID = pModel.Id;
        }
        viewModel.Model = Model;

        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id, selected = x.Id == Model.MId });

        ViewBag.List = ProducList.ToJson();

        return View(viewModel);
    }

    /// <summary>
    /// 知识库修改
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("知识库修改")]
    public IActionResult EditKnowledge(Int32 Id, string document_content, int select, string document_title, string Tags, int isbuy)
    {
        if (document_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
        }

        document_title = document_title.SafeString().Trim();
        var Modes = Knowledge.FindByName(document_title);
        if (Modes != null && Modes.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        using (var tran1 = Knowledge.Meta.CreateTrans())
        {
            var Model = Knowledge.FindById(Id);
            if (Model == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
            }
            Model.Content = document_content;
            Model.Name = document_title;

            Model.Status = isbuy;
            Model.Tags = Tags;
            if (select > 0)
            {
                var MModel = ProductModel.FindById(select);
                if (MModel != null)
                {
                    Model.MId = select;
                    Model.MIdName = MModel.Name;
                }
                else
                {
                    Model.MId = 0;
                    Model.MIdName = "";
                }
            }
            else
            {
                Model.MId = 0;
                Model.MIdName = "";
            }
            Model.Update();
            Loger.UserLog("修改", "修改知识库数据：" + Model.Name);

            if (Model.Status == 1)
            {
                var serach = SearchInfo.FindByOtherId(Model.Id, 1);
                if (serach == null)
                {
                    serach = new SearchInfo();
                }
                serach.TableName = "Knowledge";
                serach.Content = Model.Content;
                serach.CreateTime = Model.CreateTime;
                serach.UpdateTime = Model.UpdateTime;
                serach.Name = Model.Name;
                serach.MId = Model.MId;
                serach.OtherId = Model.Id;
                serach.SType = 1; //1是知识库
                serach.Save();

                if (!Model.MIdName.IsNullOrWhiteSpace())
                    Model.Content = Model.MIdName + " " + Model.Content;
                _luceneIndexer.Update(Model); //更新索引
            }

            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }
            tran1.Commit();
            Knowledge.Meta.Cache.Clear("");
            SearchInfo.Meta.Cache.Clear("");
        }

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增知识库页面")]
    public IActionResult CreateKnowledge()
    {
        dynamic viewModel = new ExpandoObject();
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(0,8);
        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id });

        ViewBag.List = ProducList.ToJson();
        return View(viewModel);
    }

    /// <summary>
    /// 新增知识库接口
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增知识库接口")]
    public IActionResult CreateKnowledge(string document_content, int select, string document_title, string Tags, int isbuy)
    {
        if (document_content.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
        }

        document_title = document_title.SafeString().Trim();
        var Modes = Knowledge.FindByName(document_title);
        if (Modes != null)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }

        using (var tran1 = Knowledge.Meta.CreateTrans())
        {
            var Model = new Knowledge();
            Model.Content = document_content;
            Model.Name = document_title;
            Model.Status = isbuy;
            Model.Tags = Tags;
            if (select > 0) //关联型号部分数据
            {
                var MModel = ProductModel.FindById(select);
                if (MModel != null)
                {
                    Model.MId = select;
                    Model.MIdName = MModel.Name;
                }
            }
            Model.Insert();
            Loger.UserLog("添加", $"添加知识库{Model.Name}");

            if (Model.Status == 1)
            {
                var serach = new SearchInfo();
                serach.TableName = "Knowledge";
                serach.Name = Model.Name;
                serach.CreateTime = Model.CreateTime;
                serach.UpdateTime = Model.UpdateTime;
                serach.MId = Model.MId;
                serach.Content = Model.Content;
                serach.OtherId = Model.Id;
                serach.SType = 1; //1是知识库
                serach.Save();

                if (!Model.MIdName.IsNullOrWhiteSpace())
                    Model.Content = Model.MIdName + " " + Model.Content;
                _luceneIndexer.Add(Model);  //添加索引
            }

            //修改没有标识的图片
            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            tran1.Commit();
            Knowledge.Meta.Cache.Clear("");
            UploadInfo.Meta.Cache.Clear("");
            SearchInfo.Meta.Cache.Clear("");
        }
        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }
}
