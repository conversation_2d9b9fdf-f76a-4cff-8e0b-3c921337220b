﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH;
using DH.Core.Domain.Localization;
using DH.Entity;
using DH.Helpers;
using DH.SearchEngine;
using DH.SearchEngine.Interfaces;

using HlktechSite.Entity;

using Lucene.Net.Analysis;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.IO;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>解决方案管理</summary>
[DisplayName("解决方案管理")]
[Description("用于解决方案管理的管理")]
[AdminArea]
[DHMenu(46,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/Solution", CurrentMenuName = "SolutionList", CurrentIcon = "&#xe71f;", LastUpdate = "20240125")]
public class SolutionController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 46;

    private readonly ISearchEngine _searchEngine;

    private readonly ILuceneIndexer _luceneIndexer;

    public SolutionController(ISearchEngine searchEngine, Lucene.Net.Store.Directory directory, Analyzer analyzer)
    {
        _searchEngine = searchEngine;
        _luceneIndexer = new LuceneIndexer(directory, analyzer);
    }

    /// <summary>
    /// 解决方案列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("解决方案列表")]
    public IActionResult Index(string name,int search_ac_id=-1, int page = 1)
    {
        name = name.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = Solution.Searchs(name, search_ac_id, pages).Select(x => new Article { Id = x.Id, AName = x.SolutionCategory?.Name, Name = x.Name, Show = x.Show, CreateTime = x.CreateTime, Sort=x.Sort });

        var List = new List<SolutionCategory>();
        var live1 = SolutionCategory.FindAllByLevel(0); //1级数据
        viewModel.Claslist = live1;

        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;

        viewModel.search_ac_id = search_ac_id;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name },{ "search_ac_id", search_ac_id.ToString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 添加解决方案管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加解决方案管理")]
    public IActionResult AddSolution()
    {
        dynamic viewModel = new ExpandoObject();
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(0,10);
        var List = new List<SolutionCategory>();
        var live1 = SolutionCategory.FindAllByLevel(0); //1级数据
        GetCategoryList(live1, List);
        var pages = new PageParameter();
        pages.PageIndex = 1;
        pages.PageSize = 10;
        pages.OrderBy = "CreateTime";

        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id });

        ViewBag.List = ProducList.ToJson();

        var Sort = Solution.FindMax("Sort");
        ViewBag.Sort = Sort + 1;
        viewModel.Plist = List;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(viewModel);
    }

    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IList<SolutionCategory> levelList, IList<SolutionCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = SolutionCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 新增提交
    /// </summary>
    /// <param name="article_title">标题</param>
    /// <param name="default_user_portrait">主图</param>
    /// <param name="gc_class_id">关联分类Id</param>
    /// <param name="article_url">链接</param>
    /// <param name="select">关联产品型号Id</param>
    /// <param name="article_show">是否显示</param>
    /// <param name="article_sort">排序</param>
    /// <param name="article_content">内容</param>
    /// <param name="summary"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("新增提交")]
    [HttpPost]
    public IActionResult CreatSolution(IFormFile default_user_portrait, string article_title, int gc_class_id, string article_url, int article_show, int article_sort, string article_content, int select,string summary)
    {
        if (article_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = Solution.FindByName(article_title.SafeString().Trim());
        if (ex != null)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        if (gc_class_id == 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属文章分类") });
        }
        using (var tran1 = Solution.Meta.CreateTrans())
        {
            var Model = new Solution();
            Model.Name = article_title.SafeString().Trim();
            Model.CId = gc_class_id;
            Model.Url = article_url.SafeString().Trim();
            Model.Show = article_show == 1 ? true : false;
            Model.Sort = article_sort;
            Model.Content = article_content;
            Model.MId = select;
            Model.Summary = summary;
            Model.Insert();

            var serach = SearchInfo.FindByOtherId(Model.Id, 4);
            if (serach == null)
            {
                serach = new SearchInfo();
            }
            serach.TableName = "Solution";
            serach.Content = Model.Content;
            serach.CreateTime = Model.CreateTime;
            serach.UpdateTime = Model.UpdateTime;
            serach.Name = Model.Name;
            serach.MId = Model.MId;
            serach.OtherId = Model.Id;
            serach.SType = 4; //4是解决方案
            serach.Save();

            _luceneIndexer.Add(Model);
            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{"Solution" + Model.Id}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = DHSetting.Current.UploadPath.CombinePath($"Solution/{filename}");
                var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath.Replace("\\", "/");
                Model.Update();
            }

            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();
                foreach (var item in Languagelist)
                {
                    var Name = $"[{item.Id}].default_user_portrait";
                    var aaaa = new SolutionLan();
                    aaaa.Name = GetRequest($"[{item.Id}].article_title").SafeString().Trim();
                    aaaa.Summary = GetRequest($"[{item.Id}].summary").SafeString().Trim();
                    //aaaa.Tags = GetRequest($"[{item.Id}].tags").SafeString().Trim();
                    //aaaa.Description = GetRequest($"[{item.Id}].description").SafeString().Trim();
                    aaaa.Content = GetRequest($"article_content_{item.Id}").SafeString().Trim();
                    aaaa.CId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Insert();
                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{"SolutionLan" + aaaa.Id}{Path.GetExtension(file.FileName)}";
                        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"SolutionLan/{filename}");
                        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Pic = filepath.Replace("\\", "/");
                        aaaa.Update();
                    }
                }
            }
            tran1.Commit();
        }
        Solution.Meta.Cache.Clear("");//清除缓存
        SolutionLan.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 解决方案管理修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("解决方案管理修改页面")]
    public IActionResult EditSolution(Int32 Id)
    {
        var Model = Solution.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(Id, 10);
        dynamic viewModel = new ExpandoObject();
        var List = new List<SolutionCategory>();
        var live1 = SolutionCategory.FindAllByLevel(0);//一级数据
        GetCategoryList(live1, List);
        ViewBag.Name = "";
        ViewBag.AID = 0;
        var pmodel = SolutionCategory.FindById(Model.CId);
        if (pmodel != null)
        {
            ViewBag.Name = pmodel.Name;
            ViewBag.AID = pmodel.Id;
        }
        viewModel.Plist = List;
        viewModel.Model = Model;
        ViewBag.Images = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Model.Pic.IsNotNullAndWhiteSpace() ? Model.Pic : "");


        var pages = new PageParameter();
        pages.PageIndex = 1;
        pages.PageSize = 10;
        pages.OrderBy = "CreateTime";

        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id, selected = x.Id == Model.MId });

        ViewBag.List = ProducList.ToJson();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(viewModel);
    }

    /// <summary>
    /// 解决方案管理修改接口
    /// </summary>
    /// <param name="Id">编号</param>
    /// <param name="article_title">标题</param>
    /// <param name="default_user_portrait">主图</param>
    /// <param name="gc_class_id">关联分类文章Id</param>
    /// <param name="article_url">链接</param>
    /// <param name="select">CId</param>
    /// <param name="article_show">是否显示</param>
    /// <param name="article_sort">排序</param>
    /// <param name="article_content">文章内容</param>
    /// <param name="summary"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("解决方案管理修改接口")]
    public IActionResult EditSolution(Int32 Id, IFormFile default_user_portrait, string article_title, int gc_class_id, string article_url, int article_show, int article_sort, string article_content, int select,string summary)
    {
        if (article_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = Solution.FindByName(article_title.SafeString().Trim());
        if (ex != null && ex.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        if (gc_class_id == 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属文章分类") });
        }
        var Model = Solution.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
        }

        using (var tran1 = Solution.Meta.CreateTrans())
        {
            Model.Name = article_title.SafeString().Trim();
            Model.CId = gc_class_id;
            Model.Url = article_url.SafeString().Trim();
            Model.MId = select;
            Model.Show = article_show == 1 ? true : false;
            Model.Sort = article_sort;
            Model.Summary = summary;
            Model.Content = article_content;
            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{"Solution" + Model.Id}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = DHSetting.Current.UploadPath.CombinePath($"Solution/{filename}");
                var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath.Replace("\\", "/");
            }
            Model.Update();

            var serach = SearchInfo.FindByOtherId(Model.Id, 4);
            if (serach == null)
            {
                serach = new SearchInfo();
            }
            serach.TableName = "Solution";
            serach.Content = Model.Content;
            serach.CreateTime = Model.CreateTime;
            serach.UpdateTime = Model.UpdateTime;
            serach.Name = Model.Name;
            serach.MId = Model.MId;
            serach.OtherId = Model.Id;
            serach.SType = 4; //4是解决方案
            serach.Save();


            Solution.Meta.Cache.Clear("");

            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();
                var lanlist = SolutionLan.FindAllByCId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new SolutionLan();
                    }
                    var Name = $"[{item.Id}].default_user_portrait";
                    aaaa.Name = GetRequest($"[{item.Id}].article_title").SafeString().Trim();
                    aaaa.Summary = GetRequest($"[{item.Id}].Summary").SafeString().Trim();
                    //aaaa.Tags = GetRequest($"[{item.Id}].tags").SafeString().Trim();
                    //aaaa.Description = GetRequest($"[{item.Id}].description").SafeString().Trim();
                    aaaa.Content = GetRequest($"article_content_{item.Id}").SafeString().Trim();
                    aaaa.CId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Save();

                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{"Solutionlan" + aaaa.Id}{Path.GetExtension(file.FileName)}";
                        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"Solutionlan/{filename}");
                        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Pic = filepath.Replace("\\", "/");
                        aaaa.Update();
                    }
                }
            }

            _luceneIndexer.Update(Model);
            tran1.Commit();
        }

        Solution.Meta.Cache.Clear("");//清除缓存
        SolutionLan.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 批量删除数据  
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("解决方案管理删除")]
    public IActionResult Deltet(string Ids)
    {
        var res = new DResult();

        var list = Solution.FindByIds(Ids);
        _luceneIndexer.Delete(list);
        Solution.DelByIds(Ids.Trim(','));
        SolutionLan.DelByCIds(Ids.Trim(','));
        SearchInfo.DelByOtherIdAndSrtpe(Ids.Trim(','), 4);
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }

        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload.Length;
        fileModel.FileType = 10;
        fileModel.ItemId = Id;
        fileModel.IsImg = true;
        fileModel.OriginFileName = fileupload.FileName;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = DHSetting.Current.UploadPath.CombinePath($"Solution/{filename}");
        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath.Replace("\\", "/");
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = Pek.Helpers.DHWeb.GetSiteUrl() + "/" + filepath.Replace("\\", "/") });
    }

    /// <summary>
    /// 图片删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片删除")]
    public IActionResult DeleteImg(Int32 Id)
    {
        var model = UploadInfo.FindById(Id);

        if (model != null)
        {
            var file = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(model.FileUrl).AsFile();

            if (file.Exists)
            {
                file.Delete();
            }
            
            model.Delete();
        }

        return Ok("true");
    }

    /// <summary>
    /// 根据名称查询
    /// </summary>
    /// <param name="title"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("根据名称查询")]
    public IActionResult FinByName(string title, int Id)
    {
        title = title.SafeString().Trim();
        var Model = Solution.FindByName(title);
        if (Id != 0)
        {
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }
        }
        else
        {
            if (Model != null)
            {
                return Json(false);
            }
        }

        return Json(true);
    }
}

