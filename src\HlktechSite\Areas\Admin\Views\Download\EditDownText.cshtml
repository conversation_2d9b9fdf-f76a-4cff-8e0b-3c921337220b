﻿@{
    // script
    Html.AppendScriptParts(ResourceLocation.Footer, "/static/admin/js/xm-select.js");
}
<style asp-location="true">
    .page {
        min-height: 410px;
    }
</style>
<div class="page">
    <form id="navigation_form" method="post">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><label class="validation" for="nav_title">产品型号:</label></td>
                    <td class="">
                        <div id="demo1"></div>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <input type="hidden" name="Id" value="@Model.Model.Id" />
                    <td colspan="15"><input class="btn" type="submit" value="提交" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript" asp-location="Footer">
    $(function(){
        $('#navigation_form').validate({
            errorPlacement: function(error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                nav_title :{
                    required: true
                },
                nav_url :{
                    url : true
                },
                nav_sort :{
                    number:true,
                    range:[0,255]
                }
            },
            messages: {
                nav_title :{
                   required: '此项为必填'
                },
                nav_url : {
                    url :'必须输入正确格式的网址'
                },
                nav_sort:{
                    number: '值为正整数',
                    range: '请输入0-255之间的数'
                }
            }
        });
    });


    var data1 = $.parseJSON('@Html.Raw(ViewBag.List)');
    var demo1 = xmSelect.render({
        el: '#demo1',
        paging: true,
        pageSize: 10,
        filterable: true,
        pageEmptyShow: false,
        data: data1
    });


</script>