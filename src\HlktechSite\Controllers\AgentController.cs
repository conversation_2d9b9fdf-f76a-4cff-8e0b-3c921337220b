﻿using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Helpers;
using DH.WebHook;

using HlktechSite.DTO;
using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek.Helpers;
using Pek.Models;

namespace HlktechSite.Controllers;

/// <summary>
/// 客户案例控制器
/// </summary>
public class AgentController : DGBaseControllerX {
    /// <summary>
    /// 客户案例首页
    /// </summary>
    /// <returns></returns>
    public IActionResult Index()
    {
        var singleArticle = SingleArticle.FindById(1) ?? new SingleArticle();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var sLan = SingleArticleLan.FindByAIdAndLIds(singleArticle.Id, WorkingLanguage.Id);
            singleArticle.Name = sLan.Name;
            singleArticle.Content = sLan.Content;
            singleArticle.MobileContent = sLan.MobileContent;
        }
        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = singleArticle.Name, IsLast = true });
        ViewBag.Locations = navigations;
        return DGView(singleArticle, true);
    }

    /// <summary>
    /// 代理招商在线申请
    /// </summary>
    /// <param name="Name">单位名称</param>
    /// <param name="ContactPerson">联系人</param>
    /// <param name="ContactAddress">联系地址</param>
    /// <param name="Phone">联系电话</param>
    /// <param name="Email">邮箱</param>
    /// <param name="Summary">申请说明</param>
    /// <param name="Code">验证码</param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult SubmitApplication(string Name, string ContactPerson, string ContactAddress, string Phone, string Email, string Summary, string Code)
    {
        var result = new DResult();
        var ybbcode = HttpContext.Session.GetString("ybbcode");

        if (Name.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("企业名称不能为空");
            return Json(result);
        }

        if (ContactPerson.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("联系人不能为空");
            return Json(result);
        }

        if (Phone.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("联系电话不能为空");
            return Json(result);
        }

        if (CurrentLanguage == "cn")
        {
            if (!ValidateHelper.IsMobile(Phone))
            {
                result.msg = GetResource("请输入正确的联系电话");
                return Json(result);
            }
        }

        if (Email.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("邮箱不能为空");
            return Json(result);
        }

        if (!ValidateHelper.IsEmail(Email))
        {
            result.msg = GetResource("请输入正确的邮箱");
            return Json(result);
        }

        if (ybbcode.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("验证码过期,请点击验证码图片刷新");
            return Json(result);
        }

        if (Code.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("验证码不能为空");
            return Json(result);
        }

        if (Code.ToLower() != ybbcode.ToLower())
        {
            result.msg = GetResource("验证码错误");
            return Json(result);
        }

        var Model = new Agent();
        Model.CompanyName = Name;
        Model.Email = Email;
        Model.Phone = Phone;
        Model.ContactPerson = ContactPerson;
        Model.Summary = Summary;
        Model.ContactAddress = ContactAddress;
        Model.IsThrough = false;
        Model.Insert();
        result.success = true;
        result.msg = GetResource("申请提交成功，我们于两个工作日内回复您");

        string template = $"#### 代理商申请通知:\n" +
           $" + 单位名称: {Name}  \n" +
           $" + 联系人: {ContactPerson}  \n" +
           $" + 联系地址: {ContactAddress}  \n" +
           $" + 电话: {Phone}   \n" +
           $" + 邮箱: {Email}  \n" +
           $" + 申请说明: {Summary}  \n";
        DingTalkRobot.OapiRobotMarkDown($"消息提醒<br />", template, new List<string>(), false);

        return Json(result);
    }

    /// <summary>
    /// 代理招商-销售网络
    /// </summary>
    /// <returns></returns>
    public IActionResult AgentMarket()
    {
        var navigations = new List<NavigationUrl>();
        var parModel = SingleArticle.FindById(1) ?? new SingleArticle();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var parModelLan = SingleArticleLan.FindByAIdAndLIds(parModel.Id, WorkingLanguage.Id);
            parModel.Name = parModelLan.Name;
        }

        var model = SingleArticle.FindById(4) ?? new SingleArticle();

        if (localizationSettings.IsEnable)
        {
            var modelLan = SingleArticleLan.FindByAIdAndLIds(model.Id, WorkingLanguage.Id);
            model.Name = modelLan.Name;
            model.Content = modelLan.Content;
            model.MobileContent = modelLan.MobileContent;
        }

        navigations.Add(new NavigationUrl { Name = parModel.Name, Url = Url.Action("Index") });
        navigations.Add(new NavigationUrl { Name = model.Name, IsLast = true });
        ViewBag.Locations = navigations;
        return DGView(model, true);
    }
}
