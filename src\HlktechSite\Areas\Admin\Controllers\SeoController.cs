﻿using System.ComponentModel;

using DG.Cube.BaseControllers;

using DH.Core.Domain.Localization;
using DH.Entity;
using DH.Extensions;
using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek;
using Pek.Models;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>Seo设置</summary>
[DisplayName("Seo设置")]
[Description("用户设备网站的Seo")]
[AdminArea]
[DHMenu(94,ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/Seo", CurrentMenuName = "Seo", CurrentIcon = "&#xe734;", LastUpdate = "20240125")]
public class SeoController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 94;

    /// <summary>
    /// SEO设置首页
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("SEO设置首页")]
    public IActionResult Index()
    {
        var seo = SeoInfo.FindBySeoType("index");
        seo = seo ?? new SeoInfo();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(seo);
    }

    /// <summary>
    /// SEO首页设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("SEO设置首页")]
    public IActionResult SEOHome(String SEO_title, String SEO_keywords, String SEO_description)
    {
        var seo = SeoInfo.FindBySeoType("index");
        if (seo == null)
        {
            seo = new SeoInfo();
        }
        if (SEO_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
        }
        if (SEO_keywords.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("关键字不能为空"), IsOk = false });
        }
        seo.SeoTitle = SEO_title.Trim();
        seo.SeoKeywords = SEO_keywords.Trim();
        seo.SeoDescription = SEO_description.SafeString().Trim();
        seo.SeoType = "index";
        seo.Save();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            foreach (var item in Languagelist)
            {
                var SeoLan = SeoInfoLan.FindBySTypeAndLId("index", item.Id);
                if (SeoLan == null)
                {
                    SeoLan = new SeoInfoLan();
                }
                SeoLan.SId = seo.Id;
                SeoLan.LId = item.Id;
                SeoLan.SeoTitle = GetRequest($"[{item.Id}]SEO_title").SafeString().Trim();
                SeoLan.SeoKeywords = GetRequest($"[{item.Id}]SEO_keywords").SafeString().Trim();
                SeoLan.SeoDescription = GetRequest($"[{item.Id}]SEO_description").SafeString().Trim();
                SeoLan.SeoType = "index";
                SeoLan.Save();
            }
            if (Languagelist.Any())
            {
                SeoInfoLan.Meta.Cache.Clear("");
            }
        }

        return Prompt(new PromptModel { Message = GetResource("设置成功"), IsOk = true });
    }


    /// <summary>
    /// SEO新闻设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("SEO新闻设置")]
    public IActionResult Journalism()
    {
        var seo = SeoInfo.FindBySeoType("journalism");
        var seo_content = SeoInfo.FindBySeoType("journalism_content");
        seo = seo ?? new SeoInfo();
        ViewBag.Content = seo_content = seo_content ?? new SeoInfo();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(seo);
    }

    /// <summary>
    /// SEO新闻设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("SEO新闻设置")]
    public IActionResult Journalism(String SEO_title, String SEO_keywords, String SEO_description, String SEO_Content_title, String SEO_Content_keywords, String SEO_Content_description)
    {
        var seo = SeoInfo.FindBySeoType("journalism");
        var seoContent = SeoInfo.FindBySeoType("journalism_content");
        if (seo == null)
        {
            seo = new SeoInfo();
        }
        if (seoContent == null)
        {
            seoContent= new SeoInfo();
        }
        if (SEO_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
        }
        if (SEO_keywords.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("关键字不能为空"), IsOk = false });
        }
        seo.SeoTitle = SEO_title.Trim();
        seo.SeoKeywords = SEO_keywords.Trim();
        seo.SeoDescription = SEO_description.SafeString().Trim();
        seo.SeoType = "journalism";
        seo.Save();
        seoContent.SeoTitle = SEO_Content_title.SafeString().Trim();
        seoContent.SeoKeywords = SEO_Content_keywords.SafeString().Trim();
        seoContent.SeoDescription = SEO_Content_description.SafeString().Trim();
        seoContent.SeoType = "journalism_content";
        seoContent.Save();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            foreach (var item in Languagelist)
            {
                var SeoLan = SeoInfoLan.FindBySTypeAndLId("journalism", item.Id);
                var SeoLanContent = SeoInfoLan.FindBySTypeAndLId("journalism_content", item.Id);
                if (SeoLan == null)
                {
                    SeoLan = new SeoInfoLan();
                }
                if (SeoLanContent == null)
                {
                    SeoLanContent = new SeoInfoLan();
                }
                SeoLan.SId = seo.Id;
                SeoLan.LId = item.Id;
                SeoLan.SeoTitle = GetRequest($"[{item.Id}]SEO_title").SafeString().Trim();
                SeoLan.SeoKeywords = GetRequest($"[{item.Id}]SEO_keywords").SafeString().Trim();
                SeoLan.SeoDescription = GetRequest($"[{item.Id}]SEO_description").SafeString().Trim();
                SeoLan.SeoType = "journalism";
                SeoLan.Save();
                SeoLanContent.SId = seoContent.Id;
                SeoLanContent.LId = item.Id;
                SeoLanContent.SeoTitle = GetRequest($"[{item.Id}]SEO_Content_title").SafeString().Trim();
                SeoLanContent.SeoKeywords = GetRequest($"[{item.Id}]SEO_Content_keywords").SafeString().Trim();
                SeoLanContent.SeoDescription = GetRequest($"[{item.Id}]SEO_Content_description").SafeString().Trim();
                SeoLanContent.SeoType = "journalism_content";
                SeoLanContent.Save();
            }
            if (Languagelist.Any())
            {
                SeoInfoLan.Meta.Cache.Clear("");
            }
        }
        return Prompt(new PromptModel { Message = GetResource("设置成功"), IsOk = true });
    }

    /// <summary>
    /// SEO案例设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("SEO案例设置")]
    public IActionResult Case()
    {
        var seo = SeoInfo.FindBySeoType("case");
        var seo_content = SeoInfo.FindBySeoType("case_content");
        seo = seo ?? new SeoInfo();
        ViewBag.Content = seo_content = seo_content ?? new SeoInfo();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(seo);
    }

    /// <summary>
    /// SEO案例设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("SEO案例设置")]
    public IActionResult Case(String SEO_title, String SEO_keywords, String SEO_description, String SEO_Content_title, String SEO_Content_keywords, String SEO_Content_description)
    {
        var seo = SeoInfo.FindBySeoType("case");
        var seoContent = SeoInfo.FindBySeoType("case_content");
        if (seo == null)
        {
            seo = new SeoInfo();
        }
        if (seoContent == null)
        {
            seoContent = new SeoInfo();
        }
        if (SEO_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
        }
        if (SEO_keywords.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("关键字不能为空"), IsOk = false });
        }
        seo.SeoTitle = SEO_title.Trim();
        seo.SeoKeywords = SEO_keywords.Trim();
        seo.SeoDescription = SEO_description.SafeString().Trim();
        seo.SeoType = "case";
        seo.Save();
        seoContent.SeoTitle = SEO_Content_title.SafeString().Trim();
        seoContent.SeoKeywords = SEO_Content_keywords.SafeString().Trim();
        seoContent.SeoDescription = SEO_Content_description.SafeString().Trim();
        seoContent.SeoType = "case_content";
        seoContent.Save();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            foreach (var item in Languagelist)
            {
                var SeoLan = SeoInfoLan.FindBySTypeAndLId("case", item.Id);
                var SeoLanContent = SeoInfoLan.FindBySTypeAndLId("case_content", item.Id);
                if (SeoLan == null)
                {
                    SeoLan = new SeoInfoLan();
                }
                if (SeoLanContent == null)
                {
                    SeoLanContent = new SeoInfoLan();
                }
                SeoLan.SId = seo.Id;
                SeoLan.LId = item.Id;
                SeoLan.SeoTitle = GetRequest($"[{item.Id}]SEO_title").SafeString().Trim();
                SeoLan.SeoKeywords = GetRequest($"[{item.Id}]SEO_keywords").SafeString().Trim();
                SeoLan.SeoDescription = GetRequest($"[{item.Id}]SEO_description").SafeString().Trim();
                SeoLan.SeoType = "case";
                SeoLan.Save();
                SeoLanContent.SId = seoContent.Id;
                SeoLanContent.LId = item.Id;
                SeoLanContent.SeoTitle = GetRequest($"[{item.Id}]SEO_Content_title").SafeString().Trim();
                SeoLanContent.SeoKeywords = GetRequest($"[{item.Id}]SEO_Content_keywords").SafeString().Trim();
                SeoLanContent.SeoDescription = GetRequest($"[{item.Id}]SEO_Content_description").SafeString().Trim();
                SeoLanContent.SeoType = "case_content";
                SeoLanContent.Save();
            }
            if (Languagelist.Any())
            {
                SeoInfoLan.Meta.Cache.Clear("");
            }
        }
        return Prompt(new PromptModel { Message = GetResource("设置成功"), IsOk = true });
    }

    /// <summary>
    /// SEO解决方案设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("SEO解决方案设置")]
    public IActionResult Solution()
    {
        var seo = SeoInfo.FindBySeoType("solution");
        var seo_content = SeoInfo.FindBySeoType("solution_content");
        seo = seo ?? new SeoInfo();
        ViewBag.Content = seo_content = seo_content ?? new SeoInfo();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(seo);
    }

    /// <summary>
    /// SEO解决方案设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("SEO解决方案设置")]
    public IActionResult Solution(String SEO_title, String SEO_keywords, String SEO_description, String SEO_Content_title, String SEO_Content_keywords, String SEO_Content_description)
    {
        var seo = SeoInfo.FindBySeoType("solution");
        var seoContent = SeoInfo.FindBySeoType("solution_content");
        if (seo == null)
        {
            seo = new SeoInfo();
        }
        if (seoContent == null)
        {
            seoContent = new SeoInfo();
        }
        if (SEO_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
        }
        if (SEO_keywords.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("关键字不能为空"), IsOk = false });
        }
        seo.SeoTitle = SEO_title.Trim();
        seo.SeoKeywords = SEO_keywords.Trim();
        seo.SeoDescription = SEO_description.SafeString().Trim();
        seo.SeoType = "solution";
        seo.Save();
        seoContent.SeoTitle = SEO_Content_title.SafeString().Trim();
        seoContent.SeoKeywords = SEO_Content_keywords.SafeString().Trim();
        seoContent.SeoDescription = SEO_Content_description.SafeString().Trim();
        seoContent.SeoType = "solution_content";
        seoContent.Save();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            foreach (var item in Languagelist)
            {
                var SeoLan = SeoInfoLan.FindBySTypeAndLId("solution", item.Id);
                var SeoLanContent = SeoInfoLan.FindBySTypeAndLId("solution_content", item.Id);
                if (SeoLan == null)
                {
                    SeoLan = new SeoInfoLan();
                }
                if (SeoLanContent == null)
                {
                    SeoLanContent = new SeoInfoLan();
                }
                SeoLan.SId = seo.Id;
                SeoLan.LId = item.Id;
                SeoLan.SeoTitle = GetRequest($"[{item.Id}]SEO_title").SafeString().Trim();
                SeoLan.SeoKeywords = GetRequest($"[{item.Id}]SEO_keywords").SafeString().Trim();
                SeoLan.SeoDescription = GetRequest($"[{item.Id}]SEO_description").SafeString().Trim();
                SeoLan.SeoType = "solution";
                SeoLan.Save();
                SeoLanContent.SId = seoContent.Id;
                SeoLanContent.LId = item.Id;
                SeoLanContent.SeoTitle = GetRequest($"[{item.Id}]SEO_Content_title").SafeString().Trim();
                SeoLanContent.SeoKeywords = GetRequest($"[{item.Id}]SEO_Content_keywords").SafeString().Trim();
                SeoLanContent.SeoDescription = GetRequest($"[{item.Id}]SEO_Content_description").SafeString().Trim();
                SeoLanContent.SeoType = "solution_content";
                SeoLanContent.Save();

            }
            if (Languagelist.Any())
            {
                SeoInfoLan.Meta.Cache.Clear("");
            }
        }
        return Prompt(new PromptModel { Message = GetResource("设置成功"), IsOk = true });
    }

    /// <summary>
    /// SEO商品设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("SEO商品设置")]
    public IActionResult Product()
    {
        var seo = SeoInfo.FindBySeoType("product");
        var seo_content = SeoInfo.FindBySeoType("product_content");
        seo = seo ?? new SeoInfo();
        ViewBag.Content = seo_content = seo_content ?? new SeoInfo();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(seo);
    }

    /// <summary>
    /// SEO商品设置
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("SEO商品设置")]
    public IActionResult Product(String SEO_title, String SEO_keywords, String SEO_description, String SEO_Content_title, String SEO_Content_keywords, String SEO_Content_description)
    {
        var seo = SeoInfo.FindBySeoType("product");
        var seoContent = SeoInfo.FindBySeoType("product_content");
        if (seo == null)
        {
            seo = new SeoInfo();
        }
        if (seoContent == null)
        {
            seoContent = new SeoInfo();
        }
        if (SEO_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
        }
        if (SEO_keywords.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("关键字不能为空"), IsOk = false });
        }
        seo.SeoTitle = SEO_title.Trim();
        seo.SeoKeywords = SEO_keywords.Trim();
        seo.SeoDescription = SEO_description.SafeString().Trim();
        seo.SeoType = "product";
        seo.Save();
        seoContent.SeoTitle = SEO_Content_title.SafeString().Trim();
        seoContent.SeoKeywords = SEO_Content_keywords.SafeString().Trim();
        seoContent.SeoDescription = SEO_Content_description.SafeString().Trim();
        seoContent.SeoType = "product_content";
        seoContent.Save();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            foreach (var item in Languagelist)
            {
                var SeoLan = SeoInfoLan.FindBySTypeAndLId("product", item.Id);
                var SeoLanContent = SeoInfoLan.FindBySTypeAndLId("product_content", item.Id);
                if (SeoLan == null)
                {
                    SeoLan = new SeoInfoLan();
                }
                if (SeoLanContent == null)
                {
                    SeoLanContent = new SeoInfoLan();
                }
                SeoLan.SId = seo.Id;
                SeoLan.LId = item.Id;
                SeoLan.SeoTitle = GetRequest($"[{item.Id}]SEO_title").SafeString().Trim();
                SeoLan.SeoKeywords = GetRequest($"[{item.Id}]SEO_keywords").SafeString().Trim();
                SeoLan.SeoDescription = GetRequest($"[{item.Id}]SEO_description").SafeString().Trim();
                SeoLan.SeoType = "product";
                SeoLan.Save();
                SeoLanContent.SId = seoContent.Id;
                SeoLanContent.LId = item.Id;
                SeoLanContent.SeoTitle = GetRequest($"[{item.Id}]SEO_Content_title").SafeString().Trim();
                SeoLanContent.SeoKeywords = GetRequest($"[{item.Id}]SEO_Content_keywords").SafeString().Trim();
                SeoLanContent.SeoDescription = GetRequest($"[{item.Id}]SEO_Content_description").SafeString().Trim();
                SeoLanContent.SeoType = "product_content";
                SeoLanContent.Save();
            }
            if (Languagelist.Any())
            {
                SeoInfoLan.Meta.Cache.Clear("");
            }
        }
        return Prompt(new PromptModel { Message = GetResource("设置成功"), IsOk = true });
    }
}
