﻿@{
}
<link rel="stylesheet" href="/js/jquery.lightbox/css/lightbox.min.css">
<script src="/js/jquery.lightbox/js/lightbox.min.js"></script>
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("友情链接管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                @*<li><a href="@Url.Action("AddFriendLinks")"><span>@T("添加")</span></a></li>*@
                <li><a href="javascript:dsLayerOpen('@Url.Action("AddFriendLinks")','新增')"><span>新增</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("标题")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
            </dl>

            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">

            </div>
        </div>
    </form>
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="提示相关设置操作时应注意的要点">操作提示</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>通过友情链接您可以，编辑、查看、删除合作伙伴信息</li>
        </ul>
    </div>
    <table class="ds-default-table">
        <thead>
            <tr>
                @*<th class="w24"></th>*@
                <th>@T("排序")</th>
                <th>@T("合作伙伴")</th>
                <th>@T("图片标识")</th>
                <th>@T("链接")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.list)
            {
                <tr id="<EMAIL>" style="background: rgb(255, 255, 255);">
                    <td class="w48 sort"><span title="@T("可编辑")" ajax_branch="goods_class_sort" datatype="number" fieldid="@item.Id" fieldname="gc_sort" ds_type="inline_edit" class="editable">@item.Sort</span></td>
                    <td class="w50pre name">
                        <span title="@T("可编辑")" required="@item.Id" fieldid="@item.Id" ajax_branch="goods_class_name" fieldname="gc_name" ds_type="inline_edit" class="editable">@item.Name</span>
                    </td>
                    <td>
                        @if (item.Pic != null || item.Pic != "")
                        {
                            <a data-lightbox="lightbox-image" data-title="" href="@item.Pic">
                                <img src="@item.Pic" onload="javascript:ResizeImage(this,31,31);">
                            </a>
                        }
                        </td>
                    <td>@item.Url</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("EditFriendLinks",new { Id=item.Id})','@item.Name')" class="dsui-btn-edit"><i class="iconfont"></i>编辑</a>
                        @*<a href="@Url.Action("EditFriendLinks",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>*@
                        @*<a href="javascript:;" onclick="submit_delete('@item.Id')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>*@
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>

                    </td>
                </tr>
            }

        </tbody>
        @*<tfoot>
                <tr class="tfoot">
                    <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                    <td colspan="16">
                        <label for="checkallBottom">@T("全选")</label>
                        &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                    </td>
                </tr>
            </tfoot>*@
    </table>
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>

<script>
    var changeNameUrl = "@Url.Action("ChangeName")";
</script>
<script type="text/javascript" src="~/static/admin/js/jquery.edit.js" charset="utf-8"></script>
<script src="~/static/admin/js/jquery_reporty.js"></script>
<script asp-location="Footer">
    @*function submit_delete(ID)
    {
        $.post("@Url.Action("Delete")", { Ids: ID }, function (res) {
            if (!res.success) {
                alert(res.msg);
            } else {
                window.location.reload(); //刷新页面
            }

        })
    }*@
    function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }
</script>
