﻿using DG.Web.Framework;

using DH;
using DH.Models.EventModel;

using NewLife;
using NewLife.Collections;
using NewLife.Reflection;

using Pek;
using Pek.Events;
using Pek.IO;
using Pek.Seo;
using Pek.Timing;

using System.Reflection;
using System.Text;

namespace HlktechSite.Events;

/// <summary>
/// 站点SiteMap消费者事件处理
/// </summary>
public class SiteMapEventConsumer
    : IConsumer<SiteMapEvent>
{
    private readonly LinkGenerator _linkGenerator;
    private readonly IHttpContextAccessor _accessor;
    private readonly String Scheme;

    public int Sort { get; set; } = 0;

    public SiteMapEventConsumer(LinkGenerator linkGenerator, IHttpContextAccessor accessor)
    {
        _linkGenerator = linkGenerator;
        _accessor = accessor;

        var url = new Uri(DHSetting.Current.CurDomainUrl);
        Scheme = url.Scheme;
    }

    public async Task HandleEventAsync(SiteMapEvent eventMessage)
    {
        CreateSitemaps();

        await Task.CompletedTask;
    }

    public void CreateSitemaps()
    {
        var types = typeof(ControllerBaseX).GetAllSubclasses();

        var typelist = types.Where(e =>
        {
            var t = e.GetCustomAttribute<DHSitemap>();
            return t != null && t.IsUse;
        });

        var SiteMapList = new List<DHSitemap>();

        foreach (var type in typelist.ToArray())
        {
            var pi = type.GetMethodEx("CreateSiteMap");
            if (pi == null) continue;
            var t = type.CreateInstance();
            var list = t.Invoke(pi)?.AsTo<IEnumerable<DHSitemap>>();

            if (list != null || list?.Any() == true)
            {
                foreach (var item in list)
                {
                    if (item.ActionName.IsNullOrWhiteSpace() || item.ControllerName.IsNullOrWhiteSpace())
                    {
                        continue;
                    }

                    switch (item.SType)
                    {
                        case SiteMap.首页:
                            if (item.DisplyOrder == 0)
                            {
                                item.DisplyOrder = 1;
                            }
                            
                            break;

                        case SiteMap.栏目首页:
                            if (item.DisplyOrder == 0)
                            {
                                item.DisplyOrder = 2;
                            }
                            break;

                        case SiteMap.栏目页:
                            if (item.DisplyOrder == 0)
                            {
                                item.DisplyOrder = 3;
                            }
                            break;

                        case SiteMap.内容页:
                            if (item.DisplyOrder == 0)
                            {
                                item.DisplyOrder = 4;
                            }
                            break;

                        case SiteMap.单页:
                            if (item.DisplyOrder == 0)
                            {
                                item.DisplyOrder = 5;
                            }
                            break;

                        case SiteMap.其他:
                            if (item.DisplyOrder == 0)
                            {
                                item.DisplyOrder = 99;
                            }
                            break;
                    }
                    item.Url = _linkGenerator.GetUriByAction(_accessor.HttpContext!, item.ActionName, item.ControllerName, item.Data, Scheme)?.Trim('/') ?? "";

                    if (!item.Url.IsNullOrWhiteSpace() && !item.UniqueSeoCode.IsNullOrWhiteSpace())
                    {
                        var url = new Uri(item.Url);
                        item.Url = item.Url.Replace(DHSetting.Current.CurDomainUrl, $"{DHSetting.Current.CurDomainUrl}/{item.UniqueSeoCode}");
                    }
                }

                SiteMapList.AddRange(list);
            }
        }

        if (SiteMapList.Count > 0)
        {
            var sitemapxml = GenerateSiteMapString(SiteMapList.OrderBy(e => e.DisplyOrder));
            if (sitemapxml.Length > 0)
            {
                var file = DHSetting.Current.WebRootPath.CombinePath("sitemap.xml").AsFile();
                if (file.Exists)
                {
                    file.Delete();
                }
                FileUtil.Write(file.FullName, sitemapxml);
            }
        }
    }

    private static String GenerateSiteMapString(IEnumerable<DHSitemap> list)
    {
        var sb = Pool.StringBuilder.Get();
        sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        sb.AppendLine("<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">");

        foreach (var item in list)
        {
            switch (item.SType)
            {
                case SiteMap.首页:
                    if (item.Priority == 0) item.Priority = 0.9;
                    if (item.ChangeFreq == SiteMapChangeFreq.none) item.ChangeFreq = SiteMapChangeFreq.always;
                    BuildSiteMap(sb, item.Url, item.ChangeFreq.ToString(), item.Priority, DateTime.Now);
                    break;

                case SiteMap.栏目首页:
                    if (item.Priority == 0) item.Priority = 0.8;
                    if (item.ChangeFreq == SiteMapChangeFreq.none) item.ChangeFreq = SiteMapChangeFreq.hourly;
                    BuildSiteMap(sb, item.Url, item.ChangeFreq.ToString(), item.Priority, null);
                    break;

                case SiteMap.栏目页:
                    if (item.Priority == 0) item.Priority = 0.8;
                    if (item.ChangeFreq == SiteMapChangeFreq.none) item.ChangeFreq = SiteMapChangeFreq.hourly;
                    BuildSiteMap(sb, item.Url, item.ChangeFreq.ToString(), item.Priority, null);
                    break;

                case SiteMap.内容页:
                    if (item.Priority == 0) item.Priority = 0.7;
                    if (item.ChangeFreq == SiteMapChangeFreq.none) item.ChangeFreq = SiteMapChangeFreq.daily;
                    BuildSiteMap(sb, item.Url, item.ChangeFreq.ToString(), item.Priority, item.UpdateTime);
                    break;

                case SiteMap.单页:
                    if (item.Priority == 0) item.Priority = 0.5;
                    if (item.ChangeFreq == SiteMapChangeFreq.none) item.ChangeFreq = SiteMapChangeFreq.weekly;
                    BuildSiteMap(sb, item.Url, item.ChangeFreq.ToString(), item.Priority, item.UpdateTime);
                    break;

                case SiteMap.其他:
                    if (item.Priority == 0) item.Priority = 0.4;
                    if (item.ChangeFreq == SiteMapChangeFreq.none) item.ChangeFreq = SiteMapChangeFreq.weekly;
                    BuildSiteMap(sb, item.Url, item.ChangeFreq.ToString(), item.Priority, null);
                    break;
            }
        }

        sb.AppendLine("</urlset>");
        return sb.Put(true);
    }

    private static void BuildSiteMap(StringBuilder sb, String url, String changefreq, Double priority, DateTime? lastmod)
    {
        sb.AppendLine("   <url>");
        sb.AppendLine($"    <loc>{url}</loc>");

        if (lastmod.HasValue)
        {
            sb.AppendLine($"    <lastmod>{lastmod.Value.FormatDate(0)}</lastmod>");
        }

        sb.AppendLine($"    <changefreq>{changefreq}</changefreq>");
        sb.AppendLine($"    <priority>{priority}</priority>");
        sb.AppendLine("   </url>");
    }

}
