﻿using System.ComponentModel;
using System.Dynamic;

using DG.Cube.BaseControllers;
using DG.Cube.Models;

using DH;
using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek;
using Pek.Helpers;
using Pek.Models;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>下载分类</summary>
[DisplayName("下载分类")]
[Description("用于下载分类的管理")]
[AdminArea]
[DHMenu(89,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/DownloadCategory", CurrentMenuName = "DownloadCategoryList", CurrentIcon = "&#xe652;", LastUpdate = "20240125")]
public class DownloadCategoryController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 89;

    /// <summary>
    /// 产品型号列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("下载分类列表")]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var list = DownloadCategory.FindAllByLevel().OrderBy(e => e.Id);
        foreach (var item in list)
        {
            if (item.ChildList.Any())
            {
                item.subset = true;
            }
        }
        viewModel.list = list;
        return View(viewModel);
    }

    /// <summary>
    /// 获取下载分类下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取下载分类下级数据")]
    public IActionResult GetSubordinateData(string Id)
    {
        var zList = new List<Hierarchy>();
        if (Id.IsNullOrWhiteSpace())
        {
            return Json(new { });
        }

        var list = DownloadCategory.FindAllByParentId(Id.ToInt());
        foreach (var item in list)
        {
            var model = new Hierarchy
            {
                gc_name = item.Name,
                CreateUser = item.CreateUser,
                CreateTime = item.CreateTime.ToString(),
                gc_id = item.Id,
                gc_parent_id = item.ParentId
            };

            var exc = DownloadCategory.FindAllByParentId(item.Id);
            if (exc.Count > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.gc_sort = item.DisplayOrder;
            model.deep = item.Level;
            zList.Add(model);
        }
        return Json(new { zList });
    }

    /// <summary>
    /// 编辑下载分类
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑下载分类")]
    public IActionResult EditDownload(Int32 Id)
    {
        dynamic viewModel = new ExpandoObject();
        var model = DownloadCategory.FindById(Id);
        if (model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }

        var list = new List<DownloadCategory>();
        var live = DownloadCategory.FindAllByLevel();//一级数据

        GetCategoryList(live, list);
        viewModel.Plist = list;
        
        viewModel.Model = model;
        return View(viewModel);
    }

    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取是否存在该名称")]
    public IActionResult GetByName(string gc_name, int gc_id)
    {
        var model = DownloadCategory.FindByName(gc_name.SafeString().Trim());
        if (gc_id == 0) {
            if (model != null) {
                return Json(false);
            }
            return Json(true);
        }
        if (model != null && model.Id != gc_id)
        {
            return Json(false);
        }

        return Json(true);
    }

    /// <summary>
    /// 修改下载分类
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="gc_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改分类接口")]
    public IActionResult EditDownload(String gc_name, Int32 gc_parent_id, Int32 gc_id, Int16 DisplayOrder)
    {
        gc_name = gc_name.SafeString().Trim();
        var model = DownloadCategory.FindByName(gc_name);
        if (model != null && model.Id != gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("该下载分类名称已存在") });
        }
        
        var models = DownloadCategory.FindById(gc_id);
        if (models == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
        }
        var oldName = models.Name;
        models.Name = gc_name;
        models.ParentId = gc_parent_id;
        models.DisplayOrder = DisplayOrder;
        var pmodel = DownloadCategory.FindById(gc_parent_id);
        var pName = "自己";
        if (pmodel == null)
        {
            models.ParentIdList = gc_id.ToString();
            models.Level = 0;
        }
        else
        {
            models.ParentIdList = pmodel.ParentIdList + "," + gc_id;
            models.Level = pmodel.Level + 1;
            pName = pmodel.Name;
        }
        if (models.Level > 2)
        {
            return Prompt(new PromptModel { Message = GetResource("分类不能超过三层") });
        }

        models.Update();
        Loger.UserLog("修改分类", $"修改下载分类，名称：{oldName??""}=>{models?.Name??""}，父级：=>{pName??""}");

        DownloadCategory.Meta.Cache.Clear("");//清除缓存

        //循环修改子集的父级Id集合
        var zList = DownloadCategory.FindAllByParentId(gc_id);
        foreach (var item in zList)
        {
            item.ParentIdList = models.ParentIdList + "," + item.Id;
            var slist = DownloadCategory.FindAllByParentId(item.Id);
            foreach (var row in slist)
            {
                item.ParentIdList = item.ParentIdList + "," + row.Id;
            }

            item.ParentIdList = item.ParentIdList.Trim(',');
            slist.Save();
        }
        zList.Save();
        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 添加下载分类
    /// </summary>
    /// <param name="parent_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加下载分类")]
    public IActionResult CreateDownload(int parent_id = 0)
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.ParentId = parent_id;
        
        var list = new List<DownloadCategory>();
        var live = DownloadCategory.FindAllByLevel();//一级数据

        GetCategoryList(live, list);
        viewModel.Plist = list;
        
        var displayOrder = DownloadCategory.FindMax("DisplayOrder");
        ViewBag.DisplayOrder = displayOrder + 1;
        
        return View(viewModel);
    }

    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IList<DownloadCategory> levelList, IList<DownloadCategory> list)
    {
        if (levelList.Count > 0) {
            foreach (var item in levelList) {
                list.Add(item);

                var level = DownloadCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 下载分类新增
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("下载分类新增")]
    public IActionResult CreateDownload(String gc_name, Int32 gc_parent_id, Int16 DisplayOrder)
    {
        gc_name = gc_name.SafeString().Trim();
        var model = DownloadCategory.FindByName(gc_name);
        if (model != null)
        {
            return Prompt(new PromptModel { Message = GetRequest("下载分类名称已存在") });
        }

        model = new DownloadCategory
        {
            Name = gc_name, ParentId = gc_parent_id, DisplayOrder = DisplayOrder
        };
        model.Insert();

        var pmodel = DownloadCategory.FindById(gc_parent_id);
        if (pmodel == null)
        {
            model.Level = 0;
            model.ParentIdList = model.Id.ToString();
        }
        else
        {
            model.Level = pmodel.Level + 1;
            model.ParentIdList = pmodel.ParentIdList + "," + model.Id;
        }
        model.Update();
        DownloadCategory.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    public IActionResult Delete(string ids)
    {
        var res = new DResult();

        var list = DownloadCategory.FindByIds(ids);
        var delList = new List<DownloadCategory>();

        res = DeleteDownloadCategory(res, delList, list);

        if (!res.msg.IsNullOrWhiteSpace())
        {
            return Json(res);
        }

        if (delList.Delete(true) > 0)
            DownloadCategory.Meta.Cache.Clear("");
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="delList"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private DResult DeleteDownloadCategory(DResult res, IList<DownloadCategory> delList, IList<DownloadCategory> list)
    {
        if (list.Count > 0)
        {
            foreach (var item in list)
            {
                var listKnowledge = Download.FindAllByDId(item.Id);
                if (listKnowledge.Any())
                {
                    res.msg = String.Format(GetResource("选中的{0}有关联文章数据 不允许被删除"), item.Name);
                    return res;
                }
                else
                {
                    delList.Add(item);
                    var childList = DownloadCategory.FindAllByParentId(item.Id);
                    res = DeleteDownloadCategory(res, delList, childList);
                }
            }
        }
        return res;
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var models = DownloadCategory.FindById(id);

        if (models == null) return Json(false);

        if (column == "gc_name")
        {
            var model = DownloadCategory.FindByName(value);
            if (model != null && model.Id != id)
            {
                return Json(false);
            }

            models.Name = value;
        }
        else if (column == "gc_sort")
        {
            models.DisplayOrder = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        models.Update();

        return Json(true);
    }
}
