﻿@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("上传设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("默认图片")</span></a></li>
                <li><a href="@Url.Action("Upload_Type")"><span>@T("上传设置")</span></a></li>
                <li><a href="@Url.Action("Upload_Type1")"><span>@T("私有上传设置")</span></a></li>
                <li><a href="@Url.Action("CDNSetting")" class="current"><span>@T("CDN设置")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("UploadCDN", "UploadSetting", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
    {
        <div class="ncap-form-default">
            <dl>
                <dt>@T("是否启用CDN")</dt>
                <dd>
                    <div class="onoff">
                        <label for="Enable_show1" class="cb-enable @(DG.CDNOptionSetting.Current.Enable ? "selected" : "")">@T("是")</label>
                        <label for="Enable_show0" class="cb-disable @(!DG.CDNOptionSetting.Current.Enable ? "selected" : "")">@T("否")</label>
                        <input id="Enable_show1" name="Enable" value="1" type="radio" @(DG.CDNOptionSetting.Current.Enable ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        <input id="Enable_show0" name="Enable" value="0" type="radio" @(!DG.CDNOptionSetting.Current.Enable ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                    </div>
                </dd>
            </dl>
            <dl>
                <dt>@T("CDN绑定域名")</dt>
                <dd>
                    <input id="Url" name="Url" value="@DG.CDNOptionSetting.Current.Url" class="input-txt" type="text">&nbsp;&nbsp;@T("CDN绑定CName的域名")
                </dd>
            </dl>
            <dl>
                <dt></dt>
                <dd><input class="btn" type="submit" value="@T("提交")" /></dd>
            </dl>
        </div>
    }
</div>