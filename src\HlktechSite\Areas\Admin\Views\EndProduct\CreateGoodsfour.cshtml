﻿@{
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
}
<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>成品管理</h3>
                <h5></h5>
            </div>
            <ul class="add-goods-step">
                <li>
                    <i class="iconfont">&#xe600;</i>
                    <h6>STEP.1</h6>
                    <h2>选择成品分类</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li>
                    <i class="icon iconfont">&#xe731;</i>
                    <h6>STEP.2</h6>
                    <h2>填写成品详情</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li>
                    <i class="icon iconfont">&#xe6a2;</i>
                    <h6>STEP.3</h6>
                    <h2>上传成品图片</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li class="current">
                    <i class="icon iconfont">&#xe64d;</i>
                    <h6>STEP.4</h6>
                    <h2>成品发布成功</h2>
                </li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>



    
    <div class="alert alert-block hr32">
        <h2><i class="iconfont mr10">&#xe64d;</i>恭喜您，成品发布成功！&nbsp;&nbsp;</h2>
        <div class="hr16"></div>
        <div>
            @*<a class="dssc-btn dssc-btn-green ml30" href="/index.php/admin/Goods/add_gift.html?commonid=124"><i class="iconfont">&#xe753;</i>为该成品添加赠品捆绑</a>*@
            @*<a class="dssc-btn dssc-btn-orange ml10" href="/index.php/admin/Goods/add_combo.html?commonid=124"><i class="iconfont">&#xe67e;</i>为该成品添加推荐组合</a>*@
        </div>
        <div class="hr16"></div>
        <strong>
            <a class="ml30" href="@Url.Action("Index")">返回列表&gt;&gt;</a>
            <a class="ml30" href="@Url.Action("UpdateGoods",new { Id=ViewBag.Id})">重新编辑刚发布的成品&gt;&gt;</a>
        </strong>
        <div class="hr16"></div>
        <h4 class="ml10">您还可以:</h4>
        <ul class="ml30">
            <li>1.  继续&quot; <a href="@Url.Action("CreateGoodsOne")">发布新成品</a>&quot;</li>
            <li>2.  管理&quot;<a href="@Url.Action("Index")">成品列表</a>&quot;</li>
            @*<li>3.  参与商城的&quot; <a href="/index.php/admin/Activity/index.html">专题活动</a> &quot;</li>*@
        </ul>
    </div>








</div>