﻿@using HlktechSite
@{
    Html.AppendCssFileParts("~/css/product.css");
    Html.AppendCssFileParts("~/css/summarize.css");

    var Product = Model.Product as Goods;
    var Titles = ViewBag.Titles as String;

    Html.AppendTitleParts(Product.ProductModelName + DG.Setting.Current.PageTitleSeparator + Titles + DG.Setting.Current.PageTitleSeparator + Product.Name + DG.Setting.Current.PageTitleSeparator + "Hi_Link");

    var cdn = CDN.GetCDN();

    var ShopUrl = Model.ShopUrl as List<String>;
    var TaoBaoUrl1 = Model.TaoBaoUrl1 as List<String>;

    var list1 = new List<Datadownload>();
    var list2 = new List<Datadownload>();
    var list3 = new List<Datadownload>();
}
<style>
    .Pro-detailed-top li {
        width: 15% !important;
    }

    .qrblock {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

        .qrblock > div {
            margin-left: 10px;
            margin-right: 10px;
        }

        .qrblock .qrcode {
            width: 150px;
        }

    body .qrblock .tip {
        text-align: center;
        font-size: 0.8em;
        margin: 0;
        padding: 0;
        margin-top: 5px;
    }

    .qrblock .tip span {
        font-size: inherit;
        margin-top: 0;
    }
</style>

<div class="top">
    <img src="@(cdn)/images/cpzt2.jpg" />
    <div>
        <h2>@T("产品中心")</h2>
        <P>@T("深耕行业十数载，累计服务超过10W+客户")</P>
    </div>
</div>

<div class="navigation">
    <div class="navigation-con">
        <div class="navigation-con-left">
            <span>
                @await Component.InvokeAsync("Location", new { model = Model.Locations })
            </span>
        </div>
        <div class="navigation-con-right">
        </div>
    </div>
</div>
<div class="Pro-intr">
    <div>
        <div class="border">
            <div class="magnify">
                <div class="large" style="background:url(@Product.Image)"></div>
                <img class="Pro-intr-BigImg small" src="@Product.Image" alt="@Product.ProductModelName" />
            </div>
        </div>
        <center>
        </center>
        <div class="imgs-div">
            <ul>
                @foreach (var item in Model.ImgList as IEnumerable<HlktechSite.Entity.GoodsImages>)
                {
                    <li><img src="@item.Url" alt="@Product.ProductModelName" /></li>
                }
            </ul>
        </div>
    </div>
    <div class="Pro-intr-right">
        <h2 title="@Model.Product.Name">@Model.Product.Name</h2>
        <span>@Model.Product.UsageScenarios</span>
        <b>@T("浏览次数") : <i>@Model.Product.Clicks</i></b>
        @*<p>@Model.Product.UsageScenarios</p>*@
        <p>@Model.Product.Summary</p>
        <p class="Pro-intr-right-menu">
            <a href="@Url.DGAction("Index","Apply")" target="_blank">@T("样机申请")</a>
            <a class="downlodad" href="#datum">@T("资料下载")</a>
            @*<a href="http://shop.hlktech.com/" target="_blank">@T("在线购买")</a>*@
            <a href="//ask.hlktech.com" target="_blank">@T("技术支持")</a>
            @if (Product.PcGouUrl.IsNullOrEmpty())
            {
                <a href="#ditch" class="tobuy">@T("在线购买")</a>
            }
            else
            {
                <a href="@Product.PcGouUrl" class="tobuy" target="_blank">@T("在线购买")</a>
            }
        </p>
        <div class="share">
            <div class="bdsharebuttonbox">
                <a href="#" class="bds_more" data-cmd="more"></a>
                <a href="#" class="bds_qzone" data-cmd="qzone" title="@T("分享到QQ空间")"></a>
                <a href="#" class="bds_tsina" data-cmd="tsina" title="@T("分享到新浪微博")"></a>
                <a href="#" class="bds_tqq" data-cmd="tqq" title="@T("分享到腾讯微博")"></a>
                <a href="#" class="bds_renren" data-cmd="renren" title="@T("分享到人人网")"></a>
                <a href="#" class="bds_weixin" data-cmd="weixin" title="@T("分享到微信")"></a>
                <a href="#" class="bds_douban" data-cmd="douban" title="@T("分享到豆瓣网")"></a>
            </div>
        </div>
        @if (language.UniqueSeoCode == "cn")
        {
            <div class="qrblock">
                <div>
                    <img class="qrcode" id="qrwx" src="~/images/qrwxkf.png" alt="" />
                    <p class="tip"><span>微信扫码咨询</span></p>
                </div>
                <div>
                    <img class="qrcode" id="qrshop" src="~/images/qrhlkshop.jpg" alt="" />
                    <p class="tip"><span>微信扫码前往商城</span></p>
                </div>
            </div>
        }
    </div>
</div>
<div class="Pro-detailed">
    <div class="Pro-detailed-top">
        <ul>
            <li id="trait" class="liSelected">@T("概述特点")</li>
            <li id="parameter">@T("参数规格")</li>
            <li id="datum">@T("资料下载")</li>
            <li id="exchange">@T("知识问答")</li>
            <li id="scheme">@T("解决方案")</li>
            <li id="ditch">@T("购买渠道")</li>
        </ul>
    </div>
    <div class="Pro-detailed-bottom">
        <div class="trait cut">
            <div class="trait-con">
                @Html.Raw(Model.Product.Content)
            </div>
        </div>
        <div class="parameter cut" style="padding-top:67px;">
            @Html.Raw(Model.Product.Specifications)
        </div>
        <div id="datum" class="datum cut have-backgroup">
            <ul>
                <li>
                    <span>@T("开发资料")</span>
                    <span>
                        @foreach (var item in Model.Development as List<Datadownload>)
                        {
                            if (list1.Contains(item)) continue;
                            list1.Add(item);

                            <i>
                                <a href="http://h.hlktech.com@(item.resource_url)" target="_blank">@item.resource_name<img src="@(cdn)/images/down.png" /></a>
                            </i>
                        }
                    </span>
                </li>
                <li>
                    <span>@T("软件应用")</span>
                    <span>
                        @foreach (var item in Model.Application as List<Datadownload>)
                        {
                            if (list2.Contains(item)) continue;
                            list2.Add(item);

                            <i>
                                <a href="http://h.hlktech.com@(item.resource_url)" target="_blank">@item.resource_name<img src="@(cdn)/images/down.png" /></a>
                            </i>
                        }

                    </span>
                </li>
                <li>
                    <span>@T("通用软件")</span>
                    <span>

                        @foreach (var item in Model.GeneralSoftware as List<Datadownload>)
                        {
                            if (list3.Contains(item)) continue;
                            list3.Add(item);

                            <i>
                                <a href="http://h.hlktech.com@(item.resource_url)" target="_blank">@item.resource_name<img src="@(cdn)/images/down.png" /></a>
                            </i>
                        }

                    </span>
                </li>
                <li>
                    <span>@T("常见问题")</span>
                    <span>
                        @*<i>
                            RM04问题文档
                            <a href="#"><img src="@(cdn)/images/down.png" /></a>
                            </i>
                            <i>
                            常见问题
                            <a href="#"><img src="@(cdn)/images/down.png" /></a>
                            </i>*@
                    </span>
                </li>
            </ul>
        </div>
        <div class="exchange cut have-backgroup">
            <ul>
                @foreach (var item in Model.knowledgeList)
                {
                    <li>
                        <span>@item.Name</span>
                        <span>
                            <img src="@(cdn)/images/time.png" /><i style="font-style:normal;text-align:left;width: 200px; display: inline-block;">@item.CreateTime</i>
                            <a href="@("http://h.hlktech.com/Knowledge/KnowledgeDetails/"+item.Id+".html")" target="_blank">@T("查看详情")></a>
                        </span>
                    </li>
                }
            </ul>
        </div>
        <div class="scheme cut have-backgroup">
            <ul>
                @foreach (var item in Model.SolutionList)
                {
                    <li>
                        <span>@item.Name</span>
                        <span>
                            <img src="@(cdn)/images/time.png" />@item.CreateTime
                            <a href="@Url">@T("查看详情")></a>
                        </span>
                    </li>
                }
            </ul>
        </div>
        <div class="ditch cut have-backgroup">
            <div>
                <span>
                    @T("在线购买")
                </span>
                <span>
                    @{
                        if (language.UniqueSeoCode == "cn")
                        {
                            if (ShopUrl.Any())
                            {
                                foreach (var item in ShopUrl)
                                {
                                    <a href="@item" target="_blank">
                                        <img src="@(cdn)/images/gfsc.png" />
                                        <Strong style="color: red;">@T("官方商城直达下单")</Strong>
                                    </a>
                                }
                            }
                            else
                            {
                                <a href="@T("商城链接")" target="_blank">
                                    <img src="@(cdn)/images/gfsc.png" />
                                    @T("官方商城")
                                </a>
                            }

                            // if (TaoBaoUrl1.Any())
                            // {
                            //     foreach (var item in TaoBaoUrl1)
                            //     {
                            //         <a href="@item" target="_blank">
                            //             <img src="@(cdn)/images/tbsc.png" />
                            //             <Strong style="color: red;">@T("淘宝直达下单")</Strong>
                            //         </a>
                            //     }
                            // }
                            // else
                            // {
                            //     <a href="https://mall.jd.com/index-12293539.html" target="_blank">
                            //         <img src="@(cdn)/images/jdimg.png" />
                            //         @T("京东店铺")
                            //     </a>
                            // }
                            <a href="https://mall.jd.com/index-12293539.html" target="_blank">
                                    <img src="@(cdn)/images/jdimg.png" />
                                    @T("京东店铺")
                                </a>

                            <a href="https://hilink.tmall.com/" target="_blank">
                                <img src="@(cdn)/images/tmsc.png" />@T("天猫商城")
                            </a>
                            <a href="https://hi-link.taobao.com/" target="_blank">
                                <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")1
                            </a>
                            <a href="https://hlktech.taobao.com/" target="_blank">
                                <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")2
                            </a>
                            <a href="https://shop57596328.taobao.com/" target="_blank">
                                <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")3
                            </a>
                            <a href="https://shop311490340.taobao.com/" target="_blank">
                                <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")4
                            </a>
                        }
                        else
                        {
                            <a href="https://www.aliexpress.com/store/911797719" target="_blank">
                                <img src="@(cdn)images/aliexpress.png" style="width: 30px;" />Aliexpress
                            </a>
                            <a href="https://www.aliexpress.com/store/1101964598" target="_blank">
                                <img src="@(cdn)images/aliexpress.png" style="width: 30px;" />Aliexpress
                            </a>
                        }
                    }

                </span>
            </div>
            <div>
                <span>
                    @T("大客户通道")
                </span>
                <span>
                    <i>
                        @T("样品申请")
                        <b>
                            [@T("会2个工作日内回复")]
                        </b>
                        <a href="@Url.DGAction("Index","Apply")" target="_blank">@T("立即申请样品")</a>
                    </i>
                    <i>
                        @T("批量采购")
                        <b>
                            [@T("联系所属省份的大客户经理")]
                        </b>
                        <a href="@Url.DGAction("Index","Contact")" target="_blank">@T("查看联系方式")</a>
                    </i>
                    <i>
                        @T("定制申请")
                        <b>
                            [@T("会2个工作日内回复")]
                        </b>
                        <a href="@Url.DGAction("Index","Apply")" target="_blank">@T("立即申请定制")</a>
                    </i>
                </span>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" asp-location="Footer">
    $(function () {

        var imgjdata = `
            {
              "title": "",
              "id": 123,
              "start": 0,
              "data": [
                {
                  "alt": "微信扫码咨询",
                  "pid": 1,
                  "src": "/images/qrwxkf.png",
                  "thumb": "/images/qrwxkf.png"
                },
                {
                  "alt": "微信扫码前往商城",
                  "pid": 2,
                  "src": "/images/qrhlkshop.jpg",
                  "thumb": "/images/qrhlkshop.jpg"
                }
              ]
            }
        `;
        var imgdata = JSON.parse(imgjdata);

        if($("#qrwx").length>0){
            $("#qrwx").click(function(){
                imgdata.start = 0;
                console.log(layer);
                console.log(imgdata);
                layer.photos({
                    photos:imgdata,
                    shift: 0,
                    shade:0.5,
                    shadeClose: true,
                    anim:5
                });
            })
        }
        

        if($("#qrshop").length>0){
            $("#qrshop").click(function(){
            imgdata.start = 1;
                layer.photos({
                    photos:imgdata,
                    shade:0.5,
                    shift: 0,
                    anim:5,
                    shadeClose: true,
                });
            })
        }
        


        $(".Pro-detailed-top li").click(function () {
            $(".liSelected").removeClass("liSelected");
            $(this).addClass("liSelected");
            $(".cut").hide()
            $("." + $(this)[0].id).show()
        });

        $(".downlodad").click(function () {
            var down = $(".Pro-detailed-top li:eq(2)")
            $(".liSelected").removeClass("liSelected");
            $(down).addClass("liSelected");
            $(".cut").hide()
            $("." + $(down).attr("id")).show()
        })

        $(".tobuy").click(function () {
            var down = $(".Pro-detailed-top li:eq(5)")
            $(".liSelected").removeClass("liSelected");
            $(down).addClass("liSelected");
            $(".cut").hide()
            $("." + $(down).attr("id")).show()
        })

        // 定义图像的实际尺寸、

        var native_width = 0;

        var native_height = 0;

        // 首先、我们应该获得图像的实际尺寸、（本地的图片）
        $('.small').load(function () {
            // 这里我们需要重新创建一个和之前相同的图像对象、

            // 因为我们不能直接获得图像尺寸的宽高、

            // 因为我们在HTML里已经指定了图片宽度为200px、

            var img_obj = new Image();

            img_obj.src = $(this).attr('src');



            //  在这里这段代码写在这里是非常有必要的、

            //  如果在图像加载之前就访问的话、return的宽高值为0、

            native_width = img_obj.width;

            native_height = img_obj.height;



            // 现在、我来开始写鼠标移动的函数、mousemove()

            $('.magnify').mousemove(function (e) {

                // 获得鼠标X轴和Y轴的坐标

                //  先获得magnify相对与document的定位position

                var magnify_offset = $(this).offset();



                // 这里我们用鼠标相对与文档的位置减去鼠标相对于magnify这个人容器的位置 来得到鼠标的位置

                var mouse_x = e.pageX - magnify_offset.left;

                var mouse_y = e.pageY - magnify_offset.top;





                // 现在、我们来调整一下放大镜的隐藏与显示、

                if (mouse_x > 0 && mouse_y > 0 && mouse_x < $(this).width() && mouse_y < $(this).height()) {

                    $('.large').fadeIn(100);

                } else {

                    $('.large').fadeOut(100);

                }

                if ($('.large').is(':visible')) {

                    // 放大镜图片背景的定位是根据鼠标在小图片上改变的位置来改变的、

                    // 因此、我们应该先得到放大的比例、来定位这个放大镜里背景图片的定位、



                    /*

                    var ratio_x = mouse_x/$('.small').width();//得到的是缩放的比例

                    var large_x = ratio_x*native_width;

                    // 我们需要让它在放大镜的中间位置显示、

                    large_x = large_x - $('.large').width()/2;

                    // 因为背景图片的定位、这里需要转化为负值、

                    large_x = large_x*-1;

                    // 现在我们来整合一下所有的计算步骤、

                    */

                    var rx = Math.round(mouse_x / $('.small').width() * native_width - $('.large').width() / 2) * -1;

                    var ry = Math.round(mouse_y / $('.small').height() * native_height - $('.large').height() / 2) * -1;

                    var bgp = rx + 'px ' + ry + 'px';



                    // 现在我们应该来写放大镜跟随鼠标的效果、

                    // 放大镜移动的位置 相对于文档的位置 减去 放大镜相对于放大这个层的offset的位置、

                    // 再减去放大镜宽高的一半、保证放大镜的中心跟随鼠标



                    var gx = mouse_x - $('.large').width() / 2;

                    var gy = mouse_y - $('.large').height() / 2;



                    $('.large').css({

                        'left': gx,

                        'top': gy,

                        'backgroundPosition': bgp

                    })

                }

            })

        })


    })
    window._bd_share_config = {
        "common": {
            "bdSnsKey": {},
            "bdText": "",
            "bdMini": "2",
            "bdMiniList": false,
            "bdPic": "",
            "bdStyle": "0",
            "bdSize": "24"
        },
        "share": {}
    };
    with (document)
    0[(getElementsByTagName('head')[0] || body)
        .appendChild(createElement('script')).src = '/static/api/js/share.js?v=89860593.js?cdnversion='
        + ~(-new Date() / 36e5)];
    $(".imgs-div li").click(function () {
        $(".Pro-intr li").removeClass("selected");
        $(this).addClass("selected");
        $(".small").attr("src", $(this).find("img").attr("src"));
        $(".large").attr("style", `background:url(${$(this).find("img").attr("src")})`);
    });
    $(".imgs-div li:eq(0)").click();
    var scroll_width = 20;  // 设置每次滚动的长度，单位 px
    var scroll_events = "mousewheel DOMMouseScroll MozMousePixelScroll";  // 鼠标滚轮滚动事件名
    $(".imgs-div").on(scroll_events, function (e) {
        var delta = e.originalEvent.wheelDelta;  // 鼠标滚轮滚动度数
        // 滑轮向上滚动，滚动条向左移动，scrollleft-
        if (delta > 0) {
            $(".imgs-div").scrollLeft($(".imgs-div").scrollLeft() - scroll_width);
            // 这里的两个html是指包含横向滚动条的那一层
        }
        // 滑轮向下滚动，滚动条向右移动，scrollleft+
        else {
            $(".imgs-div").scrollLeft($(".imgs-div").scrollLeft() + scroll_width);
        }
        return false;
    });
</script>
