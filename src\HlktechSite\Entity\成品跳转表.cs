﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>成品跳转表</summary>
[Serializable]
[DataObject]
[Description("成品跳转表")]
[BindIndex("IU_DG_JumpProduct_Name", true, "Name")]
[BindTable("DG_JumpProduct", Description = "成品跳转表", ConnName = "DG", DbType = DatabaseType.None)]
public partial class JumpProduct : IJumpProduct, IEntity<IJumpProduct>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>产品名称</summary>
    [DisplayName("产品名称")]
    [Description("产品名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Name", "产品名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _InfoUrl;
    /// <summary>推文链接</summary>
    [DisplayName("推文链接")]
    [Description("推文链接")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("InfoUrl", "推文链接", "")]
    public String? InfoUrl { get => _InfoUrl; set { if (OnPropertyChanging("InfoUrl", value)) { _InfoUrl = value; OnPropertyChanged("InfoUrl"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _AndroidPaths;
    /// <summary>国内Android下载地址</summary>
    [DisplayName("国内Android下载地址")]
    [Description("国内Android下载地址")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("AndroidPaths", "国内Android下载地址", "")]
    public String? AndroidPaths { get => _AndroidPaths; set { if (OnPropertyChanging("AndroidPaths", value)) { _AndroidPaths = value; OnPropertyChanged("AndroidPaths"); } } }

    private String? _AndroidPaths1;
    /// <summary>国外Android下载地址</summary>
    [DisplayName("国外Android下载地址")]
    [Description("国外Android下载地址")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("AndroidPaths1", "国外Android下载地址", "")]
    public String? AndroidPaths1 { get => _AndroidPaths1; set { if (OnPropertyChanging("AndroidPaths1", value)) { _AndroidPaths1 = value; OnPropertyChanged("AndroidPaths1"); } } }

    private String? _IosPaths;
    /// <summary>IOS下载地址</summary>
    [DisplayName("IOS下载地址")]
    [Description("IOS下载地址")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("IosPaths", "IOS下载地址", "")]
    public String? IosPaths { get => _IosPaths; set { if (OnPropertyChanging("IosPaths", value)) { _IosPaths = value; OnPropertyChanged("IosPaths"); } } }

    private String? _AppLogo;
    /// <summary>App Logo</summary>
    [DisplayName("AppLogo")]
    [Description("App Logo")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("AppLogo", "App Logo", "")]
    public String? AppLogo { get => _AppLogo; set { if (OnPropertyChanging("AppLogo", value)) { _AppLogo = value; OnPropertyChanged("AppLogo"); } } }

    private String? _JdUrl;
    /// <summary>京东链接</summary>
    [DisplayName("京东链接")]
    [Description("京东链接")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("JdUrl", "京东链接", "")]
    public String? JdUrl { get => _JdUrl; set { if (OnPropertyChanging("JdUrl", value)) { _JdUrl = value; OnPropertyChanged("JdUrl"); } } }

    private String? _TbUrl;
    /// <summary>淘宝链接</summary>
    [DisplayName("淘宝链接")]
    [Description("淘宝链接")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("TbUrl", "淘宝链接", "")]
    public String? TbUrl { get => _TbUrl; set { if (OnPropertyChanging("TbUrl", value)) { _TbUrl = value; OnPropertyChanged("TbUrl"); } } }

    private String? _PddUrl;
    /// <summary>拼多多链接</summary>
    [DisplayName("拼多多链接")]
    [Description("拼多多链接")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("PddUrl", "拼多多链接", "")]
    public String? PddUrl { get => _PddUrl; set { if (OnPropertyChanging("PddUrl", value)) { _PddUrl = value; OnPropertyChanged("PddUrl"); } } }

    private String? _AdWord;
    /// <summary>广告词</summary>
    [DisplayName("广告词")]
    [Description("广告词")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("AdWord", "广告词", "")]
    public String? AdWord { get => _AdWord; set { if (OnPropertyChanging("AdWord", value)) { _AdWord = value; OnPropertyChanged("AdWord"); } } }

    private Int32 _Clicks;
    /// <summary>点击数</summary>
    [DisplayName("点击数")]
    [Description("点击数")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Clicks", "点击数", "")]
    public Int32 Clicks { get => _Clicks; set { if (OnPropertyChanging("Clicks", value)) { _Clicks = value; OnPropertyChanged("Clicks"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IJumpProduct model)
    {
        Id = model.Id;
        Name = model.Name;
        InfoUrl = model.InfoUrl;
        Content = model.Content;
        AndroidPaths = model.AndroidPaths;
        AndroidPaths1 = model.AndroidPaths1;
        IosPaths = model.IosPaths;
        AppLogo = model.AppLogo;
        JdUrl = model.JdUrl;
        TbUrl = model.TbUrl;
        PddUrl = model.PddUrl;
        AdWord = model.AdWord;
        Clicks = model.Clicks;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "InfoUrl" => _InfoUrl,
            "Content" => _Content,
            "AndroidPaths" => _AndroidPaths,
            "AndroidPaths1" => _AndroidPaths1,
            "IosPaths" => _IosPaths,
            "AppLogo" => _AppLogo,
            "JdUrl" => _JdUrl,
            "TbUrl" => _TbUrl,
            "PddUrl" => _PddUrl,
            "AdWord" => _AdWord,
            "Clicks" => _Clicks,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "InfoUrl": _InfoUrl = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "AndroidPaths": _AndroidPaths = Convert.ToString(value); break;
                case "AndroidPaths1": _AndroidPaths1 = Convert.ToString(value); break;
                case "IosPaths": _IosPaths = Convert.ToString(value); break;
                case "AppLogo": _AppLogo = Convert.ToString(value); break;
                case "JdUrl": _JdUrl = Convert.ToString(value); break;
                case "TbUrl": _TbUrl = Convert.ToString(value); break;
                case "PddUrl": _PddUrl = Convert.ToString(value); break;
                case "AdWord": _AdWord = Convert.ToString(value); break;
                case "Clicks": _Clicks = value.ToInt(); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static JumpProduct? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据产品名称查找</summary>
    /// <param name="name">产品名称</param>
    /// <returns>实体对象</returns>
    public static JumpProduct? FindByName(String? name)
    {
        if (name == null) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name.EqualIgnoreCase(name));

        // 单对象缓存
        return Meta.SingleCache.GetItemWithSlaveKey(name) as JumpProduct;

        //return Find(_.Name == name);
    }
    #endregion

    #region 字段名
    /// <summary>取得成品跳转表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>产品名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>推文链接</summary>
        public static readonly Field InfoUrl = FindByName("InfoUrl");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>国内Android下载地址</summary>
        public static readonly Field AndroidPaths = FindByName("AndroidPaths");

        /// <summary>国外Android下载地址</summary>
        public static readonly Field AndroidPaths1 = FindByName("AndroidPaths1");

        /// <summary>IOS下载地址</summary>
        public static readonly Field IosPaths = FindByName("IosPaths");

        /// <summary>App Logo</summary>
        public static readonly Field AppLogo = FindByName("AppLogo");

        /// <summary>京东链接</summary>
        public static readonly Field JdUrl = FindByName("JdUrl");

        /// <summary>淘宝链接</summary>
        public static readonly Field TbUrl = FindByName("TbUrl");

        /// <summary>拼多多链接</summary>
        public static readonly Field PddUrl = FindByName("PddUrl");

        /// <summary>广告词</summary>
        public static readonly Field AdWord = FindByName("AdWord");

        /// <summary>点击数</summary>
        public static readonly Field Clicks = FindByName("Clicks");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得成品跳转表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>产品名称</summary>
        public const String Name = "Name";

        /// <summary>推文链接</summary>
        public const String InfoUrl = "InfoUrl";

        /// <summary>内容</summary>
        public const String Content = "Content";

        /// <summary>国内Android下载地址</summary>
        public const String AndroidPaths = "AndroidPaths";

        /// <summary>国外Android下载地址</summary>
        public const String AndroidPaths1 = "AndroidPaths1";

        /// <summary>IOS下载地址</summary>
        public const String IosPaths = "IosPaths";

        /// <summary>App Logo</summary>
        public const String AppLogo = "AppLogo";

        /// <summary>京东链接</summary>
        public const String JdUrl = "JdUrl";

        /// <summary>淘宝链接</summary>
        public const String TbUrl = "TbUrl";

        /// <summary>拼多多链接</summary>
        public const String PddUrl = "PddUrl";

        /// <summary>广告词</summary>
        public const String AdWord = "AdWord";

        /// <summary>点击数</summary>
        public const String Clicks = "Clicks";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
