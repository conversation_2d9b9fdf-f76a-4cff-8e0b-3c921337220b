﻿
.agent-content-top {
    padding-top: 26px;
    background-color: #FBFAFF;
}


    .agent-content-top h2 {
        color: #36364C;
        font-size: 17px;
        text-align: center;
        margin: 0px;
        padding-bottom: 13px;
    }

    .agent-content-top p {
        width: 91.47%;
        margin: 0 auto;
        font-size: 8px;
        text-align: center;
        letter-spacing: 1.5px;
        margin-bottom: 5px;
    }

    .agent-content-top img {
        width: 100%;
    }


.choose-us {
    padding-top:18px;
    background-color: #FBFAFF;
}

.choose-us h2 {
    color: #35364C;
    font-size: 17px;
    text-align: center;
    margin-bottom: 13px;
}

.choose-us p {
    width: 91.2%;
    margin: 0 auto;
    color: #666;
    font-size: 8px;
    text-align: center;
    margin-bottom: 28px;
}

.choose-us > div {
    width: 100%;
    position: relative;
    height: 276px;
}

    .choose-us > div > img {
        width: 100%;
        height: 100%;
        float: left;
    }

    .choose-us > div > div {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        bottom: 0px;
        padding-left: 9.13%;
        padding-top: 38.5px;
    }

        .choose-us > div > div > span {
            display: inline-block;
            width: 25.53%;
            margin-right: 6.13%;
            text-align: center;
            margin-bottom: 31px;
        }

            .choose-us > div > div > span > img {
                max-width: 54px;
                display: block;
                margin: 0 auto;
                margin-bottom: 10px;
            }

            .choose-us > div > div > span > i {
                color: #fff;
                font-size: 9px;
            }

.support {
    background-color: #FBFAFF;
}

.support h2 {
    color: #35364C;
    font-size: 17px;
    text-align: center;
    padding-top: 34px;
    padding-bottom: 13px;
    margin: 0px;
}

.support p {
    width: 91.2%;
    margin: 0 auto;
    color: #666;
    font-size: 8px;
    text-align: center;
    margin-bottom: 17px;
}

.support div {
    overflow: hidden;
}

    .support div span {
        display: inline-block;
        padding-left: 9.84%;
        margin-left: 3.73%;
        width: 44.3%;
        position: relative;
        float: left;
        border: 1px solid #E9E9E9;
        margin-top: 7px;
    }

        .support div span:nth-child(2n) {
            margin-left: 3.99%;
        }

        .support div span .agent-logo {
            left: 10.5%;
            bottom: 22px;
            right: auto;
            max-width: 15px;
        }

        .support div span b {
            font-weight: 400;
            margin-top: 15px;
            font-size: 8px;
            color: #000;
            display: block;
            margin-bottom: 6px;
        }

        .support div span i {
            display: inline-block;
            width: 97.1%;
            font-size: 7px;
            height: 27px;
            color: #999;
            position: relative;
            z-index: 210;
        }

        .support div span img {
            position: absolute;
            right: 5.55%;
            max-width: 25px;
            bottom: 0px;
        }

.condition {
    background-color: #FBFAFF;
    padding-bottom:38px;
    padding-top:24px;
}


.condition h2 {
    color: #35364C;
    margin-top:0px;
    font-size: 17px;
    margin-bottom: 23px;
    text-align: center;
}

.condition ul {
    overflow: hidden;
}

    .condition ul li {
        margin-left: 4%;
        position: relative;
        width: 42.65%;
        float: left;
        padding-top: 11px;
        padding-left: 3.19%;
        background: #FFFFFF;
        box-shadow: 0px 3px 8px 1px rgba(177, 174, 174, 0.2);
        margin-bottom: 14px;
        text-align: left;
        min-height: 92px;
    }

        .condition ul li span {
            display: block;
            color: #3E3E3E;
            position: relative;
            font-size: 9px;
            height: 18px;
            margin-bottom: 8px;
        }

            .condition ul li span:after {
                content: "";
                position: absolute;
                left: 0px;
                bottom: 0px;
                width: 10%;
                height: 1px;
                background-color: #5D78FF;
            }

        .condition ul li i {
            display: inline-block;
            color: #9E9E9E;
            font-size: 7px;
            width: 72.3%;
            height: 40px;
        }

        .condition ul li img {
            position: absolute;
            max-width: 27px;
            top: 11px;
            right: 8.75%;
        }

.condition > div {
    width: 92.13%;
    margin: 0 auto;
    background: #FFFFFF;
    box-shadow: 0px 3px 8px 1px rgba(177, 174, 174, 0.51);
    border-radius: 5px;
    padding-top: 24px;
}

    .condition > div > h2 {
        margin: 0px;
        margin-bottom: 10px;
        color: #333333;
        font-size: 17px;
    }

    .condition > div > span {
        color: #F60E0E;
        font-size: 7px;
        text-align: center;
        display: block;
    }

    .condition > div > .input-group {
        display: flex;
        margin-top: 12px;
    }

        .condition > div > .input-group > i {
            padding-right:1.94%;
            display: inline-block;
            width: 24.02%;
            text-align: right;
            color: #333333;
            font-size: 8px;
        }
.input-group .form-control {
    height: 15px;
    width: 57.89%;
    font-size: 7px;
    padding-left: 2%;
}
.input-group .form-control[name=Code]{
    width:19.1%;
    margin-right:1.47%;
}

    .input-group #change_captcha {
        width: 14.62%;
        height:15px;
    }

#Tijiao {
    display: inline-block;
    width: 57.89%;
    height: 15px;
    background-color: #409EFF;
    border-radius: 3px;
    text-align: center;
    font-size: 8px;
    color: #FEFEFE;
    line-height:15px;
    margin-top:13px;
    text-decoration:none;
}

.condition{
    text-align:center;
}

.condition>div> p {
    margin-top:12px;
    padding-bottom:14px;
    font-size:8px;
    padding-right: 15.92%;
    text-align: right;
}
    .condition > div > p >a{
        text-decoration:none !important;
    }


/*销售网络*/
.agent-title {
    font-size: 17px;
    color: #35364C;
    text-align: center;
    padding-top: 29px;
    padding-bottom: 23px;
    background-color: #FBFAFF;
    margin: 0px;
}

.agent-con {
    background-color: #FBFAFF;
}

    .agent-con > img {
        width: 81.87%;
        margin: 0 auto;
        display: block
    }

    .agent-con div {
        padding-top: 21px;
        padding-bottom: 5px;
    }

    .agent-con div {
        display: block;
        width: 60.57%;
        margin: 0 auto;
    }

        .agent-con div strong, .agent-con div em {
            display: block;
            margin-bottom: 11px;
            font-size: 11px;
            font-style: normal;
        }
