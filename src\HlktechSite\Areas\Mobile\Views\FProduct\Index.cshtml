﻿@{
    Layout = null;
    var UrlSuffix = DG.Setting.Current.IsAllowUrlSuffix ? DG.Setting.Current.UrlSuffix : "";
}

<!DOCTYPE html>
<html style="height: 100%;">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover" />
    <title>@T("海凌科成品新品推荐")</title>
    <meta name="description" content="海凌科下载中心为您提供WIFI模块,电源模块,蓝牙模块,人脸识别模块,语音识别模块,3G模块,4G模块,串口服务器,GPRS模块,工业级路由器等产品的资料下载" />
    <meta name="keywords" content="海凌科产品资料下载,WIFI模块,电源模块,蓝牙模块,人脸识别模块,语音识别模块,3G模块,4G模块,串口服务器,GPRS模块,工业级路由器" />
    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <link href="~/css/weui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>
    <script src="~/static/usercenter/pc/js/jquery-1.12.2.min.js"></script>
    <style>
        body, html {
            height: 100%;
            -webkit-tap-highlight-color: transparent;
        }

        .jx {
            margin: 5px;
        }

        .mcolor {
            background-color: white;
            color: black;
            height: 60px;
            font-size: 20px;
        }

        .listdiv {
            width: 100%;
        }

        ._right {
            float: right;
        }

        .change-fontSize {
            font-size: 16px;
        }

        ._fullheight {
            height: 100%;
        }

        .div_boder {
            border: 1px solid #eeeeee;
        }

        .tabheight {
            height: calc(100% - 44px);
        }

        .displaynone {
            display: none;
        }

        .displayinline {
            display: inline;
        }
    </style>
</head>
<body ontouchstart="" class="_fullheight" data-weui-theme="light">
    <div id="search">
        <div class="weui-search-bar" id="searchBar">
            <form class="weui-search-bar__form">
                <div class="weui-search-bar__box">
                    <i class="weui-icon-search"></i>
                    <input type="search" class="weui-search-bar__input" id="searchInput" placeholder="@T("搜索")" required="">
                    <a href="javascript:" class="weui-icon-clear" id="searchClear"></a>
                </div>
                <label class="weui-search-bar__label" id="searchText">
                    <i class="weui-icon-search"></i>
                    <span>@T("搜索")</span>
                </label>
            </form>
            <a href="javascript:" class="weui-search-bar__cancel-btn" id="searchCancel">@T("取消")</a>
        </div>
        <div class="weui-cells searchbar-result" id="searchResult" hidden="hidden">
        </div>
    </div>

    <div class="weui-tab tabheight" id="contentTab">
        <div class="weui-tab__panel _fullheight">
            <div id="tab0" class="cancellongpress"></div>
        </div>
    </div>
    <script>
        layui.use(['element', 'layer','jquery'], function () {
            var $searchBar = $('#searchBar'),
                $searchResult = $('#searchResult'),
                $searchText = $('#searchText'),
                $searchInput = $('#searchInput'),
                $searchClear = $('#searchClear'),
                $searchCancel = $('#searchCancel');

            var currentId = "#tab@(Model.CurrentId)";
            var hash = document.location.hash;
            if (hash.length > 0) {
                currentId = hash;
            }

            $(".weui-tabbar__item" + currentId.substring(currentId.length - 1, currentId.length)).addClass("weui-bar__item_on")

            function hideSearchResult() {
                $searchResult.hide();
                $searchInput.val('');
            }
            function cancelSearch() {
                hideSearchResult();
                $searchBar.removeClass('weui-search-bar_focusing');
                $searchText.show();
                tabShow();
            }

            $searchText.on('click', function () {
                $searchBar.addClass('weui-search-bar_focusing');
                $searchInput.focus();
                tabHide();
            });

            $searchInput
                .on('blur', function () {
                    if (!this.value.length) cancelSearch();
                })
                .on('input', function () {
                    if (this.value.length) {
                        $searchResult.show();
                    } else {
                        $searchResult.hide();
                    }
                })
                .on('input propertychange', function () {
                    searchlike(this.value);
                })
                ;

            $searchClear.on('click', function () {
                hideSearchResult();
                $searchInput.focus();
            });

            $searchCancel.on('click', function () {
                cancelSearch();
                $searchInput.blur();
            });

            getModuleInfo(currentId);
        })

        function getModuleInfo(tabid) {
            console.log(tabid);
            var typeId = tabid.substring(tabid.length - 1, tabid.length);
            //$.post("/Mobile/Download/queryModuleInfoByType", { typeId }, function (res) {
            $.post("@Url.Action("queryModuleInfoByType", "FProduct")", { typeId }, function (res) {
                if (res.success) {
                    var str = '';
                    for (var i = 0; i < res.data.length; i++) {

                        str += "<div class='layui-card jx' onclick='toDetail(\"" + res.data[i].Id + "\")'><div class='layui-card-header div_boder'>" + res.data[i].Name + "<i class='layui-icon _right'>&#xe602;</i></div></div>";
                    }

                    $(tabid).html(str);

                }
            })
        }

        function toDetail(id) {
            window.location.href = "@Url.Action("FDetail","FProduct")/" + id + "@(UrlSuffix)";
        }

        function searchlike(value) {
            if (value.length < 1 || value == "" || value == " ") {
                return;
            }
            $.ajax({
                type: "POST",
                url: "@Url.Action("searchLike", "FProduct")",
                async: true,
                dataType: 'json',
                data: { name: value },
                success: function (res) {
                    if (res.success) {
                        var res_str = "";
                        for (var i = 0; i < res.data.length; i++) {
                            if (i > 5) break;
                            res_str += '<div class="weui-cell weui-cell_access" onclick="toDetail(\'' + res.data[i].Id + '\')"><div class="weui-cell__bd weui-cell_primary"><p>' + res.data[i].Name + '</p></div></div>'
                        }
                        $("#searchResult").html(res_str);
                    }
                },
                error: function (res) {
                    layer.alert(res.msg);
                }
            });
        }

        function tabHide() {
            $("#contentTab").hide();
        }
        function tabShow() {
            $("#contentTab").show();
        }
    </script>

</body>
</html>