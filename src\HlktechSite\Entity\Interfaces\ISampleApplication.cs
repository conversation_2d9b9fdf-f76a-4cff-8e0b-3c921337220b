﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>样品申请</summary>
public partial interface ISampleApplication
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>公司名称</summary>
    String? ComName { get; set; }

    /// <summary>企业人数</summary>
    String? ComPeople { get; set; }

    /// <summary>企业官网</summary>
    String? Website { get; set; }

    /// <summary>年营业额</summary>
    String? Turnover { get; set; }

    /// <summary>企业类型</summary>
    String? ComType { get; set; }

    /// <summary>企业地址</summary>
    String? Address { get; set; }

    /// <summary>联系人</summary>
    String? Linkman { get; set; }

    /// <summary>职位</summary>
    String? Position { get; set; }

    /// <summary>电话</summary>
    String? Phone { get; set; }

    /// <summary>QQ</summary>
    String? QQ { get; set; }

    /// <summary>邮箱</summary>
    String? Email { get; set; }

    /// <summary>传真</summary>
    String? Fax { get; set; }

    /// <summary>产品型号</summary>
    String? ProModel { get; set; }

    /// <summary>月需求量</summary>
    String? Demand { get; set; }

    /// <summary>申请主题</summary>
    String? Theme { get; set; }

    /// <summary>需求描述 </summary>
    String? Describe { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
