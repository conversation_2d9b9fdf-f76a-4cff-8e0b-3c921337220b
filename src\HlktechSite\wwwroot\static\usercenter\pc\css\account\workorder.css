﻿body {
    background-color: #F0F2F5 !important;
}


.Profile-con, .add-workorder {
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 34px;
}

.Profile-con-top, .add-workorder-top {
    text-align: right;
    line-height: 39px;
    margin-bottom: 30px;
}

    .Profile-con-top > i {
        font-size: 16px;
        font-weight: bold;
        color: rgba(80,80,80,1);
        margin-right: 19px;
    }

    .Profile-con-top > a {
        width: 89px;
        height: 39px;
        font-size: 12px;
        color: rgba(255,255,255,1);
        text-align: center;
        display: inline-block;
        background-color: #44A0FF;
        border-radius: 4px;
        font-weight: bold;
        text-decoration: none;
    }


        .Profile-con-top > a:hover, add-workorder-top > a:hover {
            color: rgba(255,255,255,1);
            text-decoration: none;
        }

    .Profile-con-top a img {
        margin-right: 5px;
    }

.dropdown-toggle {
    width: 134px;
    height: 39px;
    border: 1px solid #409EFF;
    margin-right: 21px;
}

    .dropdown-toggle:hover {
        color: #333 !important;
    }

.Profile-con-list {
    padding-top: 31px;
}

.Profile-list-div {
    height: 163px;
    padding-left: 52px;
    background: rgba(255,255,255,1);
    box-shadow: 0px 4px 18px 0px rgba(78,83,87,0.16);
    border-radius: 10px;
    padding-top: 21px;
    margin-bottom: 34px;
}

    .Profile-list-div > h2 {
        color: #505050;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        cursor: pointer;
        padding-bottom: 30px;
    }

        .Profile-list-div > h2:hover {
            color: rgba(0,170,255,1);
        }

    .Profile-list-div > p {
        padding-bottom: 30px;
        font-size: 14px;
        color: rgba(79,79,79,1);
        margin: 0px;
    }


    .Profile-list-div > span {
        font-size: 14px;
        font-weight: 400;
        color: rgba(51,51,51,1);
    }


.Profile-list > a:hover {
    text-decoration: none;
}


.Profile-list i {
    width: 50px;
    display: inline-block;
    height: 20px;
    text-align: center;
    border-radius: 3px;
    color: rgba(255,255,255,1);
    font-size: 14px;
}

.IsClose {
    background-color: #909399;
}

.IsReply {
    background-color: #67C23A;
}

.IsSubmit {
    background-color: #409EFF;
}

.add-workorder-top > .input-group {
    display: inline-block;
    margin-right: 20px;
    margin-top: 0px;
}


.add-workorder-top > a {
    vertical-align: text-bottom;
    margin-bottom: 2px;
    width: 89px;
    height: 39px;
    line-height: 39px;
    font-size: 12px;
    color: rgba(255,255,255,1);
    text-align: center;
    display: inline-block;
    background-color: #44A0FF;
    border-radius: 4px;
    font-weight: bold;
    text-decoration: none;
}

    .add-workorder-top > a > img {
        margin-right: 5px;
    }

.add-workorder-con {
    background: rgba(255,255,255,1);
    box-shadow: 0px 8px 29px 0px rgba(97,97,97,0.24);
    border-radius: 25px;
    padding-left: 50px;
    padding-top: 21px;
}

    .add-workorder-con > p {
        margin-top: 31px;
        text-align: left;
    }

        .add-workorder-con > p:first-child {
            margin-top: 0px;
        }

.sub-box-left-title, .sub-box-left-title2 {
    position: relative;
}


.sub-input {
    max-width: 424px;
    margin-top: 8px;
    width: 100%;
}

.sub-box-left i, .add-workorder-con i {
    display: inline-block;
    margin-top: 34px;
    font-style: normal;
    margin-bottom: -17px;
    color: #999999;
}

.hava-placeholder {
    position: relative;
}

.input-textarea {
    max-width: 1000px;
}

.hava-placeholder:before {
    content: "[您想要实现什么功能？]：";
    display: inline-block;
    color: #141313;
    position: absolute;
    left: 11px;
    top: 7px;
    font-size: 18px;
    z-index: 100;
}

.hava-placeholder:after {
    content: "[您想要实现什么功能？]：";
    display: inline-block;
    color: #141313;
    position: absolute;
    left: 11px;
    bottom: 70px;
    font-size: 18px;
    z-index: 100;
}

.form-control {
    border-radius: 7px !important;
    padding-left: 10px;
}

.hava-placeholder b {
    font-weight: normal;
    display: inline-block;
    color: #141313;
    position: absolute;
    left: 11px;
    top: 65px;
    font-size: 18px;
    z-index: 100;
}

textarea {
    width: 1000px;
    resize: none;
    height: 217px !important;
    width: 100%;
    max-width: none;
}

.input-group2 {
    display: inline-block;
    width: 80%;
}


.sub-box-left-title:after {
    content: "*";
    color: #FF0052;
    position: absolute;
    left: 60px;
}

.sub-box-left-title2:after {
    content: "*";
    color: #FF0052;
    position: absolute;
    left: 185px;
}

.sub-button {
    margin-top: 42px;
    display: inline-block;
    width: 146px;
    height: 43px;
    line-height: 43px;
    text-align: center;
    border-radius: 21.5px;
    color: #fff !important;
    font-size: 20px;
    background-color: #3A75FF;
    margin-bottom: 117px;
    text-decoration: none;
}

    .sub-button:hover, .sub-button:active, .sub-button:visited, .sub-button:link {
        color: #fff !important;
        text-decoration: none;
    }


input[type=file] {
    display: none !important;
}

.add-workorder-con > span {
    display: block;
}

    .add-workorder-con > span > i {
        display: inline-block;
        width: 87px;
        text-align: center;
        height: 31px;
        font-size: 16px;
        color: rgba(11,10,10,1);
        margin-right: 8px;
        cursor: pointer;
    }

    .add-workorder-con > span > .selected {
        color: rgba(30,136,229,1);
        position: relative;
    }


        .add-workorder-con > span > .selected:after {
            content: '';
            left: 0px;
            right: 0px;
            position: absolute;
            background-color: rgba(30,136,229,1);
            bottom: 0px;
            height: 3px;
        }

.drop-down-one {
    flex: 2;
}

.btn-group {
    margin-left: 5px;
}

    .btn-group button {
        padding-left: 10px;
        padding-right: 10px;
        text-align: justify;
        width: 100%;
        border: 1px solid #adadad !important;
        color: #262829 !important;
    }

.precise .dropdown-menu > li > a {
    padding-left: 10px;
    padding-right: 10px;
}

.drop-down-two, .drop-down-three {
    flex: 1;
}

.drop-down {
    max-width: 479px;
    display: flex;
}

.btn-group button span {
    float: right;
    margin-top: 10px;
}

.upfile:hover {
    text-decoration: none;
}

.menu-link-myWorkOrder {
    background-image: url('../img/uc/menu-link-myWorkOrder.png');
}

    .menu-link-myWorkOrder:hover {
        background-image: url('../img/uc/menu-link-myWorkOrder2.png');
    }

.menu-link-submit {
    background-image: url('../img/uc/menu-link-submit.png');
}

    .menu-link-submit:hover {
        background-image: url('../img/uc/menu-link-submit2.png');
    }

.menu-link-service {
    background-image: url('../img/uc/menu-link-service.png');
}

    .menu-link-service:hover {
        background-image: url('../img/uc/menu-link-service2.png');
    }

.auth-code {
    width: 90px;
    height: 36px;
    cursor: pointer;
}

.search-list {
    position: relative;
}

    .search-list > ul {
        display: none;
        z-index: 10;
        position: absolute;
        top: 40px;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.44);
    }

        .search-list > ul > li {
            background-color: #fff;
            cursor: pointer;
            position: relative;
            display: block;
            width: 462px;
            padding: 5px;
            height: 34px;
            line-height: 25px;
        }

            .search-list > ul > li:not(:last-child) {
                border-bottom: 1px solid rgba(0, 0, 0, 0.16)
            }




/*工单详情*/
.Work-con {
    margin: 0 auto;
/*    max-width: 1363px;*/
    background: rgba(255,255,255,1);
    box-shadow: 1px 8px 29px 0px rgba(97,97,97,0.24);
    border-radius: 25px;
    margin-top: 73px;
    margin-bottom: 60px;
    padding-top: 61px;
    padding-left: 116px;
    padding-right: 116px;
}


/*工单详情*/
    .Work-con{
        text-align:right;
        padding-bottom:70px;
    }

    .Work-con h2 {
        text-align:left;
        font-size: 26px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: rgba(60,60,60,1);
        height: 58px;
    }

.share {
    display: flex;
    height: 47px;
    border-bottom: 1px solid rgba(216,216,216,1);
}
    .share > div {
        flex: 1;
    }

.share > a {
    display: inline-block;
    width: 122px;
    height: 32px;
    background: rgba(26,138,250,1);
    border-radius: 6px;
    line-height: 32px;
    text-align: center;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: rgba(255,255,255,1);
    text-decoration:none !important;
}

    .share > a > img {
        margin-right: 7px;
        margin-bottom: 2px;
    }

.Work-con > span {
    display:block;
    text-align:right;
    margin-top:18px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: rgba(52,52,52,1);
}

.leave>p {
    display: flex;
    margin: 0px;
    margin-top: 50px;
}
.leave>p:first-child {
    margin-top: 37px;
}


    .leave > p > span {
        text-align: center;
        width: 72px;
    }

    .leave > p > span>img {
        width: 100%;
        height:72px;
        margin-bottom:5px;
    }

    .leave > p > i {
        width: calc(100% - 102px);
        text-align: left;
        position: relative;
        margin-left: 30px;
        font-size: 16px;
        min-height: 220px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: rgba(43,43,43,1);
        padding-left: 30px;
        padding-top: 12px;
        padding-right: 45px;
        flex: 1;
        background: rgba(247,247,247,1);
        border: 1px solid rgba(218, 218, 218, 1);
        border-radius: 10px;
    }
        .leave > p > i:before {
            content: "";
            position: absolute;
            left: -17px;
            top: 10px;
            width: 0px;
            height: 0px;
            border-right: 17px solid rgba(247,247,247,1);
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
            z-index: 2;
        }
        .leave > p > i:after {
            content: "";
            position: absolute;
            left: -18px;
            top: 8px;
            width: 0px;
            height: 0px;
            border-right: 18px solid rgba(218, 218, 218, 1);
            border-top: 17px solid transparent;
            border-bottom: 17px solid transparent;
        }



.leave > .oneself {
    display: block;
    overflow: hidden;
}
.leave > .oneself > span {
    float: right;
}

    .leave > .oneself > i {
        margin-left: 0px;
        flex: none;
        float: left;
        padding-left: 20px;
        padding-right: 37px;
    }

        .leave > .oneself > i:before {
            left: auto;
            right: -17px;
            border-right: 0px;
            border-left: 17px solid rgba(247,247,247,1);
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
        }

        .leave > .oneself > i:after {
            left: auto;
            right: -18px;
            border-right: 0px;
            border-left: 18px solid rgba(218, 218, 218, 1);
            border-top: 17px solid transparent;
            border-bottom: 17px solid transparent;
        }

            .Work-con > a {
                display: inline-block;
                text-align: center;
                width: 112px;
                height: 41px;
                line-height: 41px;
                background: rgba(64,158,255,1);
                border-radius: 6px;
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: rgba(255,255,255,1);
                margin-top: 37px;
                text-decoration:none !important;
            }

.Work-con > a {
    text-decoration: none;
}
.have-like {
    background-color: #ccc !important;
    cursor: no-drop;
}

.like i {
   color:inherit;
}