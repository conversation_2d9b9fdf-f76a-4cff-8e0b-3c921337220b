﻿@{
    Html.AppendTitleParts(T("产品详情").Text);
    var cdn = CDN.GetCDN();

    Html.AppendCssFileParts("~/iconfont/iconfont.css");
}

<style>
    .detail * {
        padding: 0;
        margin: 0;
        list-style: none;
        text-decoration: none;
        outline: none;
    }

    .detail .banner {
        height: 55vh;
        background-color: #EBEDEC;
        box-sizing: border-box;
        position: relative;
    }

        .detail .banner img {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
        }


    .detail .path {
        background-color: white;
        display: flex;
        justify-content: center;
        align-items: stretch;
    }

    .detail .inner-box {
        width: 1000px;
    }

    .detail .path .inner-box .location {
        height: 50px;
        line-height: 50px;
    }

    .detail .goods {
        /*height: 600px;*/
        background-color: #FAF9FE;
        display: flex;
        justify-content: center;
    }

        .detail .goods .info {
            display: flex;
            justify-content: space-between;
        }

            .detail .goods .info .left {
                flex-basis: 400px;
                overflow: hidden;
                box-sizing: border-box;
            }

                .detail .goods .info .left .main {
                    height: 450px;
                }

                    .detail .goods .info .left .main img {
                        width: 100%;
                        height: 100%;
                        background-color: deepskyblue;
                    }

                .detail .goods .info .left .other {
                    margin-top: 10px;
                }

                    .detail .goods .info .left .other > li {
                        margin-right: 3px;
                        margin-bottom: 3px;
                        display: inline-block;
                    }

                    .detail .goods .info .left .other img {
                        width: 70px;
                        height: 70px;
                        background-color: orange;
                    }

            .detail .goods .info .right {
                flex-basis: 380px;
                flex-grow: 1;
                /*background-color: antiquewhite;*/
                margin-left: 60px;
            }

                .detail .goods .info .right .title {
                    /*font-size: 1.9em;*/
                    margin-bottom: 10px;
                }

                .detail .goods .info .right .datas > div {
                    height: 60px;
                    line-height: 60px;
                    border-bottom: 1px solid #ccc;
                }

                .detail .goods .info .right .datas span {
                    font-size: 1.1em;
                    font-weight: 500;
                }

                .detail .goods .info .right .datas .look .val {
                    color: red;
                }

                .detail .goods .info .right .btns {
                    margin-top: 30px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                    .detail .goods .info .right .btns button {
                        width: 250px;
                        height: 50px;
                        line-height: 50px;
                        text-align: center;
                        color: white;
                        cursor: pointer;
                        border-style: none;
                    }

                    .detail .goods .info .right .btns #consult {
                        background-color: #2C9FFB;
                    }

                    .detail .goods .info .right .btns #by {
                        background-color: #DE0347;
                    }

                .detail .goods .info .right .flg {
                    height: 60px;
                    font-size: 1.3em;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-top: 30px;
                    color: #999;
                }

                    .detail .goods .info .right .flg .iconfont {
                        font-size: 2.5em;
                        transform: translateY(5px);
                        margin-right: 10px;
                    }

                    .detail .goods .info .right .flg > span {
                        font-size: 0.9em;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                    }

                        .detail .goods .info .right .flg > span > .text {
                            height: 35px;
                            line-height: 35px;
                            display: inline-block;
                            padding-right: 20px;
                        }

                        .detail .goods .info .right .flg > span:nth-child(-n+2) > .text {
                            border-right: 2px solid #999;
                            margin-right: 30px;
                        }

    .detail .tabs {
        margin-top: 50px;
        display: flex;
        justify-content: space-around;
        align-items: stretch;
        border-top: 1px solid rgba(230,230,230,1);
        border-bottom: 1px solid rgba(230,230,230,1);
    }

        .detail .tabs li {
            cursor: pointer;
            height: 60px;
            line-height: 60px;
            width: 70px;
            /*box-sizing: border-box;*/
            white-space: nowrap;
            font-weight: bold;
            text-align: center;
        }

            .detail .tabs li:hover {
                /*border-bottom: 2px solid deepskyblue;*/
                color: deepskyblue;
            }

        .detail .tabs .this {
            border-bottom: 2px solid deepskyblue;
            color: deepskyblue;
        }


    .detail .page-box {
    }


    .detail .page {
        display: flex;
        justify-content: center;
        padding-top: 10px;
    }

    /*图文价绍*/
    .detail #page1 .exp {
        font-size: 1.1em;
        text-align: center;
        font-family: 微软雅黑,sans-serif;
        margin-bottom: 5px;
    }

    .detail #page1 .intr {
        margin-bottom: 20px;
    }

    .detail #page1 img {
        width: 100%;
        background-color: cornflowerblue;
        height: 500px;
    }

    /*产品配置*/
    .detail #page2 {
    }

        .detail #page2 .topimg {
            width: 100%;
            height: 400px;
            background-color: aliceblue;
            margin-bottom: 10px;
        }

        .detail #page2 dl {
            padding-top: 60px;
            padding-bottom: 50px;
            border-top: 1px solid rgb(220,220,220);
        }

        .detail #page2 dt {
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .detail #page2 dd {
            margin-bottom: 10px;
        }
</style>


<div class="detail">
    <div class="banner">
        <img src="" alt="" />
    </div>
    <div class="path">
        <div class="inner-box">
            <div class="location">
                <a href="#">首页</a><var>&gt;</var>
                <a href="#">产品详情</a><var>&gt;</var>
                <a href="#">测试产品名称</a>
            </div>
        </div>
    </div>
    <div class="goods">
        <div class="inner-box">
            <div class="info">
                <div class="left">
                    <div class="main">
                        <img src="" alt="" />
                    </div>
                    <ul class="other">
                        @for (int i = 0; i < 5; i++)
                        {
                            <li> <img src="" /> </li>
                        }
                    </ul>
                </div>
                <div class="right">
                    <h1 class="title">
                        GICISKY 云考勤打卡机 考勤机人脸识别无接触手机考勤门禁
                    </h1>
                    <div class="datas">
                        <div class="no">
                            <span class="name">@T("物料编号"):</span>
                            <span class="val">10057555</span>
                        </div>
                        <div class="look">
                            <span class="name">@T("浏览次数"):</span>
                            <span class="val">10554</span>
                        </div>
                        <div class="rmk">
                            <span class="name">@T("产品标语"):</span>
                            <span class="val">不是所有的牛奶都叫特仑苏</span>
                        </div>
                    </div>
                    <div class="btns">
                        <button id="consult">@T("咨询客服")</button>
                        <button id="by">@T("在线购买")</button>
                    </div>
                    <div class="flg">
                        <span>
                            <i class="iconfont icon-aixin"></i>
                            <span class="text">全国联保</span>
                        </span>
                        <span>
                            <i class="iconfont icon-rexian"></i>
                            <span class="text">售后服务热线</span>
                        </span>
                        <span>
                            <i class="iconfont icon-baoxiu"></i>
                            <span class="text">保修政策</span>
                        </span>
                    </div>
                </div>
            </div>
            <ul class="tabs">
                <li tag="#page1" class="this">@T("图文详情")</li>
                <li tag="#page2">@T("参数规格")</li>
                <li tag="#page3">@T("资料下载")</li>
                <li tag="#page4">@T("购买渠道")</li>
            </ul>
        </div>
    </div>
    <div class="page-box">
        <div id="page1" class="page this">
            <div class="inner-box">
                @for (int i = 0; i < 4; i++)
                {
                    <div class="intr">
                        <p class="exp">回复哈算法设计开发和环境发送返回数据发送发发送皮肤就<br />发顺丰还是,发丝发顺丰发生覅司法审判发顺丰</p>
                        <img src="" alt="" />
                    </div>
                }
            </div>
        </div>
        <div id="page2" class="page">
            <div class="inner-box">
                <img class="topimg" src="" alt="" />
                @for (int i = 0; i < 4; i++)
                {
                    <div>
                        <dl>
                            <dt>产品配置:</dt>
                            <dd>处理器：双核Ai芯片</dd>
                            <dd>内存：8M</dd>
                            <dd>联网方式：以太网、WIFI</dd>
                            <dd>摄像头：彩色，红外双目</dd>
                        </dl>
                    </div>
                }
            </div>
        </div>
        <div id="page3" class="page"></div>
        <div id="page4" class="page"></div>
    </div>

    <script>
        changePage("#page1");

        $(".detail .tabs li").on("click",function(e){
            $(this).parent().children().removeClass("this");
            $(this).addClass("this");
            changePage($(this).attr("tag"));
        })

        function changePage(id){
           $(".detail .page-box .page").removeClass("this").hide();
           $(id).addClass("this").show();
        }
    </script>
</div>
