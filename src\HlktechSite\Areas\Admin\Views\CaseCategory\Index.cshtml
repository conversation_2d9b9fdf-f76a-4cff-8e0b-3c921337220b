﻿@{
    var user = ViewBag.User as IUser ?? User.Identity as IUser;
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
}
<script asp-location="Head">
    var changeNameUrl = "@Url.Action("ChangeName")";
    var getData = "@Url.Action("GetSubordinateData")";
    var createData = "@Url.Action("AddCaseCategory")";
    var editData = "@Url.Action("EditCaseCategory")";
    var deleteData = "@Url.Action("Delete")";
</script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("案例分类")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="javascript:;" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("AddCaseCategory")"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("您可以点击分类名称右边的新增下级来直接给该分类添加下级分类")</li>
            @*<li>@T("默认的文章分类不可以删除")</li>*@   
        </ul>
    </div>

    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="w24"></th>
                <th>@T("排序")</th>
                <th>@T("分类名称")</th>
                <th></th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.list)
            {
                <tr class="hover edit" id="<EMAIL>" style="background: rgb(255, 255, 255);">
                    <td class="w48">
                        <input type="checkbox" name="check_gc_id[]" value="@item.Id" class="checkitem">
                        <img fieldid="@item.Id" status="@(item.subset?"open":"close")" ds_type="flex" src="/static/admin/images/treetable/@T(item.subset?"tv-expandable.gif":"tv-item.gif")">
                    </td>
                    <td class="w48 sort"><span title="@T("可编辑")" ajax_branch="goods_class_sort" datatype="number" fieldid="@item.Id" fieldname="gc_sort"   ds_type="inline_edit" class="editable">@item.DisplayOrder</span></td>
                    <td class="w50pre name">
                        <span title="@T("可编辑")" required="@item.Id" fieldid="@item.Id" ajax_branch="goods_class_name" fieldname="gc_name"  ds_type="inline_edit" class="editable">@item.Name</span>
                        <a class="btn-add-nofloat marginleft" href="@Url.Action("AddCaseCategory",new { parent_id=item.Id})">
                            <span>@T("新增下级")</span>
                        </a>
                    </td>
                    <td></td>
                    <td class="w96">
                        <a href="@Url.Action("EditCaseCategory",new { Id=item.Id})">@T("编辑")</a> |
                        <a href="javascript:dsLayerConfirm('@Url.Action("delete",new {Ids=item.Id})','@T("您确定要删除该数据吗，会一起删除它的子集数据")?',1)">@T("删除")</a>
                    </td>
                </tr>
            }

        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkall_2"></td>
                <td id="batchAction" colspan="15">
                    <span class="all_checkbox">
                        <label for="checkall_2">@T("全选")</label>
                    </span>&nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
</div>
<script type="text/javascript"  asp-location="Head">
    var ADMINSITEURL = "/@adminarea";
    var BASESITEURL = "/@adminarea";
    var ADMINSITEROOT = "/static/admin";
</script>
<script type="text/javascript" src="~/static/admin/js/jquery.edit.js" charset="utf-8"></script>
<script src="~/static/admin/js/jquery_reporty.js"></script>
<script asp-location="Footer">
     function submit_delete(IDS)
    {
        $.post("@Url.Action("Delete")", { Ids: IDS }, function (res) {
            if (!res.success) {
                alert(res.msg);
            } else {
                window.location.reload(); //刷新页面
            }

        })
    }
</script>
