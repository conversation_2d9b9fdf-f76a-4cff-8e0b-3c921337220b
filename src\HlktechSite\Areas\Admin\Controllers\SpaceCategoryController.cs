﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;

using SixLabors.ImageSharp;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>商品</summary>
[DisplayName("空间管理")]
[Description("用于空间的管理")]
[AdminArea]
[DHMenu(81,ParentMenuName = "ProductMenu", CurrentMenuUrl = "~/{area}/SpaceCategory", CurrentMenuName = "SpaceCategoryList", CurrentIcon = "&#xe71f;", LastUpdate = "20240125")]
public class SpaceCategoryController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 81;

    /// <summary>
    /// 相册列表
    /// </summary>
    /// <param name="key"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("相册列表")]
    public IActionResult Index(string key, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        var Lists = AlbumCategory.FindAllWithCache();
        viewModel.Lists = Lists;

        var list = AlbumCategory.Search(key, pages).Select(x => new { Id = x.Id, Name = x.Name, Count = AlbumPic.FindCount(AlbumPic._.AId == x.Id) }).ToDynamicList();
        viewModel.list = list;
        viewModel.page = page;
        viewModel.key = key;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key } });
        return View(viewModel);
    }

    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    public IActionResult GetByNames(string gc_name, int gc_id = -1)
    {
        var Model = AlbumCategory.FindByName(gc_name.SafeString().Trim());

        if (gc_id > 0)
        {
            if (Model != null && Model.Id != gc_id)
            {
                return Json(false);
            }
            else
            {
                return Json(true);
            }
        }
        else
        {

            if (Model != null)
            {
                return Json(false);
            }
            else
            {
                return Json(true);
            }
        }

    }


    /// <summary>
    /// 添加相册
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加相册")]
    public IActionResult CreateAlbumCategory()
    {
        var Sort = AlbumCategory.FindMax("Sort");
        ViewBag.Sort = Sort + 1;
        return View();
    }


    /// <summary>
    /// 添加相册
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("添加相册")]
    public IActionResult CreateAlbumCategory(string aclass_name, string aclass_des, int aclass_sort)
    {
        if (aclass_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("相册名称不能为空"), IsOk = false });
        }
        var exit = AlbumCategory.FindByName(aclass_name.SafeString().Trim());
        if (exit != null)
        {
            return Prompt(new PromptModel { Message = GetResource("相册名称已存在"), IsOk = false });
        }



        var Model = new AlbumCategory();
        Model.Name = aclass_name;
        Model.Content = aclass_des;
        Model.Sort = aclass_sort;
        Model.Insert();
        AlbumCategory.Meta.Cache.Clear("");//清除缓存

        Loger.UserLog("添加", $"添加相册：{Model.Name}");

        return MessageTip(GetResource("保存成功"));
    }

    /// <summary>
    /// 打开修改页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("打开修改页面")]
    public IActionResult UpdateAlbumCategory(int Id)
    {
        var Model = AlbumCategory.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        return View(Model);
    }

    /// <summary>
    ///  修改接口
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="aclass_name"></param>
    /// <param name="aclass_des"></param>
    /// <param name="aclass_sort"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改接口")]
    public IActionResult UpdateAlbumCategory(int Id, string aclass_name, string aclass_des, int aclass_sort)
    {
        var Model = AlbumCategory.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        var exit = AlbumCategory.FindByName(aclass_name.SafeString().Trim());
        if (exit != null && Model.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("相册名称已存在"), IsOk = false });
        }
        Model.Name = aclass_name;
        Model.Content = aclass_des;
        Model.Sort = aclass_sort;
        Model.Update();
        AlbumCategory.Meta.Cache.Clear("");//清除缓存
        
        Loger.UserLog("修改", $"修改相册：{Model.Name}");
        return MessageTip(GetResource("保存成功"));
    }


    /// <summary>
    /// 批量相册删除数据
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("相册删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        if (Ids.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请选择要删除的数据");
            return Json(res);
        }
        var arr = Ids.Split(",");
        var exit = arr.Where(x => x == "1").FirstOrDefault();
        if (exit != null)
        {
            res.msg = GetResource("默认相册无法被删除 请重新选则删除数据");
            return Json(res);
        }

        using (var tran1 = AlbumCategory.Meta.CreateTrans())
        {
            AlbumCategory.DelByIds(Ids.Trim(','));
            AlbumPic.DelByAIds(Ids.Trim(','));
            var List = AlbumPic.FindByAIds(Ids.Trim(','));
            foreach (var item in List)
            {
                DHSetting.Current.WebRootPath.GetFullPath().CombinePath(item.Cover).AsFile().Delete();
            }
           
            tran1.Commit();
        }
        AlbumCategory.Meta.Cache.Clear("");//清除缓存
        AlbumPic.Meta.Cache.Clear("");//清除缓存
        Loger.UserLog("删除", $"删除相册：{Ids}");
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }



    /// <summary>
    /// 查看图片列表
    /// </summary>
    /// <param name="keyword"></param>
    /// <param name="AId"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("查看图片列表")]
    public IActionResult Imagelist(string keyword, int AId=-1, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = AlbumPic.Searchs(AId, keyword, pages).Select(x => new { Id = x.Id, Name = x.Name, CreateTime = x.CreateTime, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover) ,Spec =x.Spec}).ToDynamicList();
        viewModel.list = list;
        viewModel.page = page;
        viewModel.key = keyword;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Imagelist"), new Dictionary<String, String> { { "keyword", keyword },{ "AId", AId.ToString() } });
        return View(viewModel);
    }


    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(int ItemId, int category_id=1)
    {
        var filea = Request.Form.Files;
        var list = filea.Count();

        var img = filea.FirstOrDefault();
        var bytes = img!.OpenReadStream().ReadBytes(img.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(img.FileName)}";
        var filepath = DHSetting.Current.UploadPath.CombinePath($"AlbumPic/{filename}");
        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        img.SaveAs(saveFileName);
        var Models = new AlbumPic();
        using (var tran1 = AlbumPic.Meta.CreateTrans())
        {
            var image = Image.Load(bytes);
            var Spec = image.Width + "X" + image.Height;
            
            Models.Name = filename;
            Models.AId = category_id;
            Models.Cover = filepath.Replace("\\", "/");
            Models.Spec = Spec;
            Models.Insert();
            tran1.Commit();
        }
        AlbumPic.Meta.Cache.Clear("");//清除缓存
        Loger.UserLog("上传", $"相册上传图片：{filename}");
        return Json(new { success = true, file_id = Models.Id, state = true, origin_file_name = img.FileName, file_name = filename, file_path = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), filepath) });
    }

    /// <summary>
    /// 图片修改名称
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片修改名称")]
    [HttpPost]
    public IActionResult ModifyName(int id,string name)
    {
        var res = new DResult();
        if (name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("名称不能为空");
            return Json(res);
        }

        var exit = AlbumPic.FindByName(name);
        if (exit != null && exit.Id != id)
        {
            res.msg = GetResource("名称已存在");
            return Json(res);
        }

        var Model = AlbumPic.FindById(id);
        if (Model == null)
        {
            res.msg = GetResource("数据不存在或已被删除");
            return Json(res);
        }
        Model.Name = name;
        Model.Update();
        res.success = true;
        return Json(res);
    }


    /// <summary>
    /// 相册图片删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    //[HttpPost]
    [DisplayName("相册图片删除")]
    public IActionResult DeletePic(string[] Ids)
    {
        string str = string.Join(",", Ids);
        var List = AlbumPic.FindByIds(str.Trim(','));
        foreach (var item in List)
        {
            DHSetting.Current.WebRootPath.GetFullPath().CombinePath(item.Cover).AsFile().Delete();
        }
        AlbumPic.DelByIds(str.Trim(','));
        Loger.UserLog("删除", $"删除相册图片：{str}");
        return Prompt(new PromptModel { Message = GetResource("删除成功"), IsOk = true });
    }

}
