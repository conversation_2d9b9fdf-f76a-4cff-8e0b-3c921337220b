﻿using DG;
using DG.Cube.BaseControllers;

using DH;
using DH.Core.Infrastructure;
using DH.Entity;
using DH.Helpers;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;

using Pek;
using Pek.Helpers;
using Pek.IO;
using Pek.Models;
using Pek.Timing;
using Pek.Webs;

using Qiniu.Storage;
using Qiniu.Util;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

using YRY.Web.Controllers;
using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>上传设置</summary>
[DisplayName("上传设置")]
[Description("用于第三方网站单点登录的应用系统")]
[AdminArea]
[DHMenu(95,ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/UploadSetting", CurrentMenuName = "UploadSetting", CurrentIcon = "&#xe72a;", LastUpdate = "20240125")]
public class UploadSettingController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 95;

    /// <summary>
    /// 默认图片
    /// </summary>,
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("默认图片")]
    public IActionResult Index()
    {
        var model = SiteSettingInfo.SiteSettings;

        dynamic viewModel = new ExpandoObject();
        viewModel.default_user_portrait = Pek.UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), model.DefaultUserPortrait.IsNotNullAndWhiteSpace() ? $"{model.DefaultUserPortrait}?{UnixTime.ToTimestamp()}" : $"{DHSetting.Current.UploadPath}/common/default_user_portrait.gif?{UnixTime.ToTimestamp()}");

        return View(viewModel);
    }

    /// <summary>
    /// 上传设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("上传设置")]
    public IActionResult Upload_Type()
    {
        return View();
    }

    /// <summary>
    /// 私有上传设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("私有上传设置")]
    public IActionResult Upload_Type1()
    {
        return View();
    }

    /// <summary>
    /// 上传设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("上传设置")]
    [HttpPost]
    public IActionResult UpdateUploadType(String upload_type, String uploadpath)
    {
        var set = OssSetting.Current;
        switch (upload_type)
        {
            default:
            case "local":
                set.UploadType = 0;
                DHSetting.Current.UploadPath = uploadpath;
                break;

            case "alioss":
                set.UploadType = 1;
                set.AliOSS.IsAliEndPoint = GetRequest("aliendpoint_type") == "1";
                set.AliOSS.OssAccessKeyId = GetRequest("alioss_accessid");
                set.AliOSS.OssSecretAccess = GetRequest("alioss_accesssecret");
                set.AliOSS.OssBucket = GetRequest("alioss_bucket");
                set.AliOSS.OssEndpoint = GetRequest("alioss_endpoint");
                break;

            case "qiniuoss":
                set.UploadType = 2;
                set.QiNiu.IsQiNiuEndPoint = GetRequest("qiniuendpoint_type") == "1";
                set.QiNiu.AccessKey = GetRequest("qiniuoss_accessid");
                set.QiNiu.SecretKey = GetRequest("qiniuoss_accesssecret");
                set.QiNiu.Bucket = GetRequest("qiniuoss_bucket");
                set.QiNiu.Zone = GetRequest("qiniuoss_zone");
                set.QiNiu.BasePath = GetRequest("qiniuoss_basepath");
                set.QiNiu.Domain = GetRequest("qiniuoss_endpoint");

                break;
        }

        DG.Setting.Current.Save();
        set.Save();
        Loger.UserLog($"更新上传设置");

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
    }

    /// <summary>
    /// 私有上传设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("私有上传设置")]
    [HttpPost]
    public IActionResult UpdateUploadType1(String upload_type, String uploadpath)
    {
        var set = DG.PrivateOssSetting.Current;
        switch (upload_type)
        {
            default:
            case "local":
                set.UploadType = 0;
                DHSetting.Current.UploadPath = uploadpath;
                break;

            case "alioss":
                set.UploadType = 1;
                set.AliOSS.IsAliEndPoint = GetRequest("aliendpoint_type") == "1";
                set.AliOSS.OssAccessKeyId = GetRequest("alioss_accessid");
                set.AliOSS.OssSecretAccess = GetRequest("alioss_accesssecret");
                set.AliOSS.OssBucket = GetRequest("alioss_bucket");
                set.AliOSS.OssEndpoint = GetRequest("alioss_endpoint");
                break;

            case "qiniuoss":
                set.UploadType = 2;
                set.QiNiu.IsQiNiuEndPoint = GetRequest("qiniuendpoint_type") == "1";
                set.QiNiu.AccessKey = GetRequest("qiniuoss_accessid");
                set.QiNiu.SecretKey = GetRequest("qiniuoss_accesssecret");
                set.QiNiu.Bucket = GetRequest("qiniuoss_bucket");
                set.QiNiu.Zone = GetRequest("qiniuoss_zone");
                set.QiNiu.BasePath = GetRequest("qiniuoss_basepath");
                set.QiNiu.Domain = GetRequest("qiniuoss_endpoint");

                break;
        }

        DG.Setting.Current.Save();
        set.Save();

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
    }

    /// <summary>
    /// CDN设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("CDN设置")]
    public IActionResult CDNSetting()
    {
        return View();
    }

    /// <summary>
    /// CDN设置保存
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("CDN设置保存")]
    [HttpPost]
    public IActionResult UploadCDN(Int32 enable, String url)
    {
        var set = CDNOptionSetting.Current;

        set.Enable = enable == 1;
        set.Url = url;
        set.Save();

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
    }

    /// <summary>
    /// 默认图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("默认图片上传")]
    [HttpPost]
    public IActionResult UploadThumb(IFormFile default_user_portrait)
    {
        if (default_user_portrait == null)
        {
            return Prompt(new PromptModel { Message = GetResource("未上传图片！"), IsOk = false });
        }

        var model = SiteSettingInfo.SiteSettings;

        var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传正常图片！"), IsOk = false });
        }

        var filename = $"default_user_portrait{Path.GetExtension(default_user_portrait.FileName)}";
        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"common/{filename}");
        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        default_user_portrait.SaveAs(saveFileName);

        model.DefaultUserPortrait = filepath.Replace("\\", "/");

        SiteSettingInfo.SaveChanges();
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
    }

    /// <summary>
    /// 获取七牛云Token
    /// </summary>
    [DHAuthorize]
    public IActionResult GetQNToken()
    {
        var result = new DGResult();

        if (!OssSetting.Current.QiNiu.IsQiNiuEndPoint || OssSetting.Current.QiNiu.Domain.IsNullOrEmpty())
        {
            result.Message = GetResource("请在上传设置配置好Cname和绑定域名");
            return result;
        }

        var cache = EngineContext.Current.Resolve<ICache>();
        var token = cache.Get<String>("qiniuToken");
        if (token.IsNullOrWhiteSpace())
        {
            Mac mac = new Mac(OssSetting.Current.QiNiu.AccessKey, OssSetting.Current.QiNiu.SecretKey);

            // 设置上传策略
            PutPolicy putPolicy = new PutPolicy();
            putPolicy.Scope = OssSetting.Current.QiNiu.Bucket; // 设置要上传的目标空间
            putPolicy.SetExpires(7200);  // 上传策略的过期时间(单位:秒)

            // 生成上传token
            token = Auth.CreateUploadToken(mac, putPolicy.ToJsonString());
            cache.Set("qiniuToken", token, 7200);
        }

        result.Data = token;
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 保存文件写入数据库
    /// </summary>
    /// <returns></returns>
    [DHAuthorize]
    public IActionResult UploadFile(Int64 size, Int32 fileType, Int32 Id, Boolean isImg, String originFileName, String fileName, String fileUrl)
    {
        var fileModel = new UploadInfo();
        fileModel.FileSize = size;
        fileModel.FileType = fileType;
        fileModel.ItemId = Id;
        fileModel.IsImg = isImg;
        fileModel.OriginFileName = originFileName;
        fileModel.FileName = fileName;
        fileModel.FileUrl = fileUrl;
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = fileName, file_path = fileUrl });
    }
}
