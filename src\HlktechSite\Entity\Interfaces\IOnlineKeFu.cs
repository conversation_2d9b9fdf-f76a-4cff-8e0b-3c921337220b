﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>在线客服</summary>
public partial interface IOnlineKeFu
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>语言Id集合</summary>
    String? LIds { get; set; }

    /// <summary>所属平台集合。1为PC，2为移动端,多个以逗号分隔</summary>
    String? OIds { get; set; }

    /// <summary>客服名称</summary>
    String? OName { get; set; }

    /// <summary>客服号码</summary>
    String? ONumber { get; set; }

    /// <summary>客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp</summary>
    Int16 OType { get; set; }

    /// <summary>客服所属平台,0为全部,1为PC,2为手机</summary>
    Int32 Location { get; set; }

    /// <summary>排序</summary>
    Int32 Sort { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
