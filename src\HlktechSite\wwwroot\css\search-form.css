.shi<PERSON>an{
	float: right;
	margin-right: 20px;
}
.box<PERSON>o{
	width: 160px;
	height: 400px;
	float: left;
}
.yuan{
	width: 10px;
	height: 10px;
	float: left;
	border-radius:100px 100px 100px 100px;
	margin: 8px 0 0 0;

}
.darong<PERSON> li{
	width: 140px;
}
.rongqi p{
	font-size: 30px;
}
.rongqi span{
	font-size: 18px;
	padding-left: 10px;
}
.juxing{
	width: 124px;
	height: 32px;
	background-color: #3a75ff;
	border-radius:100px 100px 100px 100px;
	margin-left: 200px;
	margin-top: 6px;
}
.juxing p{
	text-align:center;
	color: #FFFFFF;
	font-size: 18px;
	line-height:32px;
}
.footer-copyright{
	width: 100%;
	height: 44px;
}
.darongqi a,.darongqi2 a{
	color: #616161;
	text-decoration:none;
}
.wenzi{
	margin-top: 20px;
}
.wew{
	margin-left: 10px;
}

.box li a:hover,.darongqi li a:hover{
	color: #397dff;
	cursor: pointer;
}
.container img:hover,.juxing:hover,.juxingwenzi:hover{
	cursor: pointer;
}
.box1 a:hover{
	color: #397dff;
	cursor: pointer;
}
p{
    text-align:center;
}
.dabox{
	width: 500px;
	height: 500px;
	float: left;
}
.dabox1{
	width: 670px;
	height:500px;
	float: right;
}

.box{
	width: 500px;
	height: 300px;
	background-color: #FFFFFF;
	border-radius:10px 10px 10px 10px;
	float: left;
	margin-top: 20px;
	box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-moz-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
}
.box1{
	width: 670px;
	height: 300px;
	background-color: #FFFFFF;
	border-radius:10px 10px 10px 10px;
	float: right;
	margin-left: 30px;
	margin-top: 20px;
	box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-moz-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
}
.xiaohezi2{
	width: 400px;
	height: 120px;
	background-color: #FFFFFF;
	border-radius:10px 10px 10px 10px;
	float: right;
	box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-moz-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
}
.darongqi{
	margin-top: 50px;
	width: 1200px;
	height: 500px;
}
.darongqi2{
	width: 1200px;
	height: 300px;
}
.hezi{
	width: 1200px;
	height:120px;
	margin-top: 150px;
}
.bt{
	text-align:center;
}
h4{
	text-align:center;
}
.fangkuang{
	border-radius:10px 10px 10px 10px;
	width: 180px;
	height: 400px;
	background-color: #FFFFFF;
	float:left;
	margin-top: 20px;
	margin-right: 39px;
	box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-moz-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
}
.fangkuang1{
	border-radius:10px 10px 10px 10px;
	width: 180px;
	height: 400px;
	background-color: #FFFFFF;
	float:left;
	margin-left: 0px;
	margin-top: 20px;
	box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-moz-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
}
.fangkuang2{
	border-radius:10px 10px 10px 10px;
	width: 360px;
	height: 400px;
	background-color: #FFFFFF;
	float:left;
	margin: 20px 39px 0 0;
	box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
	-moz-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.52);
}
.xiaohezi{
    width: 750px;
	height: 120px;
	background-color: #FFFFFF;
	border-radius:10px 10px 10px 0px;
	float:left;
	box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.40);
	-webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.40);
	-moz-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.40);
}

.rongqi{
	width: 100%;
	height: 120px;
}

.rongqi ul{
	width: 100%;
	height: 100px;
	padding-top: 30px;
}
.rongqi ul li{
	
	margin-left: 90px;
	float: left;
}
li{
	list-style:none
}
/* .navbar-text{
	font: "微软雅黑";
	font-size: 20px;
	color: #676767;
	margin: 28px 50px 0 50px;
	float: left;
} */

.search-wrapper {
    position: absolute;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    top:50%;
    left:50%;
}

.dh a{
	color: #FFFFFF;
}

.dh a:hover{
	color: #00a6fa;
}
	


.search-wrapper .input-holder {
    overflow: hidden;
    height: 70px;
    background: rgba(255,255,255,0);
    border-radius:6px;
    position: relative;
    width:70px;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.search-wrapper.active .input-holder {
    border-radius: 50px;
    width:600px;
    background: rgba(255, 255, 255, 0.5);
    -webkit-transition: all .5s cubic-bezier(0.000, 0.105, 0.035, 1.570);
    -moz-transition: all .5s cubic-bezier(0.000, 0.105, 0.035, 1.570);
    transition: all .5s cubic-bezier(0.000, 0.105, 0.035, 1.570);
}

.search-wrapper .input-holder .search-input {
    width: 100%;
    height: 50px;
    padding:0px 70px 0 20px;
    opacity: 0;
    position: absolute;
    top:0px;
    left:0px;
    background: transparent;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border:none;
    outline:none;
    font-family:"Open Sans", Arial, Verdana;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    color:#FFF;
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transition: all .3s cubic-bezier(0.000, 0.105, 0.035, 1.570);
    -moz-transition: all .3s cubic-bezier(0.000, 0.105, 0.035, 1.570);
    transition: all .3s cubic-bezier(0.000, 0.105, 0.035, 1.570);

    -webkit-transition-delay: 0.3s;
    -moz-transition-delay: 0.3s;
    transition-delay: 0.3s;
}
.search-wrapper.active .input-holder .search-input {
    opacity: 1;
    -webkit-transform: translate(0, 10px);
    -moz-transform: translate(0, 10px);
    transform: translate(0, 10px);
}

.search-wrapper .result-container {
    width: 100%;
    position: absolute;
    top:80px;
    left:0px;
    text-align: center;
    font-family: "Open Sans", Arial, Verdana;
    font-size: 14px;
    display:none;
    color:#B7B7B7;
}


@media screen and (max-width: 560px) {
    .search-wrapper.active .input-holder {width:300px;}
}