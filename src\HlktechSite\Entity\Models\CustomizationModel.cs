﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>定制申请</summary>
public partial class CustomizationModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>公司名称</summary>
    public String? ComName { get; set; }

    /// <summary>主营业务</summary>
    public String? Business { get; set; }

    /// <summary>申请人职务</summary>
    public String? Job { get; set; }

    /// <summary>邮箱</summary>
    public String? Email { get; set; }

    /// <summary>公司网址</summary>
    public String? ComUrl { get; set; }

    /// <summary>公司联系人</summary>
    public String? Linkman { get; set; }

    /// <summary>手机号</summary>
    public String? Phone { get; set; }

    /// <summary>定制类型 0:解决方案 1:产品</summary>
    public Int32 Type { get; set; }

    /// <summary>定制基于产品型号</summary>
    public String? Model { get; set; }

    /// <summary>定制产品首批采购量</summary>
    public String? Purchase { get; set; }

    /// <summary>定制产品预计年均需求量</summary>
    public String? Predict { get; set; }

    /// <summary>定制需求</summary>
    public String? Demand { get; set; }

    /// <summary>应用的项目背景</summary>
    public String? Setting { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ICustomization model)
    {
        Id = model.Id;
        ComName = model.ComName;
        Business = model.Business;
        Job = model.Job;
        Email = model.Email;
        ComUrl = model.ComUrl;
        Linkman = model.Linkman;
        Phone = model.Phone;
        Type = model.Type;
        Model = model.Model;
        Purchase = model.Purchase;
        Predict = model.Predict;
        Demand = model.Demand;
        Setting = model.Setting;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
