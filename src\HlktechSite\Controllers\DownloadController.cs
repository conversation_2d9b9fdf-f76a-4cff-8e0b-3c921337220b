﻿using DG.Cube;
using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Helpers;

using HlktechSite.DTO;
using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using Pek;

using System.Dynamic;
using System.Text;

namespace HlktechSite.Controllers;

//下载模块控制器
public class DownloadController : DGBaseControllerX
{
    /// <summary>
    /// 下载中心
    /// </summary>
    /// <param name="key">关键词</param>
    /// <param name="CId">产品中心</param>
    /// <param name="page">当前页</param>
    /// <returns></returns>
    public IActionResult Index(string key,int CId=0, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();

        key = key.SafeString().Trim();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime",
            Desc = true
        };

        //商品分类表
        viewModel.ProTypelist = ProductCategory.FindAllByLevel(0);


        //商品列表(分页)
        IEnumerable<Goods> ProList;
        ////商品列表(分页)
        //var ProList = Goods.SearchByCId(key, CId, pages).Select(x => new Goods { Id = x.Id, AdvWord = x.AdvWord, Content = x.Content, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? DHUrl.Combine(DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" }).ToList();
        if (CId==0)
        {
            ProList = Goods.Search(key, pages).Select(x => new Goods { Id = x.Id, AdvWord = x.AdvWord, Content = x.Content, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" });
        }
        else
        {
            IEnumerable<ProductCategory> SecondList;
            //查询是否有二级菜单
            SecondList = ProductCategory.FindAllWithCache().Where(x => x.ParentId == CId.ToInt()); ;

            //如果存在子级  则查询自己和子集  否则就查询自己
            if (SecondList.Any())
            {
                StringBuilder CIds = new StringBuilder();
                CIds.Append(CId);
                foreach (var item in SecondList)
                {
                    CIds.Append("," + item.Id.ToString());
                }
                ProList = Goods.SearchByCIds(key, CIds.ToString(), pages).Select(x => new Goods { Id = x.Id, AdvWord = x.AdvWord, Content = x.Content, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" }).ToList();

            }
            else
            {
                ProList = Goods.SearchByCId(key, CId.ToInt(), pages).Select(x => new Goods { Id = x.Id, AdvWord = x.AdvWord, Content = x.Content, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" }).ToList();
            }
           
        }


        viewModel.list = ProList;
        viewModel.Key = key;
        viewModel.CId = CId;
      
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key },{ "CId",CId.ToString() } });
        return DGView(viewModel,true);
    }




    /// <summary>
    /// 下载中心
    /// </summary>
    /// <param name="CId">产品类型Id</param>
    /// <param name="IsChild">是否是子级</param>
    /// <param name="Key">模糊查询关键字</param>
    /// <param name="page">当前页</param>
    /// <returns></returns>
    public IActionResult Indexs(string CId, String Key, int IsChild, int page = 1)
    {
        Key = Key.SafeString().Trim();

        dynamic viewModel = new ExpandoObject();

        //所有的商品分类
        //var ProTypelist = new List<ProductCategory>();
        var ProTypelist = ProductCategory.FindAllByLevel(0);


        //一级产品类型
        viewModel.ProTypelist = ProTypelist;

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 9,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime",
            Desc = true
        };


        //商品列表(分页)
        IEnumerable<Goods> ProList;
        if (CId.IsNullOrEmpty())
        {
            ProList = Goods.Search(Key, pages).Select(x => new Goods { Id = x.Id, AdvWord = x.AdvWord, Content = x.Content, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" });
        }
        else
        {

            IEnumerable<ProductCategory> SecondList;
            //查询是否有二级菜单
            SecondList = ProductCategory.FindAllWithCache().Where(x => x.ParentId == CId.ToInt()); ;

            //如果存在子级  则查询自己和子集  否则就查询自己
            if (SecondList.Any())
            {
                StringBuilder CIds = new StringBuilder();
                CIds.Append(CId);
                foreach (var item in SecondList)
                {
                    CIds.Append("," + item.Id.ToString());
                }
                ProList = Goods.SearchByCIds(Key, CIds.ToString(), pages).Select(x => new Goods { Id = x.Id, AdvWord = x.AdvWord, Content = x.Content, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" }).ToList();
            }
            else
            {
                ProList = Goods.SearchByCId(Key, CId.ToInt(), pages).Select(x => new Goods { Id = x.Id, AdvWord = x.AdvWord, Content = x.Content, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" }).ToList();
            }
        }

        viewModel.list = ProList;
        viewModel.page = page;
        viewModel.Key = Key.IsNullOrEmpty() ? "" : Key;
        //如果是二级菜单  则显示父级的选中效果
        if (IsChild == 1)
        {
            viewModel.CId = ProductCategory.FindById(CId.ToInt()).ParentId.ToString();
        }
        else
        {
            viewModel.CId = CId;
        }

        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index", "Product"), new Dictionary<String, String> { { "CId", CId } });
        return DGView(viewModel, true);
    }



    /// <summary>
    /// 下载中心详情页
    /// </summary>
    /// <returns></returns>
    public IActionResult Details(int Id)
    {
        dynamic viewModel = new ExpandoObject();
        var Product = Goods.FindById(Id);
        var List1 = new List<Datadownload>();
        var List2 = new List<Datadownload>();
        var List3 = new List<Datadownload>();
        //var downlod = Download.FindInIds(Product.MId);
        var downlod = new List<Download>();

        var mids = Product.MId.SplitAsInt(",");
        //XTrace.WriteLine("获取到的mids的长度=="+mids.Count());
        foreach (var item in mids)
        {
            XTrace.WriteLine("循环打印mids的item=="+item);
            var list = Download.FindInIds(item);
            //XTrace.WriteLine("循环打印list的数量==" + list.Count);
            foreach (var row in list)
            {
                downlod.Add(row);
            }
        }

        foreach (var item in downlod)
        {

            if (item.Development.IsNotNullAndWhiteSpace())
            {
                var Developmentdata = item.Development.Split('♪');
                foreach (var row in Developmentdata)
                {
                    var data = row.Split("|");
                    var model = new Datadownload();
                    model.resource_id = 1;//开发资料是1
                    model.resource_name = data[0];
                    if (data[1].Contains("http://") || data[1].Contains("https://"))
                    {
                        model.resource_url = data[1];
                        model.filesuffix = "";
                    }
                    else
                    {
                        model.resource_url = "/download" + data[1];
                        model.filesuffix = Path.GetExtension(model.resource_url);
                    }

                    List1.Add(model);
                }
            }
            if (item.Application.IsNotNullAndWhiteSpace())
            {
                var Application = item.Application.Split('♪');
                foreach (var row in Application)
                {
                    var data = row.Split("|");
                    var model = new Datadownload();
                    model.resource_id = 2;//应用软件是2
                    model.resource_name = data[0];
                    if (data[1].Contains("http://") || data[1].Contains("https://"))
                    {
                        model.resource_url = data[1];
                        model.filesuffix = "";
                    }
                    else
                    {
                        model.resource_url = "/download" + data[1];
                        model.filesuffix = Path.GetExtension(model.resource_url);
                    }

                    List2.Add(model);
                }
            }
            if (item.GeneralSoftware.IsNotNullAndWhiteSpace())
            {
                var GeneralSoftware = item.GeneralSoftware.Split('♪');
                foreach (var row in GeneralSoftware)
                {
                    var data = row.Split("|");
                    var model = new Datadownload();
                    model.resource_id = 3;//通用软件是3
                    model.resource_name = data[0];
                    if (data[1].Contains("http://") || data[1].Contains("https://"))
                    {
                        model.resource_url = data[1];
                        model.filesuffix = "";
                    }
                    else
                    {
                        model.resource_url = "/download" + data[1];
                        model.filesuffix = Path.GetExtension(model.resource_url);
                    }
                    List3.Add(model);
                }
            }
        }


        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("下载中心"), Url = Url.Action("Index") });

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
            navigations.Add(new NavigationUrl { Name = ProductCategoryLan.FindByCIdAndLId(Product.CId, WorkingLanguage.Id)?.Name, Url = Url.Action("Index", new { CId = Product.CId }),IsLast=true });
        else
            navigations.Add(new NavigationUrl { Name = ProductCategory.FindById(Product.CId)?.Name, Url = Url.Action("index", new { CId = Product.CId }), IsLast = true });
        viewModel.Locations = navigations;
        viewModel.Product = Product;
        viewModel.Development = List1;
        viewModel.Application = List2;
        viewModel.GeneralSoftware = List3;

        return DGView(viewModel,true);
    }
}
