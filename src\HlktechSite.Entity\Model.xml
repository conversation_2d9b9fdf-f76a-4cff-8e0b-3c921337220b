﻿<?xml version="1.0" encoding="utf-8"?>
<EntityModel xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:schemaLocation="https://newlifex.com https://newlifex.com/Model202407.xsd" Document="https://newlifex.com/xcode/model" Version="8.1.2022.1004-beta00011" xmlns="https://newlifex.com/Model202407.xsd">
  <Option>
    <!--类名模板。其中{name}替换为Table.Name，如{name}Model/I{name}Dto等-->
    <ClassNameTemplate />
    <!--显示名模板。其中{displayName}替换为Table.DisplayName-->
    <DisplayNameTemplate />
    <!--基类。可能包含基类和接口，其中{name}替换为Table.Name-->
    <BaseClass>CubeEntityBase</BaseClass>
    <!--命名空间-->
    <Namespace>HlktechSite.Entity</Namespace>
    <!--输出目录-->
    <Output>../HlktechSite/Entity</Output>
    <!--是否使用中文文件名。默认false-->
    <ChineseFileName>False</ChineseFileName>
    <!--用于生成Copy函数的参数类型。例如{name}或I{name}-->
    <ModelNameForCopy />
    <!--带有索引器。实现IModel接口-->
    <HasIModel>False</HasIModel>
    <!--可为null上下文。生成String?等-->
    <Nullable>True</Nullable>
    <!--数据库连接名-->
    <ConnName>DG</ConnName>
    <!--模型类模版。设置后生成模型类，用于接口数据传输，例如{name}Model-->
    <ModelClass>{name}Model</ModelClass>
    <!--模型类输出目录。默认当前目录的Models子目录-->
    <ModelsOutput>.\Models\</ModelsOutput>
    <!--模型接口模版。设置后生成模型接口，用于约束模型类和实体类，例如I{name}-->
    <ModelInterface>I{name}</ModelInterface>
    <!--模型接口输出目录。默认当前目录的Interfaces子目录-->
    <InterfacesOutput>.\Interfaces\</InterfacesOutput>
    <!--用户实体转为模型类的模型类。例如{name}或{name}DTO-->
    <ModelNameForToModel />
    <!--命名格式。Default/Upper/Lower/Underline-->
    <NameFormat>Default</NameFormat>
    <!--魔方区域显示名-->
    <DisplayName />
    <!--魔方控制器输出目录-->
    <CubeOutput />
  </Option>
  <Tables>
    <Table Name="DownloadCategory" TableName="DG_DownloadCategory" Description="下载分类" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
      </Indexes>
    </Table>
    <Table Name="DownloadCategoryLan" TableName="DG_DownloadCategoryLan" Description="下载分类翻译" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int32" Description="下载分类表Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Download" TableName="DG_Download" Description="下载文件" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="下载文件名称" />
        <Column Name="Development" DataType="String" Length="1000" Description="开发资料 格式：名称|地址|下载次数_名称|地址|下载次数" />
        <Column Name="Application" DataType="String" Length="1000" Description="应用软件 格式：名称|地址|下载次数_名称|地址|下载次数" />
        <Column Name="GeneralSoftware" DataType="String" Length="1000" Description="通用软件 格式：名称|地址|下载次数_名称|地址|下载次数" />
        <Column Name="MIds" DataType="String" Length="1000" Description="产品型号Id，以逗号分隔开" />
        <Column Name="DId" DataType="Int32" Description="下载分类Id" />
        <Column Name="Clicks" DataType="Int32" Description="点击数" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MIds" />
        <Index Columns="DId" />
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="ProductCategory" TableName="DG_ProductCategory" Description="产品分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="TypeId" DataType="Boolean" Description="类型ID。是否联动" />
        <Column Name="TypeName" DataType="String" Description="类型名称。联动或者空" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
      </Indexes>
    </Table>
    <Table Name="ProductCategoryLan" TableName="DG_ProductCategoryLan" Description="产品分类翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int32" Description="产品分类表Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="GoodsClassStaple" TableName="DG_GoodsClassStaple" Description="产品常用分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Counter" DataType="Int32" Description="计数器。使用次数计数" />
        <Column Name="Name" DataType="String" Master="True" Description="常用分类名称" />
        <Column Name="Cid1" ColumnName="CId_1" DataType="Int32" Description="一级分类ID" />
        <Column Name="Cid2" ColumnName="CId_2" DataType="Int32" Description="二级分类ID" />
        <Column Name="Cid3" ColumnName="CId_3" DataType="Int32" Description="三级分类ID" />
        <Column Name="TypeId" DataType="Boolean" Description="类型Id。产品分类是否联动" />
      </Columns>
    </Table>
    <Table Name="Goods" TableName="DG_Goods" Description="商品表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="商品名称+规格名称" />
        <Column Name="AdvWord" DataType="String" Length="150" Description="商品广告词" />
        <Column Name="MId" DataType="String" Description="产品型号Id集合，以逗号区分" />
        <Column Name="CId" DataType="Int32" Description="商品分类ID" />
        <Column Name="Cid1" ColumnName="CId_1" DataType="Int32" Description="一级分类ID" />
        <Column Name="Cid2" ColumnName="CId_2" DataType="Int32" Description="二级分类ID" />
        <Column Name="Cid3" ColumnName="CId_3" DataType="Int32" Description="三级分类ID" />
        <Column Name="Image" DataType="String" Length="150" Description="商品主图" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="商品内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="手机端商品描述" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="UsageScenarios" DataType="String" Length="512" Description="使用场景" />
        <Column Name="Specifications" DataType="String" RawType="text" Length="0" Description="参数规格" />
        <Column Name="Clicks" DataType="Int32" Description="商品点击数量" />
        <Column Name="Commend" DataType="Boolean" Description="商品推荐" />
        <Column Name="Shelf" DataType="Boolean" Description="商品是否上架" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="PcGouUrl" DataType="String" Length="200" Description="PC购买Url" />
        <Column Name="MobileGouUrl" DataType="String" Length="200" Description="移动购买Url" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="CId" />
        <Index Columns="CId_1" />
        <Index Columns="CId_2" />
        <Index Columns="CId_3" />
        <Index Columns="MId" />
      </Indexes>
    </Table>
    <Table Name="GoodsLan" TableName="DG_GoodsLan" Description="商品翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="商品名称+规格名称" />
        <Column Name="AdvWord" DataType="String" Length="150" Description="商品广告词" />
        <Column Name="Image" DataType="String" Length="150" Description="商品主图" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="商品内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="手机端商品描述" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="UsageScenarios" DataType="String" Length="512" Description="使用场景" />
        <Column Name="Specifications" DataType="String" RawType="text" Length="0" Description="参数规格" />
        <Column Name="Commend" DataType="Boolean" Description="商品推荐" />
        <Column Name="Shelf" DataType="Boolean" Description="商品是否上架" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="GId" DataType="Int32" Description="商品表Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="PcGouUrl" DataType="String" Length="200" Description="PC购买Url" />
        <Column Name="MobileGouUrl" DataType="String" Length="200" Description="移动购买Url" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="GoodsImages" TableName="DG_GoodsImages" Description="商品图片" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GId" DataType="Int32" Description="商品ID" />
        <Column Name="Url" DataType="String" Length="255" Description="商品图片" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否默认主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GId" />
      </Indexes>
    </Table>
    <Table Name="GoodsImagesLan" TableName="DG_GoodsImagesLan" Description="多语言商品图片" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GId" DataType="Int32" Description="商品ID" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Url" DataType="String" Length="255" Description="商品图片" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否默认主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GId" />
      </Indexes>
    </Table>
    <Table Name="AlbumCategory" TableName="DG_AlbumCategory" Description="相册" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="相册名称" />
        <Column Name="Content" DataType="String" Length="255" Description="相册描述" />
        <Column Name="Sort" DataType="Int32" Description="相册排序" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否为默认相册" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="AlbumPic" TableName="DG_AlbumPic" Description="相册图片" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="图片名称" />
        <Column Name="Tag" DataType="String" Length="255" Description="图片标签" />
        <Column Name="AId" DataType="Int32" Description="相册Id" />
        <Column Name="Cover" DataType="String" Length="255" Description="图片路径" />
        <Column Name="Spec" DataType="String" Length="255" Description="图片规格" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="AId" />
      </Indexes>
    </Table>
    <Table Name="Knowledge" TableName="DG_Knowledge" Description="知识库" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" LuceneIndexKey="True" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="标题" LuceneIndexKey="True" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" LuceneIndexKey="True" IsLuceneHtml="True" />
        <Column Name="Tags" DataType="String" Master="True" Length="100" Description="标签" />
        <Column Name="MId" DataType="Int32" Description="产品型号Id" />
        <Column Name="MIdName" DataType="String" Length="100" Description="产品型号内容" />
        <Column Name="Clicks" DataType="Int32" Description="点击数" />
        <Column Name="HelpFuls" DataType="Int32" Description="点赞数" />
        <Column Name="Status" DataType="Int32" Description="数据状态 0为不可用，1为已发表" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MId" />
      </Indexes>
    </Table>
    <Table Name="KnowledgeQuiz" TableName="DG_KnowledgeQuiz" Description="知识问答" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" LuceneIndexKey="True" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="标题" LuceneIndexKey="True" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" LuceneIndexKey="True" IsLuceneHtml="True" />
        <Column Name="Tags" DataType="String" Master="True" Length="100" Description="标签" />
        <Column Name="MId" DataType="Int32" Description="产品型号Id" />
        <Column Name="MIdName" DataType="String" Length="100" Description="产品型号内容" />
        <Column Name="Clicks" DataType="Int32" Description="点击数" />
        <Column Name="HelpFuls" DataType="Int32" Description="点赞数" />
        <Column Name="Status" DataType="Int32" Description="数据状态 0为不可用，1为已发表" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MId" />
      </Indexes>
    </Table>
    <Table Name="ProductModel" TableName="DG_ProductModel" Description="产品型号" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="型号" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="PcShopUrl" DataType="String" Length="200" Description="商城Pc链接" />
        <Column Name="MobileShopUrl" DataType="String" Length="200" Description="商城移动端链接" />
        <Column Name="TaobaoShopUrl1" DataType="String" Length="200" Description="淘宝产品链接" />
        <Column Name="AppletQrUrl" DataType="String" Length="300" Description="小程序二维码图片链接" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="ProductModelEx" TableName="DG_ProductModelEx" Description="产品型号扩展" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" PrimaryKey="True" Description="编号" />
        <Column Name="CodeType" DataType="String" Description="字典值——标识" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="UploadInfo" TableName="DG_Upload" Description="上传文件表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="OriginFileName" DataType="String" Description="上传实际文件名" />
        <Column Name="FileName" DataType="String" Description="上传文件名" />
        <Column Name="FileSize" DataType="Int64" Description="上传文件大小" />
        <Column Name="FileType" DataType="Int32" Description="上传文件类别 0:无 1:后台文章 2:帮助内容 3:店铺幻灯片 4:会员协议 5:积分礼品切换 6:积分礼品内容 7:可视化编辑模块 10:解决方案" />
        <Column Name="IsImg" DataType="Boolean" Description="文件类型 true为图片" />
        <Column Name="FileUrl" DataType="String" Length="100" Description="文件Url" />
        <Column Name="ItemId" DataType="Int32" Description="文件信息ID" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ItemId,FileType" />
      </Indexes>
    </Table>
    <Table Name="ArticleCategory" TableName="DG_ArticleCategory" Description="文章分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
      </Indexes>
    </Table>
    <Table Name="ArticleCategoryLan" TableName="DG_ArticleCategoryLan" Description="文章分类翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="AId" DataType="Int32" Description="文章分类Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
    </Table>
    <Table Name="Article" TableName="DG_Article" Description="文章" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="MId" DataType="String" Description="产品型号Id" />
        <Column Name="AId" DataType="Int32" Description="文章分类Id" />
        <Column Name="Url" DataType="String" Length="200" Description="文章跳转链接" />
        <Column Name="Show" DataType="Boolean" Description="文章是否显示，0为否，1为是，默认为1" />
        <Column Name="Sort" DataType="Int32" Description="文章排序" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MId" />
        <Index Columns="AId" />
      </Indexes>
    </Table>
    <Table Name="ArticleLan" TableName="DG_ArticleLan" Description="文章翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="AId" DataType="Int32" Description="文章Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
      </Columns>
      <Indexes>
        <Index Columns="AId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="FriendLinks" TableName="DG_FriendLinks" Description="友情链接" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="友情链接标题" />
        <Column Name="FType" DataType="Int16" Description="类型。0为文字，1为图片" />
        <Column Name="Url" DataType="String" Length="100" Description="友情链接地址" />
        <Column Name="Pic" DataType="String" Length="100" Description="友情链接图片" />
        <Column Name="Sort" DataType="Int32" Description="友情链接排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="FType" />
      </Indexes>
    </Table>
    <Table Name="FriendLinksLan" TableName="DG_FriendLinksLan" Description="友情链接翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="FId" DataType="Int32" Description="友情链接Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="友情链接标题" />
        <Column Name="FType" DataType="Int16" Description="类型。0为文字，1为图片。冗余字段" />
        <Column Name="Url" DataType="String" Length="100" Description="友情链接地址" />
        <Column Name="Pic" DataType="String" Length="100" Description="友情链接图片" />
        <Column Name="Sort" DataType="Int32" Description="友情链接排序" />
        <Column Name="Enabled" DataType="Int32" Description="是否启用" />
      </Columns>
      <Indexes>
        <Index Columns="FId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SingleArticle" TableName="DG_SingleArticle" Description="单页文章" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Url" DataType="String" Length="200" Description="文章跳转链接" />
        <Column Name="Show" DataType="Boolean" Description="文章是否显示，0为否，1为是，默认为1" />
        <Column Name="Sort" DataType="Int32" Description="文章排序" />
        <Column Name="Code" DataType="String" Nullable="False" Description="调用别名。不可重复" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="内容(移动端)" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Code" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SingleArticleLan" TableName="DG_SingleArticleLan" Description="单页文章翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="SId" DataType="Int32" Description="单页文章Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="内容(移动端)" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
      </Columns>
      <Indexes>
        <Index Columns="SId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SearchInfo" TableName="DG_SearchInfo" Description="全文检索默认存储" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="TableName" DataType="String" Description="表名。如SearchInfo" />
        <Column Name="OtherId" DataType="Int32" Description="关联Id" />
        <Column Name="MId" DataType="Int32" Description="产品型号Id" />
        <Column Name="SType" DataType="Int32" Description="关联类型。1为知识库，2为开放工单，3为知识问答" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="SType" />
        <Index Columns="MId" />
      </Indexes>
    </Table>
    <Table Name="SearchRecords" TableName="DG_SearchRecords" Description="搜索记录表">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="KeyWord" DataType="String" Nullable="False" Description="关键字" />
        <Column Name="Hits" DataType="Int32" Description="搜索次数" />
        <Column Name="Importance" DataType="Int32" Description="权重" />
        <Column Name="SType" DataType="Int32" Description="关联类型。0为全部, 1为知识库，2为开放工单，3为知识问答" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="SType" />
      </Indexes>
    </Table>
    <Table Name="DictionariesCategory" TableName="DG_DictionariesCategory" Description="字典分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="IsSystem" DataType="Boolean" Description="是否系统级字典" />
        <Column Name="Code" DataType="String" Description="字典分类标识" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
        <Index Columns="Code" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="DictionariesCategoryLan" TableName="DG_DictionariesCategoryLan" Description="字典分类翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="DId" DataType="Int32" Description="字典分类Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
      <Indexes>
        <Index Columns="DId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="DataDictionary" TableName="DG_DataDictionary" Description="数据字典" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="DId" DataType="Int32" Description="字典类型Id" />
        <Column Name="CodeType" DataType="String" Description="字典值——标识" />
        <Column Name="Name" DataType="String" Master="True" Description="字典值——名称" />
        <Column Name="Status" DataType="Boolean" Description="字典值——状态" />
        <Column Name="Sort" DataType="Int32" Description="字典值——排序" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="字典值——描述" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="DId" />
      </Indexes>
    </Table>
    <Table Name="DataDictionaryLan" TableName="DG_DataDictionaryLan" Description="数据字典翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="DId" DataType="Int32" Description="字典Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="字典值——名称" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="字典值——描述" />
      </Columns>
      <Indexes>
        <Index Columns="DId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="CaseCategory" TableName="DG_CaseCategory" Description="案例分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
      </Indexes>
    </Table>
    <Table Name="CaseCategoryLan" TableName="DG_CaseCategoryLan" Description="案例分类翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int32" Description="案例分类Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Case" TableName="DG_Case" Description="案例" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="MId" DataType="Int32" Description="产品型号Id" />
        <Column Name="CId" DataType="Int32" Description="案例分类Id" />
        <Column Name="Url" DataType="String" Length="200" Description="案例跳转链接" />
        <Column Name="Show" DataType="Boolean" Description="案例是否显示，0为否，1为是，默认为1" />
        <Column Name="Sort" DataType="Int32" Description="案例排序" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="案例标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="案例主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="发布时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MId" />
        <Index Columns="CId" />
      </Indexes>
    </Table>
    <Table Name="CaseLan" TableName="DG_CaseLan" Description="案例翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int32" Description="CaseId" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="案例标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="案例主图" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Navigation" TableName="DG_Navigation" Description="页面导航" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="页面导航标题" />
        <Column Name="Url" DataType="String" Length="255" Description="页面导航链接" />
        <Column Name="Location" DataType="String" Description="页面导航位置。header头部，middle中部，footer底部" />
        <Column Name="NewOpen" DataType="Boolean" Description="是否以新窗口打开" />
        <Column Name="Sort" DataType="Int32" Description="页面导航排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Location" />
      </Indexes>
    </Table>
    <Table Name="NavigationLan" TableName="DG_NavigationLan" Description="页面导航翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="NId" DataType="Int32" Description="页面导航Id" />
        <Column Name="LId" DataType="Int32" Description="关联所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="页面导航标题" />
        <Column Name="Url" DataType="String" Length="255" Description="页面导航链接" />
      </Columns>
      <Indexes>
        <Index Columns="NId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SolutionCategory" TableName="DG_SolutionCategory" Description="解决方案分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
      </Indexes>
    </Table>
    <Table Name="SolutionCategoryLan" TableName="DG_SolutionCategoryLan" Description="解决方案分类翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int32" Description="解决方案分类Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Solution" TableName="DG_Solution" Description="解决方案" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" LuceneIndexKey="True" />
        <Column Name="MId" DataType="Int32" Description="产品型号Id" />
        <Column Name="CId" DataType="Int32" Description="解决方案分类Id" />
        <Column Name="Url" DataType="String" Length="200" Description="解决方案跳转链接" />
        <Column Name="Show" DataType="Boolean" Description="解决方案是否显示，0为否，1为是，默认为1" />
        <Column Name="Sort" DataType="Int32" Description="解决方案排序" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="解决方案标题" LuceneIndexKey="True" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" LuceneIndexKey="True" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="解决方案主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="发布时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MId" />
        <Index Columns="CId" />
      </Indexes>
    </Table>
    <Table Name="SolutionLan" TableName="DG_SolutionLan" Description="解决方案翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int32" Description="CaseId" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="解决方案标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="解决方案主图" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Agent" TableName="DG_Agent" Description="代理商申请" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CompanyName" DataType="String" Master="True" Length="200" Description="公司名称" />
        <Column Name="ContactPerson" DataType="String" Length="10" Description="联系人" />
        <Column Name="Phone" DataType="String" Length="15" Description="联系电话" />
        <Column Name="Email" DataType="String" Description="邮箱" />
        <Column Name="ContactAddress" DataType="String" Length="512" Description="联系地址" />
        <Column Name="Summary" DataType="String" Length="512" Description="申请说明" />
        <Column Name="IsThrough" DataType="Boolean" Description="是否通过" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="OnlineKeFu" TableName="DG_OnlineKeFu" Description="在线客服" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="LIds" DataType="String" Description="语言Id集合" />
        <Column Name="OIds" DataType="String" Description="所属平台集合。1为PC，2为移动端,多个以逗号分隔" />
        <Column Name="OName" DataType="String" Description="客服名称" />
        <Column Name="ONumber" DataType="String" Description="客服号码" />
        <Column Name="OType" DataType="Int16" Description="客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp" />
        <Column Name="Location" DataType="Int32" Description="客服所属平台,0为全部,1为PC,2为手机" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="OType" />
      </Indexes>
    </Table>
    <Table Name="Customization" TableName="DG_Customization" Description="定制申请" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="ComName" DataType="String" Description="公司名称" />
        <Column Name="Business" DataType="String" Description="主营业务" />
        <Column Name="Job" DataType="String" Description="申请人职务" />
        <Column Name="Email" DataType="String" Description="邮箱" />
        <Column Name="ComUrl" DataType="String" Description="公司网址" />
        <Column Name="Linkman" DataType="String" Description="公司联系人" />
        <Column Name="Phone" DataType="String" Description="手机号" />
        <Column Name="Type" DataType="Int32" Description="定制类型 0:解决方案 1:产品" />
        <Column Name="Model" DataType="String" Description="定制基于产品型号" />
        <Column Name="Purchase" DataType="String" Description="定制产品首批采购量" />
        <Column Name="Predict" DataType="String" Description="定制产品预计年均需求量" />
        <Column Name="Demand" DataType="String" Description="定制需求" />
        <Column Name="Setting" DataType="String" Description="应用的项目背景" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="SampleApplication" TableName="DG_SampleApplication" Description="样品申请" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="ComName" DataType="String" Description="公司名称" />
        <Column Name="ComPeople" DataType="String" Description="企业人数" />
        <Column Name="Website" DataType="String" Description="企业官网" />
        <Column Name="Turnover" DataType="String" Description="年营业额" />
        <Column Name="ComType" DataType="String" Description="企业类型" />
        <Column Name="Address" DataType="String" Description="企业地址" />
        <Column Name="Linkman" DataType="String" Description="联系人" />
        <Column Name="Position" DataType="String" Description="职位" />
        <Column Name="Phone" DataType="String" Description="电话" />
        <Column Name="QQ" DataType="String" Description="QQ" />
        <Column Name="Email" DataType="String" Description="邮箱" />
        <Column Name="Fax" DataType="String" Description="传真" />
        <Column Name="ProModel" DataType="String" Description="产品型号" />
        <Column Name="Demand" DataType="String" Description="月需求量" />
        <Column Name="Theme" DataType="String" Description="申请主题" />
        <Column Name="Describe" DataType="String" Description="需求描述 " />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="SeoInfo" TableName="DG_Seo" Description="SEO信息存放表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="SeoTitle" DataType="String" Description="SEO标题" />
        <Column Name="SeoKeywords" DataType="String" Description="SEO关键词" />
        <Column Name="SeoDescription" DataType="String" Description="SEO描述" />
        <Column Name="SeoType" DataType="String" Description="SEO类型" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="SeoType" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SeoInfoLan" TableName="DG_SeoLan" Description="SEO多语言信息存放表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="SId" DataType="Int32" Description="SeoId" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="SeoTitle" DataType="String" Master="True" Length="200" Description="SEO标题" />
        <Column Name="SeoKeywords" DataType="String" RawType="text" Length="0" Description="SEO关键词" />
        <Column Name="SeoDescription" DataType="String" Length="512" Description="SEO描述" />
        <Column Name="SeoType" DataType="String" Length="255" Description="类型" />
      </Columns>
      <Indexes>
        <Index Columns="SId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="JumpProduct" TableName="DG_JumpProduct" Description="成品跳转表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="产品名称" />
        <Column Name="InfoUrl" DataType="String" Length="512" Description="推文链接" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="AndroidPaths" DataType="String" Length="512" Description="国内Android下载地址" />
        <Column Name="AndroidPaths1" DataType="String" Length="512" Description="国外Android下载地址" />
        <Column Name="IosPaths" DataType="String" Length="512" Description="IOS下载地址" />
        <Column Name="AppLogo" DataType="String" Length="100" Description="App Logo" />
        <Column Name="JdUrl" DataType="String" Length="100" Description="京东链接" />
        <Column Name="TbUrl" DataType="String" Length="100" Description="淘宝链接" />
        <Column Name="PddUrl" DataType="String" Length="100" Description="拼多多链接" />
        <Column Name="AdWord" DataType="String" Length="100" Description="广告词" />
        <Column Name="Clicks" DataType="Int32" Description="点击数" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="JumpProductLan" TableName="DG_JumpProductLan" Description="成品跳转表多语言信息存放表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="JId" DataType="Int32" Description="成品Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="产品名称" />
        <Column Name="InfoUrl" DataType="String" Length="512" Description="推文链接" />
        <Column Name="AdWord" DataType="String" Length="100" Description="广告词" />
      </Columns>
      <Indexes>
        <Index Columns="JId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="EndProducts" TableName="DG_EndProduct" Description="成品表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="成品名称+规格名称" />
        <Column Name="AdvWord" DataType="String" Length="150" Description="成品广告词" />
        <Column Name="MId" DataType="String" Description="产品型号Id集合，以逗号区分" />
        <Column Name="CId" DataType="Int32" Description="成品分类ID" />
        <Column Name="Cid1" ColumnName="CId_1" DataType="Int32" Description="一级分类ID" />
        <Column Name="Cid2" ColumnName="CId_2" DataType="Int32" Description="二级分类ID" />
        <Column Name="Cid3" ColumnName="CId_3" DataType="Int32" Description="三级分类ID" />
        <Column Name="Image" DataType="String" Length="150" Description="成品主图" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="成品内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="手机端成品描述" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="UsageScenarios" DataType="String" Length="512" Description="使用场景" />
        <Column Name="Specifications" DataType="String" RawType="text" Length="0" Description="参数规格" />
        <Column Name="Clicks" DataType="Int32" Description="成品点击数量" />
        <Column Name="Commend" DataType="Boolean" Description="成品推荐" />
        <Column Name="Shelf" DataType="Boolean" Description="成品是否上架" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="PcGouUrl" DataType="String" Length="200" Description="PC购买Url" />
        <Column Name="MobileGouUrl" DataType="String" Length="200" Description="移动购买Url" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="CId" />
        <Index Columns="CId_1" />
        <Index Columns="CId_2" />
        <Index Columns="CId_3" />
        <Index Columns="MId" />
      </Indexes>
    </Table>
    <Table Name="EndProductLan" TableName="DG_EndProductLan" Description="成品翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="成品名称+规格名称" />
        <Column Name="AdvWord" DataType="String" Length="150" Description="成品广告词" />
        <Column Name="Image" DataType="String" Length="150" Description="成品主图" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="成品内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="手机端成品描述" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="UsageScenarios" DataType="String" Length="512" Description="使用场景" />
        <Column Name="Specifications" DataType="String" RawType="text" Length="0" Description="参数规格" />
        <Column Name="Commend" DataType="Boolean" Description="成品推荐" />
        <Column Name="Shelf" DataType="Boolean" Description="成品是否上架" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="GId" DataType="Int32" Description="成品表Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="EndProductClass" TableName="DG_EndProductClass" Description="成品分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="TypeId" DataType="Boolean" Description="类型ID。是否联动" />
        <Column Name="TypeName" DataType="String" Description="类型名称。联动或者空" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
      </Indexes>
    </Table>
    <Table Name="EndProductClassStaple" TableName="DG_EndProductClassStaple" Description="成品常用分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Counter" DataType="Int32" Description="计数器。使用次数计数" />
        <Column Name="Name" DataType="String" Master="True" Description="常用分类名称" />
        <Column Name="Cid1" ColumnName="CId_1" DataType="Int32" Description="一级分类ID" />
        <Column Name="Cid2" ColumnName="CId_2" DataType="Int32" Description="二级分类ID" />
        <Column Name="Cid3" ColumnName="CId_3" DataType="Int32" Description="三级分类ID" />
        <Column Name="TypeId" DataType="Boolean" Description="类型Id。成品分类是否联动" />
      </Columns>
    </Table>
    <Table Name="EndProductClassLan" TableName="DG_EndProductCategoryLan" Description="成品分类翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int32" Description="成品分类表Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="EndProductImages" TableName="DG_EndProductImages" Description="成品图片" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GId" DataType="Int32" Description="成品ID" />
        <Column Name="Url" DataType="String" Length="255" Description="成品图片" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否默认主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GId" />
      </Indexes>
    </Table>
    <Table Name="EndProductImagesLan" TableName="DG_EndProductImagesLan" Description="多语言成品图片" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GId" DataType="Int32" Description="商品ID" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Url" DataType="String" Length="255" Description="商品图片" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否默认主图" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GId" />
      </Indexes>
    </Table>
    <Table Name="EndProductModels" TableName="DG_EndProductModel" Description="成品型号" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="型号" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="PcShopUrl" DataType="String" Length="200" Description="商城Pc链接" />
        <Column Name="MobileShopUrl" DataType="String" Length="200" Description="商城移动端链接" />
        <Column Name="TaobaoShopUrl1" DataType="String" Length="200" Description="淘宝产品链接" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="EndProductModelEx" TableName="DG_EndProductModelEx" Description="成品型号扩展" ConnName="OnlineKeFu" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" PrimaryKey="True" Description="编号" />
        <Column Name="CodeType" DataType="String" Description="字典值——标识" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="AppManagers" TableName="DH_AppManagers" Description="App下载表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="产品名称" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="AndroidPaths" DataType="String" Length="512" Description="国内Android下载地址" />
        <Column Name="AndroidPaths1" DataType="String" Length="512" Description="国外Android下载地址" />
        <Column Name="IosPaths" DataType="String" Length="512" Description="IOS下载地址" />
        <Column Name="ApkFilePath" DataType="String" Length="512" Description="Apk下载地址" />
        <Column Name="AppLogo" DataType="String" Length="100" Description="App Logo" />
        <Column Name="Clicks" DataType="Int32" Description="点击数" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="AndroidVersion" DataType="String" Description="Android版本号" />
        <Column Name="IosVersion" DataType="String" Description="Ios版本号" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="AppManagersLan" TableName="DG_AppManagersLan" Description="App下载表多语言" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="JId" DataType="Int32" Map="AppManagers@Id@$" Description="App下载Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="产品名称" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
      </Columns>
      <Indexes>
        <Index Columns="JId,LId" Unique="True" />
      </Indexes>
    </Table>
  </Tables>
</EntityModel>