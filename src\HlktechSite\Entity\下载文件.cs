﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>下载文件</summary>
[Serializable]
[DataObject]
[Description("下载文件")]
[BindIndex("IX_DG_Download_MIds", false, "MIds")]
[BindIndex("IX_DG_Download_DId", false, "DId")]
[BindIndex("IU_DG_Download_Name", true, "Name")]
[BindTable("DG_Download", Description = "下载文件", ConnName = "OnlineKeFu", DbType = DatabaseType.None)]
public partial class Download : IDownload, IEntity<IDownload>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>下载文件名称</summary>
    [DisplayName("下载文件名称")]
    [Description("下载文件名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Name", "下载文件名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Development;
    /// <summary>开发资料 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    [DisplayName("开发资料格式")]
    [Description("开发资料 格式：名称|地址|下载次数_名称|地址|下载次数")]
    [DataObjectField(false, false, true, 1000)]
    [BindColumn("Development", "开发资料 格式：名称|地址|下载次数_名称|地址|下载次数", "")]
    public String? Development { get => _Development; set { if (OnPropertyChanging("Development", value)) { _Development = value; OnPropertyChanged("Development"); } } }

    private String? _Application;
    /// <summary>应用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    [DisplayName("应用软件格式")]
    [Description("应用软件 格式：名称|地址|下载次数_名称|地址|下载次数")]
    [DataObjectField(false, false, true, 1000)]
    [BindColumn("Application", "应用软件 格式：名称|地址|下载次数_名称|地址|下载次数", "")]
    public String? Application { get => _Application; set { if (OnPropertyChanging("Application", value)) { _Application = value; OnPropertyChanged("Application"); } } }

    private String? _GeneralSoftware;
    /// <summary>通用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    [DisplayName("通用软件格式")]
    [Description("通用软件 格式：名称|地址|下载次数_名称|地址|下载次数")]
    [DataObjectField(false, false, true, 1000)]
    [BindColumn("GeneralSoftware", "通用软件 格式：名称|地址|下载次数_名称|地址|下载次数", "")]
    public String? GeneralSoftware { get => _GeneralSoftware; set { if (OnPropertyChanging("GeneralSoftware", value)) { _GeneralSoftware = value; OnPropertyChanged("GeneralSoftware"); } } }

    private String? _MIds;
    /// <summary>产品型号Id，以逗号分隔开</summary>
    [DisplayName("产品型号Id")]
    [Description("产品型号Id，以逗号分隔开")]
    [DataObjectField(false, false, true, 1000)]
    [BindColumn("MIds", "产品型号Id，以逗号分隔开", "")]
    public String? MIds { get => _MIds; set { if (OnPropertyChanging("MIds", value)) { _MIds = value; OnPropertyChanged("MIds"); } } }

    private Int32 _DId;
    /// <summary>下载分类Id</summary>
    [DisplayName("下载分类Id")]
    [Description("下载分类Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DId", "下载分类Id", "")]
    public Int32 DId { get => _DId; set { if (OnPropertyChanging("DId", value)) { _DId = value; OnPropertyChanged("DId"); } } }

    private Int32 _Clicks;
    /// <summary>点击数</summary>
    [DisplayName("点击数")]
    [Description("点击数")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Clicks", "点击数", "")]
    public Int32 Clicks { get => _Clicks; set { if (OnPropertyChanging("Clicks", value)) { _Clicks = value; OnPropertyChanged("Clicks"); } } }

    private Int16 _DisplayOrder;
    /// <summary>排序</summary>
    [DisplayName("排序")]
    [Description("排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DisplayOrder", "排序", "")]
    public Int16 DisplayOrder { get => _DisplayOrder; set { if (OnPropertyChanging("DisplayOrder", value)) { _DisplayOrder = value; OnPropertyChanged("DisplayOrder"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDownload model)
    {
        Id = model.Id;
        Name = model.Name;
        Development = model.Development;
        Application = model.Application;
        GeneralSoftware = model.GeneralSoftware;
        MIds = model.MIds;
        DId = model.DId;
        Clicks = model.Clicks;
        DisplayOrder = model.DisplayOrder;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "Development" => _Development,
            "Application" => _Application,
            "GeneralSoftware" => _GeneralSoftware,
            "MIds" => _MIds,
            "DId" => _DId,
            "Clicks" => _Clicks,
            "DisplayOrder" => _DisplayOrder,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Development": _Development = Convert.ToString(value); break;
                case "Application": _Application = Convert.ToString(value); break;
                case "GeneralSoftware": _GeneralSoftware = Convert.ToString(value); break;
                case "MIds": _MIds = Convert.ToString(value); break;
                case "DId": _DId = value.ToInt(); break;
                case "Clicks": _Clicks = value.ToInt(); break;
                case "DisplayOrder": _DisplayOrder = Convert.ToInt16(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static Download? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据产品型号Id查找</summary>
    /// <param name="mIds">产品型号Id</param>
    /// <returns>实体列表</returns>
    public static IList<Download> FindAllByMIds(String? mIds)
    {
        if (mIds == null) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.MIds.EqualIgnoreCase(mIds));

        return FindAll(_.MIds == mIds);
    }

    /// <summary>根据下载文件名称查找</summary>
    /// <param name="name">下载文件名称</param>
    /// <returns>实体对象</returns>
    public static Download? FindByName(String? name)
    {
        if (name == null) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name.EqualIgnoreCase(name));

        // 单对象缓存
        return Meta.SingleCache.GetItemWithSlaveKey(name) as Download;

        //return Find(_.Name == name);
    }
    #endregion

    #region 字段名
    /// <summary>取得下载文件字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>下载文件名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>开发资料 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
        public static readonly Field Development = FindByName("Development");

        /// <summary>应用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
        public static readonly Field Application = FindByName("Application");

        /// <summary>通用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
        public static readonly Field GeneralSoftware = FindByName("GeneralSoftware");

        /// <summary>产品型号Id，以逗号分隔开</summary>
        public static readonly Field MIds = FindByName("MIds");

        /// <summary>下载分类Id</summary>
        public static readonly Field DId = FindByName("DId");

        /// <summary>点击数</summary>
        public static readonly Field Clicks = FindByName("Clicks");

        /// <summary>排序</summary>
        public static readonly Field DisplayOrder = FindByName("DisplayOrder");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得下载文件字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>下载文件名称</summary>
        public const String Name = "Name";

        /// <summary>开发资料 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
        public const String Development = "Development";

        /// <summary>应用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
        public const String Application = "Application";

        /// <summary>通用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
        public const String GeneralSoftware = "GeneralSoftware";

        /// <summary>产品型号Id，以逗号分隔开</summary>
        public const String MIds = "MIds";

        /// <summary>下载分类Id</summary>
        public const String DId = "DId";

        /// <summary>点击数</summary>
        public const String Clicks = "Clicks";

        /// <summary>排序</summary>
        public const String DisplayOrder = "DisplayOrder";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
