﻿@*@model HlktechSite.Entity.Knowledge*@
@{
    var content = Model.Model.Content as String;
    content = content.SafeString().ToUnicodeString();
}
<style asp-location="true">
    .layui-form-select dl {
        z-index: 9999 !important;
    }

    .layui-input-block {
        margin-left: auto !important;
    }

    .xm-body.absolute {
        z-index: 99999 !important;
    }
</style>
<script src="~/static/admin/js/xm-select.js"></script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("知识问答编辑")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("编辑")</span></a></li>
            </ul>
        </div>
    </div>

    <form id="doc_form" method="post">
        <input type="hidden" name="document_id" value="1" />
        <table class="ds-default-table">
            <tbody>
                <tr>
                    <td class="required"><label class="validation">@T("标题") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="@Model.Model.Name" name="document_title" id="document_title" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td colspan="2" class="required"><label for="parent_id">@T("关联产品型号")</label></td>
                </tr>
                <tr class="noborder">
                    <td class="">
                        <div id="demo1"></div>
                    </td>
                </tr>
                <tr>
                    <td class="required"><label class="">@T("标签") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="@Model.Model.Tags" name="Tags" id="Tags" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td class="required"><label>状态:</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform onoff">

                        @if (Model.Model.Status == 1)
                        {
                            <label for="isbuy_1" class="cb-enable selected"><span>@T("已发表")</span></label>
                            <label for="isbuy_2" class="cb-disable "><span>@T("不可用")</span></label>
                            <input id="isbuy_1" name="isbuy" checked="checked" value="1" type="radio">
                            <input id="isbuy_2" name="isbuy" value="0" type="radio">
                        }
                        else
                        {
                            <label for="isbuy_1" class="cb-enable"><span>@T("已发表")</span></label>
                            <label for="isbuy_2" class="cb-disable selected"><span>@T("不可用")</span></label>
                            <input id="isbuy_1" name="isbuy" value="1" type="radio">
                            <input id="isbuy_2" name="isbuy" checked="checked" value="0" type="radio">
                        }
                        @*<div class="layui-input-block">
                    @if (Model.Model.Status == 1)
                    {
                        <input type="radio" name="isbuy" value="1" title="启用" checked>
                        <input type="radio" name="isbuy" value="0" title="禁用">
                    }
                    else
                    {
                        <input type="radio" name="isbuy" value="1" title="启用">
                        <input type="radio" name="isbuy" value="0" title="禁用" checked>
                    }
                </div>*@
                    </td>
                </tr>
                @*<tr>
            <td class="required"><label class="validation">@T("点击数:") </label></td>
        </tr>
        <tr class="noborder">
            <td><input type="text" name="Clicks" id="Clicks" value="@Model.Model.Clicks" class="w200"></td>
            <td></td>
        </tr>

        <tr>
            <td class="required"><label class="validation">@T("点赞数:") </label></td>
        </tr>
        <tr class="noborder">
            <td><input type="text" name="HelpFuls" id="HelpFuls" value="@Model.Model.HelpFuls" class="w200"></td>
            <td></td>
        </tr>
        <tr>
            <td class="required"><label class="validation">@T("无用数:") </label></td>
        </tr>
        <tr class="noborder">
            <td><input type="text" name="Useless" id="Useless" value="@Model.Model.Useless" class="w200"></td>
            <td></td>
        </tr>*@

                @*<tr>
            <td colspan="2" class="required"><label for="parent_id">@T("商品分类1"):</label></td>
        </tr>
        <tr class="noborder">
            <td class="vatop">
                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <select name="MId" lay-verify="required" lay-search lay-filter="search_type">
                            <option value="@Model.Model.KId">@ViewBag.Name</option>
                        </select>
                    </div>
                </div>
            </td>
        </tr>*@




                <tr>
                    <td class="required"><label class="validation">@T("内容") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop">
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                        <script type="text/javascript">
                            var ue = UE.getEditor('document_content', {
                                toolbars: [[
                                    'fullscreen', 'source', '|', 'undo', 'redo', '|',
                                    'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                                    'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                                    'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                                    'directionalityltr', 'directionalityrtl', 'indent', '|',
                                    'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
                                    'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                                    'emotion', 'map', 'gmap', 'insertcode', 'template', '|',
                                    'horizontal', 'date', 'time', 'spechars', '|',
                                    'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',
                                    'searchreplace', 'help', 'drafts', 'charts'
                                ]],
                            });

                            ue.ready(function () {
                                //this.setContent("@Model.Model.Content");
                               this.setContent('@Html.Raw(content)');

                            })

                        </script>
                        <textarea name="document_content" id="document_content" style="width:100%"></textarea>
                    </td>
                </tr>
                <tr>
                    <td class="required">@T("图片上传")</td>
                </tr>
                <tr class="noborder">
                    <td id="divComUploadContainer"><input type="file" multiple="multiple" id="fileupload" name="fileupload" /></td>
                </tr>
                <tr>
                    <td class="required">@T("已传图片")</td>
                </tr>
                <tr>
                    <td>
                        <div class="tdare">
                            <table width="600px" cellspacing="0" class="dataTable">
                                <tbody id="thumbnails">
                                    @foreach (var item in ViewBag.FileList as IList<HlktechSite.Entity.UploadInfo>)
                                    {
                                        <tr id="@item.Id" class="tatr2">
                                            <input type="hidden" name="file_id[]" value="@item.Id" />
                                            <td><img width="40px" height="40px" src="@(Pek.Helpers.DHWeb.GetSiteUrl())/@(item.FileUrl)" /></td>
                                            <td>@item.FileName</td>
                                            <td><a href="javascript:insert_editor('@(Pek.Helpers.DHWeb.GetSiteUrl())/@(item.FileUrl)');">插入编辑器</a> | <a href="javascript:del_file_upload('@item.Id');">删除</a></td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>

</div>

<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
@*<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>*@
<script asp-location="Footer">





    $(document).ready(function () {

         @*layui.config({
        base: '/static/plugins/js/layui/lay/modules/'
    }).use(['layer', 'table', 'jquery', 'form', 'upload', 'element'], function () {
        var $ = layui.jquery,
            table = layui.table,
            layer = layui.layer,
            form = layui.form,
            upload = layui.upload,
            element = layui.element;
        $(".layui-select-title input").on("input", function (e) {
            //获取input输入的值
            console.log(e.delegateTarget.value);
            $("[name='MId']").empty();
            console.log("进入change事件" + e.delegateTarget.value)

            $.get("@Url.Action("GetlikeName")", { Key: e.delegateTarget.value }, function (res) {

                var str = "";
                for (var i = 0; i < res.length; i++) {
                    str += "<option value=\"" + res[i].Id + "\">" + res[i].Name+"</option>"
                }
                $("[name='MId']").append(str);
                form.render("select");
            })
        });

    })*@



        $('#doc_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().prev().find('td:first'));
            },
            rules: {
                document_title: {
                    required: true
                },
                document_content: {
                    required: true
                },
                Clicks: {
                    digits: true,
                },
                HelpFuls: {
                    digits: true,
                },
                Useless:{
                    digits: true,
                }
            },
            messages: {
                document_title: {
                    required: '@T("标题不能为空")'
                },
                document_content: {
                    required: '@T("内容不能为空")'
                },
                Clicks: {
                    digits: '@T("点击数请填写数字")',
                },
                HelpFuls: {
                    digits: '@T("点赞数请填写数字")',
                },
                Useless: {
                    digits: '@T("无用数请填写数字")',
                }
            }
        });
        // 图片上传
        $('#fileupload').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: "@Url.Action("UploadImg", new { Id = Model.Model.Id })",
                done: function (e, data) {
                    if (data != 'error') {
                        add_uploadedfile(data.result);
                    }
                }
            });
        });
    });
    function add_uploadedfile(file_data) {
        var newImg = '<tr id="' + file_data.file_id + '" class="tatr2"><input type="hidden" name="file_id[]" value="' + file_data.file_id + '" /><td><img width="40px" height="40px" src="' + file_data.file_path + '" /></td><td>' + file_data.file_name + '</td><td><a href="javascript:insert_editor(\'' + file_data.file_path + '\');">插入编辑器</a> | <a href="javascript:del_file_upload(' + file_data.file_id + ');">删除</a></td></tr>';
        $('#thumbnails').prepend(newImg);
    }
    function insert_editor(file_path) {
        ue.execCommand('insertimage', { src: file_path });
    }
    function del_file_upload(file_id) {
        layer.confirm('您确定要删除吗?', {
            btn: ['确定', '取消'],
            title: false,
        }, function () {
            $.getJSON("@Url.Action("DeleteImg")", { id: + file_id }, function (result) {
                if (result) {
                    $('#' + file_id).remove();
                    layer.close(layer.index);
                } else {
                    layer.alert('删除失败');
                }
            });
        });
    }

    var data1 = $.parseJSON('@Html.Raw(ViewBag.List)');

    var demo1 = xmSelect.render({
        el: '#demo1',
        radio: true,
        paging: true,
        pageSize: 10,
        filterable: true,
        filterMethod: function (val, item, index, prop) {
            if (item.name.toLowerCase().indexOf(val.toLowerCase()) != -1) {//名称中包含的大小写都搜索出来
                return true;
            }
            return false;//其他的就不要了
        },
        pageEmptyShow: false,
        clickClose: true,
        data: data1
    });

   
</script>