﻿
.title1 {
    color: #333 !important;
    background-color: #fff;
}

    .title1:hover {
        background-color: #eee !important;
    }


.title9 {
    color: #fff !important;
    background-color: #337ab7;
}

    .title9:hover {
        background-color: #337ab7 !important;
    }


.jour-top > img {
    width: 100%;
}

.jour-top {
    position: relative;
}

    .jour-top > div {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        bottom: 0px;
        color: #FFFFFF;
    }

        .jour-top > div > h2 {
            margin-top: 149px;
            text-align: center;
            font-size: 56px;
        }

        .jour-top > div > p {
            width: 844px;
            margin: 0 auto;
            margin-top: 35px;
            text-align: center;
            font-size: 20px;
            color:
        }




.jour-nav {
    border-bottom: 1px solid #EEEEEE;
}

    .jour-nav > div {
        width: 1200px;
        margin: 0 auto;
        display: flex;
        padding-top: 14px;
    }

        .jour-nav > div > p {
            flex: 1;
            margin-bottom: 0px;
        }

            .jour-nav > div > p > a {
                display: inline-block;
                text-align: center;
                width: 82px;
                color: #909399;
                text-decoration: none;
                margin-right: 16px;
                height: 42px;
            }

            .jour-nav > div > p > .selected {
                color: #333333;
                position: relative;
            }

                .jour-nav > div > p > .selected:after {
                    content: "";
                    position: absolute;
                    left: 0px;
                    right: 0px;
                    bottom: 0px;
                    height: 2px;
                    background-color: #414141;
                }


            .jour-nav > div > p > a:hover {
                text-decoration: none;
            }


        .jour-nav > div > div {
            width: 280px;
        }

    .jour-nav .input-group-addon {
        background-color: #4E6EF2;
        cursor: pointer;
        padding-top: 8px;
        padding-bottom: 8px;
    }


.jour-list {
    padding-top: 53px;
}

    .jour-list > h2 {
        color: #343434;
        font-size: 32px;
        margin: 0px;
        text-align: center;
        /* padding-bottom: 71px; */
    }

    .jour-list > div {
        display: flex;
        width: 1200px;
        margin: 0 auto;
        padding-bottom: 28px;
        padding-top: 75px;
        position: relative;
    }

        .jour-list > div:after {
            content: "";
            position: absolute;
            bottom: 0px;
            height: 1px;
            left: 0px;
            right: 0px;
            background-color: #DADADA;
        }

        .jour-list > div > a > img {
            width: 411px;
            height: 258px;
        }

        .jour-list > div > p {
            padding-left: 88px;
            flex: 1;
            margin-bottom: 0px;
        }

            .jour-list > div > p > a {
                color: #202020;
                margin-top: 28px;
                font-size: 23px;
                display: block;
            }

                .jour-list > div > p > a:hover {
                    text-decoration: none;
                }

            .jour-list > div > p > span {
                display: block;
                padding-top: 18px;
                color: rgba(170,171,170,1);
                font-weight: 300;
                font-size: 14px;
                padding-bottom: 20px;
            }

                .jour-list > div > p > span > img {
                    margin-right: 4px;
                    margin-bottom: 2px;
                }

            .jour-list > div > p > i {
                width: calc(100% - 25px);
                font-size: 17px;
                color: #202020;
                line-height: 27px;
                font-style: normal;
                height: 82px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                white-space: pre-wrap;
            }

            .jour-list > div > p > a:last-child {
                color: #FFFFFF;
                margin: 0px;
                display: block;
                width: 127px;
                height: 37px;
                line-height: 37px;
                text-align: center;
                border: 1px solid #AAACAA;
                margin-top: 8px;
                color: #AAABAA;
                font-size: 14px;
                border-radius: 3px;
            }

                .jour-list > div > p > a:last-child:hover {
                    background-color: #4E6EF2;
                    color: #fff;
                    border: 1px solid #4E6EF2;
                }
