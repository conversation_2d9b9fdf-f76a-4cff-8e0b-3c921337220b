﻿@{
    Html.AppendCssFileParts("~/css/Case.css");

    if (Model.CId == 0)
    {
        Html.AppendTitleParts(T("解决方案").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        var modelSolutionCategory = Model.Model as SolutionCategory;
        Html.AppendTitleParts(modelSolutionCategory.Name + DG.Setting.Current.PageTitleSeparator + T("解决方案").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
}
<style>
    .title1 {
        color: #333 !important;
        background-color: #fff;
    }

        .title1:hover {
            background-color: #eee !important;
        }


    .title4 {
        color: #333 !important;
        background-color: #fff;
    }

        .title4:hover {
            background-color: #eee !important;
        }

    .title3 {
        color: #fff !important;
        background-color: #337ab7;
    }

        .title3:hover {
            background-color: #337ab7 !important;
        }
</style>

<div class="top">
    <img src="@(CDN.GetCDN())/images/CaseBanner.png" />
    <div>
        <h2>@T("智能解决方案")</h2>
        <P>
            @T("智能解决方案能够帮助工业企业迅速建立远程连接和管理能力")
        </P>
    </div>
</div>

<div class="navigation" style="border-bottom: 1px solid #eeeeee;">
    <div class="navigation-con">
        <div class="navigation-con-left">
            <div>
                <i>@T("方案分类")：</i>
                <a class='@(Model.CId==0?"selected":"")' href="@Url.DGAction("Index","Solution",IsHtml:true)">@T("全部")</a>
                @foreach (var item in Model.SolutionTypelist as IEnumerable<SolutionCategory>)
                {
                    <a class="@(Model.CId == item.Id ? "selected" : "")" href="@Url.DGAction("List", "Solution", new { CId = item.Id})" id="@item.Id">@item.Name</a>
                }
            </div>
        </div>
        <div class="navigation-con-right">
            <div class="input-group">
                <input type="text" class="form-control" id="keyVal" value="@Model.key" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2">
                <span class="input-group-addon" id="basic-addon2"><img src="@(CDN.GetCDN())/images/fdj.png" /></span>
            </div>
        </div>
    </div>
</div>
<div style=" background-color: #FAFAFA;">
    <div class="Case">
        @foreach (var item in Model.SolutionList)
        {
            <a target="_blank" href="@(item.Url==""||item.Url==null?Url.DGAction("Details",new { Id=item.Id}):item.Url)">
                <div>
                    <img src="@(CDN.GetCDN())/@item.Pic" />
                    <p>@item.Name</p>
                    <span>@item.Summary</span>
                </div>
            </a>
        }
    </div>

    <div class="paging" style="text-align: center;padding-bottom:70px">
        <ul class="pagination">
            @Html.Raw(Model.Str)
        </ul>
    </div>
</div>
<script asp-location="Footer">
    $("#basic-addon2").click(function () {
        window.location.href = '@Url.DGAction("Index")?key=' + $("#keyVal").val();
    })
    $("#keyVal").keyup(function (e) {
        if (e.keyCode == 13) {
            $("#basic-addon2").click();
        }
    });
</script>