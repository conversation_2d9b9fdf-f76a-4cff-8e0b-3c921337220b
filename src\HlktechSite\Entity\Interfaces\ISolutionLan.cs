﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>解决方案翻译</summary>
public partial interface ISolutionLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>CaseId</summary>
    Int32 CId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>解决方案标题</summary>
    String? Name { get; set; }

    /// <summary>内容</summary>
    String? Content { get; set; }

    /// <summary>简介</summary>
    String? Summary { get; set; }

    /// <summary>解决方案主图</summary>
    String? Pic { get; set; }
    #endregion
}
