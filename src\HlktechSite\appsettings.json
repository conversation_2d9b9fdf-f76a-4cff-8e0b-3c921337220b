{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Information"
      },
      "FormatterName": "systemd", //json
      "FormatterOptions": {
        "JsonWriterOptions": {
          "Indented": true
        },
        "ExtraCustomProperty": "modified",
        "IncludeScopes": true,
        "TimestampFormat": "HH:mm:ss",
        "UseUtcTimestamp": true
      }
    }
  },
  "RedisCache": "server=127.0.0.1;password=;db=2",
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DG": {
      //"connectionString": "Server=denghao.rwlb.rds.aliyuncs.com;Port=3306;Database=hlktech_com;Uid=hlktechcom;Pwd=****************;SslMode=None;Migration=Full;",
      //"providerName": "MySql.Data.MySqlClient"
    },
    "Membership": {
      //"connectionString": "Server=denghao.rwlb.rds.aliyuncs.com;Port=3306;Database=hlktech_com;Uid=hlktechcom;Pwd=****************;SslMode=None;;Migration=Full;",
      //"providerName": "MySql.Data.MySqlClient"
    },
    "OnlineKeFu": {
      //"connectionString": "Server=denghao.rwlb.rds.aliyuncs.com;Port=3306;Database=h_hlktechcom;Uid=hlktechcom;Pwd=****************;SslMode=None;Migration=Off;",
      //"providerName": "MySql.Data.MySqlClient"
    }
  }
}