﻿using AIAddress.Entity;

using DH;

using Flurl;
using Flurl.Http;

using MiniExcelLibs;

using NewLife;
using NewLife.Log;
using NewLife.Serialization;

namespace AIAddress;

internal class Program {
    static async Task Main(string[] args)
    {
        XTrace.UseConsole();

        System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);


        // 设置APPID/AK/SK
        var APP_ID = "5095549";
        var API_KEY = "jbw8YGDIGtysPNAPGwHHjN8q";
        var SECRET_KEY = "uwvc4oTOd0g9pf8ughW2EPDrGwper4vV";

        //var client = new Baidu.Aip.Nlp.Nlp(API_KEY, SECRET_KEY);
        //client.Timeout = 60000;  // 修改超时时间

        //var text = "百度是一家高科技公司";

        //// 调用词法分析，可能会抛出网络等异常，请使用try/catch捕获
        //var result = client.Lexer(text);
        //XTrace.WriteLine(result.ToString());

        //var rows = MiniExcel.Query("C:\\Users\\<USER>\\Desktop\\客户资料7.xlsx", useHeaderRow: true).ToList();
        //XTrace.WriteLine($"{rows[0].业务员}");

        //var dic = new List<Dictionary<string, object>>();

        //var i = 1;
        //foreach (var item in rows)
        //{
        //    var dic1 = new Dictionary<string, object>();
        //    dic1.Add("编号", i);
        //    dic1.Add("业务员", item.业务员);
        //    dic1.Add("部门名称", item.部门名称);
        //    dic1.Add("收货联系人", item.收货联系人);
        //    dic1.Add("收货地址", item.收货地址);
        //    dic.Add(dic1);

        //    i++;
        //}

        //MiniExcel.SaveAs("资料.xlsx".GetFullPath(), dic);

        var response = await $"https://aip.baidubce.com/oauth/2.0/token"
                   .SetQueryParams(new
                   {
                       client_id = API_KEY,
                       client_secret = SECRET_KEY,
                       grant_type = "client_credentials"
                   })
        //.GetJsonAsync<BaiDuAccessTokenModel>();
                   .GetStringAsync();

        XTrace.WriteLine(response);

        var result = response.ToJsonEntity<BaiDuAccessTokenModel>();

        if (result?.error != null)
        {
            XTrace.WriteLine($"获取百度AccessToken出错：{result?.error_description}");
            return;
        }

        XTrace.WriteLine(result?.access_token);

        var rows = MiniExcel.Query("C:\\Users\\<USER>\\Desktop\\资料.xlsx", useHeaderRow: true).ToList();
        XTrace.WriteLine($"{rows[0].业务员}");

        foreach (var item in rows)
        {
            // 对每个元素执行操作
            Double Id = item.编号;
            String Salesman = item.业务员;
            String Department = item.部门名称;
            String Receiving = item.收货联系人;
            String Delivery = item.收货地址;

            var model = Address.FindByIdAndSalesmanAndDepartmentAndReceiving(Id.ToInt(), Salesman, Department, Receiving);
            if (model == null)
            {
                model = new Address();
                model.Id = Id.ToInt();
                model.Salesman = Salesman;
                model.Department = Department;
                model.Receiving = Receiving;
                model.Delivery = Delivery;
                model.Insert();
            }

            if (Id == model.Id && !model.Process)
            {
                if (!Delivery.IsNullOrWhiteSpace() && Delivery != "无")
                {
                    try
                    {
                        var rep = $"https://aip.baidubce.com/rpc/2.0/nlp/v1/address"
                            .SetQueryParams(new
                            {
                                result?.access_token
                            })
                            .PostJsonAsync(new
                            {
                                text = Delivery
                            }).Result;

                        var res = rep.ResponseMessage.Content.ReadAsStringAsync().Result;
                        var result1 = res.ToJsonEntity<AddressModel>();

                        XTrace.WriteLine($"获取到的地址解析：{result1?.ToJson()}");

                        if (result1 == null || result1?.city == null)
                        {
                            break;
                        }

                        model.Lat = result1?.lat ?? 0.0;
                        model.Detail = result1?.detail;
                        model.Town = result1?.town;
                        model.PhoneNum = result1?.phonenum;
                        model.CityCode = result1?.city_code;
                        model.Province = result1?.province;
                        model.Person = result1?.person;
                        model.Lng = result1?.lng ?? 0.0;
                        model.ProvinceCode = result1?.province_code;
                        model.Text = result1?.text;
                        model.County = result1?.county;
                        model.City = result1?.city;
                        model.CountyCode = result1?.county_code;
                        model.TownCode = result1?.town_code;
                        model.Process = true;
                        model.Update();
                    }
                    catch (Exception ex)
                    {
                        XTrace.WriteException(ex);

                        Thread.Sleep(5_000);

                        if (Id == model.Id && !model.Process)
                        {
                            if (!Delivery.IsNullOrWhiteSpace())
                            {
                                var rep = $"https://aip.baidubce.com/rpc/2.0/nlp/v1/address"
                                        .SetQueryParams(new
                                        {
                                            result?.access_token
                                        })
                                        .PostJsonAsync(new
                                        {
                                            text = Delivery
                                        }).Result;

                                var res = rep.ResponseMessage.Content.ReadAsStringAsync().Result;
                                var result1 = res.ToJsonEntity<AddressModel>();

                                XTrace.WriteLine($"获取到的地址解析：{result1?.ToJson()}");

                                model.Lat = result1?.lat ?? 0.0;
                                model.Detail = result1?.detail;
                                model.Town = result1?.town;
                                model.PhoneNum = result1?.phonenum;
                                model.CityCode = result1?.city_code;
                                model.Province = result1?.province;
                                model.Person = result1?.person;
                                model.Lng = result1?.lng ?? 0.0;
                                model.ProvinceCode = result1?.province_code;
                                model.Text = result1?.text;
                                model.County = result1?.county;
                                model.City = result1?.city;
                                model.CountyCode = result1?.county_code;
                                model.TownCode = result1?.town_code;
                                model.Process = true;
                                model.Update();
                            }
                            else
                            {
                                model.Process = true;
                                model.Update();
                            }
                        }
                    }
                }
                else
                {
                    model.Process = true;
                    model.Update();
                }
            }

            //foreach (var item in rows)
            //{


            //    //var dic1 = new Dictionary<string, object>();
            //    //dic1.Add("业务员", item.业务员);
            //    //dic1.Add("部门名称", item.部门名称);
            //    //dic1.Add("收货联系人", item.收货联系人);
            //    //dic1.Add("收货地址", item.收货地址);
            //    //dic1.Add("城市", result1?.city);
            //    //dic.Add(dic1);
            //}

            //MiniExcel.SaveAs("资料.xlsx".GetFullPath(), dic);
        }
    }
}