﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>App下载表多语言</summary>
public partial class AppManagersLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>App下载Id</summary>
    public Int32 JId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>产品名称</summary>
    public String? Name { get; set; }

    /// <summary>内容</summary>
    public String? Content { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAppManagersLan model)
    {
        Id = model.Id;
        JId = model.JId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
    }
    #endregion
}
