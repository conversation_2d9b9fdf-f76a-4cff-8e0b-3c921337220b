﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>代理商申请</summary>
[Serializable]
[DataObject]
[Description("代理商申请")]
[BindTable("DG_Agent", Description = "代理商申请", ConnName = "DG", DbType = DatabaseType.None)]
public partial class Agent : IAgent, IEntity<IAgent>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _CompanyName;
    /// <summary>公司名称</summary>
    [DisplayName("公司名称")]
    [Description("公司名称")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("CompanyName", "公司名称", "", Master = true)]
    public String? CompanyName { get => _CompanyName; set { if (OnPropertyChanging("CompanyName", value)) { _CompanyName = value; OnPropertyChanged("CompanyName"); } } }

    private String? _ContactPerson;
    /// <summary>联系人</summary>
    [DisplayName("联系人")]
    [Description("联系人")]
    [DataObjectField(false, false, true, 10)]
    [BindColumn("ContactPerson", "联系人", "")]
    public String? ContactPerson { get => _ContactPerson; set { if (OnPropertyChanging("ContactPerson", value)) { _ContactPerson = value; OnPropertyChanged("ContactPerson"); } } }

    private String? _Phone;
    /// <summary>联系电话</summary>
    [DisplayName("联系电话")]
    [Description("联系电话")]
    [DataObjectField(false, false, true, 15)]
    [BindColumn("Phone", "联系电话", "")]
    public String? Phone { get => _Phone; set { if (OnPropertyChanging("Phone", value)) { _Phone = value; OnPropertyChanged("Phone"); } } }

    private String? _Email;
    /// <summary>邮箱</summary>
    [DisplayName("邮箱")]
    [Description("邮箱")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Email", "邮箱", "")]
    public String? Email { get => _Email; set { if (OnPropertyChanging("Email", value)) { _Email = value; OnPropertyChanged("Email"); } } }

    private String? _ContactAddress;
    /// <summary>联系地址</summary>
    [DisplayName("联系地址")]
    [Description("联系地址")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("ContactAddress", "联系地址", "")]
    public String? ContactAddress { get => _ContactAddress; set { if (OnPropertyChanging("ContactAddress", value)) { _ContactAddress = value; OnPropertyChanged("ContactAddress"); } } }

    private String? _Summary;
    /// <summary>申请说明</summary>
    [DisplayName("申请说明")]
    [Description("申请说明")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Summary", "申请说明", "")]
    public String? Summary { get => _Summary; set { if (OnPropertyChanging("Summary", value)) { _Summary = value; OnPropertyChanged("Summary"); } } }

    private Boolean _IsThrough;
    /// <summary>是否通过</summary>
    [DisplayName("是否通过")]
    [Description("是否通过")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsThrough", "是否通过", "")]
    public Boolean IsThrough { get => _IsThrough; set { if (OnPropertyChanging("IsThrough", value)) { _IsThrough = value; OnPropertyChanged("IsThrough"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAgent model)
    {
        Id = model.Id;
        CompanyName = model.CompanyName;
        ContactPerson = model.ContactPerson;
        Phone = model.Phone;
        Email = model.Email;
        ContactAddress = model.ContactAddress;
        Summary = model.Summary;
        IsThrough = model.IsThrough;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "CompanyName" => _CompanyName,
            "ContactPerson" => _ContactPerson,
            "Phone" => _Phone,
            "Email" => _Email,
            "ContactAddress" => _ContactAddress,
            "Summary" => _Summary,
            "IsThrough" => _IsThrough,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "CompanyName": _CompanyName = Convert.ToString(value); break;
                case "ContactPerson": _ContactPerson = Convert.ToString(value); break;
                case "Phone": _Phone = Convert.ToString(value); break;
                case "Email": _Email = Convert.ToString(value); break;
                case "ContactAddress": _ContactAddress = Convert.ToString(value); break;
                case "Summary": _Summary = Convert.ToString(value); break;
                case "IsThrough": _IsThrough = value.ToBoolean(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得代理商申请字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>公司名称</summary>
        public static readonly Field CompanyName = FindByName("CompanyName");

        /// <summary>联系人</summary>
        public static readonly Field ContactPerson = FindByName("ContactPerson");

        /// <summary>联系电话</summary>
        public static readonly Field Phone = FindByName("Phone");

        /// <summary>邮箱</summary>
        public static readonly Field Email = FindByName("Email");

        /// <summary>联系地址</summary>
        public static readonly Field ContactAddress = FindByName("ContactAddress");

        /// <summary>申请说明</summary>
        public static readonly Field Summary = FindByName("Summary");

        /// <summary>是否通过</summary>
        public static readonly Field IsThrough = FindByName("IsThrough");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得代理商申请字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>公司名称</summary>
        public const String CompanyName = "CompanyName";

        /// <summary>联系人</summary>
        public const String ContactPerson = "ContactPerson";

        /// <summary>联系电话</summary>
        public const String Phone = "Phone";

        /// <summary>邮箱</summary>
        public const String Email = "Email";

        /// <summary>联系地址</summary>
        public const String ContactAddress = "ContactAddress";

        /// <summary>申请说明</summary>
        public const String Summary = "Summary";

        /// <summary>是否通过</summary>
        public const String IsThrough = "IsThrough";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
