﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>产品型号</summary>
[Serializable]
[DataObject]
[Description("产品型号")]
[BindTable("DG_ProductModel", Description = "产品型号", ConnName = "OnlineKeFu", DbType = DatabaseType.None)]
public partial class ProductModel : IProductModel, IEntity<IProductModel>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>型号</summary>
    [DisplayName("型号")]
    [Description("型号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Name", "型号", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private Int16 _DisplayOrder;
    /// <summary>排序</summary>
    [DisplayName("排序")]
    [Description("排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DisplayOrder", "排序", "")]
    public Int16 DisplayOrder { get => _DisplayOrder; set { if (OnPropertyChanging("DisplayOrder", value)) { _DisplayOrder = value; OnPropertyChanged("DisplayOrder"); } } }

    private String? _PcShopUrl;
    /// <summary>商城Pc链接</summary>
    [DisplayName("商城Pc链接")]
    [Description("商城Pc链接")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("PcShopUrl", "商城Pc链接", "")]
    public String? PcShopUrl { get => _PcShopUrl; set { if (OnPropertyChanging("PcShopUrl", value)) { _PcShopUrl = value; OnPropertyChanged("PcShopUrl"); } } }

    private String? _MobileShopUrl;
    /// <summary>商城移动端链接</summary>
    [DisplayName("商城移动端链接")]
    [Description("商城移动端链接")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("MobileShopUrl", "商城移动端链接", "")]
    public String? MobileShopUrl { get => _MobileShopUrl; set { if (OnPropertyChanging("MobileShopUrl", value)) { _MobileShopUrl = value; OnPropertyChanged("MobileShopUrl"); } } }

    private String? _TaobaoShopUrl1;
    /// <summary>淘宝产品链接</summary>
    [DisplayName("淘宝产品链接")]
    [Description("淘宝产品链接")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("TaobaoShopUrl1", "淘宝产品链接", "")]
    public String? TaobaoShopUrl1 { get => _TaobaoShopUrl1; set { if (OnPropertyChanging("TaobaoShopUrl1", value)) { _TaobaoShopUrl1 = value; OnPropertyChanged("TaobaoShopUrl1"); } } }

    private String? _AppletQrUrl;
    /// <summary>小程序二维码图片链接</summary>
    [DisplayName("小程序二维码图片链接")]
    [Description("小程序二维码图片链接")]
    [DataObjectField(false, false, true, 300)]
    [BindColumn("AppletQrUrl", "小程序二维码图片链接", "")]
    public String? AppletQrUrl { get => _AppletQrUrl; set { if (OnPropertyChanging("AppletQrUrl", value)) { _AppletQrUrl = value; OnPropertyChanged("AppletQrUrl"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductModel model)
    {
        Id = model.Id;
        Name = model.Name;
        DisplayOrder = model.DisplayOrder;
        PcShopUrl = model.PcShopUrl;
        MobileShopUrl = model.MobileShopUrl;
        TaobaoShopUrl1 = model.TaobaoShopUrl1;
        AppletQrUrl = model.AppletQrUrl;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "DisplayOrder" => _DisplayOrder,
            "PcShopUrl" => _PcShopUrl,
            "MobileShopUrl" => _MobileShopUrl,
            "TaobaoShopUrl1" => _TaobaoShopUrl1,
            "AppletQrUrl" => _AppletQrUrl,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "DisplayOrder": _DisplayOrder = Convert.ToInt16(value); break;
                case "PcShopUrl": _PcShopUrl = Convert.ToString(value); break;
                case "MobileShopUrl": _MobileShopUrl = Convert.ToString(value); break;
                case "TaobaoShopUrl1": _TaobaoShopUrl1 = Convert.ToString(value); break;
                case "AppletQrUrl": _AppletQrUrl = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductModel? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }
    #endregion

    #region 字段名
    /// <summary>取得产品型号字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>型号</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>排序</summary>
        public static readonly Field DisplayOrder = FindByName("DisplayOrder");

        /// <summary>商城Pc链接</summary>
        public static readonly Field PcShopUrl = FindByName("PcShopUrl");

        /// <summary>商城移动端链接</summary>
        public static readonly Field MobileShopUrl = FindByName("MobileShopUrl");

        /// <summary>淘宝产品链接</summary>
        public static readonly Field TaobaoShopUrl1 = FindByName("TaobaoShopUrl1");

        /// <summary>小程序二维码图片链接</summary>
        public static readonly Field AppletQrUrl = FindByName("AppletQrUrl");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得产品型号字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>型号</summary>
        public const String Name = "Name";

        /// <summary>排序</summary>
        public const String DisplayOrder = "DisplayOrder";

        /// <summary>商城Pc链接</summary>
        public const String PcShopUrl = "PcShopUrl";

        /// <summary>商城移动端链接</summary>
        public const String MobileShopUrl = "MobileShopUrl";

        /// <summary>淘宝产品链接</summary>
        public const String TaobaoShopUrl1 = "TaobaoShopUrl1";

        /// <summary>小程序二维码图片链接</summary>
        public const String AppletQrUrl = "AppletQrUrl";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
