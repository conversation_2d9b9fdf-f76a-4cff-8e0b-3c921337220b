﻿@{
    Html.AppendCssFileParts("~/css/Case.css");
    Html.AppendCssFileParts("~/css/Journalism.css");

    if (Model.AId == 0)
    {
        Html.AppendTitleParts(T("新闻中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        var modelArticleCategory = Model.Model as ArticleCategory;
        Html.AppendTitleParts(modelArticleCategory?.Name + DG.Setting.Current.PageTitleSeparator + T("新闻中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }

    var typeName = "";
}
<style type="text/css">
    .title4 {
        color: #333 !important;
        background-color: #fff;
    }

        .title4:hover {
            background-color: #eee !important;
        }
</style>
<div class="jour-top">
    <img src="@(CDN.GetCDN())/images/journalism.png" />
    <div>
        <h2>@T("新闻资讯")</h2>
        <P>@T("热点/痛点/观点 连点成线，物联大事件脉络尽在掌握")</P>
    </div>
</div>

<div class="navigation" style="border-bottom: 1px solid #eeeeee;">
    <div class="navigation-con">
        <div class="navigation-con-left">
            <div>
                <i>@T("新闻分类")：</i>
                <a id="0" class='@(Model.AId == 0?"selected":"")' href="@Url.DGAction("Index", "Journalism", IsHtml:true)">@T("全部")</a>
                @foreach (var item in Model.ArticleTypelist as IEnumerable<ArticleCategory>)
                {
                    if (Model.AId == item.Id)
                    {
                        typeName = item.Name;
                    }
                    <a class="@(Model.AId==item.Id?"selected":"")" href="@Url.DGAction("List",new { AId = item.Id})" id="@item.Id">@item.Name</a>
                }
            </div>
        </div>
        <div class="navigation-con-right">
            <div class="input-group">
                <input type="text" class="form-control" id="KeyVal" value="@Model.Key" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2">
                <span class="input-group-addon" id="basic-addon2"><img src="@(CDN.GetCDN())/images/fdj.png" /></span>
            </div>
        </div>
    </div>
</div>


<div class="jour-list">

    <h2>@(typeName.IsNullOrWhiteSpace()?"": typeName)</h2>
    @foreach (var item in Model.JournalismList as IEnumerable<Article>)
    {
        <div>
            <a href="@(item.Url==""||item.Url==null?Url.DGAction("Details","Journalism",new {Id =item.Id}):item.Url)"><img src="@UrlHelper.Combine(CDN.GetCDN(), item.Pic.IsNullOrWhiteSpace() ? "" : item.Pic)" /></a>
            <p>
                <a href="@(item.Url==""||item.Url==null?Url.DGAction("Details",new { Id=item.Id}):item.Url)">@item.Name</a>
                <span>
                    @T("发布时间")：@item.CreateTime.ToString("yyyy-MM-dd hh:mm")
                </span>
                <i>@(item.Summary.IsNullOrWhiteSpace()?item.Name:item.Summary)</i>
                <a href="@(item.Url==""||item.Url==null?Url.DGAction("Details","Journalism",new {Id =item.Id}):item.Url)">@T("查看详情")</a>
            </p>
        </div>
    }

    <div class="paging" style=" display: block; box-shadow: inherit; text-align: center;">
        <ul class="pagination">
            @Html.Raw(Model.Str)
        </ul>
    </div>

</div>

<script type="text/javascript" asp-location="Footer">
    $(function () {
        $("#basic-addon2").click(function () {
            window.location.href = '@Url.DGAction("Index")?key=' + $("#KeyVal").val()+'&AId='+$(".selected").attr("id");
        });

        $("#KeyVal").keyup(function (e) {
            if (e.keyCode == 13) {
                $("#basic-addon2").click();
            }
        })
    })
</script>