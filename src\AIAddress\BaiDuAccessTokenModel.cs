﻿namespace AIAddress;

public class BaiDuAccessTokenModel {

    /// <summary>
    /// 暂时未使用，可忽略
    /// </summary>
    public String? refresh_token { get; set; }

    /// <summary>
    /// 有效期，Access Token的有效期。
    /// 说明：单位是秒，有效期30天
    /// </summary>
    public Int32 expires_in { get; set; }

    /// <summary>
    /// 暂时未使用，可忽略
    /// </summary>
    public String? session_key { get; set; }

    /// <summary>
    /// 访问凭证
    /// </summary>
    public String? access_token { get; set; }

    /// <summary>
    /// 暂时未使用，可忽略
    /// </summary>
    public String? scope { get; set; }

    /// <summary>
    /// 暂时未使用，可忽略
    /// </summary>
    public String? session_secret { get; set; }

    /// <summary>
    /// 错误码
    /// 说明：响应失败时返回该字段，成功时不返回
    /// </summary>
    public String? error { get; set; }

    /// <summary>
    /// 错误描述信息，帮助理解和解决发生的错误
    /// 说明：响应失败时返回该字段，成功时不返回
    /// </summary>
    public String? error_description { get; set; }

    /// <summary>
    /// 实际过期时间
    /// </summary>
    public DateTimeOffset? ExpireTimestamp { get; set; }
}