using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>下载分类</summary>
    public partial class DownloadCategory : CubeEntityBase<DownloadCategory>
    {
        #region 对象操作
        static DownloadCategory()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(ParentId));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }
        
        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        /// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        [EditorBrowsable(EditorBrowsableState.Never)]
        protected override void InitData()
        {
            // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
            if (Meta.Session.Count > 0) return;

            if (XTrace.Debug) XTrace.WriteLine("开始初始化DownloadCategory[下载分类]数据……");

            var list = new List<DownloadCategory>();
            list.Add(new DownloadCategory { Name = "IOT模块", ParentId = 0, ParentIdList = "0", Level = 0, DisplayOrder = 1 });
            list.Add(new DownloadCategory { Name = "路由模块", ParentId = 0, ParentIdList = "0", Level = 0, DisplayOrder = 2 });
            list.Add(new DownloadCategory { Name = "继电器模块", ParentId = 0, ParentIdList = "0", Level = 0, DisplayOrder = 3 });
            list.Add(new DownloadCategory { Name = "感知模块", ParentId = 0, ParentIdList = "0", Level = 0, DisplayOrder = 4 });
            list.Add(new DownloadCategory { Name = "电源模块", ParentId = 0, ParentIdList = "0", Level = 0, DisplayOrder = 5 });
            list.Insert();

            if (XTrace.Debug) XTrace.WriteLine("完成初始化DownloadCategory[下载分类]数据！");
        }

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        /// 获取父级的子集合
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public IEnumerable<DownloadCategory> ParentChildList => Extends.Get(nameof(ParentChildList), k => FindAllByParentId(ParentId).OrderBy(e => e.Id));

        /// <summary>
        /// 获取子集合
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public IEnumerable<DownloadCategory> ChildList => Extends.Get(nameof(ChildList), k => FindAllByParentId(Id).OrderBy(e => e.Id));

        /// <summary>
        ///是否存在子集
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public Boolean subset { get; set; }
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static DownloadCategory FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据名称查找</summary>
        /// <param name="name">设备DeviceName</param>
        /// <returns>实体对象</returns>
        public static DownloadCategory FindByName(String name)
        {
            if (name.IsNullOrWhiteSpace()) return null;

            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

            return Find(_.Name == name);
        }

        /// <summary>根据编号列表查找</summary>
        /// <param name="ids">编号列表</param>
        /// <returns>实体对象</returns>
        public static IList<DownloadCategory> FindByIds(String ids)
        {
            if (ids.IsNullOrWhiteSpace()) return new List<DownloadCategory>();

            ids = ids.Trim(',');

            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => ids.SplitAsInt(",").Contains(x.Id));
            }

            return FindAll(_.Id.In(ids.Split(',')));
        }

        /// <summary>根据所属父级Id查找</summary>
        /// <param name="parentId">所属父级Id</param>
        /// <returns>实体列表</returns>
        public static IList<DownloadCategory> FindAllByParentId(Int32 parentId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ParentId == parentId);

            return FindAll(_.ParentId == parentId);
        }

        /// <summary>根据编号、当前层级查找</summary>
        /// <param name="id">编号</param>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IList<DownloadCategory> FindAllByIdAndLevel(Int32 id, Int32 level)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Id == id && e.Level == level);

            return FindAll(_.Id == id & _.Level == level);
        }

        /// <summary>根据当前层级查找</summary>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IList<DownloadCategory> FindAllByLevel(Int32 level = 0)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Level == level);

            return FindAll(_.Level == level);
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="parentId">所属父级Id</param>
        /// <param name="level">当前层级</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<DownloadCategory> Search(Int32 parentId, Int32 level, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (parentId >= 0) exp &= _.ParentId == parentId;
            if (level >= 0) exp &= _.Level == level;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.ParentIdList.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        // Select Count(Id) as Id,Category From DownloadCategory Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<DownloadCategory> _CategoryCache = new FieldCache<DownloadCategory>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}