/* 等待打款 */
.test-pay-container {
	width: 650px;
	padding-top: 10px;
}
.test-pay-content {
	position: relative;
	border-bottom: 1px solid #eee;
	padding-bottom: 25px;
	padding-right: 20px;
}
.verify-result-icon {
	position: absolute;
	top: -9px;
	left: -58px;
	width: 40px;
	height: 40px;
	background-position: center;
}
.test-pay-content,
.view-rights {
	font-size: 14px;
}
.certify-money {
	width: 53px;
	padding-right: 13px;
	padding-left: 0;
	text-align: right;
}
.reject-reason,
.test-pay-tips ul {
	margin-top: 10px;
}
.test-pay-tips li {
	line-height: 1.8em;
}


/*新个人认证*/
.auth-sprite {
	background: url(img/auth_sprite.png) no-repeat;
}
.upload-sprite {
	background: url(img/upload_sprite.png) no-repeat;
}
.choose-box {
	width: 100%;
	margin: 0 auto;
}
.choose-box .choose-item {
	position: relative;
	width: 100%;
	height: 136px;
	margin-bottom: 25px;
}
.auth-type-link {
	display: block;
	border: 1px solid #e1e4e6;
	width: 100%;
	height: 136px;
	padding: 40px 0 0 120px;
	background: no-repeat 42px center;
}
.auth-type-link:hover {
	border-color: #00aaff;
	text-decoration: none;
}
.auth-type-link.choose-type-disabled:hover {
	border-color: #e1e4e6;
}
.auth-type-title {
	display: block;
	margin-bottom: 14px;
	font-size: 18px;
	color: #262829;
}
.auth-type-subtitle {
	display: block;
	font-size: 12px;
	color: #a0a2a3;
}
.recommend-label {
	display: inline-block;
	height: 24px;
	line-height: 24px;
	text-align: center;
	color: #fff;
	font-size: 12px;
	padding: 0 14px;
	background: #ff6600;
	vertical-align: middle;
	margin-left: 14px;
}
.choose-type1 {
	background-image: url(img/icon_auth_mobile.png);
}
.choose-type1:hover {
	background-image: url(img/icon_auth_mobile_hover.png);
}

.choose-type2 {
	background-image: url(img/icon_auth_card.png);

}
.choose-type2:hover {
	background-image: url(img/icon_auth_card_hover.png);
}

.choose-type-disabled {
	position: relative;
	cursor: no-drop;
}
.choose-type-disabled:after {
	position: absolute;
	content: "";
	width: 56px;
	height: 56px;
	left: 0;
	top: 0;
	background: url(img/auth_icon_close.png);
}
.choose-type-disabled.choose-type1 {
	background-image: url(img/icon_auth_mobile_disabled.png);
}
.choose-type-disabled.choose-type2 {
	background-image: url(img/icon_auth_card_disabled.png);
}
.margin-top-30 {
	margin-top: 30px;
}
.margin-bottom-30 {
	margin-bottom: 30px;
}
.input-reminder span {
	color: #ff8800;
}
.edit-mobile-input {
	width: 331px !important;
}
.form-group-relative {
	position: relative;
}
.user-card {
	display: none;
	position: absolute;
	left: 15px;
	top: -35px;
	width: 300px;
	height: 35px;
	text-indent: 8px;
	line-height: 35px;
	font-size: 20px;
	color: #ff0000;
	background-color: #fff5c2;
	border: 1px solid #ffbb00;
}

.upload-btn {
	width: 160px;
	height: 120px;
	background-position: 0 0;
}
.upload-btn .user-img {
	position: relative;
	display: block;
	height: 100%;
	overflow: hidden;
}
.demonstrate-img-box {
	padding-left: 40px;
}
.demonstrate-img-box p {
	font-size: 12px;
	color: #00aaff;
}
.demonstrate-img {
	width: 120px;
	height: 80px;
	background-position: -170px 0;
}
.demonstrate-img2 {
	background-position: -170px -90px;
}
.demonstrate-img-reminder {
	clear: left;
	padding: 20px 0 8px;
	padding-left: 1em;
}
.demonstrate-img-reminder li {
	list-style: disc;
	font-size: 12px;
	color: #999;
}
.no-img .user-img,
.no-img .upload-reminder {
	display: none;
}
.user-img img {
	min-height: 100%;
}

.repeat-submit {
	cursor: pointer;
}
.auth-links {
	line-height: 1;
	margin-right: 20px;
	padding: 10px 25px;
	font-size: 14px;
	color: #fff;
	border-radius: 4px;
	border: none;
	outline: none;
}
.auth-links.go-next {
	background-color: #ff8800;
}
.auth-links.go-prev,
.auth-links.canel {
	padding: 0;
	color: #00aaff;
	background-color: #fff;
}
.auth-links.go-prev:hover,
.auth-links.canel:hover {
	text-decoration: none;
}
.auth-links.disabled-btn {
	background-color: #ccc;
}
.alert-warn-lg {
	background-position: 20px 95px;
}
.tip-content p a {
	color: #00aaff;
}

.check-user-info {
	width: 18px;
	height: 18px;
	margin: 0 10px 0 0 !important;
	vertical-align: middle;
}
.auth-num {
	color: #ff8800;
}
.auth-complete {
	padding-top: 30px;
	padding-bottom: 40px;
	text-align: center;
}
.auth-complete h4 {
	height: 40px;
	line-height: 40px;
	font-weight: 400;
	font-size: 18px;
	color: #4c4c4c;
}
.auth-complete h4 i {
	display: inline-block;
	width: 40px;
	height: 40px;
	margin-right: 20px;
	vertical-align: middle;
}
.complete-success i {
	background: url(img/icon_auth_success.png) no-repeat center;
}
.complete-error i {
	background: url(img/icon_auth_fail.png) no-repeat center;
}
.auth-jump {
	margin-top: 10px;
	font-size: 14px;
	color: #999;
}
.fail-desc {
	width: 360px;
	margin: 20px auto;
	padding-left: 40px;
	text-align: left;
	font-size: 12px;
}
.fail-desc p {
	padding-top: 20px;
	color: #ff8800;
	margin-bottom: 8px;
	border-top: 1px solid #eee;
}
.fail-desc ul {
	padding-left: 1em;
}
.fail-desc ul li {
	line-height: 2;
	list-style: disc;
	color: #999;
}
.error-reminder span {
	display: none;
}
.ny-warn,
.auth-post-container .alert-warn {
	white-space:initial;
}
.auth-post-container .alert-warn span,
.ny-warn span {
	color: #00aaff;
}

.auth-post-container table {
	width: 100%;
	margin-top: 20px;
}
.auth-post-container table tr {
	height: 40px;
	line-height: 40px;
}
.auth-post-container table tr th,
.auth-post-container table tr td {
	padding-left: 20px;
	border: 1px solid #ddd;
	font-size: 12px;
	color: #333;
}
.auth-post-container table tr th {
	width: 160px;
	padding-right: 20px;
	background-color: #f5f9fa;
	color: #666;
	font-weight: 400;
	text-align: right;
}
.auth-post-container .download {
	display: block;
	width: 200px;
	height: 32px;
	line-height: 32px;
	margin-top: 30px;
	margin-left: 160px;
	font-size: 14px;
	color: #fff;
	background-color: #00aaff;
	outline: none;
	border-radius: 4px;
	text-align: center;
	border: 1px solid #00aaff;
}
.auth-post-container .download:hover {
	text-decoration: none;
}
.auth-complete-info {
	border: 1px solid #eee;
	padding-bottom: 50px;
}
.auth-success-title {
	margin-bottom: 30px;
	font-weight: bold;
	font-size: 22px;
	color: #262829;
}
.auth-complete-info > div {
	float: left;
	width: 30%;
	padding-top: 90px;
}
.auth-complete-info .left-img {
	text-align: right;
}
.auth-complete-info .right-info {
	padding-left: 60px;
	padding-top: 40px;
	width: auto;
	font-size: 12px;
	color: #666;
}
.auth-complete-info .right-info p {
	margin-bottom: 10px;
}
.auth-complete-info .right-info p:last-child {
	margin-top: 35px;
}
.auth-complete-info .left-img i {
	position: relative;
	display: inline-block;
	width: 129px;
	height: 120px;
	background: url(img/auth_icon_success.png) no-repeat right center;
}
.auth-last-upload {
	display: block;
	width: 120px;
	height: 34px;
	line-height: 30px;
	margin: 40px auto;
	text-align: center;
	font-size: 14px;
	color: #fff;
	background-color: #00aaff;
	outline: none;
	border: 1px solid #00aaff;
	border-radius: 4px;
}
.auth-success-level {
	display: inline-block;
	vertical-align: middle;
	background: no-repeat center;
	width: 30px;
	height: 18px;
}
.auth-success-level.v1 {
	background-image: url(img/auth_level_1.png);
}
.auth-success-level.v2 {
	background-image: url(img/auth_level_2.png);
}
