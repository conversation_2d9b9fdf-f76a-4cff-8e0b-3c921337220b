﻿using System.ComponentModel;

using DH.Services.Jobs;

using NewLife.Log;

namespace HlktechSite.Jobs;

/// <summary>
/// 定期清理Lucene索引
/// </summary>
public class LuceneJobArgument {

}

/// <summary>定期清理Lucene索引服务</summary>
[DisplayName("定期清理Lucene索引")]
[Description("定期清理Lucene索引")]
[CronJob("LuceneJob", "0 0 0 ? * 1 *", Enable = true)]
public class LuceneJobService : CubeJobBase<LuceneJobArgument> {
    private readonly ITracer _tracer;

    /// <summary>实例化定期清理Lucene索引服务</summary>
    /// <param name="tracer"></param>
    public LuceneJobService(ITracer tracer) => _tracer = tracer;

    /// <summary>执行作业</summary>
    /// <param name="argument"></param>
    /// <returns></returns>
    protected override Task<String> OnExecute(LuceneJobArgument argument)
    {
        using var span = _tracer?.NewSpan("LuceneJob", argument);

        XTrace.WriteLine($"LuceneJob执行");
        LuceneHelper.CreateLuceneIndexs();

        return Task.FromResult("OK");
    }
}