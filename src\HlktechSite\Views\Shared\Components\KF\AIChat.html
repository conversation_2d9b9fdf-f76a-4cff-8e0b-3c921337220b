<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta HTTP-EQUIV="pragma" CONTENT="no-cache">
  <meta HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <link rel="stylesheet" href="/layui/css/layui.css?v=EpyP--KqayhsmZVOaCF-RpnEQPmzYlp1gbQCsNBaO9o" media="all">
  <script src="/layui/layui.js?v=r1qJZ34MNTRUy5mUwMc7qzw1S3OV1ZNO21vnLjiUd3k"></script>
  <link href="/css/chuangchu.min.css?v=ze2zk2INDcbHL8AZRkMdaV1nkrHClRzsZb1jwDMzT20" rel="stylesheet">
  <link rel="shortcut icon" href="/assets/images/openai.png" type="image/x-icon">
  <title>AI客服</title>
  <script src="/js/showdown/showdown.min.js?v=rrT6tO5aWC-1Fg79SMJ91Hy-zy9b8q_TfwEh0q7IyaM"></script>
  <script src="/js/clipboard.min.js?v=IXCNttf44gOHGD1zWGSAZdxF99Y1Nw_tsk31kfaPHms"></script>
  <link rel="stylesheet" href="/AIKeFu.styles.css?v=RyCb3UspJhAStPNuVbqr_DaXfiJSsWc6y6HCXAjNT0c" />
  <!-- 引入 markdown-it -->
  <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
  <!-- 引入代码高亮 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/styles/github.min.css">
  <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/highlight.min.js"></script>
  <style>
    .chat-list img {
      width: 100%;
    }

    .chat-list video {
      width: 100%;
    }

    .blob {
      white-space: normal;
    }

    .blob-ai h3 {
      letter-spacing: 1px;
      line-height: 1.3;
      margin: 10px 0 10px 0;
    }

    .blob-ai a[href^="http"]::after {
      content: "↗";
      margin-left: 2px;
    }


    .blob-ai a[href^="http"] {
      color: #0066cc;
    }

    .blob-ai th,
    .blob-ai td {
      border: 1px solid #ddd;
      padding: 8px;
    }

    /* 修正列表缩进 */
    .blob-ai ul,
    .blob-ai ol {
      padding-left: 2em !important;
    }
  </style>
  <script>
    pageRnd = 7363834821339045888;
  </script>
</head>

<body>
  <div id="app">
    <div class="app-container">
      <div class="home-container">
        <div class="chat-container container">
          <div id="chatlist" class="chat-list" style="scroll-behavior: unset;">
            <div class="wrapper">
              <div class="avatar"><img class="img" src="/assets/images/openai.png"></div>
              <div class="msg-box">
                <div class="blob blob-ai">你好，我是海凌科AI客服，有什么问题可以先联系我哦！</div><span
                  class="iconfont icon-fuzhi copy-btn"></span>
              </div>
            </div>
          </div>

          <div class="input-container"><input class="input" type="text" id="mes" placeholder="你想和我聊点什么?">
            <div class="send disable"><img
                src="data:image/png;base64,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">
            </div>
          </div>
        </div>
        <div class="tabbar-container"><a aria-current="page" href="#/home/<USER>"
            class="active router-link-exact-active link">
            <figure><img
                src="data:image/png;base64,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">
              <figcaption>聊天</figcaption>
            </figure>
          </a></div>
      </div>
    </div>
  </div>

  <script>
    // 初始化 markdown-it
    const md = window.markdownit({
      html: true,
      linkify: true,
      typographer: true,
      highlight: function (str, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(str, { language: lang }).value;
          } catch (__) { }
        }
        return ''; // 使用默认高亮
      }
    });

    var isstart = true;
    var SId = '6HeBtGwucwF9OaJY';
    var rndId;


    var contentArray = [];
    var currentIndex = 0;
    var charIndex = 0;
    var isTyping = false;

    layui.use(['layer', 'jquery'], function () {
      var layer = layui.layer
        , $ = layui.jquery;

      // 初始化 ClipboardJS
      var copyBtn = new ClipboardJS('.copy-btn');

      copyBtn.on("success", function (e) {
        // 复制成功
        // alert(e.text);
        //showMessage('已成功复制邀请码', 'success', 2000)
        layer.msg('复制成功');
        e.clearSelection();
      });
      copyBtn.on("error", function (e) {
        //复制失败；
        //showMessage('复制邀请码失败', 'error', 2000)
        layer.msg('复制失败');
        console.log(e.action)
      });

      function establishSSEConnection() {
        var eventSource = new EventSource("/sse-notifications");

        eventSource.onopen = function (event) {
          console.log("SSE connection established.", event);
        };

        eventSource.onmessage = function (event) {
          // console.log("Received message:", event.data);
          const data = JSON.parse(event.data)
          console.log('onmessage', data);
          if (data.session_id) {
            SessionId = data.session_id
          }
          if (data.SId != SId) {
            return;
          }

          if (data.PageRnd != pageRnd) {
            return;
          }

          showNotification(data);
        };

        eventSource.onerror = function (event) {
          console.error("SSE error:", event);
          // Handle error, e.g., reconnect here
          setTimeout(establishSSEConnection, 2000); // Reconnect after 2 seconds
        };

        eventSource.addEventListener('alert', function (event) {
          alert(event.data);
        });
      }

      establishSSEConnection();


      var showNotification = function (data) {
        html = data.result;
        $("#" + rndId).attr("data-Aid", data.AId);
        addNewContent(html);

      };

      function typeContent() {
        isTyping = true;

        if (charIndex < contentArray[currentIndex].length) {
          var charToAdd = contentArray[currentIndex].charAt(charIndex);
          if (charToAdd === '<') {
            while (contentArray[currentIndex].substr(charIndex, 4) !== '</em>' &&
              charIndex < contentArray[currentIndex].length) {
              charToAdd += contentArray[currentIndex].charAt(charIndex + 1);
              charIndex++;
            }
            charToAdd += contentArray[currentIndex].charAt(charIndex + 1); // add the closing '>'
            charIndex++;
          }
          $("#" + rndId).append(charToAdd);
          charIndex++;
          setTimeout(typeContent, 100);
        } else {
          currentIndex++;
          if (currentIndex < contentArray.length) {
            charIndex = 0;
            setTimeout(typeContent, 500);
          } else {
            isTyping = false;
          }
        }
      }

      function addNewContent(newContent) {
        contentArray.push(newContent);
        $("#" + rndId).append(newContent);

        // 渲染 Markdown
        document.getElementById(`${rndId}`).innerHTML = md.render(contentArray.join(''));

        // 高亮代码
        document.querySelectorAll('pre code').forEach((block) => {
          hljs.highlightElement(block);
        });

      }

      // 生成一个随机字符，可以是大写字母、小写字母或数字
      function getRandomCharacter() {
        var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        var randomIndex = Math.floor(Math.random() * characters.length);
        return characters.charAt(randomIndex);
      }

      // 生成一个指定长度的随机字符串，包含字母和数字
      function generateRandomString(length) {
        var result = '';
        for (var i = 0; i < length; i++) {
          result += getRandomCharacter();
        }
        return result;
      }

      $("#mes").on("input propertychange", function () {
        if ($("#mes").length == 0) {
          $(".send").addClass("disable");
        } else {
          $(".send").removeClass("disable");
        }
      })

      $(".chat-list").on("click", ".icon-thumbs-up", function () {
        var Aid = $("#" + rndId).attr("data-Aid");
        $.ajax({
          type: "POST",
          url: "/api/V1/Robot/Status",
          contentType: "application/x-www-form-urlencoded",
          data: { Aid: Aid, Type: 1 },
          success: function (res) {
            var code = res.Code;
            if (code == 1) {
              layer.msg('感谢您的支持');
            }
            else if (code == 401) {
              layer.msg(res.Message);
            }
          }
        })
      });

      $(".chat-list").on("click", ".icon-thumbs-down", function () {
        var Aid = $("#" + rndId).attr("data-Aid");
        $.ajax({
          type: "POST",
          url: "/api/V1/Robot/Status",
          contentType: "application/x-www-form-urlencoded",
          data: { Aid: Aid, Type: 2 },
          success: function (res) {
            var code = res.Code;
            if (code == 1) {
              layer.msg('感谢您的支持');
            }
            else if (code == 401) {
              layer.msg(res.Message);
            }
          }
        })
      });


      var avatar = "";
      var SessionId = ''
      $(".send").click(function () {
        var mes = $("#mes").val();
        if (mes.length == 0) return;

        contentArray = [];
        currentIndex = 0;
        charIndex = 0;
        isTyping = false;

        rndId = generateRandomString(8); // 生成长度为8的随机字符串
        $(".chat-list").append("<div class=\"wrapper wrapper-user\"><div class=\" blob blob-user\">" + mes + "</div><div class=\"avatar avatar-user\">" + avatar + "</div></div><div class=\"wrapper\"><div class=\"avatar\"><img class=\"img\" src=\"/assets/images/openai.png\"></div><div class=\"msg-box\"><div class=\"blob blob-ai\"><div id=\"" + rndId + "\"  style=\"margin-bottom: 10px;\"></div><span class=\"dhfont icon-thumbs-up\" style=\"font-size: 18px;\"></span><span class=\"dhfont icon-thumbs-down\" style=\"margin-left: 10px; font-size: 18px;\"></span></div><span class=\"iconfont icon-fuzhi copy-btn\" data-clipboard-target=\"#" + rndId + "\"></span></div></div>");

        var message = document.getElementById('chatlist');
        message.scrollTop = message.scrollHeight;

        $("#mes").val("");
        $(".send").addClass("disable");

        $.ajax({
          type: "POST",
          url: "/api/V1/Robot/Call2",
          contentType: "application/json", //必须这样写
          dataType: "json",
          data: JSON.stringify({ Message: mes, SId: SId, PageRnd: pageRnd, SessionId }),
          success: function (res) {
            var code = res.Code;
            if (code == 1) {
              //typeContent();
              //rndId = generateRandomString(8); // 生成长度为8的随机字符串
            }
            else if (code == 401) {
              layer.msg(res.Message);
            }
          }
        })
      });
    });

    // 添加 postMessage 事件监听器
    window.addEventListener('message', function (event) {
      // 验证消息来源 - 支持多个域名
      const allowedOrigins = ['https://ask.hlktech.com', 'https://kf.hlktech.com'];
      if (!allowedOrigins.includes(event.origin)) {
        return;
      }

      console.log('收到父页面消息:', event.data);

      // 处理填充输入框的消息
      if (event.data.type === 'fillChatInput') {
        var input = document.getElementById('mes');
        if (input) {
          input.value = event.data.title + '\n' + event.data.content;
          console.log('已填充聊天输入框');

          // 触发输入事件以激活相关功能
          var inputEvent = new Event('input', { bubbles: true });
          input.dispatchEvent(inputEvent);

          // 通知父页面我们已处理消息
          event.source.postMessage({
            type: 'inputFilled',
            success: true
          }, event.origin);
        } else {
          console.error('未找到输入框元素');
        }
      }

      // 处理设置回车监听器的消息
      if (event.data.type === 'SETUP_ENTER_LISTENER') {
        console.log('收到设置回车监听器请求');
        setupEnterKeyListenerInIframe();

        // 通知父页面设置完成
        event.source.postMessage({
          type: 'ENTER_LISTENER_SETUP_COMPLETE',
          success: true
        }, event.origin);
      }
    }, false);

    // 在iframe内部设置回车监听器
    function setupEnterKeyListenerInIframe() {
      console.log('🔧 在iframe内部设置回车监听器');

      const messageInput = document.getElementById('mes');
      const sendButton = document.querySelector('.send');

      console.log('🔍 查找元素结果:', {
        messageInput: messageInput ? '✅找到' : '❌未找到',
        sendButton: sendButton ? '✅找到' : '❌未找到',
        inputId: messageInput ? messageInput.id : 'null',
        sendClass: sendButton ? sendButton.className : 'null'
      });

      if (!messageInput || !sendButton) {
        console.error('❌ 未找到必要的元素:', { messageInput, sendButton });

        // 尝试查找所有可能的元素
        console.log('🔍 尝试查找所有input元素:', document.querySelectorAll('input'));
        console.log('🔍 尝试查找所有.send元素:', document.querySelectorAll('.send'));
        return;
      }

      // 移除可能存在的旧监听器
      messageInput.removeEventListener('keydown', handleEnterKey);

      // 添加新的监听器
      messageInput.addEventListener('keydown', handleEnterKey);

      // 测试监听器是否工作
      console.log('🧪 测试键盘监听器...');
      messageInput.addEventListener('keydown', function (e) {
        console.log('🎹 检测到按键:', e.key, '在输入框:', e.target.id);
      });

      console.log('✅ iframe内部回车监听器设置完成');
    }

    // 处理回车键事件的函数
    function handleEnterKey(event) {
      console.log('🎯 handleEnterKey被调用, 按键:', event.key, 'shiftKey:', event.shiftKey);

      if (event.key === 'Enter' && !event.shiftKey) {
        console.log('✅ 检测到回车键（非Shift+Enter）');
        event.preventDefault();

        const message = event.target.value.trim();
        console.log('📝 输入框内容:', `"${message}"`, '长度:', message.length);

        if (message.length > 0) {
          console.log('🚀 iframe内检测到回车发送:', message);

          // 触发发送按钮点击
          const sendButton = document.querySelector('.send');
          console.log('🔍 发送按钮状态:', {
            found: sendButton ? '✅找到' : '❌未找到',
            disabled: sendButton ? sendButton.classList.contains('disable') : 'N/A',
            className: sendButton ? sendButton.className : 'null'
          });

          if (sendButton && !sendButton.classList.contains('disable')) {
            console.log('🎯 准备点击发送按钮...');
            sendButton.click();
            console.log('✅ iframe内已触发发送按钮点击');

            // 通知父页面
            if (window.parent !== window) {
              console.log('📤 通知父页面回车事件');
              window.parent.postMessage({
                type: 'ENTER_KEY_PRESSED',
                message: message
              }, '*');
            }
          } else {
            console.log('❌ 发送按钮不可用或未找到');
          }
        } else {
          console.log('⚠️ 输入框为空，不发送消息');
        }
      } else {
        console.log('⏭️ 非回车键或Shift+回车，忽略');
      }
    }

    // 页面加载完成后通知父窗口并设置回车监听器
    window.onload = function () {
      console.log('AIChat页面加载完成');

      // 自动设置回车监听器
      setTimeout(function () {
        setupEnterKeyListenerInIframe();
      }, 1000);

      // 通知父窗口页面已加载
      if (window.parent !== window) {
        try {
          window.parent.postMessage({
            type: 'iframeLoaded'
          }, '*');
        } catch (e) {
          console.error('通信失败:', e);
        }
      }
    };
  </script>
</body>

</html>