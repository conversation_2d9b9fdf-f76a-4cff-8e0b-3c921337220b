﻿@inject IWorkContext workContext
@{
    var language = workContext.WorkingLanguage;

    var addDefaultTitle = true;

    if (ViewBag.AddDefaultTitle != null)
    {
        addDefaultTitle = ViewBag.AddDefaultTitle.SafeString().ToBoolean();
    }
}

<!DOCTYPE html>
<html lang="@language.LanguageCulture" @(this.ShouldUseRtlTheme() ? Html.Raw(" dir=\"rtl\"") : null) @Html.DGPageCssClasses()>
<head>
    <title>@Html.DGTitle(addDefaultTitle)</title>
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8" />
    <meta name="description" content="@(Html.DGMetaDescription())" />
    <meta name="keywords" content="@(Html.DGMetaKeywords())" />
    <meta name="generator" content="gicisky" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    @Html.DGHeadCustom()
    @*这是为了使主题可以将内容注入标头*@
    @await Html.PartialAsync("Head")
    @Html.DGCssFiles(ResourceLocation.Head)
    @Html.DGScripts(ResourceLocation.Head)
    @Html.DGCanonicalUrls()
    @*Insert favicon and app icons head code*@
    @await Component.InvokeAsync("NewsRssHeaderLink")
    @await Component.InvokeAsync("Favicon")
    @Html.DGInlineCss(ResourceLocation.Head)
    @Html.DGInlineScripts(ResourceLocation.Head)
    <!--Powered by Gicisky - https://www.gicisky.net-->
</head>
<body class="@Html.DGBodyCssClasses("", false)">
    @RenderBody()
    @Html.DGCssFiles(ResourceLocation.Footer)
    @Html.DGScripts(ResourceLocation.Footer)
    @Html.DGInlineScripts(ResourceLocation.Footer)
</body>
</html>