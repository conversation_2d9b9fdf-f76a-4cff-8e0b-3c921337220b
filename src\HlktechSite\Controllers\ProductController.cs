﻿using DG.Cube;
using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Entity;
using DH.Helpers;

using HlktechSite.DTO;
using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Models;
using Pek.Seo;

using System.Dynamic;
using System.Text;

using XCode;

namespace HlktechSite.Controllers;

/// <summary>
/// 产品控制器
/// </summary>
[DHSitemap(IsUse = true)]
public class ProductController : DGBaseControllerX
{
    public override IEnumerable<DHSitemap> CreateSiteMap()
    {
        var list = new List<DHSitemap>();

        list.Add(new DHSitemap
        {
            SType = SiteMap.栏目首页,
            ActionName = "Index",
            ControllerName = "Product"
        });

        var ProductCategoryList = ProductCategory.GetAll();
        foreach (var row in ProductCategoryList)
        {
            list.Add(new DHSitemap
            {
                SType = SiteMap.栏目页,
                ActionName = "List",
                ControllerName = "Product",
                Data = new Dictionary<String, Object> { { "CId", row.Id } }
            });
        }

        var GoodList = Goods.GetAll();
        foreach (var row in GoodList)
        {
            list.Add(new DHSitemap
            {
                SType = SiteMap.内容页,
                ActionName = "Details",
                ControllerName = "Product",
                Data = new Dictionary<String, Object> { { "Id", row.Id } }
            });
        }

        var langlist = Language.FindByStatus();
        var defaultlang = Language.FindByDefault();

        foreach (var item in langlist)
        {
            if (item.Id == defaultlang.Id) continue;
            list.Add(new DHSitemap
            {
                SType = SiteMap.栏目首页,
                ActionName = "Index",
                ControllerName = "Product",
                UniqueSeoCode = item.UniqueSeoCode
            });

            foreach (var row in ProductCategoryList)
            {
                list.Add(new DHSitemap
                {
                    SType = SiteMap.栏目页,
                    ActionName = "List",
                    ControllerName = "Product",
                    UniqueSeoCode = item.UniqueSeoCode,
                    Data = new Dictionary<String, Object> { { "CId", row.Id } }
                });
            }

            foreach (var row in GoodList)
            {
                list.Add(new DHSitemap
                {
                    SType = SiteMap.内容页,
                    ActionName = "Details",
                    ControllerName = "Product",
                    UniqueSeoCode = item.UniqueSeoCode,
                    Data = new Dictionary<String, Object> { { "Id", row.Id } }
                });
            }

        }

        return list;
    }

    /// <summary>
    /// 产品中心
    /// </summary>
    /// <param name="CId">产品类型Id</param>
    /// <param name="Key">模糊查询关键字</param>
    /// <param name="p">当前页</param>
    /// <returns></returns>
    public IActionResult Index(Int32 CId, String Key, Int32 p = 1)
    {
        Key = Key.SafeString().Trim();

        dynamic viewModel = new ExpandoObject();
        var model = ProductCategory.FindById(CId)??new ProductCategory();
        //所有的商品分类
        var ProTypelist = ProductCategory.FindAllByLevel(0);

        var IsChild = model.ParentId != 0;

        var localizationSettings = LocalizationSettings.Current;

        //一级产品类型
        viewModel.ProTypelist = ProTypelist.Select(x => new ProductCategory
        {
            Id = x.Id,
            Name = localizationSettings.IsEnable ? ProductCategoryLan.FindByCIdAndLId(x.Id, WorkingLanguage.Id)?.Name : x.Name
        });

        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 12,
            RetrieveTotalCount = true,
            OrderBy = "Sort desc,CreateTime desc",
        };

        //商品列表(分页)
        var ProList = localizationSettings.IsEnable ?
                GoodsLan.SearchByShelf(Key, pages, WorkingLanguage.Id).Select(x => new Goods
                {
                    Id = x.GId,
                    AdvWord = x.AdvWord.IsNullOrWhiteSpace() ? x.Goods?.AdvWord : x.AdvWord,
                    Content = x.Content.IsNullOrWhiteSpace() ? x.Goods?.Content : x.Content,
                    MobileContent = x.MobileContent.IsNullOrWhiteSpace() ? x.Goods?.MobileContent : x.MobileContent,
                    Name = x.Name.IsNullOrWhiteSpace() ? x.Goods?.Name : x.Name,
                    Image = UrlHelper.Combine(CDN.GetCDN(), (x.Image.IsNullOrWhiteSpace() ? x.Goods?.Image : x.Image).Replace("\\", "/"))
                }) :
                Goods.SearchByShelf(Key, pages).Select(x => new Goods
                {
                    Id = x.Id,
                    AdvWord = x.AdvWord,
                    Content = x.Content,
                    MobileContent = x.MobileContent,
                    Name = x.Name,
                    Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(CDN.GetCDN(), x.Image.Replace("\\", "/")) : ""
                });
   
        viewModel.list = ProList;
        viewModel.page = p;
        viewModel.Key = Key.IsNullOrEmpty() ? "" : Key;
        //如果是二级菜单  则显示父级的选中效果
        if (IsChild)
        {
            viewModel.ChildId = CId;
            var parent = ProductCategory.FindById(CId);
            viewModel.CId = parent == null ? 0 : parent.ParentId;
        }
        else
        {
            viewModel.ChildId = CId;
            viewModel.CId = CId;
        }
        viewModel.Model = model;
        //viewModel.Str = PageHelper.CreatePage(p, pages.PageCount, Url.Action("Index", "Product"), new Dictionary<String, String> { { "CId", CId.ToString() } });

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());

        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "Index", "Product", "", dic, "p");
        return DGView(viewModel, true);
    }

    public IActionResult List(Int32 CId, String Key, Int32 p = 1)
    {
        var model = ProductCategory.FindById(CId);
        if (model == null)
        {
            return View404();
        }

        Key = Key.SafeString().Trim();

        dynamic viewModel = new ExpandoObject();

        var localizationSettings = LocalizationSettings.Current;

        //所有的商品分类
        //var ProTypelist = new List<ProductCategory>();
        var ProTypelist = ProductCategory.FindAllByLevel(0).Select(x => new ProductCategory
        {
            Id = x.Id,
            Name = localizationSettings.IsEnable ? ProductCategoryLan.FindByCIdAndLId(x.Id, WorkingLanguage.Id)?.Name : x.Name
        });

        //一级产品类型
        viewModel.ProTypelist = ProTypelist;

        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 12,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime",
            Desc = true
        };

        //商品列表(分页)
        IEnumerable<Goods> ProList;

        var IsChild = model.ParentId != 0;
        IEnumerable<ProductCategory> SecondList;
        //查询是否有二级菜单
        SecondList = ProductCategory.FindAllByParentId(CId);

        //如果存在子级  则查询自己和子集  否则就查询自己
        if (SecondList.Any())
        {
            StringBuilder CIds = new StringBuilder();
            CIds.Append(CId);
            foreach (var item in SecondList)
            {
                CIds.Append("," + item.Id.ToString());
            }

            ProList = localizationSettings.IsEnable ?
                GoodsLan.SearchByShelf(Key, CIds.ToString(), pages, WorkingLanguage.Id).Select(x => new Goods
                {
                    Id = x.GId,
                    AdvWord = x.AdvWord.IsNullOrWhiteSpace() ? x.Goods?.AdvWord : x.AdvWord,
                    Content = x.Content.IsNullOrWhiteSpace() ? x.Goods?.Content : x.Content,
                    Name = x.Name.IsNullOrWhiteSpace() ? x.Goods?.Name : x.Name,
                    Image = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", (x.Image.IsNullOrWhiteSpace() ? x.Goods?.Image : x.Image).Replace("\\", "/"))
                }) :
                Goods.SearchByCIds(Key, CIds.ToString(), pages).Select(x => new Goods
                {
                    Id = x.Id,
                    AdvWord = x.AdvWord,
                    Content = x.Content,
                    MobileContent = x.MobileContent,
                    Name = x.Name,
                    Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : ""
                });
        }
        else
        {
            ProList = localizationSettings.IsEnable ?
                GoodsLan.SearchByShelf(Key, CId.ToInt(), pages, WorkingLanguage.Id).Select(x => new Goods
                {
                    Id = x.GId,
                    AdvWord = x.AdvWord.IsNullOrWhiteSpace() ? x.Goods?.AdvWord : x.AdvWord,
                    Content = x.Content.IsNullOrWhiteSpace() ? x.Goods?.Content : x.Content,
                    Name = x.Name.IsNullOrWhiteSpace() ? x.Goods?.Name : x.Name,
                    Image = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", (x.Image.IsNullOrWhiteSpace() ? x.Goods?.Image : x.Image).Replace("\\", "/"))
                }) :
                Goods.SearchByCId(Key, CId.ToInt(), pages).Select(x => new Goods
                {
                    Id = x.Id,
                    AdvWord = x.AdvWord,
                    Content = x.Content,
                    MobileContent = x.MobileContent,
                    Name = x.Name,
                    Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : ""
                });
        }

        viewModel.list = ProList;
        viewModel.page = p;
        viewModel.Key = Key.IsNullOrEmpty() ? "" : Key;
        //如果是二级菜单  则显示父级的选中效果
        if (IsChild)
        {
            viewModel.ChildId = CId;
            viewModel.CId = ProductCategory.FindById(CId)?.ParentId;
            viewModel.Model = model;
        }
        else
        {
            viewModel.ChildId = CId;
            viewModel.CId = CId;
            viewModel.Model = model;
        }

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
        dic.TryAdd("CId", CId.ToString());

        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "List", "Product", "", dic, "p");
        return DGView(viewModel, "Index", true);
    }

    /// <summary>
    /// 产品中心详情
    /// </summary>
    /// <returns></returns>
    public IActionResult Details(int Id)
    {
        dynamic viewModel = new ExpandoObject();

        var Product = new Goods();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var prolan = GoodsLan.FindByGIdAndLId(Id, WorkingLanguage.Id);
            if (prolan == null)
                return View404();
            if (prolan.Shelf == false)
            {
                return Content(GetResource("产品已下架"));
            }

            Product = new Goods
            {
                Id = prolan.GId,
                Name = prolan.Name.IsNullOrWhiteSpace() ? prolan.Goods?.Name : prolan.Name,
                Summary = prolan.Summary.IsNullOrWhiteSpace() ? prolan.Goods?.Summary : prolan.Summary,
                Content = prolan.Content.IsNullOrWhiteSpace() ? prolan.Goods?.Content : prolan.Content,
                MobileContent = prolan.MobileContent.IsNullOrWhiteSpace() ? prolan.Goods?.MobileContent : prolan.MobileContent,
                UsageScenarios = prolan.UsageScenarios.IsNullOrWhiteSpace() ? prolan.Goods?.UsageScenarios : prolan.UsageScenarios,
                AdvWord = prolan.AdvWord.IsNullOrWhiteSpace() ? prolan.Goods?.AdvWord : prolan.AdvWord,
                Image = prolan.Image.IsNullOrWhiteSpace() ? prolan.Goods?.Image : prolan.Image,
                MId = prolan.Goods.MId,
                CId = prolan.Goods.CId,
                Cid1 = prolan.Goods.Cid1,
                Cid2 = prolan.Goods.Cid2,
                Cid3 = prolan.Goods.Cid3,
                Specifications = prolan.Specifications.IsNullOrWhiteSpace() ? prolan.Goods?.Specifications : prolan.Specifications,
                Clicks = prolan.Goods.Clicks
            };
        }
        else
        {
            var pro = Goods.FindById(Id);
            if (pro == null)
            {
                return View404();
            }
            if (pro.Shelf == false)
                return Content(GetResource("商品已下架"));

            Product = pro;
        }
        Product.Content = Product.Content.SafeString();
        Product.MobileContent = Product.MobileContent.SafeString();
        var proclick = Goods.FindById(Product.Id);
        proclick.Clicks++;
        proclick.UpdateAsync();

        IEnumerable<GoodsImages> ImgList;

        if (localizationSettings.IsEnable)
        {
            ImgList = GoodsImagesLan.FindAllByGIdAndLId(Id, WorkingLanguage.Id).Select(x => new GoodsImages { Url = UrlHelper.Combine(CDN.GetCDN(), x.Url), Sort = x.Sort }).OrderBy(x => x.Sort);
        }
        else
        {
            ImgList = GoodsImages.FindAllByGId(Product.Id).Select(x => new GoodsImages { Url = UrlHelper.Combine(CDN.GetCDN(), x.Url), Sort = x.Sort }).OrderBy(x => x.Sort);
        }

        if (localizationSettings.IsEnable && ImgList == null || ImgList.Count() == 0)
        {
            ImgList = GoodsImages.FindAllByGId(Product.Id).Select(x => new GoodsImages { Url = UrlHelper.Combine(CDN.GetCDN(), x.Url), Sort = x.Sort }).OrderBy(x => x.Sort);
        }
        viewModel.ImgList = ImgList;
        //viewModel.ImgList = localizationSettings.IsEnable ?
        //    GoodsImagesLan.FindAllByGIdAndLId(Id, WorkingLanguage.Id).Select(x => new GoodsImages { Url = CDN.GetCDN(x.Url) }).OrderBy(x => x.Sort)
        //    : GoodsImages.FindAllByGId(Product.Id).Select(x => new GoodsImages { Url = CDN.GetCDN(x.Url) }).OrderBy(x => x.Sort);
        Product.Content = Product.Content.SafeString();
        Product.MobileContent = Product.MobileContent.SafeString();
        viewModel.Product = Product;

        var List1 = new List<Datadownload>();
        var List2 = new List<Datadownload>();
        var List3 = new List<Datadownload>();

        var downlod = new List<Download>();

        var mids = Product.MId.SplitAsInt(",");
        foreach (var item in mids)
        {
            var list = Download.FindInIds(item);
            foreach (var row in list)
            {
                downlod.Add(row);
            }
        }

        foreach (var item in downlod)
        {

            if (item.Development.IsNotNullAndWhiteSpace())
            {
                var Developmentdata = item.Development.Split('♪');
                foreach (var row in Developmentdata)
                {
                    var data = row.Split("|");
                    var model = new Datadownload();
                    model.resource_id = 1;//开发资料是1
                    model.resource_name = data[0];
                    if (data[1].Contains("http://") || data[1].Contains("https://"))
                    {
                        model.resource_url = data[1];
                        model.filesuffix = "";
                    }
                    else
                    {
                        model.resource_url = "/download" + data[1];
                        model.filesuffix = Path.GetExtension(model.resource_url);
                    }

                    List1.Add(model);
                }
            }
            if (item.Application.IsNotNullAndWhiteSpace())
            {
                var Application = item.Application.Split('♪');
                foreach (var row in Application)
                {
                    var data = row.Split("|");
                    var model = new Datadownload();
                    model.resource_id = 2;//应用软件是2
                    model.resource_name = data[0];
                    if (data[1].Contains("http://") || data[1].Contains("https://"))
                    {
                        model.resource_url = data[1];
                        model.filesuffix = "";
                    }
                    else
                    {
                        model.resource_url = "/download" + data[1];
                        model.filesuffix = Path.GetExtension(model.resource_url);
                    }

                    List2.Add(model);
                }
            }
            if (item.GeneralSoftware.IsNotNullAndWhiteSpace())
            {
                var GeneralSoftware = item.GeneralSoftware.Split('♪');
                foreach (var row in GeneralSoftware)
                {
                    var data = row.Split("|");
                    var model = new Datadownload();
                    model.resource_id = 3;//通用软件是3
                    model.resource_name = data[0];
                    if (data[1].Contains("http://") || data[1].Contains("https://"))
                    {
                        model.resource_url = data[1];
                        model.filesuffix = "";
                    }
                    else
                    {
                        model.resource_url = "/download" + data[1];
                        model.filesuffix = Path.GetExtension(model.resource_url);
                    }
                    List3.Add(model);
                }
            }
        }

        //解决方案
        var Lists = new List<Solution>();
        foreach (var item in mids)
        {
            var list = Solution.FindAllByMId(item);
            foreach (var row in list)
            {
                Lists.Add(row);
            }
        }
        viewModel.SolutionList = Lists;

        viewModel.Development = List1;
        viewModel.Application = List2;
        viewModel.GeneralSoftware = List3;

        var knilist = new List<Knowledge>();
        foreach (var item in mids)
        {
            var ilist = Knowledge.FindAllByMId(item);
            foreach (var row in ilist)
            {
                knilist.Add(row);
            }
        }

        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("产品中心"), Url = Url.DGAction("Index") });
        var category = ProductCategory.FindById(Product.CId);
        var Cids = category.ParentIdList.SplitAsInt(",");
        var Names = new List<String>();

        if (localizationSettings.IsEnable)
        {
            foreach (var item in Cids)
            {
                var Name = ProductCategoryLan.FindByCIdAndLId(item, WorkingLanguage.Id)?.Name;
                navigations.Add(new NavigationUrl { Name = Name, Url = Url.DGAction("List", new { CId = item }) });
                Names.Add(Name);
            }
        }
        else
        {
            foreach (var item in Cids)
            {
                var Name = ProductCategory.FindById(item)?.Name;
                navigations.Add(new NavigationUrl { Name = Name, Url = Url.DGAction("List", new { CId = item }) });
                Names.Add(Name);
            }
        }

        ViewBag.Titles = Names.Join(DG.Setting.Current.PageTitleSeparator);
        navigations.Add(new NavigationUrl { Name = Product.Name, IsLast = true });
        viewModel.Locations = navigations;
        viewModel.knowledgeList = knilist.OrderByDescending(x => x.Clicks);

        var listShopUrl = ProductModel.FindByIds(mids);
        var ShopUrl = new List<String>();
        var TaoBaoUrl1 = new List<String>();
        if (listShopUrl.Any())
        {
            if (!HttpContext.Request.IsMobileBrowser())
            {
                foreach (var item in listShopUrl)
                {
                    if (!item.PcShopUrl.IsNullOrWhiteSpace())
                    {
                        ShopUrl.Add(item.PcShopUrl);
                    }
                    if (!item.TaobaoShopUrl1.IsNullOrWhiteSpace())
                    {
                        TaoBaoUrl1.Add(item.TaobaoShopUrl1);
                    }
                }
            }
            else
            {
                foreach (var item in listShopUrl)
                {
                    if (!item.MobileShopUrl.IsNullOrWhiteSpace())
                    {
                        ShopUrl.Add(item.MobileShopUrl);
                    }
                    if (!item.TaobaoShopUrl1.IsNullOrWhiteSpace())
                    {
                        TaoBaoUrl1.Add(item.TaobaoShopUrl1);
                    }
                }
            }
        }

        viewModel.ShopUrl = ShopUrl;
        viewModel.TaoBaoUrl1 = TaoBaoUrl1;

        return DGView(viewModel, true);
    }

    /// <summary>
    /// 获取根据产品分类Id获取下面的二级菜单
    /// </summary>
    /// <param name="CID"></param>
    /// <returns></returns>
    public IActionResult GetSecond(int CID)
    {
        var res = new DResult();
        if (CID == 0) return Json(res);

        var localizationSettings = LocalizationSettings.Current;

        res.data = ProductCategory.FindAllByParentId(CID).Select(x => new
        {
            x.Id,
            Name = localizationSettings.IsEnable ? ProductCategoryLan.FindByCIdAndLId(x.Id, WorkingLanguage.Id)?.Name : x.Name
        });
        res.success = true;
        return Json(res);
    }
}
