﻿/*搜索下拉的样式*/
.title1 {
    color: initial !important;
    background-color: initial;
}
.ApplyCon ul li a {
    text-decoration: none !important;
    color: #0B0A0A;
}
.ApplyCon ul li a:hover{
    color:#0B0A0A;
}
.carousel-inner button {
    border: none;
    height: 70px;
    border-radius: 35px;
}

.open > .dropdown-toggle.btn-default:hover, .open > .dropdown-toggle.btn-default:focus, .btn-default:active:focus, .btn-default:active:hover {
    background-color: #fff;
    box-shadow: none;
}

.container .btn-group {
    left: 0;
    top: 70px;
    z-index: 1;
    border-radius: 35px;
}

.search-wrapper {
    top: 20%;
}

.carousel-inner button:hover {
    background-color: #fff;
}

.carousel-inner input {
    margin-left: 14%;
}

.input-holder {
    background-color: #fff;
}

li{
    list-style:none;
}

.ApplyCon {
    border-radius: 25px;
    padding-top: 91px;
    max-width: 1363px;
    padding-left: 116px;
    padding-right: 116px;
    padding-bottom: 73px;
    margin: 0 auto;
    background-color: #fff;
}

    .ApplyCon .datum-title .col-md-5 {
        font-weight: normal;
        font-size: 30px;
        text-align: center;
        padding-top: 129px;
        color: #0068AA;
        height: 369px;
    }


    .ApplyCon .datum-title img {
        width: 100%;
        padding-bottom: 84px;
        max-width: 626px;
        margin: auto;
        display: block;
    }

    .ApplyCon ul {
        padding: 0px;
        display: flex;
        margin: 0 auto;
        max-width: 1500px;
    }

        .ApplyCon ul li {
            flex: 1;
            font-size: 16px;
            color: #0B0A0A;
            text-align: center;
            height: 28px;
            cursor: pointer;
        }

.selected {
    position: relative;
    color: #1E88E5 !important;
}

    .selected:after {
        content: "";
        position: absolute;
        left: 0px;
        right: 0px;
        bottom: 0px;
        height: 3px;
        background-color: #1E88E5;
    }

.fillIn > .title {
    font-size: 16px;
    color: #0B0A0A;
    text-align: center;
    padding-top: 66px;
    padding-bottom: 52px;
}

.fillIn p {
    text-align: left;
    margin-top: 21px;
}

.fillIn i {
    color: #FF0052;
}
/*.sub-input{
    padding-top:20px;
}*/
.fillIn .input-group {
    width: 100%;
}

    .fillIn .input-group input {
        border-radius: 7px !important;
    }

.fillIn > p:not(:first-child) {
    margin-top: 41px;
}

.fillIn textarea {
    margin-top: 12px;
    resize: none;
    height: 188px;
}

.fillIn > .input-group > input {
    max-width: 315px;
}

.fillIn img {
    width: 90px;
    padding: 0px;
    height: 36px;
    margin-left: 15px;
}

.fillIn a {
    float: right;
    display: inline-block;
    width: 146px;
    height: 43px;
    line-height: 43px;
    text-align: center;
    border-radius: 21.5px;
    color: #fff;
    font-size: 20px;
    background-color: #3A75FF;
    text-decoration:none !important;
    margin-bottom: 117px;
}

.search-input {
    width: 54% !important;
    padding-right: 0px !important;
}

.dropdown-menu a {
    padding-left: 10% !important;
}

.dropdown-toggle {
    background-color: #fff !important;
}
.fillIn input:disabled{
    background-color:#ccc;
}
.fillIn select {
    width: 100%;
    height: 31px;
    border-radius: 5px;
    border-color: #CCC;
    padding-left: 10px;
}
.fillIn .title {
    font-size: 16px;
    color: #0B0A0A;
    text-align: center;
    padding-top: 0px;
    padding-bottom: 0px;
}
#apply2{
   display:none;
}
@media screen and (max-width:750px) {
    .ApplyCon {
        padding-left: 20px;
        padding-right: 20px;
    }

    .search-input {
        width: 33% !important;
        padding-right: 0px !important;
    }

    .fillIn .sub-input a {
        display: block;
        margin: 0 auto;
        margin-top: 20px;
        margin-bottom: 0;
        float:none;
    }

    .ApplyCon ul li {
        font-size: 11px;
    }
    input[name=verification] {
        width: calc(100% - 120px) !important;
        max-width: initial !important;
    }
    .fillIn .title {
        font-size: 16px;
        color: #0B0A0A;
        text-align: center;
        padding-top: 0px;
        padding-bottom: 0px;
    }
    .fillIn form {
        margin-top: 20px;
    }
}

@media screen and (max-width: 560px) {
    .carousel-inner button {
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 35px;
        font-size: 12px;
    }
    .ApplyCon .datum-title .col-md-5 {
        font-weight: normal;
        font-size: 30px;
        text-align: center;
        padding-top: 0px;
        color: #0068AA;
        height: 115px;
    }
    .container .btn-group {
        left: 35px;
        top: 52px;
        z-index: 1;
        border-radius: 35px;
    }

    .input-holder input {
        width: 53% !important;
    }

    .dropdown-menu a {
        font-size: 12px !important;
        padding-top: 5px !important;
        padding-bottom: 5px !important;
    }
}
