﻿

.title1 {
    color: #333 !important;
    background-color: #fff;
}

    .title1:hover {
        background-color: #eee !important;
    }

.title5 {
    color: #fff !important;
    background-color: #337ab7;
}

    .title5:hover {
        background-color: #337ab7 !important;
    }


/*代理商销售网络*/

.agent-top > img {
    width: 100%;
}

.agent-top {
    position: relative;
}

    .agent-top > div {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        bottom: 0px;
        color: #FFFFFF;
    }

        .agent-top > div > h2 {
            margin-top: 149px;
            text-align: center;
            font-size: 56px;
        }

        .agent-top > div > p {
            width: 844px;
            margin: 0 auto;
            margin-top: 35px;
            text-align: center;
            font-size: 20px;
        }

.navigation-div {
    border-bottom: 1px solid #EDEDED;
}

.navigation {
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 24px;
    font-size: 16px;
    padding-bottom: 24px;
    color: #333333;
}


    .navigation a {
        color: #333333;
    }

        .navigation a:hover {
            text-decoration: none;
        }

.agent-con {
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 99px;
    text-align: center;
    padding-bottom: 40px;
}

    .agent-con > img {
        display: inline-block;
        width: 758px;
        height: 437px;
    }


    .agent-con > div {
        display: flex;
    }


    .agent-con > div {
        display: inline-block;
        float: left;
        width: 50%;
        margin-top: 91px;
        border-bottom: 1px solid #DDDDDD;
        padding-bottom: 60px;
    }

        .agent-con > div:nth-child(2n) {
            padding-left: 70px;
        }

        .agent-con > div:nth-child(2n+1) {
            padding-left: 139px;
        }

        /*        .agent-con > div:last-child {
            margin-top: 53px;
            border-bottom: none;
        }*/



        .agent-con > div > strong , .agent-con > div> em {
            display: block;
            text-align: left;
            font-style: normal;
            margin-bottom: 25px;
            font-size: 20px;
        }

        .agent-con > div > em {
            font-size: 18px;
        }

/*代理商*/
.agent-content {
    padding-top: 40px;
    /* padding-bottom: 156px; */
}

.agent-content-top > h2 {
    color: #333333;
    font-size: 32px;
    text-align: center;
}

.agent-content-top > p {
    font-size: 18px;
    color: #656565;
    padding-top: 54px;
    text-align: center;
    width: 1199px;
    margin: 0 auto;
    padding-bottom: 29px;
}


.agent-content-top > img {
    display: block;
    width: 1150px;
    height: 454px;
    margin: 0 auto;
    margin-bottom: 127px;
}

.choose-us {
    background: url('../images/agentcarpet.png') no-repeat;
    background-size: cover;
    height: 591px;
    color: #fff;
}

    .choose-us h2 {
        font-size: 32px;
        padding-top: 79px;
        padding-bottom: 54px;
        text-align: center;
    }

    .choose-us p {
        font-size: 18px;
        margin: 0 auto;
        width: 1199px;
        text-align: center;
        padding-bottom: 98px;
    }

    .choose-us > div {
        display: flex;
        width: 1200px;
        margin: 0 auto;
    }

        .choose-us > div > span {
            text-align: center;
            flex: 1;
        }


            .choose-us > div > span > img {
                width: 101px;
                height: 101px;
                display: block;
                margin: 0 auto;
                margin-bottom: 31px;
            }

            .choose-us > div > span > i {
                font-size: 18px;
                font-style: normal;
            }

.support {
    background-color: #F0F2F5;
    padding-top: 117px;
    padding-bottom: 50px;
}


    .support > h2 {
        color: #333333;
        text-align: center;
        font-size: 32px;
        font-weight: 400;
        padding-bottom: 54px;
    }

    .support > p {
        color: #666666;
        text-align: center;
        font-size: 18px;
        padding-bottom: 96px;
        width: 1116px;
        margin: 0 auto;
    }

    .support > div {
        margin: 0 auto;
        max-width: 1200px;
    }

        .support > div > span {
            border: 1px solid #E9E9E9;
            display: inline-block;
            height: 139px;
            width: 375px;
            margin-right: 33px;
            background-color: #fff;
            list-style-type: none;
            margin-bottom: 22px;
            position: relative;
        }

            .support > div > span:hover {
                border: 1px solid #5D78FF;
            }

            .support > div > span:nth-child(3n) {
                margin-right: 0px;
            }

            .support > div > span > i {
                font-style: normal;
                position: absolute;
                left: 92px;
                top: 37px;
                color: #000000;
                font-size: 18px;
            }

            .support > div > span > .agent-support-con {
                z-index: 2;
                width: 238px;
                line-height: 24px;
                top: 70px;
                color: #999999;
                font-size: 14px;
            }

            .support > div > span > .agent-logo {
                position: absolute;
                left: 38px;
                bottom: 51px;
            }

            .support > div > span > img {
                position: absolute;
                bottom: 0px;
                right: 21px;
            }

.condition {
    background-color: #F9FAFF;
    /* padding-top: 65px; */
    padding-bottom: 66px;
}

    .condition > h2 {
        color: #333;
        text-align: center;
        font-size: 32px;
        margin-top: 0px;
        padding-bottom: 70px;
        padding-top: 30px;
    }


    .condition > ul {
        padding-top: 10px;
        display: block;
        padding-left: 10px;
        padding-right: 10px;
        width: 1120px;
        margin: 0 auto;
        overflow: hidden;
    }

        .condition > ul > li {
            width: 510px;
            height: 251px;
            display: inline-block;
            float: left;
            margin-bottom: 49px;
            position: relative;
            box-shadow: 0px 6px 19px 1px rgba(177,174,174,0.2);
        }

            .condition > ul > li:hover {
                box-shadow: 0px 6px 19px 1px rgba(177,174,174,0.51);
            }

            .condition > ul > li:nth-child(2n+1) {
                margin-right: 80px;
            }

            .condition > ul > li > span {
                position: relative;
                display: block;
                font-size: 22px;
                color: #3E3E3E;
                margin-top: 39px;
                margin-left: 36px;
                padding-bottom: 37px;
            }

                .condition > ul > li > span:after {
                    content: "";
                    position: absolute;
                    display: block;
                    background-color: #5D78FF;
                    left: 0px;
                    width: 20px;
                    height: 2px;
                    bottom: 0px;
                }

            .condition > ul > li > i {
                width: 288px;
                display: block;
                font-size: 16px;
                color: #9E9E9E;
                padding-top: 29px;
                margin-left: 36px;
                font-style: normal;
            }

            .condition > ul > li > img {
                width: 91px;
                height: 91px;
                position: absolute;
                top: 39px;
                right: 44px;
            }

    .condition > div {
        width: 838px;
        background: rgba(255,255,255,1);
        box-shadow: 0px 6px 19px 1px rgba(177,174,174,0.51);
        border-radius: 10px;
        padding-bottom: 10px;
        margin: 0 auto;
        margin-top: 46px;
        text-align: center;
    }


        .condition > div > h2 {
            font-size: 32px;
            color: rgba(51,51,51,1);
            padding-top: 53px;
            margin-bottom: 25px;
        }

        .condition > div > span {
            font-size: 14px;
            padding-bottom: 32px;
            color: rgba(246,14,14,1);
            display: block;
        }

        .condition > div > .input-group {
            display: flex;
            padding-right: 224px;
            margin-bottom: 25px;
        }

            .condition > div > .input-group > i {
                width: 214px;
                margin-top: 8px;
                text-align: right;
                padding-right: 20px;
                font-style: normal;
            }


            .condition > div > .input-group > input {
                flex: 1;
                border-radius: 6px !important;
            }

            .condition > div > .input-group > img {
                margin-right: 156px;
                margin-left: 11px;
                width: 101px;
                height: 33px;
            }

        .condition > div > a {
            display: block;
            width: 400px;
            height: 33px;
            border-radius: 6px;
            text-align: center;
            background-color: #409EFF;
            font-size: 14px;
            color: rgba(254,254,254,1);
            margin: 0 auto;
            line-height: 33px;
            margin-top: 27px;
        }

            .condition > div > a:hover {
                text-decoration: none;
            }


        .condition > div > p {
            display: block;
            width: 400px;
            text-align: right;
            font-size: 14px;
            color: #333333;
            margin: 0 auto;
            margin-top: 27px;
        }


            .condition > div > p > a {
                margin-left: 6px;
            }

                .condition > div > p > a:hover {
                    text-decoration: none;
                }
