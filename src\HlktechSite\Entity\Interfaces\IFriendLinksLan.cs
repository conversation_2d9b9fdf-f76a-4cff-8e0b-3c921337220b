﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>友情链接翻译</summary>
public partial interface IFriendLinksLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>友情链接Id</summary>
    Int32 FId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>友情链接标题</summary>
    String? Name { get; set; }

    /// <summary>类型。0为文字，1为图片。冗余字段</summary>
    Int16 FType { get; set; }

    /// <summary>友情链接地址</summary>
    String? Url { get; set; }

    /// <summary>友情链接图片</summary>
    String? Pic { get; set; }

    /// <summary>友情链接排序</summary>
    Int32 Sort { get; set; }

    /// <summary>是否启用</summary>
    Int32 Enabled { get; set; }
    #endregion
}
