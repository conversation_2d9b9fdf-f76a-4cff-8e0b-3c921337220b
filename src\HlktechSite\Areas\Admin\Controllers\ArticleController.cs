﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH;
using DH.Core.Domain.Localization;
using DH.Entity;
using DH.SearchEngine;
using DH.SearchEngine.Interfaces;

using HlktechSite.Entity;

using Lucene.Net.Analysis;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>文章管理</summary>
[DisplayName("文章管理")]
[Description("用于文章分类的管理")]
[AdminArea]
[DHMenu(84,ParentMenuName = "Site", ParentMenuDisplayName = "网站", ParentMenuUrl = "~/{area}/ProductModel", ParentMenuOrder = 40, CurrentMenuUrl = "~/{area}/Article", CurrentMenuName = "ArticleList", CurrentIcon = "&#xe72a;", LastUpdate = "20240125")]
public class ArticleController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 84;


    private readonly ISearchEngine _searchEngine;

    private readonly ILuceneIndexer _luceneIndexer;

    public ArticleController(ISearchEngine searchEngine, Lucene.Net.Store.Directory directory, Analyzer analyzer)
    {
        _searchEngine = searchEngine;
        _luceneIndexer = new LuceneIndexer(directory, analyzer);
    }

    /// <summary>
    /// 文章列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("文章列表")]
    public IActionResult Index(string name, int search_ac_id = -1, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var Ids = "";
        var Category = ArticleCategory.FindById(search_ac_id);
        if (Category != null)
        {
            var CategoryList = new List<ArticleCategory>();
            CategoryList.Clear();
            var CategoryList1 = new List<ArticleCategory>();
            CategoryList1.Clear();
            CategoryList.Add(Category);
            GetCategoryList(CategoryList, CategoryList1);
            Ids = string.Join(",", CategoryList1.Select(p => p.Id).Distinct().ToList());
        }

        var list = Article.SearchAIds(name, Ids, pages).Select(x => new Article { Id = x.Id, AName = x.ParentList.Select(e => e.Name).Join(" => "), Name = x.Name, Show = x.Show, CreateTime = x.CreateTime, Sort = x.Sort });

        var List = new List<ArticleCategory>();
        var live1 = ArticleCategory.FindAllByLevel(0); //1级数据
        GetCategoryList(live1, List);

        var Lists = List.Select(x => new ArticleCategory { Name = x.ParentList.Select(e => e.Name).Join(" => "), Id = x.Id, Level = x.Level });
        viewModel.Claslist = Lists;


        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;

        viewModel.search_ac_id = search_ac_id;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name }, { "search_ac_id", search_ac_id.ToString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 模糊查询产品型号分类数据
    /// </summary>
    /// <param name="Key"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("模糊查询产品型号分类数据")]
    public IActionResult GetlikeName(string Key)
    {
        var List1 = ProductModel.FindByLikeNames(Key).Select(x => new { name = x.Name, value = x.Id });
        return Json(List1);
    }

    /// <summary>
    /// 添加文章管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加文章管理")]
    public IActionResult AddArticle()
    {
        dynamic viewModel = new ExpandoObject();
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(0, 1);
        ViewBag.FileList2 = UploadInfo.FindAllByItemIdAndFileType(0, 11);
        var List = new List<ArticleCategory>();
        var live1 = ArticleCategory.FindAllByLevel(0); //1级数据
        GetCategoryList(live1, List);
        var Lists = List.Select(x => new ArticleCategory { Name = x.ParentList.Select(e => e.Name).Join(" => "), Id = x.Id, Level = x.Level });
        var pages = new PageParameter();
        pages.PageIndex = 1;
        pages.PageSize = 10;
        pages.OrderBy = "CreateTime";

        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id });

        ViewBag.List = ProducList.ToJson();

        ViewBag.Sort =255;
        viewModel.Plist = Lists;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(viewModel);
    }

    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IList<ArticleCategory> levelList, IList<ArticleCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = ArticleCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 新增提交
    /// </summary>
    /// <param name="article_title">文章标题</param>
    /// <param name="default_user_portrait">文章主图</param>
    /// <param name="gc_class_id">关联分类文章Id</param>
    /// <param name="CreateTime">发布时间</param>
    /// <param name="article_url">链接</param>
    /// <param name="select">关联产品型号Id</param>
    /// <param name="article_show">文章是否显示</param>
    /// <param name="article_sort">文章排序</param>
    /// <param name="article_content">文章内容</param>
    /// <param name="summary"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("新增提交")]
    [HttpPost]
    public IActionResult CreatArticle(IFormFile default_user_portrait, string article_title, int gc_class_id, string CreateTime, string article_url, int article_show, int article_sort, string article_content, String select, string summary)
    {
        if (article_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = Article.FindByName(article_title.SafeString().Trim());
        if (ex != null)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        if (gc_class_id == 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属文章分类") });
        }
        using (var tran1 = Article.Meta.CreateTrans())
        {
            var Model = new Article();
            Model.Name = article_title.SafeString().Trim();
            Model.AId = gc_class_id;
            Model.Url = article_url.SafeString().Trim();
            Model.Show = article_show == 1 ? true : false;
            Model.Sort = article_sort;
            Model.Content = article_content;
            Model.MId = select;
            Model.Summary = summary;
            Model.CreateTime = CreateTime.IsNullOrWhiteSpace() ? DateTime.Now : CreateTime.ToDateTime();
            Model.Insert();
            _luceneIndexer.Add(Model);
            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"Article{Model.Id}_{Randoms.MakeFileRndName()}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = $"Article/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath;

                Model.Update();
                _luceneIndexer.Update(Model);
            }

            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();
                foreach (var item in Languagelist)
                {
                    var aaaa = new ArticleLan();
                    aaaa.Name = GetRequest($"[{item.Id}].article_title").SafeString().Trim();
                    aaaa.Summary = GetRequest($"[{item.Id}].summary").SafeString().Trim();
                    //aaaa.Tags = GetRequest($"[{item.Id}].tags").SafeString().Trim();
                    //aaaa.Description = GetRequest($"[{item.Id}].description").SafeString().Trim();
                    aaaa.Content = GetRequest($"article_content_{item.Id}").SafeString().Trim();
                    aaaa.AId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Insert();

                    var Name = $"[{item.Id}].default_user_portrait";
                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"articlelan{aaaa.Id}_{Randoms.MakeFileRndName()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"articlelan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Pic = filepath;

                        aaaa.Update();
                    }
                }
            }
            tran1.Commit();
        }

        Article.Meta.Cache.Clear("");//清除缓存
        ArticleLan.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 文章管理修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("文章管理修改页面")]
    public IActionResult EditArticle(Int32 Id)
    {
        var Model = Article.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(Id, 1);
        ViewBag.FileList2 = UploadInfo.FindAllByItemIdAndFileType(Id, 11);
        dynamic viewModel = new ExpandoObject();
        var List = new List<ArticleCategory>();
        var live1 = ArticleCategory.FindAllByLevel(0);//一级数据
        GetCategoryList(live1, List);
        var Lists = List.Select(x => new ArticleCategory { Name = x.ParentList.Select(e => e.Name).Join(" => "), Id = x.Id, Level = x.Level });
        ViewBag.Name = "";
        ViewBag.AID = 0;
        var pmodel = ArticleCategory.FindById(Model.AId);
        if (pmodel != null)
        {
            ViewBag.Name = pmodel.Name;
            ViewBag.AID = pmodel.Id;
        }
        viewModel.Plist = Lists;
        viewModel.Model = Model;
        ViewBag.Images = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Model.Pic.IsNotNullAndWhiteSpace() ? Model.Pic : "");

        var pages = new PageParameter();
        pages.PageIndex = 1;
        pages.PageSize = 10;
        pages.OrderBy = "CreateTime";

        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id, selected = Model.MId?.SplitAsInt(",").Contains(x.Id) });

        ViewBag.List = ProducList.ToJson();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(viewModel);
    }

    /// <summary>
    /// 文章管理修改接口
    /// </summary>
    /// <param name="Id">文章编号</param>
    /// <param name="article_title">文章标题</param>
    /// <param name="default_user_portrait">文章主图</param>
    /// <param name="gc_class_id">关联分类文章Id</param>
    /// <param name="CreateTime">发布时间</param>
    /// <param name="article_url">链接</param>
    /// <param name="select">MId</param>
    /// <param name="article_show">文章是否显示</param>
    /// <param name="article_sort">文章排序</param>
    /// <param name="article_content">文章内容</param>
    /// <param name="summary"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("文章管理修改接口")]
    public IActionResult EditArticle(Int32 Id, IFormFile default_user_portrait, string article_title, int gc_class_id, string CreateTime, string article_url, int article_show, int article_sort, string article_content, String select, string summary)
    {
        if (article_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        if (gc_class_id == 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属文章分类") });
        }
        var Model = Article.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
        }

        using (var tran1 = Article.Meta.CreateTrans())
        {
            Model.Name = article_title.SafeString().Trim();
            Model.AId = gc_class_id;
            Model.Url = article_url.SafeString().Trim();
            Model.MId = select;
            Model.Show = article_show == 1 ? true : false;
            Model.Sort = article_sort;
            Model.Content = article_content;
            Model.Summary = summary;

            if (!CreateTime.IsNullOrWhiteSpace())
                Model.CreateTime = CreateTime.ToDateTime();

            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"Article{Model.Id}_{Randoms.MakeFileRndName()}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = $"Article/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                var f = saveFileName.AsFile();
                if (f.Exists)
                {
                    f.Delete();
                }

                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath;
            }
            Model.Update();
            _luceneIndexer.Update(Model);
            Article.Meta.Cache.Clear("");

            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();

                var lanlist = ArticleLan.FindAllByAId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new ArticleLan();
                    }
                    var Name = $"[{item.Id}].default_user_portrait";
                    aaaa.Name = GetRequest($"[{item.Id}].article_title").SafeString().Trim();
                    aaaa.Summary = GetRequest($"[{item.Id}].Summary").SafeString().Trim();

                    aaaa.Content = GetRequest($"article_content_{item.Id}").SafeString().Trim();
                    aaaa.AId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Save();

                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"articlelan{aaaa.Id}_{Randoms.MakeFileRndName()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"Articlelan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                        var f = saveFileName.AsFile();
                        if (f.Exists)
                        {
                            f.Delete();
                        }

                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Pic = filepath;

                        aaaa.Update();
                    }
                }
            }
            tran1.Commit();
        }
        Article.Meta.Cache.Clear("");//清除缓存
        ArticleLan.Meta.Cache.Clear("");//清除缓存

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 批量删除数据
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("文章管理删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        Article.DelByIds(Ids.Trim(','));
        ArticleLan.DelByAIds(Ids.Trim(','));
        _luceneIndexer.Delete(Article.FindByIds(Ids.Trim(','))); //批量删除索引
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload.Length;
        fileModel.FileType = 1;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = $"Article/{filename}";
        var saveFileName = DH.DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

        XTrace.WriteLine($"获取保存的路径:{saveFileName}");

        filepath = $"/{DH.DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath;
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path =Pek.Helpers.DHWeb.GetSiteUrl() + filepath });
    }

    /// <summary>
    /// 图片删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片删除")]
    public IActionResult DeleteImg(Int32 Id)
    {
        var model = UploadInfo.FindById(Id);

        if (model != null)
        {
            DH.DHSetting.Current.WebRootPath.GetFullPath().CombinePath(model.FileUrl).AsFile().Delete();
            model.Delete();
        }

        return Ok("true");
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("文件上传")]
    [HttpPost]
    public IActionResult UploadFile(Int32 Id, IFormFile fileupload2)
    {
        if (fileupload2.Length > 2097152)
        {
            return Prompt(new PromptModel { Message = GetResource("最大支持2M的文件！"), IsOk = false });
        }

        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload2.Length;
        fileModel.FileType = 11;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload2.FileName)}";
        var filepath = $"Article/{filename}";
        var saveFileName = DH.DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

        XTrace.WriteLine($"获取保存的路径:{saveFileName}");

        filepath = $"/{DH.DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

        saveFileName.EnsureDirectory();
        fileupload2.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath;
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path =Pek.Helpers.DHWeb.GetSiteUrl() + filepath });
    }

    /// <summary>
    /// 根据名称查询
    /// </summary>
    /// <param name="title"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("根据名称查询")]
    public IActionResult FinByName(string title, int Id)
    {
        var Model = Article.FindByName(title);
        if (Id != 0)
        {
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }
        }
        else
        {
            if (Model != null)
            {
                return Json(false);
            }
        }

        return Json(true);
    }

    ///// <summary>查询产品型号名称</summary>
    ///// <returns></returns>
    //[DisplayName("查询产品型号名称")]
    //[EntityAuthorize(PermissionFlags.Detail)]
    //public IActionResult GetbyProductName(string keyword)
    //{
    //    var List = ProductModel.FindByLikeNames(keyword).Select(x => new { name = x.Name, value = x.Id });
    //    return Json(List);
    //}
}

