﻿using System.ComponentModel;
using System.Dynamic;

using DG.Cube;
using DG.Cube.BaseControllers;

using DH;
using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Models;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>下载文件</summary>
[DisplayName("下载文件")]
[Description("用于下载文件的管理")]
[AdminArea]
[DHMenu(88,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/Download", CurrentMenuName = "DownloadList", CurrentIcon = "&#xe72a;", LastUpdate = "20240125")]
public class DownloadController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 88;

    /// <summary>
    /// 下载文件列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("下载文件列表")]
    public IActionResult Index(string name, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        name = name.SafeString().Trim();
        var list = Download.Search(name, pages);
        ViewBag.list = list;

        viewModel.page = page;
        viewModel.name = name;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });
        return View(viewModel);
    }

    /// <summary>
    /// 编辑下载文件页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑下载文件页面")]
    public IActionResult EditDownText(int Id)
    {
        dynamic viewModel = new ExpandoObject();
        var Model = Download.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        viewModel.Model = Model;

        var ProducList = ProductModel.GetAll().Select(x => new { name = x.Name, value = x.Id, selected = Model.MIds.IsNotNullAndWhiteSpace() && Model.MIds.Trim(',').Split(',').Any(e => e == x.Id.ToString()) });
        ViewBag.List = ProducList.ToJson();

        return View(viewModel);
    }

    /// <summary>
    /// 修改接口
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="select"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("编辑下载文件")]
    public IActionResult EditDownText(Int32 Id, String select)
    {
        var Model = Download.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("下载对象不存在"));
        }

        if (!select.IsNullOrWhiteSpace())
        {
            Model.MIds = "," + select + ",";
            Model.Update();
        }

        Loger.UserLog("修改", $"修改下载文件：{Model.Name},变更关联型号:{select}");

        return MessageTip(GetResource("修改成功"));
    }

}
