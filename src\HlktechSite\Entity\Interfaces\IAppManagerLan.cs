﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>App下载表多语言</summary>
public partial interface IAppManagerLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>App下载Id</summary>
    Int32 JId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>产品名称</summary>
    String? Name { get; set; }

    /// <summary>内容</summary>
    String? Content { get; set; }
    #endregion
}
