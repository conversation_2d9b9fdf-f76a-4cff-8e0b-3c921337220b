﻿@{
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
    var content = Model.Content as String;
    content = content.SafeString().ToUnicodeString();

    var Mbilecontent = Model.MobileContent as String;
    Mbilecontent = Mbilecontent.SafeString().ToUnicodeString();

    var Specifications = Model.Specifications as String;
    Specifications = Specifications.SafeString().ToUnicodeString();

    var localizationSettings = LocalizationSettings.Current;
}
@model HlktechSite.Entity.EndProducts

<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>成品管理</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">

                <li><a href="javascript:" class="current"><span>编辑成品</span></a></li>
                <li><a href="@Url.Action("ModifyAPicture",new { Id=Model.Id})"><span>编辑图片</span></a></li>

                <li><a href="@Url.Action("SearchCategories",new { Id=Model.Id})"><span>选择分类</span></a></li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>
    <div class="item-publish">
        <form method="post" id="goods_form" action="@Url.Action("UpdateGoods1")">
            <input type="hidden" name="type_id" value="@Model.CId" />
            <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                @if (localizationSettings.IsEnable)
                {
                    <ul class="layui-tab-title">
                        <li data="" class="layui-this">标准 </li>
                        @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                        {
                            <li data="@item.Id" class="LId">@item.DisplayName</li>
                        }
                    </ul>
                }
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="dssc-form-goods">
                            <h3 id="demos">成品基本信息</h3>
                            <dl>
                                <dt><i class="required">*</i>成品名称：</dt>
                                <dd>
                                    <input name="g_name" type="text" class="text w400" value="@Model.Name" />
                                    <span></span>
                                    <p class="hint">成品标题名称长度至少3个字符，最长50个汉字</p>
                                </dd>
                            </dl>
                            <dl>
                                <dt>成品卖点：</dt>
                                <dd>
                                    <textarea name="g_jingle" class="textarea h60 w400">@Model.AdvWord</textarea>
                                    <span></span>
                                    <p class="hint">成品卖点最长不能超过140个汉字</p>
                                </dd>
                            </dl>
                            <dl>
                                <dt>成品简介：</dt>
                                <dd>
                                    <textarea name="summary" class="textarea h60 w400">@Model.Summary</textarea>
                                    <span></span>
                                    <p class="hint"></p>
                                </dd>
                            </dl>
                            <dl>
                                <dt> 使用场景：</dt>
                                <dd>
                                    <textarea name="UsageScenarios" class="textarea h60 w400">@Model.UsageScenarios</textarea>
                                    <span></span>
                                    <p class="hint"></p>
                                </dd>
                            </dl>
                            <dl>
                                <dt>上架：</dt>
                                <dd class="onoff">
                                    <label for="sms_login_shang1" class="cb-enable @(Model.Shelf?"selected":"") ">是</label>
                                    <label for="sms_login_shang0" class="cb-disable  @(!Model.Shelf?"selected":"") ">否</label>
                                    <input id="sms_login_shang1" name="shelf" value="1" type="radio" @(Model.Shelf ? "checked" : "")>
                                    <input id="sms_login_shang0" name="shelf" value="0" type="radio" @(!Model.Shelf ? "checked" : "")>
                                </dd>
                            </dl>
                            <dl>
                                <dt>@T("是否推荐")：</dt>
                                <dd class="onoff">
                                    <label for="sms_login_show1" class="cb-enable @(Model.Commend?"selected":"") ">是</label>
                                    <label for="sms_login_show0" class="cb-disable  @(!Model.Commend?"selected":"") ">否</label>
                                    <input id="sms_login_show1" name="nav_new_open" value="1" type="radio" @(Model.Commend ? "checked" : "")>
                                    <input id="sms_login_show0" name="nav_new_open" value="0" type="radio" @(!Model.Commend ? "checked" : "")>
                                </dd>
                            </dl>
                            <dl>
                                <dt><i class="required">*</i>排序：</dt>
                                <dd>
                                    <input name="sort" type="text" class="text w400" value="@Model.Sort" />
                                    <span></span>
                                </dd>
                            </dl>
                            <dl>
                                <dt>成品图片：</dt>
                                <dd>
                                    <div class="dssc-goods-default-pic">
                                        <div class="goodspic-uplaod">
                                            <div class="upload-thumb"> <img dstype="goods_image" src="@Model.Images" /> </div>
                                            <input type="hidden" name="image_path" id="image_path" dstype="goods_image" value="" />
                                            @*<input type="hidden" name="imageType" id="imageType" dstype="imageType" value="0" />*@
                                            <span></span>
                                            <p class="hint">最多可发布5张成品图片。上传成品默认主图，如多规格值时将默认使用该图或分规格上传各规格主图；支持jpg、gif、png格式上传或从图片空间中选择，建议使用<font color="red">尺寸800x800像素以上、大小不超过1M的正方形图片</font>，上传后的图片将会自动保存在图片空间的默认分类中。</p>
                                            <div class="handle">
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span>
                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="goods_image" id="goods_image" value="">
                                                        </span>
                                                        <p><i class="iconfont">&#xe733;</i>图片上传</p>
                                                    </a>
                                                </div>
                                                <a class="dssc-btn mt5" dstype="show_image" href="@Url.Action("Piclist")"><i class="iconfont">&#xe72a;</i>从图片空间选择</a> <a href="javascript:void(0);" dstype="del_goods_demo" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="demo"></div>
                                </dd>
                            </dl>
                            <h3 id="demos">成品详情描述</h3>

                            <dl>
                                <dt>成品描述：</dt>
                                <dd id="dsProductDetails">
                                    <div class="tabs">
                                        <ul class="ui-tabs-nav">
                                            <li class="ui-tabs-selected"><a href="#panel-1"><i class="iconfont">&#xe60c;</i> 电脑端</a></li>
                                            <li class="selected"><a href="#panel-2"><i class="iconfont">&#xe60e;</i>手机端</a></li>
                                        </ul>
                                        <div id="panel-1" class="ui-tabs-panel">
                                            <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                                            <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                                            <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                                            <textarea name="goods_body" id="goods_body"></textarea>
                                            <div class="hr8">

                                                <a class="dssc-btn mt5" dstype="show_desc" href="@Url.Action("PiclistConten",new { type="Pc"})"><i class="iconfont">&#xe72a;</i>插入相册图片</a> <a href="javascript:void(0);" dstype="del_desc" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                            </div>
                                            <p id="des_demo"></p>
                                        </div>
                                        <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                            <div>
                                              
                                            <textarea name="goods_Mobilebody" id="goods_Mobilebody"></textarea>
                                            <div class="hr8">
                                                <a class="dssc-btn mt5" dstype="show_desca" href="@Url.Action("PiclistConten",new { type = "Mobile" })"><i class="iconfont">&#xe72a;</i>@T("插入相册图片")</a> <a href="javascript:void(0);" dstype="del_desca" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                            </div>
                                            <p id="des_Mobieldemo"></p>
                                        </div>
                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span>
                                                    <input type="file" hidefocus="true" size="1" class="input-file" name="add_album" id="add_album" multiple="multiple">
                                                </span>
                                                <p><i class="iconfont" data_type="0" dstype="add_album_i">&#xe733;</i>图片上传</p>
                                            </a>
                                        </div>
                                    </div>
                                </dd>
                            </dl>

                            <dl>
                                <dt>规格参数：</dt>
                                <dd id="dsProductDetailss">
                                    <div class="tabs">

                                        <div id="panel-2" class="ui-tabs-panel">

                                            <textarea name="spec" id="spec"></textarea>
                                            <div class="hr8">

                                                <a class="dssc-btn mt5" dstype="show_descs" href="@Url.Action("PiclistContens",new { type = "Pc" })"><i class="iconfont">&#xe72a;</i>插入相册图片</a> <a href="javascript:void(0);" dstype="del_descs" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                            </div>
                                            <p id="des_demos"></p>
                                        </div>

                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span>
                                                    <input type="file" hidefocus="true" size="1" class="input-file" name="add_albums" id="add_albums" multiple="multiple">
                                                </span>
                                                <p><i class="iconfont" data_type="0" dstype="add_album_is">&#xe733;</i>图片上传</p>
                                            </a>
                                        </div>
                                    </div>
                                </dd>
                            </dl>

                        </div>
                    </div>
                    @if (localizationSettings.IsEnable)
                    {
                        @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                        {
                            var ModelLan = EndProductLan.FindByGIdAndLId(Model.Id, item.Id, false);
                            var  Content =  ModelLan.Content.SafeString().ToUnicodeString();
                            var MobileContent = ModelLan.MobileContent.SafeString().ToUnicodeString();
                            <div class="layui-tab-item">
                                <div class="dssc-form-goods">
                                    <h3 id="demos">成品基本信息</h3>
                                    <dl>
                                        <dt><i class="required">*</i>成品名称：</dt>
                                        <dd>
                                            <input name="[@item.Id].g_name" type="text" class="text w400" value="@ModelLan.Name" />
                                            <span></span>
                                            <p class="hint">成品标题名称长度至少3个字符，最长50个汉字</p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>成品卖点：</dt>
                                        <dd>
                                            <textarea name="[@item.Id].g_jingle" class="textarea h60 w400">@ModelLan.AdvWord</textarea>
                                            <span></span>
                                            <p class="hint">成品卖点最长不能超过140个汉字</p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>成品简介：</dt>
                                        <dd>
                                            <textarea name="[@item.Id].summary" class="textarea h60 w400">@ModelLan.Summary</textarea>
                                            <span></span>
                                            <p class="hint"></p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt> 使用场景：</dt>
                                        <dd>
                                            <textarea name="[@item.Id].UsageScenarios" class="textarea h60 w400">@ModelLan.UsageScenarios</textarea>
                                            <span></span>
                                            <p class="hint"></p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>上架：</dt>
                                        <dd class="onoff">
                                            <label for="[@item.Id].sms_login_shang1" class="cb-enable @(ModelLan.Shelf?"selected":"") ">是</label>
                                            <label for="[@item.Id].sms_login_shang0" class="cb-disable  @(!ModelLan.Shelf?"selected":"") ">否</label>
                                            <input id="[@item.Id].sms_login_shang1" name="[@item.Id].shelf" value="1" type="radio" @(ModelLan.Shelf ? "checked" : "")>
                                            <input id="[@item.Id].sms_login_shang0" name="[@item.Id].shelf" value="0" type="radio" @(!ModelLan.Shelf ? "checked" : "")>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("是否推荐")：</dt>
                                        <dd class="onoff">
                                            <label for="[@item.Id].sms_login_show1" class="cb-enable @(ModelLan.Commend?"selected":"") ">是</label>
                                            <label for="[@item.Id].sms_login_show0" class="cb-disable  @(!ModelLan.Commend?"selected":"") ">否</label>
                                            <input id="[@item.Id].sms_login_show1" name="[@item.Id].nav_new_open" value="1" type="radio" @(ModelLan.Commend ? "checked" : "")>
                                            <input id="[@item.Id].sms_login_show0" name="[@item.Id].nav_new_open" value="0" type="radio" @(!ModelLan.Commend ? "checked" : "")>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt><i class="required">*</i>排序：</dt>
                                        <dd>
                                            <input name="[@item.Id].sort" type="text" class="text w400" value="@ModelLan.sort" />
                                            <span></span>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("成品图片")：</dt>
                                        <dd>
                                            <div class="dssc-goods-default-pic">
                                                <div class="goodspic-uplaod">
                                                    <div class="upload-thumb"> <img dstype="goods_image@(item.Id)" src="@(ModelLan.Image.IsNullOrWhiteSpace()?"/uploads/common/default_goods_image.jpg":UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(),ModelLan.Image))" /> </div>
                                                    <input type="hidden" name="[@item.Id].image_path" id="image_path@(item.Id)" dstype="goods_image@(item.Id)" value="" />
                                                    @*<input type="hidden" name="imageType" id="imageType" dstype="imageType" value="0" />*@
                                                    <span></span>
                                                    <p class="hint">最多可发布5张成品图片。上传成品默认主图，如多规格值时将默认使用该图或分规格上传各规格主图；支持jpg、gif、png格式上传或从图片空间中选择，建议使用<font color="red">尺寸800x800像素以上、大小不超过1M的正方形图片</font>，上传后的图片将会自动保存在图片空间的默认分类中。</p>
                                                    <div class="handle">
                                                        <div class="dssc-upload-btn">
                                                            <a href="javascript:void(0);">
                                                                <span>
                                                                    <input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].EndProduct_image" id="goods_image@(item.Id)" value="">
                                                                </span>
                                                                <p><i class="iconfont">&#xe733;</i>@T("图片上传")</p>
                                                            </a>
                                                        </div>
                                                        <a class="dssc-btn mt5" dstype="show_image@(item.Id)" href="@Url.Action("Piclist")"><i class="iconfont">&#xe72a;</i>从图片空间选择</a> <a href="javascript:void(0);" dstype="del_goods_demo@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="demo@(item.Id)"></div>
                                        </dd>
                                    </dl>
                                    <h3 id="demos@(item.Id)">@T("成品详情描述")</h3>

                                    <dl>
                                        <dt>@T("成品描述")：</dt>
                                        <dd id="dsProductDetails@(item.Id)">
                                            <div class="tabs">
                                                <ul class="ui-tabs-nav">
                                                    <li class="ui-tabs-selected"><a href="#panel-1"><i class="iconfont">&#xe60c;</i> 电脑端</a></li>
                                                    <li class="selected"><a href="#panel-2"><i class="iconfont">&#xe60e;</i>手机端</a></li>
                                                </ul>
                                                <div id="panel-1" class="ui-tabs-panel">
                                                    <script type="text/javascript">
                                                        var ue@(item.Id) = UE.getEditor('<EMAIL>');
                                                        if (true) {
                                                            ue@(item.Id).ready(function () {
                                                                this.setContent('@Html.Raw(Content)');
                                                            })
                                                        }
                                                    </script>
                                                    <textarea name="goods_body@(item.Id)" id="goods_body_@(item.Id)"></textarea>
                                                    <div class="hr8">

                                                        <a class="dssc-btn mt5" dstype="show_desc@(item.Id)" href="@Url.Action("PiclistConten",new { type="Pc"})"><i class="iconfont">&#xe72a;</i>插入相册图片</a> <a href="javascript:void(0);" dstype="del_desc@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                                    </div>
                                                    <p id="des_demo@(item.Id)"></p>
                                                </div>
                                                <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                                    <div>
                                                    </div>

                                                    <script type="text/javascript">
                                                        var ur@(item.Id) = UE.getEditor('goods_Mobilebody_@(item.Id)');
                                                            ur@(item.Id).ready(function ()
                                                            {
                                                                 this.setContent('@Html.Raw(MobileContent)');
                                                            })

                                                    </script>
                                                    <textarea name="goods_Mobilebody_@(item.Id)" id="goods_Mobilebody_@(item.Id)"></textarea>
                                                    <div class="hr8">
                                                        <a class="dssc-btn mt5" dstype="show_desca@(item.Id)" href="@Url.Action("PiclistConten",new { type = "Mobile" })"><i class="iconfont">&#xe72a;</i>@T("插入相册图片")</a> <a href="javascript:void(0);" dstype="del_desca@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                                    </div>
                                                    <p id="des_Mobieldemo@(item.Id)"></p>
                                                </div>
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span>
                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].add_album" id="add_album@(item.Id)" multiple="multiple">
                                                        </span>
                                                        <p><i class="iconfont" data_type="0" dstype="add_album_i@(item.Id)">&#xe733;</i>图片上传</p>
                                                    </a>
                                                </div>
                                            </div>
                                        </dd>
                                    </dl>

                                    <dl>
                                        <dt>@T("规格参数")：</dt>
                                        <dd id="dsProductDetailss@(item.Id)">
                                            <div class="tabs">

                                                <div id="panel-2@(item.Id)" class="ui-tabs-panel">
                                                    <script type="text/javascript">
                                                     var us@(item.Id) = UE.getEditor('<EMAIL>');
                                                        us@(item.Id).ready(function () {
                                                            this.setContent('@Html.Raw(ModelLan.Specifications)');
                                                        })
                                                    </script>
                                                    <textarea name="[@item.Id].spec" id="spec_@(item.Id)"></textarea>
                                                    <div class="hr8">

                                                        <a class="dssc-btn mt5" dstype="show_descs@(item.Id)" href="@Url.Action("PiclistContens",new { type = "Pc" })"><i class="iconfont">&#xe72a;</i>插入相册图片</a> <a href="javascript:void(0);" dstype="del_descs@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                                    </div>
                                                    <p id="des_demos@(item.Id)"></p>
                                                </div>

                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span>
                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].add_albums" id="add_albums@(item.Id)" multiple="multiple">
                                                        </span>
                                                        <p><i class="iconfont" data_type="0" dstype="add_album_is@(item.Id)">&#xe733;</i>图片上传</p>
                                                    </a>
                                                </div>
                                            </div>
                                        </dd>
                                    </dl>

                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
            <div class="dssc-form-goods">
                <h3 id="demos">@T("成品基本信息")</h3>
                <dl>
                    <dt>@T("成品分类")：</dt>
                    <dd id="gcategory">
                        @ViewBag.Name          <a class="dssc-btn" href="@Url.Action("SearchCategories",new { Id=Model.Id})">编辑</a>
                        @*<input type="hidden" id="cate_id" name="cate_id" value="90" class="text" />*@
                        <input type="hidden" name="cate_name" value="" class="text" />
                    </dd>
                </dl>
                <dl style="overflow:inherit">
                    <dt><i class="required">*</i>@T("成品型号")：</dt>
                    <dd>
                        <div id="demo000" style="width:50%"></div>
                    </dd>
                </dl>

                <div class="bottom tc hr32">
                    <input type="hidden" name="Id" id="ModelId" value="@Model.Id" />
                    <input type="submit" class="btn" value="提交" />
                </div>
            </div>
        </form>
    </div>

    <script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
    <script src="/static/plugins/mlselection.js"></script>
    <script src="/static/plugins/js/fileupload/jquery.iframe-transport.js"></script>
    <script src="/static/plugins/js/fileupload/jquery.ui.widget.js"></script>
    <script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
    @*<script src="/static/home/<USER>/sellergoods_add_step2.js"></script>*@
    <script src="~/static/admin/home/<USER>"></script>
    <script src="~/static/admin/js/xm-select.js"></script>




    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>
    <script type="text/javascript" asp-location="Footer">
        var ue = UE.getEditor('goods_body');
        ue.ready(function () {
             this.setContent('@Html.Raw(content)');
        })

        var ud = UE.getEditor('goods_Mobilebody');
        ud.ready(function () {
             this.setContent('@Html.Raw(Mbilecontent)');
        })

        var us = UE.getEditor('spec');
        us.ready(function () {
            this.setContent('@Html.Raw(Specifications)');
        })
    </script>

    <script type="text/javascript" asp-location="Footer">
        var DEFAULT_GOODS_IMAGE = "/uploads/home/<USER>/default_goods_image.jpg";

        layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
            var $ = layui.jquery,
                form = layui.form,
                layer = layui.layer,
                upload = layui.upload,
                layer = layui.layer,
                element = layui.element;
        })


        var lid = "";
        var demo = "";
        var show_image = "";
        var del_goods_demo = "";
        var goods_image = "";
        var show_desc = "";
        var des_demo = "";
        var del_desc = "";
        var show_descs = "";
        var des_demos = "";
        var del_descs = "";
        var spec = "";
        var des_Mobieldemo = "#des_Mobieldemo";
        var show_desca = "";
        var del_desca = "";
        $(".layui-tab-title").on("click", "li", function () {
            console.log($(this).attr("data"));
            lid = $(this).attr("data");
            demo = "#demo" + lid;
            show_image = "show_image" + lid;
            del_goods_demo = "del_goods_demo" + lid;
            goods_image = "goods_image" + lid;
            show_desc = "show_desc" + lid;
            des_demo = "#des_demo" + lid;
            des_demos = "#des_demos" + lid;
            del_desc = "del_desc" + lid;
            add_album = "add_album" + lid;
            add_album_i = "add_album_i" + lid;
            show_descs = "show_descs" + lid;
            spec = "spec_" + lid;
            del_descs = "del_descs" + lid;
            des_Mobieldemo = "#des_Mobieldemo" + lid;
            show_desca = "show_desca" + lid;
            del_desca = "del_desca" + lid;
            console.log("打印的demo==" + demo);
            //主图打开图片空间
            $('a[dstype="' + show_image + '"]').unbind().ajaxContent({
                event: 'click', //mouseover
                loaderType: "img",
                loadingMsg: "/images/loading.gif",
                //target: '#demo'
                target: "#demo" + lid
            }).click(function () {
                $('a[dstype="' + del_goods_demo + '"]').show();
                $(this).hide();
            });
            $('a[dstype="' + del_goods_demo + '"]').unbind().click(function () {
                $('' + demo + '').html('');
                $('a[dstype="' + show_image + '"]').show();
                $(this).hide();

            });


            //参数手机端使用
            //成品手机端内容使用
            $('a[dstype="' + show_desca + '"]').unbind().ajaxContent({
                event: 'click', //mouseover
                loaderType: "img",
                loadingMsg: "/images/loading.gif",
                target: des_Mobieldemo
            }).click(function () {
                $(this).hide();
                $('a[dstype="' + del_desca + '"]').show();
            });

            $('a[dstype="' + del_desca + '"]').click(function () {
                $('' + des_Mobieldemo + '').html('');
                $(this).hide();
                $('a[dstype="' + des_Mobieldemo + '"]').show();
            });


            /* 插入成品描述 */
            // 成品描述使用
            $('a[dstype="' + show_desc + '"]').unbind().ajaxContent({
                event: 'click', //mouseover
                loaderType: "img",
                loadingMsg: "/images/loading.gif",
                target: des_demo
            }).click(function () {
                $(this).hide();
                $('a[dstype="' + del_desc + '"]').show();
            });

            $('a[dstype="' + del_desc + '"]').click(function () {
                $('' + des_demo + '').html('');
                $(this).hide();
                $('a[dstype="' + des_demo + '"]').show();
            });
            $('#' + add_album + '').fileupload({
                dataType: 'json',
                //url: HOMESITEURL+'/Sellergoodsadd/image_upload.html',
                url: CreateImgs,
                formData: { name: '' + add_album + '' },
                add: function (e, data) {
                    $('i[dstype="' + add_album_i + '"]').html("&#xe717;").addClass('rotate').attr('data_type', parseInt($('i[dstype="' + add_album_i + '"]').attr('data_type')) + 1);
                    data.submit();
                },
                done: function (e, data) {
                    var _counter = parseInt($('i[dstype="' + add_album_i + '"]').attr('data_type'));
                    _counter -= 1;
                    if (_counter == 0) {
                        $('i[dstype="' + add_album_i + '"]').removeClass('rotate').html("&#xe733;");
                        $('a[dstype="' + show_desc + '"]').click();
                    }
                    $('i[dstype="' + add_album_i + '"]').attr('' + show_desc + '', _counter);

                }
            });

            //参数规格使用
            //成品规格参数使用
            $('a[dstype="' + show_descs+'"]').unbind().ajaxContent({
                event: 'click', //mouseover
                loaderType: "img",
                loadingMsg: "/images/loading.gif",
                target: des_demos
            }).click(function () {
                $(this).hide();
                $('a[dstype="' + del_descs+'"]').show();
            });

            $('a[dstype="'+del_descs+'"]').click(function () {
                $('' + del_descs+'').html('');
                $(this).hide();
                $('a[dstype="' + show_descs+'"]').show();
            });




            //上传成品主图
            $('#' + goods_image + '').fileupload({
                dataType: 'json',
                url: CreateImgs,
                formData: { name: $("#ModelId").val() },
                add: function (e, data) {
                    $('img[dstype="' + goods_image + '"]').attr('src', '/images/loading.gif');
                    data.submit();
                },
                done: function (e, data) {

                    var param = data.result;
                    if (!param.success) {
                        alert(param.meg);
                        $('img[dstype="' + goods_image + '"]').attr('src', DEFAULT_GOODS_IMAGE);
                    } else {
                        $('input[dstype="' + goods_image + '"]').val(param.file_id);
                        $('img[dstype="' + goods_image + '"]').attr('src', param.file_path);
                    }
                }
            });

        })

        insert_mobile_img = function (file_path) {
            if (!lid) {
                var ue = UE.getEditor('goods_Mobilebody');
                ue.execCommand('insertimage', { src: file_path });
            }
            else {
                var ue = UE.getEditor('goods_Mobilebody_' + lid);
                ue.execCommand('insertimage', { src: file_path });
            }
        }

        function insert_img(name, src) {
            if (!goods_image) {
                goods_image = "goods_image";
            }
            console.log("insert_img事件触发");
            $('input[dstype="' + goods_image + '"]').val(name);
            $('img[dstype="' + goods_image + '"]').attr('src', src);
            //$("#imageType").val("1");//验证是从从相册图片选取的
        }

        function insert_editor(file_path) {
            if (!lid) {
                var ue = UE.getEditor('goods_body');
                ue.execCommand('insertimage', { src: file_path });
            }
            else {
                var ue = UE.getEditor('goods_body_' + lid);
                ue.execCommand('insertimage', { src: file_path });
            }

        }


        function insert_editor1(file_path) {
            if (!lid) {
                var ue = UE.getEditor('spec');
                ue.execCommand('insertimage', { src: file_path });
            } else {
                var ue = UE.getEditor('spec_' + lid);
                ue.execCommand('insertimage', { src: file_path });
            }
        }




        $(function () {
            $("#region").ds_region({ show_deep: 2, tip_type: 1 });
            //电脑端手机端tab切换
            $(".tabs").tabs();
            $.validator.addMethod('checkPrice', function (value, element) {
                _g_price = parseFloat($('input[name="g_price"]').val());
                _g_marketprice = parseFloat($('input[name="g_marketprice"]').val());
                if (_g_price > _g_marketprice && _g_marketprice != '0.00') {
                    return false;
                } else {
                    return true;
                }
            }, '<i class="iconfont">&#xe64c;</i>成品价格不能高于市场价格');
            jQuery.validator.addMethod("checkFCodePrefix", function (value, element) {
                return this.optional(element) || /^[a-zA-Z]+$/.test(value);
            }, '<i class="iconfont">&#xe64c;</i>请填写不多于5位的英文字母');
            $('#goods_form').validate({
                errorPlacement: function (error, element) {
                    $(element).nextAll('span').append(error);
                },
                ////方便测试一下可进行删除
                //submitHandler: function (form) {
                //    ds_ajaxpost('goods_form', 'url', '/index.php/admin/goods/index.html');
                //},
                rules: {
                    g_name: {
                        required: true,
                        minlength: 3,
                        maxlength: 50
                    },
                    g_jingle: {
                        maxlength: 140
                    },
                    g_price: {
                        required: true,
                        number: true,
                        min: 0.01,
                        max: 9999999,
                        checkPrice: true
                    },
                    g_marketprice: {
                        number: true,
                        min: 0.00,
                        max: 9999999
                    },
                    g_costprice: {
                        number: true,
                        min: 0.00,
                        max: 9999999
                    },
                    g_storage: {
                        required: true,
                        digits: true,
                        min: 0,
                        max: 999999999
                    },
                    image_path: {
                        required: false
                    },
                    g_vindate: {
                        required: function () { if ($("#is_gv_1").prop("checked")) { return true; } else { return false; } }
                    },
                    g_vlimit: {
                        required: function () { if ($("#is_gv_1").prop("checked")) { return true; } else { return false; } },
                        range: [1, 10]
                    },
                    g_fccount: {
                        range: [1, 100]
                    },
                    g_fcprefix: {
                        checkFCodePrefix: true,
                        rangelength: [3, 5]
                    },
                    g_saledate: {
                        required: function () { if ($('#is_appoint_1').prop("checked")) { return true; } else { return false; } }
                    },
                    g_deliverdate: {
                        required: function () { if ($('#is_presell_1').prop("checked")) { return true; } else { return false; } }
                    }
                },
                messages: {
                    g_name: {
                        required: '<i class="iconfont">&#xe64c;</i>成品名称不能为空',
                        minlength: '<i class="iconfont">&#xe64c;</i>成品标题名称长度至少3个字符，最长50个汉字',
                        maxlength: '<i class="iconfont">&#xe64c;</i>成品标题名称长度至少3个字符，最长50个汉字'
                    },
                    g_jingle: {
                        maxlength: '<i class="iconfont">&#xe64c;</i>成品卖点不能超过140个字符'
                    },

                    image_path: {
                        required: '<i class="iconfont">&#xe64c;</i>请设置成品主图'
                    }

                }
            });
            setTimeout("setArea(0, 0)", 1000);
        });
        // 按规格存储规格值数据
        var spec_group_checked = new Array();
        var str = '';
        var V = new Array();

        var ADMINSITEURL = "/@adminarea";
        var HOMESITEROOT = "/@adminarea";
        var BASESITEURL = "/@adminarea";
        var ADMINSITEROOT = "/static/admin";
        var Querytheson = "@Url.Action("Querytheson", "EndProductClass")";
            var Selleralbum = "@Url.Action("PiclistConten",new { type = "Mobile" })";

        var CreateImgs = "@Url.Action("UploadImg", "SpaceCategory",new { category_id=1})";
        var createData = "@Url.Action("CreateGoods")";
            var CreateImg = "@Url.Action("UploadImg")";

        var data1 = $.parseJSON('@Html.Raw(ViewBag.List)')
        var demo1 = xmSelect.render({
            el: '#demo000',
            //radio: true,
            autoRow: true,
            paging: true,
            pageSize: 5,
            toolbar: { show: true },
            filterable: true,
            remoteSearch: true,
            remoteMethod: function (val, cb, show) {
                //这里如果val为空, 则不触发搜索
                if (!val) {
                    return cb(data1);
                }
                $.ajax({
                    type: 'post',
                    url: '@Url.Action("GetlikeName")',
                    data: {
                        Key: val
                    },
                    success(data) {
                        cb(data.Data,10);
                    },
                    error: function (arg1) {
                        cb([],0);
                    }

                })
            }
        });
    </script>
</div>  