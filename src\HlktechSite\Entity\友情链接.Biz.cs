using System.ComponentModel;

using DG.Entity;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using Pek;

using XCode;

namespace HlktechSite.Entity {
    /// <summary>友情链接</summary>
    public partial class FriendLinks : CubeEntityBase<FriendLinks>
    {
        #region 对象操作
        static FriendLinks()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(Sort));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        [EditorBrowsable(EditorBrowsableState.Never)]
        protected override void InitData()
        {
            // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
            if (Meta.Session.Count > 0) return;

            if (XTrace.Debug) XTrace.WriteLine("开始初始化FriendLinks[友情链接]数据……");

            var entity = new FriendLinks();
            entity.Name = "模块邦";
            entity.FType = 1;
            entity.Url = "https://www.mokuaibang.com/";
            entity.Pic = "";
            entity.Sort = 1;
            entity.Insert();

            var entity1 = new FriendLinks();
            entity1.Name = "极思灵创";
            entity1.FType = 1;
            entity1.Url = "http://www.gicisky.net/";
            entity1.Pic = "";
            entity1.Sort = 2;
            entity1.Insert();

            var entity2 = new FriendLinks();
            entity2.Name = "串口WiFi";
            entity2.FType = 1;
            entity2.Url = "http://www.hlktech.com/";
            entity2.Pic = "";
            entity2.Sort = 3;
            entity2.Insert();

            var entity4 = new FriendLinks();
            entity4.Name = "中国制造网";
            entity4.FType = 1;
            entity4.Url = "https://cn.made-in-china.com/";
            entity4.Pic = "";
            entity4.Sort = 4;
            entity4.Insert();

            var entity5 = new FriendLinks();
            entity5.Name = "Hi-Link Alibaba";
            entity5.FType = 1;
            entity5.Url = "https://hlktech.en.alibaba.com/";
            entity5.Pic = "";
            entity5.Sort = 5;
            entity5.Insert();

            var entity6 = new FriendLinks();
            entity6.Name = "电源模块防伪查询";
            entity6.FType = 1;
            entity6.Url = "http://ybbhlk.hlktech.com:8090/pm01/scanpage";
            entity6.Pic = "";
            entity6.Sort = 6;
            entity6.Insert();

            if (XTrace.Debug) XTrace.WriteLine("完成初始化FriendLinks[友情链接]数据！");
        }

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static FriendLinks FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据类型查找</summary>
        /// <param name="fType">类型</param>
        /// <returns>实体列表</returns>
        public static IList<FriendLinks> FindAllByFType(Int16 fType)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.FType == fType);

            return FindAll(_.FType == fType);
        }

        /// <summary>
        /// 根据友情链接标题查询
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static FriendLinks FindByName(String name)
        {
            if (name.IsNullOrEmpty()) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

            // 单对象缓存
            //return Meta.SingleCache.GetItemWithSlaveKey(name) as App;

            return Find(_.Name == name);
        }


        // <summary>根据编号列表查找</summary>
        /// <param name="ids">编号列表</param>
        /// <returns>实体对象</returns>
        public static IList<FriendLinks> FindByIds(String ids)
        {
            if (ids.IsNullOrWhiteSpace()) return new List<FriendLinks>();

            ids = ids.Trim(',');

            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => ids.SplitAsInt(",").Contains(x.Id));
            }

            return FindAll(_.Id.In(ids.Split(',')));
        }


        /// <summary>
        /// 根据ID集合删除数据
        /// </summary>
        /// <param name="Ids">ID集合</param>
        public static void DelByIds(String Ids)
        {
            if (Delete(_.Id.In(Ids.Trim(','))) > 0)
                Meta.Cache.Clear("");
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="fType">类型。0为文字，1为图片</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<FriendLinks> Search(Int16 fType, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (fType >= 0) exp &= _.FType == fType;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.Url.Contains(key) | _.Pic.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }


        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="name"></param>
        /// <param name="fType"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        public static IEnumerable<FriendLinks> Searchs(Int16 fType, string name, PageParameter page)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                IEnumerable<FriendLinks> list = Meta.Cache.Entities;
                if (name.IsNotNullAndWhiteSpace())
                {
                    list = list.Where(e => e.Name.Contains(name, StringComparison.OrdinalIgnoreCase));
                }
                if (fType >= 0)
                {
                    list = list.Where(e => e.FType == fType);
                }
                page.TotalCount = list.Count();

                list = list.OrderBy(e => e.Sort).Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize);
                return list;
            }
            var exp = new WhereExpression();
            if (name.IsNotNullAndWhiteSpace())
            {
                exp &= _.Name.Contains(name);
            }
            if (fType >= 0)
            {
                exp &= _.FType == fType;
            }
            return FindAll(exp, page);
        }


        // Select Count(Id) as Id,Category From FriendLinks Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<FriendLinks> _CategoryCache = new FieldCache<FriendLinks>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}