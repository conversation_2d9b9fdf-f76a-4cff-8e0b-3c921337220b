﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>App下载表多语言</summary>
[Serializable]
[DataObject]
[Description("App下载表多语言")]
[BindIndex("IU_DG_AppManagersLan_JId_LId", true, "JId,LId")]
[BindTable("DG_AppManagersLan", Description = "App下载表多语言", ConnName = "DG", DbType = DatabaseType.None)]
public partial class AppManagersLan : IAppManagersLan, IEntity<IAppManagersLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _JId;
    /// <summary>App下载Id</summary>
    [DisplayName("App下载Id")]
    [Description("App下载Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("JId", "App下载Id", "")]
    public Int32 JId { get => _JId; set { if (OnPropertyChanging("JId", value)) { _JId = value; OnPropertyChanged("JId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>产品名称</summary>
    [DisplayName("产品名称")]
    [Description("产品名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Name", "产品名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAppManagersLan model)
    {
        Id = model.Id;
        JId = model.JId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "JId" => _JId,
            "LId" => _LId,
            "Name" => _Name,
            "Content" => _Content,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "JId": _JId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    /// <summary>App下载Id</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public AppManagers? J => Extends.Get(nameof(J), k => AppManagers.FindById(JId));

    /// <summary>App下载Id</summary>
    [Map(nameof(JId), typeof(AppManagers), "Id")]
    public String? JName => J?.ToString();

    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static AppManagersLan? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据App下载Id、所属语言Id查找</summary>
    /// <param name="jId">App下载Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <returns>实体对象</returns>
    public static AppManagersLan? FindByJIdAndLId(Int32 jId, Int32 lId)
    {
        if (jId < 0) return null;
        if (lId < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.JId == jId && e.LId == lId);

        return Find(_.JId == jId & _.LId == lId);
    }
    #endregion

    #region 字段名
    /// <summary>取得App下载表多语言字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>App下载Id</summary>
        public static readonly Field JId = FindByName("JId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>产品名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得App下载表多语言字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>App下载Id</summary>
        public const String JId = "JId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>产品名称</summary>
        public const String Name = "Name";

        /// <summary>内容</summary>
        public const String Content = "Content";
    }
    #endregion
}
