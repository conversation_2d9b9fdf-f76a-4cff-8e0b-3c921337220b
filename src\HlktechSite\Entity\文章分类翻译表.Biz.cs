using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;
using DH.SearchEngine;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>文章分类翻译表</summary>
    public partial class ArticleCategoryLan : CubeEntityBase<ArticleCategoryLan>
    {
        #region 对象操作
        static ArticleCategoryLan()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(CId));

            // 过滤器 UserModule、TimeModule、IPModule
        }

        /// <summary>验证并修补数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 建议先调用基类方法，基类方法会做一些统一处理
            base.Valid(isNew);

            // 在新插入数据或者修改了指定字段时进行修正
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化ArticleCategoryLan[文章分类翻译表]数据……");

        //    var entity = new ArticleCategoryLan();
        //    entity.Id = 0;
        //    entity.CId = 0;
        //    entity.LId = 0;
        //    entity.Name = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化ArticleCategoryLan[文章分类翻译表]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        /// 获取标准的结局方案分类的数据
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public ArticleCategory articleCategory => Extends.Get(nameof(articleCategory), k => ArticleCategory.FindById(AId));
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static ArticleCategoryLan FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }
        /// <summary>
        /// 根据文章分类ID,语言ID查询
        /// </summary>
        /// <param name="AID">文章分类ID</param>
        /// <param name="LId">语言ID</param>
        /// <returns></returns>
        public static ArticleCategoryLan FindByAIdAndLId(int AID, int LId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.AId == AID && e.LId == LId);

            // 单对象缓存
            return Find(_.AId == AID & _.LId == LId);
        }

        /// <summary>根据案例分类Id、所属语言Id查找</summary>
        /// <param name="lId">所属语言Id</param>
        /// <returns>实体对象</returns>
        public static IEnumerable<ArticleCategoryLan> FindAllByLevel(Int32 lId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.LId == lId && e.articleCategory.Level == 0);

            return FindAll(_.LId == lId & _.AId.In(ArticleCategory.FindSQLWithKey(ArticleCategory._.Level == 0)));
        }

        /// <summary>
        /// 通过分类Id和语言Id获取翻译数据
        /// </summary>
        /// <param name="aId">分类Id</param>
        /// <param name="lId">语言Id</param>
        /// <param name="IsGetDefault">当翻译为空时是否获取默认数据</param>
        /// <returns></returns>
        public static String FindByAIdAndLId(Int32 aId, Int32 lId, Boolean IsGetDefault = true)
        {
            if (aId <= 0 || lId <= 0) return "";

            if (Meta.Session.Count < 1000)
            {
                var model = Meta.Cache.Find(e => e.AId == aId && e.LId == lId);

                if (IsGetDefault)
                {
                    return FindData(aId, model);
                }
                else
                {
                    if (model == null)
                        return "";
                    else
                        return model.Name;
                }
            }

            var exp = new WhereExpression();
            exp = _.AId == aId & _.LId == lId;

            var m = Find(exp);

            if (IsGetDefault)
            {
                return FindData(aId, m);
            }
            else
            {
                if (m == null)
                    return "";
                else
                    return m.Name;
            }
        }

        /// <summary>
        /// 获取翻译数据
        /// </summary>
        /// <param name="aId">分类Id</param>
        /// <param name="model">翻译实体</param>
        /// <returns></returns>
        private static String FindData(Int32 aId, ArticleCategoryLan model)
        {
            var r = ArticleCategoryLan.FindById(aId);

            if (model == null)
            {
                return r.Name;
            }
            else
            {
                var Name = model.Name.IsNullOrWhiteSpace() ? r.Name : model.Name;
                return Name;
            }
        }

        #endregion

        #region 高级查询

        // Select Count(Id) as Id,Category From DG_ArticleCategoryLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<ArticleCategoryLan> _CategoryCache = new FieldCache<ArticleCategoryLan>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}