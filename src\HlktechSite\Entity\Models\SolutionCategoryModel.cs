﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>解决方案分类</summary>
public partial class SolutionCategoryModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>分类名称</summary>
    public String? Name { get; set; }

    /// <summary>所属父级Id</summary>
    public Int32 ParentId { get; set; }

    /// <summary>父级Id集合</summary>
    public String? ParentIdList { get; set; }

    /// <summary>当前层级</summary>
    public Int32 Level { get; set; }

    /// <summary>排序</summary>
    public Int16 DisplayOrder { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISolutionCategory model)
    {
        Id = model.Id;
        Name = model.Name;
        ParentId = model.ParentId;
        ParentIdList = model.ParentIdList;
        Level = model.Level;
        DisplayOrder = model.DisplayOrder;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
