﻿using System.ComponentModel;
using System.Text.Json;

using DG.Cube.BaseControllers;

using DH.Entity;
using DH.Models;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek.Models;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>会员级别</summary>
[DisplayName("会员级别")]
[Description("用于会员级别的管理")]
[AdminArea]
[DHMenu(80,ParentMenuName = "Members", CurrentMenuUrl = "~/{area}/MemberGrade", CurrentMenuName = "MemberGrade", CurrentIcon = "&#xe6a3;", LastUpdate = "20240125")]
public class MemberGradeController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 80;

    /// <summary>
    /// 会员级别设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("会员级别设置")]
    public IActionResult Index()
    {
        var SiteSettings = SiteSettingInfo.SiteSettings;

        var MemberGrade = new List<GradeModel>();

        if (!SiteSettings.MemberGrade.IsNullOrEmpty())
        {
            MemberGrade = JsonSerializer.Deserialize<List<GradeModel>>(SiteSettings.MemberGrade);
        }
        else
        {
            MemberGrade.Add(new GradeModel { level_name = "V1", level = 1, exppoints = 0 });
        }

        return View(MemberGrade);
    }

    /// <summary>
    /// 会员级别设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("会员级别设置")]
    [HttpPost]
    public IActionResult UpdateGrade()
    {
        var list = new List<GradeModel>();

        var s = Request.Form;
        var keys = s.Keys.Count(e => e.Contains("[level_name]"));
        for (var i = 1; i <= keys; i++)
        {
            list.Add(new GradeModel { level = i, level_name = s[$"mg[{i}][level_name]"], exppoints = s[$"mg[{i}][exppoints]"].ToString().ToInt() });
        }

        var SiteSettings = SiteSettingInfo.SiteSettings;
        SiteSettings.MemberGrade = JsonSerializer.Serialize(list);
        SiteSettingInfo.SaveChanges();

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
    }
}
