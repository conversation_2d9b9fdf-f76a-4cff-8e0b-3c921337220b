﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>产品型号</summary>
public partial class ProductModelModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>型号</summary>
    public String? Name { get; set; }

    /// <summary>排序</summary>
    public Int16 DisplayOrder { get; set; }

    /// <summary>商城Pc链接</summary>
    public String? PcShopUrl { get; set; }

    /// <summary>商城移动端链接</summary>
    public String? MobileShopUrl { get; set; }

    /// <summary>淘宝产品链接</summary>
    public String? TaobaoShopUrl1 { get; set; }

    /// <summary>小程序二维码图片链接</summary>
    public String? AppletQrUrl { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductModel model)
    {
        Id = model.Id;
        Name = model.Name;
        DisplayOrder = model.DisplayOrder;
        PcShopUrl = model.PcShopUrl;
        MobileShopUrl = model.MobileShopUrl;
        TaobaoShopUrl1 = model.TaobaoShopUrl1;
        AppletQrUrl = model.AppletQrUrl;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
