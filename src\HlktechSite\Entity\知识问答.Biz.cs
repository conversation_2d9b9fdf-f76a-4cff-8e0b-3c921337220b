using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;

using NewLife;
using NewLife.Data;

using Pek;

using XCode;

namespace HlktechSite.Entity {
    /// <summary>知识问答</summary>
    public partial class KnowledgeQuiz : CubeEntityBase<KnowledgeQuiz>
    {
        #region 对象操作
        static KnowledgeQuiz()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            var df = Meta.Factory.AdditionalFields;
            df.Add(nameof(Clicks));
            df.Add(nameof(HelpFuls));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化KnowledgeQuiz[知识问答]数据……");

        //    var entity = new KnowledgeQuiz();
        //    entity.Id = 0;
        //    entity.Name = "abc";
        //    entity.Content = "abc";
        //    entity.Tags = "abc";
        //    entity.MId = 0;
        //    entity.Clicks = 0;
        //    entity.HelpFuls = 0;
        //    entity.Status = 0;
        //    entity.CreateUser = "abc";
        //    entity.CreateUserID = 0;
        //    entity.CreateTime = DateTime.Now;
        //    entity.CreateIP = "abc";
        //    entity.UpdateUser = "abc";
        //    entity.UpdateUserID = 0;
        //    entity.UpdateTime = DateTime.Now;
        //    entity.UpdateIP = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化KnowledgeQuiz[知识问答]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        /// 获取产品型号
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public ProductModel ProductModel => Extends.Get(nameof(ProductModel), k => ProductModel.FindById(MId));

        /// <summary>
        /// 产品分类名称
        /// </summary>
        [XmlIgnore, ScriptIgnore]
        public String MName => ProductModel?.Name;
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static KnowledgeQuiz FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据关联产品型号Id查找</summary>
        /// <param name="mId">关联产品型号Id</param>
        /// <returns>实体列表</returns>
        public static IList<KnowledgeQuiz> FindAllByMId(Int32 mId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.MId == mId);

            return FindAll(_.MId == mId);
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="name"></param>
        /// <param name="MId">产品类型关键字</param>
        /// <param name="page"></param>
        /// <returns></returns>
        public static IEnumerable<KnowledgeQuiz> Searchs(string name, int MId, PageParameter page)
        {
            var exp = new WhereExpression();
            if (name.IsNotNullAndWhiteSpace())
            {
                exp &= _.Name.Contains(name);
            }
            if (MId > 0)
            {
                exp &= _.MId == MId;
            }
            //if (key.IsNotNullAndWhiteSpace())
            //{
            //    exp &= _.MId.In(ProductModel.FindSQLWithKey(ProductModel._.Name.Contains(key)));
            //}

            return FindAll(exp, page);
        }

        /// <summary>
        /// 根据ID集合删除数据
        /// </summary>
        /// <param name="Ids">ID集合</param>
        public static void DelByIds(String Ids)
        {
            if (Delete(_.Id.In(Ids.Trim(','))) > 0)
                Meta.Cache.Clear("");
        }


        /// <summary>根据名称查找</summary>
        /// <param name="name">设备DeviceName</param>
        /// <returns>实体对象</returns>
        public static KnowledgeQuiz FindByName(String name)
        {
            if (name.IsNullOrWhiteSpace()) return null;

            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

            return Find(_.Name == name);
        }

        /// <summary>获取全部已发布数据</summary>
        /// <returns>实体列表</returns>
        public static IList<KnowledgeQuiz> GetAllByPublish()
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
                return Meta.Cache.FindAll(e => e.Status == 1);

            return FindAll(_.Status == 1);
        }

        /// <summary>根据文章ID查找上一篇文章</summary>
        /// <returns>实体对象列表</returns>
        public static KnowledgeQuiz Findprevious(DateTime time)
        {

            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(e => e.CreateTime > time && e.Status == 1).OrderBy(o => o.CreateTime).FirstOrDefault();
            }
            else
            {
                var exp = new WhereExpression();
                exp &= _.CreateTime > time & _.Status == 1;
                return FindAll(exp, new PageParameter { Sort = _.CreateTime, Desc = true, PageSize = 1 }).FirstOrDefault();
            }
        }

        /// <summary>根据文章ID查找下一篇文章</summary>
        /// <returns>实体对象列表</returns>
        public static KnowledgeQuiz FindNext(DateTime time)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(e => e.CreateTime < time && e.Status == 1).OrderByDescending(o => o.CreateTime).FirstOrDefault();
            }
            else
            {
                var exp = new WhereExpression();
                exp &= _.CreateTime < time & _.Status == 1;
                return FindAll(exp, new PageParameter { Sort = _.CreateTime, Desc = false, PageSize = 1 }).FirstOrDefault();
            }
        }


        /// <summary>根据编号列表查找</summary>
        /// <param name="ids">编号列表</param>
        /// <returns>实体对象</returns>
        public static IList<KnowledgeQuiz> FindByIds(String ids)
        {
            if (ids.IsNullOrWhiteSpace()) return new List<KnowledgeQuiz>();

            ids = ids.Trim(',');

            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => ids.SplitAsInt(",").Contains(x.Id));
            }

            return FindAll(_.Id.In(ids.Split(',')));
        }

        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="mId">关联产品型号Id</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<KnowledgeQuiz> Search(Int32 mId, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (mId >= 0) exp &= _.MId == mId;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.Content.Contains(key) | _.Tags.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        // Select Count(Id) as Id,Category From KnowledgeQuiz Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<KnowledgeQuiz> _CategoryCache = new FieldCache<KnowledgeQuiz>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}