﻿@using NewLife.Log
@using NewLife.Serialization
@{

}
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("文章管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("AddArticle")"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("标题")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
            </dl>
            <dl>
                <dt>@T("分类")</dt>
                <dd>
                    <select name="search_ac_id">
                        <option value="-1">请选择...</option>
                        @foreach (var item in Model.Claslist)
                        {
                            if (item.Level == 0)
                            {
                                <!option value="@item.Id" @(item.Id == Model.search_ac_id ? "selected" : "")>&nbsp;&nbsp; @item.Name</!option>
                            }
                            else if (item.Level == 1)
                            {
                                <!option value="@item.Id" @(item.Id == Model.search_ac_id ? "selected" : "")>&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</!option>
                            }
                            else
                            {
                                <!option value="@item.Id" @(item.Id == Model.search_ac_id ? "selected" : "")>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</!option>
                            }
                        }
                    </select>
                </dd>

            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
                @*<a href="/index.php/admin/navigation/index.html" class="btn btn-default" title="@T("取消")">@T("取消")</a>*@
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("排序")</th>
                <th>@T("文章Id")</th>
                <th>@T("标题")</th>
                <th>@T("所属分类")</th>
                <th>@T("显示")</th>
                <th>@T("添加时间")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.list as IEnumerable<Article>)
            {
                <tr id="<EMAIL>" style="background: rgb(255, 255, 255);">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td>@item.Sort</td>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.AName</td>
                    <td>@(item.Show?"是":"否")</td>
                    <td>@item.CreateTime</td>
                    <td>

                        <a href="@Url.Action("EditArticle",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        @*<a href="javascript:dsLayerConfirm('@Url.Action("Delete",new {Ids=item.Id})','@T("您确定要删除吗")?',1)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>*@
                        @*<a href="javascript:;" onclick="submit_delete('@item.Id')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>*@
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>

                    </td>
                </tr>
            }

        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>


<script asp-location="Footer">
   function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }
</script>
