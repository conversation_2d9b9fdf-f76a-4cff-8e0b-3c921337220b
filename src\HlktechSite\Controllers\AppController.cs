﻿using DG.Web.Framework;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

namespace HlktechSite.Controllers;

public class AppController : ControllerBaseX {
    public IActionResult Index()
    {
        return View();
    }

    public IActionResult PrivacyPolicy()
    {
        var language = WorkingLanguage;

        var model = SingleArticle.FindByCode("PrivacyPolicy");
        if (model == null)
        {
            return Content(GetResource("别名不存在"));
        }

        var result = SingleArticleLan.FindByAIdAndLIds(model.Id, language.Id, true);

        ViewBag.Name = result.Name;
        ViewBag.Content = result.Content;

        return View();
    }

    public IActionResult GetInfo(String code)
    {
        var language = WorkingLanguage;

        var model = SingleArticle.FindByCode(code);
        if (model == null)
        {
            return Content(GetResource("别名不存在"));
        }

        var result = SingleArticleLan.FindByAIdAndLIds(model.Id, language.Id, true);

        ViewBag.Name = result.Name;
        ViewBag.Content = result.Content;

        return View();
    }

}
