
.clearfix:after {
    clear: both;
    content: ".";
    display: block;
    height: 0;
    visibility: hidden;
}

/* 必要布局样式css */
.footer-box {
    background: #383838;
    overflow: hidden;
    width: 100%;
    color: #888;
    padding: 30px 10px 0 30px;
	height: 410px;
}
.footer-container {
    width: 1200px;
    margin: 0 auto;
    padding: 0;
}
.yeweilogo{
	float: left;
}
.footer-service:hover {
	cursor: pointer;
}

.footer-service-item {
    float: left;
}

	
.footer-service-item i {
    width: 50px;
    
    margin: 17px 17px 0 29px;
    opacity: .8;
    -webkit-transform: scale(.6);
    -moz-transform: scale(.6);
    transform: scale(.6);
}

.footer-service-item span {
    font-size: 16px;
    color: #ccc;
    float: left;
    margin-top: 8px;
}

.footer-icon-001 i {
    background: url(../images/icon-footer01.png) no-repeat center center;
    background-size: contain;
}

.footer-icon-002 i {
    background: url(../images/icon-footer02.png) no-repeat center center;
    background-size: contain;
}

.footer-icon-003 i {
    background: url(../images/icon-footer03.png) no-repeat center center;
    background-size: contain;
}

.footer-icon-004 i {
    background: url(../images/icon-footer04.png) no-repeat center center;
    background-size: contain;
}
.xian{
	height: 220px;
	width: 1px;
	background-color: #484848;
	float: left;
	margin-left: 50px;
}
.footer-line {
    height: 0;
    display: block;
    clear: both;
    border-bottom: 1px solid #484848;
	
	margin-top: 10px;
}

.footer-links {
    padding-top: 40px;
}

.footer-article-item {
    width: 185px;
    height: 230px;
    float: left;
	
    margin-right: 12px;
    line-height: 2;
    font-size: 14px;
}

.footer-article-item dt {
    color: #ccc;
    font-size: 16px;
    margin-bottom: 10px;
    line-height: 2;
    font-weight: 300;
}

.footer-article-item dd {
    line-height: 2;
}

.footer-article-item dd a {
    color: #888;
    transition: all .1s;
}

.footer-article-item dd a:hover {
    color: #fff;
}

.footer-contact-item {
    width: 145px;
    float: left;
}

.footer-contact-item h3 {
    font-size: 16px;
    color: #ccc;
    line-height: 2;
    margin-bottom: 10px;
    font-weight: 400;
}

.footer-contact-item p {
    font-size: 22px;
    color: #fff;
    font-family: Arial,"Microsoft Yahei","HanHei SC",PingHei,"PingFang SC","Helvetica Neue",Helvetica,Arial,"Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
    margin-bottom: 15px;
}

.footer-ewm {
	margin-top: 40px;
    float: left;
    width: 110px;
    margin-right: 18px;
    text-align: center;
}

.footer-ewm img {
    width: 110px;
    height: 110px;
    display: block;
    border: none;
}

.footer-ewm p {
    font-size: 14px;
    color: #ccc;
    margin-top: 12px;
}

.footer-friend {
    width: 1050px;
    height: 20px;
    line-height: 20px;
    margin: 30px auto;
}

.footer-friend-title {
    float: left;
    color: #ccc;
    font-size: 14px;
}

.footer-friend-title-list {
    float: left;
}

.footer-friend-title-list li {
    float: left;
}

.footer-friend-title-list li a {
    color: #888;
    transition: all .1s;
    display: inline-block;
    padding: 0 10px;
    font-size: 14px;
}

.footer-friend-title-list li a:hover {
    color: #fff;
}

.footer-friend-more {
    color: #ccc;
    transition: all .1s;
    float: left;
    font-size: 14px;
}

.footer-copyright {
    line-height: 40px;
    font-size: 14px;
    text-align: center;
}

.footer-copyright p {
    margin-bottom: 9px;
    font-size: 14px;
    color: #888;
}
