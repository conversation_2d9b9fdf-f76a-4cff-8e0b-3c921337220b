using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;
using DH.SearchEngine;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>成品跳转表多语言信息存放表</summary>
    public partial class JumpProductLan : CubeEntityBase<JumpProductLan>
    {
        #region 对象操作
        static JumpProductLan()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(JId));

            // 过滤器 UserModule、TimeModule、IPModule
        }

        /// <summary>验证并修补数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 建议先调用基类方法，基类方法会做一些统一处理
            base.Valid(isNew);

            // 在新插入数据或者修改了指定字段时进行修正
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化JumpProductLan[成品跳转表多语言信息存放表]数据……");

        //    var entity = new JumpProductLan();
        //    entity.Id = 0;
        //    entity.JId = 0;
        //    entity.LId = 0;
        //    entity.Name = "abc";
        //    entity.InfoUrl = "abc";
        //    entity.AdWord = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化JumpProductLan[成品跳转表多语言信息存放表]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        #endregion

        #region 扩展查询
        /// <summary>
        /// 根据成品Id获取所属语言数据
        /// </summary>
        /// <param name="JId">成品Id</param>
        /// <returns></returns>
        public static IList<JumpProductLan> FindAllByJId(Int32 JId)
        {
            if (JId <= 0) return new List<JumpProductLan>();

            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.JId == JId);

            return FindAll(_.JId == JId);
        }

        /// <summary>
        /// 通过成品Id和语言Id获取翻译数据
        /// </summary>
        /// <param name="JId">成品Id</param>
        /// <param name="lId">语言Id</param>
        /// <param name="IsGetDefault">是否获取默认数据</param>
        /// <returns></returns>
        public static (String? Name, String? InfoUrl, String? AdWord) FindByJIdAndLId(Int32 JId, Int32 lId, Boolean IsGetDefault = true)
        {
            if (JId <= 0 || lId <= 0) return ("", "", "");

            if (Meta.Session.Count < 1000)
            {
                var model = Meta.Cache.Find(e => e.JId == JId && e.LId == lId);
                if (model == null)
                {
                    if (!IsGetDefault)
                    {
                        return ("", "", "");
                    }
                    else
                    {
                        return FindNameAndRemark(JId);
                    }
                }
                else
                {
                    if (IsGetDefault)
                    {
                        var name = model.Name;
                        var infoUrl = model.InfoUrl;
                        var adWord = model.AdWord;

                        if (name.IsNullOrWhiteSpace() || infoUrl.IsNullOrWhiteSpace() || adWord.IsNullOrWhiteSpace())
                        {
                            var r = JumpProduct.FindById(JId);

                            if (r != null)
                            {
                                if (name.IsNullOrWhiteSpace()) name = r.Name;
                                if (infoUrl.IsNullOrWhiteSpace()) infoUrl = r.InfoUrl;
                                if (adWord.IsNullOrWhiteSpace()) adWord = r.AdWord;

                                return (name, infoUrl, adWord);
                            }
                        }
                    }
                }
                return (model.Name, model.InfoUrl, model.AdWord);
            }
            var exp = new WhereExpression();
            exp = _.JId == JId & _.LId == lId;

            var m = Find(exp);
            if (m == null)
            {
                if (!IsGetDefault)
                {
                    return ("", "", "");
                }
                else
                {
                    return FindNameAndRemark(JId);
                }
            }
            //else
            //{
            //    var name = m.Name;
            //    var infoUrl = m.InfoUrl;
            //    var adWord = m.AdWord;

            //    if (name.IsNullOrWhiteSpace() || infoUrl.IsNullOrWhiteSpace() || adWord.IsNullOrWhiteSpace())
            //    {
            //        var r = JumpProduct.FindById(JId);

            //        if (r != null)
            //        {
            //            if (name.IsNullOrWhiteSpace()) name = r.Name;
            //            if (infoUrl.IsNullOrWhiteSpace()) infoUrl = r.InfoUrl;
            //            if (adWord.IsNullOrWhiteSpace()) adWord = r.AdWord;

            //            return (name, infoUrl, adWord);
            //        }
            //    }
            //}
            return ("", "", "");
        }

        /// <summary>
        /// 获取翻译数据
        /// </summary>
        /// <param name="JId"></param>
        /// <returns></returns>
        private static (String? Name, String? InfoUrl, String? AdWord) FindNameAndRemark(Int32 JId)
        {
            var r = JumpProduct.FindById(JId);

            if (r == null)
            {
                return ("", "", "");
            }
            else
            {
                return (r.Name, r.InfoUrl, r.AdWord);
            }
        }

        #endregion

        #region 高级查询

        // Select Count(Id) as Id,Category From DG_JumpProductLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<JumpProductLan> _CategoryCache = new FieldCache<JumpProductLan>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}