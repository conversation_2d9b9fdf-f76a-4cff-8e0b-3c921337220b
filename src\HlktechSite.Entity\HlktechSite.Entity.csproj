<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>

    <OutputPath>..\..\BinWeb</OutputPath>

    <!--将此参数设置为true以获取从NuGet缓存复制到项目输出的dll。-->
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Config\**" />
    <Compile Remove="Log\**" />
    <EmbeddedResource Remove="Config\**" />
    <EmbeddedResource Remove="Log\**" />
    <None Remove="Config\**" />
    <None Remove="Log\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Build.tt" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Build.tt">
      <LastGenOutput>Build.log</LastGenOutput>
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="Build.log">
      <DependentUpon>Build.tt</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

</Project>
