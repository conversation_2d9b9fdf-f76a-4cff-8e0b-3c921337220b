﻿using System.ComponentModel;
using System.Dynamic;

using DG.Cube;
using DG.Cube.BaseControllers;

using DH.Core.Domain.Localization;
using DH.Entity;
using DH.Helpers;
using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Models;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>导航管理</summary>
[DisplayName("导航管理")]
[Description("用于导航的管理")]
[AdminArea]
[DHMenu(79,ParentMenuName = "Site", ParentMenuDisplayName = "网站", CurrentMenuUrl = "~/{area}/Navigation", CurrentMenuName = "NavigationList", CurrentIcon = "&#xe652;", LastUpdate = "20240125")]
public class NavigationController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 79;

    /// <summary>
    /// 导航管理列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("导航管理列表")]
    public IActionResult Index(string name, string Location, int page = 1)
    {
        name = name.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        ViewBag.list = Navigation.Searchs(name, Location, pages);
        viewModel.page = page;
        viewModel.name = name;
        viewModel.Location = Location;

        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name }, { "Location", Location } });

        return View(viewModel);
    }

    /// <summary>
    /// 打开创建导航页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("创建导航页面")]
    public IActionResult CreateNavigation()
    {

        ViewBag.Sort = Navigation.FindMax("Sort") + 1;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View();
    }

    /// <summary>
    /// 创建导航
    /// </summary>
    /// <param name="nav_title"></param>
    /// <param name="nav_url"></param>
    /// <param name="nav_location"></param>
    /// <param name="nav_new_open"></param>
    /// <param name="nav_sort"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("创建导航")]
    public IActionResult CreateNavigation(string nav_title, string nav_url, string nav_location, int nav_new_open, int nav_sort)
    {
        nav_title = nav_title.SafeString().Trim();
        if (nav_title.IsNullOrEmpty())
        {
            return MessageTip(GetResource("标题名称不能为空！"));
        }
      
        if (nav_location.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("导航位置不能为空！"));
        }
        var Model = new Navigation();
        Model.Name = nav_title.SafeString().Trim();
        Model.Url = nav_url.SafeString().Trim();
        Model.Location = nav_location.SafeString().Trim();
        //Model.IsShow = "";
        Model.NewOpen = nav_new_open == 1;
        Model.Sort = nav_sort;
        Model.Insert();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

            using (var tran1 = NavigationLan.Meta.CreateTrans())
            {
                var List = new List<NavigationLan>();
                foreach (var item in Languagelist)
                {
                    var ex = new NavigationLan();
                    ex.Name = (GetRequest($"[{item.Id}].navtitle")).SafeString().Trim();
                    ex.Url = GetRequest($"[{item.Id}].navurl").SafeString().Trim();
                    if (ex.Name.IsNullOrWhiteSpace())
                    {
                        continue;
                    }
                    ex.LId = item.Id;
                    ex.NId = Model.Id;
                    List.Add(ex);
                }
                List.Save();
                tran1.Commit();
                NavigationLan.Meta.Cache.Clear("");
                Navigation.Meta.Cache.Clear("");
            }
        }
        return MessageTip(GetResource("保存成功！"));
    }

    /// <summary>
    /// 打开编辑导航页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑导航页面")]
    public IActionResult UpdateNavigation(int Id)
    {
        var Model = Navigation.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除!"));
        }
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(Model);
    }

    /// <summary>
    /// 编辑导航
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="nav_title"></param>
    /// <param name="nav_url"></param>
    /// <param name="nav_location"></param>
    /// <param name="nav_new_open"></param>
    /// <param name="nav_sort"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("编辑导航")]
    public IActionResult UpdateNavigation(int Id, string nav_title, string nav_url, string nav_location, int nav_new_open, int nav_sort)
    {
        nav_title = nav_title.SafeString().Trim();
        if (nav_title.IsNullOrEmpty())
        {
            return MessageTip(GetResource("标题名称不能为空！"));
        }
       
        if (nav_location.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("导航位置不能为空！"));
        }
        var Model = Navigation.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除!"));
        }
        Model.Name = nav_title;
        Model.Url = nav_url.SafeString().Trim();
        Model.Location = nav_location.SafeString().Trim();

        Model.NewOpen = nav_new_open == 1;
        Model.Sort = nav_sort;
        Model.Update();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

            var list = NavigationLan.FindAllByNId(Model.Id);  //获取到的当前权限的语言
            using (var tran1 = NavigationLan.Meta.CreateTrans())
            {
                foreach (var item in Languagelist)
                {
                    var ex = list.Find(x => x.LId == item.Id);
                    if (ex != null)
                    {
                        ex.Name = (GetRequest($"[{item.Id}].navtitle")).SafeString().Trim();
                        ex.Url = GetRequest($"[{item.Id}].navurl").SafeString().Trim();
                        ex.Update();
                    }
                    else
                    {
                        ex = new NavigationLan();
                        ex.Name = (GetRequest($"[{item.Id}].navtitle")).SafeString().Trim();
                        ex.Url = GetRequest($"[{item.Id}].navurl").SafeString().Trim();
                        if (ex.Name.IsNullOrWhiteSpace())
                        {
                            continue;
                        }
                        ex.LId = item.Id;
                        ex.NId = Model.Id;
                        ex.Insert();
                    }
                }
                tran1.Commit();
                NavigationLan.Meta.Cache.Clear("");
                Navigation.Meta.Cache.Clear("");
            }
        }
        return MessageTip(GetResource("保存成功！"));
    }


    /// <summary>
    /// 批量删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("批量删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        Navigation.DelByIds(Ids.Trim(','));
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
