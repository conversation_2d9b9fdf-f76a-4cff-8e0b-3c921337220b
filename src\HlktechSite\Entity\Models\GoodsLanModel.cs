﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>商品翻译表</summary>
public partial class GoodsLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>商品名称+规格名称</summary>
    public String? Name { get; set; }

    /// <summary>商品广告词</summary>
    public String? AdvWord { get; set; }

    /// <summary>商品主图</summary>
    public String? Image { get; set; }

    /// <summary>商品内容</summary>
    public String? Content { get; set; }

    /// <summary>手机端商品描述</summary>
    public String? MobileContent { get; set; }

    /// <summary>简介</summary>
    public String? Summary { get; set; }

    /// <summary>使用场景</summary>
    public String? UsageScenarios { get; set; }

    /// <summary>参数规格</summary>
    public String? Specifications { get; set; }

    /// <summary>商品推荐</summary>
    public Boolean Commend { get; set; }

    /// <summary>商品是否上架</summary>
    public Boolean Shelf { get; set; }

    /// <summary>排序</summary>
    public Int32 Sort { get; set; }

    /// <summary>商品表Id</summary>
    public Int32 GId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>PC购买Url</summary>
    public String? PcGouUrl { get; set; }

    /// <summary>移动购买Url</summary>
    public String? MobileGouUrl { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IGoodsLan model)
    {
        Id = model.Id;
        Name = model.Name;
        AdvWord = model.AdvWord;
        Image = model.Image;
        Content = model.Content;
        MobileContent = model.MobileContent;
        Summary = model.Summary;
        UsageScenarios = model.UsageScenarios;
        Specifications = model.Specifications;
        Commend = model.Commend;
        Shelf = model.Shelf;
        Sort = model.Sort;
        GId = model.GId;
        LId = model.LId;
        PcGouUrl = model.PcGouUrl;
        MobileGouUrl = model.MobileGouUrl;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
