using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>解决方案分类翻译</summary>
    public partial class SolutionCategoryLan : CubeEntityBase<SolutionCategoryLan>
    {
        #region 对象操作
        static SolutionCategoryLan()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(CId));

            // 过滤器 UserModule、TimeModule、IPModule
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正

            // 检查唯一索引
            // CheckExist(isNew, nameof(CId), nameof(LId));
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化SolutionCategoryLan[解决方案分类翻译]数据……");

        //    var entity = new SolutionCategoryLan();
        //    entity.Id = 0;
        //    entity.CId = 0;
        //    entity.LId = 0;
        //    entity.Name = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化SolutionCategoryLan[解决方案分类翻译]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        /// 获取标准的结局方案分类的数据
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public SolutionCategory solutionCategory => Extends.Get(nameof(solutionCategory), k => SolutionCategory.FindById(CId));
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static SolutionCategoryLan FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据解决方案分类Id、所属语言Id查找</summary>
        /// <param name="cId">解决方案分类Id</param>
        /// <param name="lId">所属语言Id</param>
        /// <returns>实体对象</returns>
        public static SolutionCategoryLan FindByCIdAndLId(Int32 cId, Int32 lId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.CId == cId && e.LId == lId);

            return Find(_.CId == cId & _.LId == lId);
        }

        /// <summary>根据案例分类Id、所属语言Id查找</summary>
        /// <param name="lId">所属语言Id</param>
        /// <returns>实体对象</returns>
        public static IEnumerable<SolutionCategoryLan> FindAllByLevel(Int32 lId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.LId == lId && e.solutionCategory.Level == 0);

            return FindAll(_.LId == lId & _.CId.In(CaseCategory.FindSQLWithKey(CaseCategory._.Level == 0)));
        }

        /// <summary>
        /// 通过解决方案Id和语言Id获取翻译数据
        /// </summary>
        /// <param name="CId"></param>
        /// <param name="lId">语言Id</param>
        /// <param name="IsGetDefault">当翻译为空时是否获取默认数据</param>
        /// <returns></returns>
        public static String FindNameByCIdAndlId(Int32 CId, Int32 lId, Boolean IsGetDefault = true)
        {
            if (CId <= 0 || lId <= 0) return ("");

            if (Meta.Session.Count < 1000)
            {
                var model = Meta.Cache.Find(e => e.CId == CId && e.LId == lId);

                if (IsGetDefault)
                {
                    return FindNameAndRemark(CId, model);
                }
                else
                {
                    if (model == null)
                        return "";

                    return model.Name;
                }
            }

            var exp = new WhereExpression();
            exp = _.CId == CId & _.LId == lId;

            var m = Find(exp);

            if (IsGetDefault)
            {
                return FindNameAndRemark(lId, m);
            }
            else
            {
                if (m == null)
                    return "";

                return m.Name;
            }
        }

        /// <summary>
        /// 获取翻译数据
        /// </summary>
        /// <param name="CId"></param>
        /// <param name="model">翻译实体</param>
        /// <returns></returns>
        private static String FindNameAndRemark(Int32 CId, SolutionCategoryLan model)
        {
            var r = SolutionCategory.FindById(CId);

            if (model == null)
            {
                return r.Name;
            }
            else
            {
                return model.Name.IsNullOrWhiteSpace() ? r.Name : model.Name;
            }
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="cId">解决方案分类Id</param>
        /// <param name="lId">所属语言Id</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<SolutionCategoryLan> Search(Int32 cId, Int32 lId, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (cId >= 0) exp &= _.CId == cId;
            if (lId >= 0) exp &= _.LId == lId;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key);

            return FindAll(exp, page);
        }

        /// <summary>
        /// 根据Id获取所属语言数据
        /// </summary>
        /// <param name="CId">文章Id</param>
        /// <returns></returns>
        public static IList<SolutionCategoryLan> FindAllByCId(Int32 CId)
        {
            if (CId <= 0) return new List<SolutionCategoryLan>();

            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.CId == CId);

            return FindAll(_.CId == CId);
        }

        // Select Count(Id) as Id,Category From SolutionCategoryLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<SolutionCategoryLan> _CategoryCache = new FieldCache<SolutionCategoryLan>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}