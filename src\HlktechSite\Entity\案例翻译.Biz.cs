using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;

using DH;
using DH.Extensions;
using DH.Helpers;

using NewLife;
using NewLife.Data;

using Pek;

using XCode;

namespace HlktechSite.Entity {
    /// <summary>案例翻译</summary>
    public partial class CaseLan : CubeEntityBase<CaseLan>
    {
        #region 对象操作
        static CaseLan()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(CId));

            // 过滤器 UserModule、TimeModule、IPModule
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正

            // 检查唯一索引
            // CheckExist(isNew, nameof(CId), nameof(LId));
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化CaseLan[案例翻译]数据……");

        //    var entity = new CaseLan();
        //    entity.Id = 0;
        //    entity.CId = 0;
        //    entity.LId = 0;
        //    entity.Name = "abc";
        //    entity.Content = "abc";
        //    entity.Pic = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化CaseLan[案例翻译]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        /// 获取标准案例的数据
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public Case caseModel => Extends.Get(nameof(caseModel), k => Case.FindById(CId));
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static CaseLan FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据CaseId、所属语言Id查找</summary>
        /// <param name="cId">CaseId</param>
        /// <param name="lId">所属语言Id</param>
        /// <returns>实体对象</returns>
        public static CaseLan FindByCIdAndLId(Int32 cId, Int32 lId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.CId == cId && e.LId == lId);

            return Find(_.CId == cId & _.LId == lId);
        }

        /// <summary>
        /// 分页根据类型名字模糊查询
        /// </summary>
        /// <param name="Key">关键字</param>
        /// <param name="type">类型</param>
        /// <param name="p">分页条件</param>
        /// <param name="LId">语言Id</param>
        /// <returns></returns>
        public static IEnumerable<CaseLan> Searchs(String Key, PageParameter p,int type, int LId)
        {
            if (Meta.Session.Count < 1000)
            {
                var list = FindAllWithCache().Where(x => x.LId == LId && x.caseModel.Show);

                if (Key.IsNotNullAndWhiteSpace())
                    list = list.Where(x => x.Name.SafeString().ToLower().Contains(Key.ToLower()) || x.caseModel.Name.SafeString().ToLower().Contains(Key.ToLower()));
                if (type > 0)
                {
                    list=list.Where(x => x.caseModel.CId == type);
                }

                list = list.OrderByDescending(x => x.caseModel.CreateTime);
                p.TotalCount = list.Count();

                return list.Skip(--p.PageIndex * p.PageSize).Take(p.PageSize);
            }

            var exp = new WhereExpression();

            exp &= _.LId == LId;
            if (Key.IsNotNullAndWhiteSpace()) exp &= _.Name.Contains(Key);
            if (type > 0)
                exp &= _.Id.In(Case.FindSQLWithKey(Case._.CId==type));
            exp &= _.Id.In(Case.FindSQLWithKey(Case._.Show));
            return FindAll(exp, p);
        }

        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="cId">CaseId</param>
        /// <param name="lId">所属语言Id</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<CaseLan> Search(Int32 cId, Int32 lId, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (cId >= 0) exp &= _.CId == cId;
            if (lId >= 0) exp &= _.LId == lId;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.Content.Contains(key) | _.Pic.Contains(key);

            return FindAll(exp, page);
        }

        /// <summary>
        /// 根据文章Id获取所属语言数据
        /// </summary>
        /// <param name="CId">文章Id</param>
        /// <returns></returns>
        public static IList<CaseLan> FindAllByCId(Int32 CId)
        {
            if (CId <= 0) return new List<CaseLan>();

            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.CId == CId);

            return FindAll(_.CId == CId);
        }

        // Select Count(Id) as Id,Category From CaseLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<CaseLan> _CategoryCache = new FieldCache<CaseLan>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();


        /// <summary>
        /// 通过文章Id和语言Id获取翻译数据
        /// </summary>
        /// <param name="CId"></param>
        /// <param name="lId">语言Id</param>
        /// <param name="IsGetDefault">当翻译为空时是否获取默认数据</param>
        /// <returns></returns>
        public static (String Name, String Content, String Pic, String Summary) FindByCIdAndlId(Int32 CId, Int32 lId, Boolean IsGetDefault = true)
        {
            if (CId <= 0 || lId <= 0) return ("", "", "", "");

            if (Meta.Session.Count < 1000)
            {
                var model = Meta.Cache.Find(e => e.CId == CId && e.LId == lId);

                if (IsGetDefault)
                {
                    return FindNameAndRemark(CId, model);
                }
                else
                {
                    if (model == null)
                        return ("", "", "", "");
                    else
                        //var PIC=DHUrl.Combine(DHWeb.GetSiteUrl(), model.Pic.IsNotNullAndWhiteSpace() ? model.Pic : "");
                        model.Pic = model.Pic.IsNotNullAndWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), model.Pic) : "";
                    return (model.Name, model.Content, model.Pic, model.Summary);
                }
            }

            var exp = new WhereExpression();
            exp = _.CId == CId & _.LId == lId;

            var m = Find(exp);

            if (IsGetDefault)
            {
                return FindNameAndRemark(lId, m);
            }
            else
            {
                if (m == null)
                    return ("", "", "", "");
                else
                    m.Pic = m.Pic.IsNotNullAndWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), m.Pic) : "";
                return (m.Name, m.Content, m.Pic, m.Summary);
            }

            
        }

        /// <summary>
        /// 根据ID集合删除数据
        /// </summary>
        /// <param name="CIds">ID集合</param>
        public static void DelByCIds(String CIds)
        {
            //var list = FindByIds(Ids);
            //if (list.Delete() > 0)
            if (Delete(_.CId.In(CIds.Trim(','))) > 0)
                Meta.Cache.Clear("");
        }
        /// <summary>
        /// 获取翻译数据
        /// </summary>
        /// <param name="CId"></param>
        /// <param name="model">翻译实体</param>
        /// <returns></returns>
        private static (String Name, String Content, String Pic, String Description) FindNameAndRemark(Int32 CId, CaseLan model)
        {
            var r = Case.FindById(CId);

            if (model == null)
            {
                return (r.Name, r.Content, r.Pic, r.Summary);
            }
            else
            {
                var Name = model.Name.IsNullOrWhiteSpace() ? r.Name : model.Name;
                var Content = model.Content.IsNullOrWhiteSpace() ? r.Content : model.Content;
                var PIC = model.Pic.IsNullOrWhiteSpace() ? r.Pic : model.Pic;
                PIC = PIC.IsNotNullAndWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), PIC) : "";
                var Summary = model.Summary.IsNullOrWhiteSpace() ? r.Summary : model.Summary;
                //var Keys = model.Keys.IsNullOrWhiteSpace() ? r.Keys : model.Keys;
                //var Tags = model.Tags.IsNullOrWhiteSpace() ? r.Tags : model.Tags;
                return (Name, Content, PIC, Summary);
            }
        }
        #endregion

        #region 业务操作
        #endregion
    }
}