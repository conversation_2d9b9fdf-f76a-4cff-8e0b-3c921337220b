﻿using DG.Cube.BaseControllers;

using DH.Core.Infrastructure;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Log;

using Pek.Models;

using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>首页</summary>
[DisplayName("首页")]
[Description("后台登录之后的首页")]
[AdminArea]
[DHMenu(100,ParentMenuName = "Home", CurrentMenuUrl = "~/{area}/Home/DashBoard", CurrentMenuName = "DashBoard", CurrentVisible = false, LastUpdate = "20240125")]
public class HomeController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 控制台
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("控制台")]
    public IActionResult DashBoard()
    {
        var menus = GetMenu();

        var _cache = EngineContext.Current.Resolve<ICache>();
        _cache.Remove($"{Sid}_{AdminArea.AreaName}");

        return View(menus);
    }

    /// <summary>
    /// 清除缓存
    /// </summary>
    /// <returns></returns>
    [DisplayName("清除缓存")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult ClearCache()
    {
        // 清除缓存数据
        var _cache = EngineContext.Current.Resolve<ICache>();
        _cache.Clear();

        // 清除内存
        try
        {
            GC.Collect();

            // 释放当前进程所占用的内存
            var p = Process.GetCurrentProcess();
            SetProcessWorkingSetSize(p.Handle, -1, -1);
        }
        catch (Exception ex)
        {
            XTrace.WriteException(ex);
        }

        return Json(new DResult { code = 10000, msg = GetResource("操作成功") });
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改密码")]
    [EntityAuthorize((PermissionFlags)32)]
    [HttpGet]
    public IActionResult ModifyPw()
    {
        return View();
    }

    /// <summary>
    /// 密码提交修改
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改密码")]
    [EntityAuthorize((PermissionFlags)32)]
    [HttpPost]
    public IActionResult ModifyPw(String oldPassword, String password, String repassword)
    {
        var model = ManageProvider.User;
        if (ManageProvider.Provider?.PasswordProvider.Verify(oldPassword, model.Password!) == false)
        {
            return Prompt(new PromptModel { Message = GetResource("旧密码错误") });
        }

        if (password.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("新密码不能为空") });
        }

        if (password != repassword)
        {
            return Prompt(new PromptModel { Message = GetResource("两次密码不一致") });
        }

        model.Password = repassword;
        (model as IEntity).Update();

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true });
    }

    [DllImport("kernel32.dll")]
    extern static Boolean SetProcessWorkingSetSize(IntPtr proc, Int32 min, Int32 max);
}

/// <summary>欢迎界面</summary>
[DisplayName("欢迎界面")]
[Description("后台登录之后的首页调用的默认区域")]
[AdminArea]
[DHMenu(100,ParentMenuName = "Home", CurrentMenuUrl = "~/{area}/Main", CurrentMenuName = "Main", CurrentIcon = "&#xe70b;", LastUpdate = "20240125")]
public class MainController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 欢迎界面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("欢迎界面")]
    public IActionResult Index()
    {
        return View();
    }
}
