﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>案例</summary>
public partial interface ICase
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>产品型号Id</summary>
    Int32 MId { get; set; }

    /// <summary>案例分类Id</summary>
    Int32 CId { get; set; }

    /// <summary>案例跳转链接</summary>
    String? Url { get; set; }

    /// <summary>案例是否显示，0为否，1为是，默认为1</summary>
    Boolean Show { get; set; }

    /// <summary>案例排序</summary>
    Int32 Sort { get; set; }

    /// <summary>案例标题</summary>
    String? Name { get; set; }

    /// <summary>内容</summary>
    String? Content { get; set; }

    /// <summary>简介</summary>
    String? Summary { get; set; }

    /// <summary>案例主图</summary>
    String? Pic { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>发布时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
