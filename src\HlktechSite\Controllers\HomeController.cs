﻿using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechSite.DTO;
using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Seo;

using System.Dynamic;

namespace DG.Web.Controllers;

/// <summary>主页面</summary>
[DHSitemap(IsUse = true)]
public class CubeHomeController : DGBaseControllerX
{
    /// <summary>主页面</summary>
    /// <returns></returns>
    ///
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();

        var gPages = new PageParameter() { PageIndex = 1, PageSize = 8, OrderBy = "Sort desc,CreateTime desc" };
        //电子标签临时推荐

        //产品推荐列表
        IEnumerable<Goods> goods;

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
            goods = GoodsLan.FinByRecommend(gPages, WorkingLanguage.Id).Select(x => new Goods
            {
                Id = x.GId,
                Content = x.Content.IsNullOrWhiteSpace() ? x.Goods.Content : x.Content,
                AdvWord = x.AdvWord.IsNullOrWhiteSpace() ? x.Goods.AdvWord : x.AdvWord,
                Name = x.Name.IsNullOrWhiteSpace() ? x.Goods.Name : x.Name,
                Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : x.Goods.Image.IsNotNullAndWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Goods.Image.Replace("\\", "/")) : ""
            });
        else
            goods = Goods.Searchs(gPages).Select(x => new Goods { Id = x.Id, Content = x.Content, AdvWord = x.AdvWord, Name = x.Name, Image = x.Image.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Image.Replace("\\", "/")) : "" });
        viewModel.productRecomList = goods;

        //客户案例推荐列表
        viewModel.caseRecomList = Case.Searchs("", 0, new PageParameter() { PageIndex = 1, PageSize = 6 }).Select(x => new Case { Id = x.Id, Content = x.Content, Summary = x.Summary, Name = x.Name, Pic = x.Pic.IsNotNullOrWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), "/", x.Pic.Replace("\\", "/")) : "" });

        //公司新闻列表
        var list = Article.Searchss("", 1, new PageParameter() { PageIndex = 1, PageSize = 7, OrderBy = "Sort desc,CreateTime  desc" });
        viewModel.ArticleList = list;

        //行业新闻列表
        viewModel.IndustryList = Article.Searchss("", 2, new PageParameter() { PageIndex = 1, PageSize = 7, OrderBy = "Sort desc,CreateTime  desc" });

        //应用案例
        //viewModel.caseList = Case.Searchs("",0, new PageParameter() { PageIndex = 1, PageSize = 7, OrderBy = "CreateTime", Desc = true });

        //解决方案名称
        viewModel.Solutionlist = Solution.Searchs("", 0, new PageParameter() { PageIndex = 1, PageSize = 7, OrderBy = "Sort desc,CreateTime  desc" });
        return DGView(viewModel, true);
    }

    /// <summary>
    /// 关于我们
    /// </summary>
    /// <returns></returns>
    public IActionResult AboutUs()
    {
        var Model = SingleArticle.FindById(2);
        var navigations = new List<NavigationUrl>();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable) {
            var modelLan = SingleArticleLan.FindByAIdAndLIds(Model.Id, WorkingLanguage.Id);
            Model.Name = modelLan.Name.IsNotNullOrWhiteSpace() ? modelLan.Name : Model.Name;
            Model.Content = modelLan.Content.IsNotNullOrWhiteSpace() ? modelLan.Content : Model.Content;
        }
        navigations.Add(new NavigationUrl { Name = Model.Name, IsLast = true });
        ViewBag.Locations = navigations;
        return DGView(Model, true);
    }

    /// <summary>
    /// 定制申请
    /// </summary>
    /// <returns></returns>
    public IActionResult Customization()
    {
        return DGView();
    }

    public override IEnumerable<DHSitemap> CreateSiteMap()
    {
        var list = new List<DHSitemap>();
        list.Add(new DHSitemap
        {
            SType = SiteMap.首页,
            ActionName = "Index",
            ControllerName = "CubeHome"
        });
        list.Add(new DHSitemap
        {
            SType = SiteMap.单页,
            ActionName = "AboutUs",
            ControllerName = "CubeHome"
        });
        list.Add(new DHSitemap
        {
            SType = SiteMap.单页,
            ActionName = "Customization",
            ControllerName = "CubeHome"
        });

        var langlist = Language.FindByStatus();
        var defaultlang = Language.FindByDefault();
        foreach (var item in langlist)
        {
            if (item.Id == defaultlang.Id) continue;
            list.Add(new DHSitemap
            {
                SType = SiteMap.首页,
                ActionName = "Index",
                ControllerName = "CubeHome",
                UniqueSeoCode = item.UniqueSeoCode
            });
            list.Add(new DHSitemap
            {
                SType = SiteMap.单页,
                ActionName = "AboutUs",
                ControllerName = "CubeHome",
                UniqueSeoCode = item.UniqueSeoCode
            });
            list.Add(new DHSitemap
            {
                SType = SiteMap.单页,
                ActionName = "Customization",
                ControllerName = "CubeHome",
                UniqueSeoCode = item.UniqueSeoCode
            });
        }

        return list;
    }

}
