﻿@{
}


<div class="goods-gallery add-step2">
    <a class="sample_demo" id="select_s" href="/index.php/admin/Goodsalbum/pic_list.html?item=des" style="display:none;">提交</a>
    <div class="nav">
        <span class="l">
            用户相册 >
            全部图片
        </span><span class="r">
                   <select name="jump_menu" id="jump_menu" style="width:100px;">
                       <option value="0" style="width:80px;">请选择</option>
                       @foreach (var item in ViewBag.ListXC)
                       {
                           <option style="width:80px;" value="@item.Id">@item.Name</option>
                       }
                   </select>
        </span>
    </div>
    <ul class="list">
        @*<li onclick="insert_editor('http://b2c1.gicisky.net/uploads/home/<USER>/goods/2020082111141077203.png');"><a href="JavaScript:void(0);"><img src="http://b2c1.gicisky.net/uploads/home/<USER>/goods/2020082111141077203_240.png" title='http://b2c1.gicisky.net/uploads/home/<USER>/goods/2020082111141077203.png' /></span></a></li>*@
        @foreach (var item in ViewBag.list)
        {
            if (ViewBag.type == "Pc")
            {
                <li onclick="insert_editor('@item.Cover');">
                    <a href="JavaScript:void(0);"><img src="@item.Cover" /></a>
                </li>
            }
            else if (ViewBag.type == "Mobile")
            {
                <li onclick="insert_mobile_img('@item.Cover');">
                    <a href="JavaScript:void(0);"><img src="@item.Cover" /></a>
                </li>
            }

        }
    </ul>
    <div class="pagination"><ul class="pagination">
    @Html.Raw(ViewBag.Str)
</ul></div>
</div>
<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script asp-location="Footer">
    $(document).ready(function () {
        $('.EndProducts-gallery .pagination li a').ajaxContent({
            event: 'click', //mouseover
            loaderType: 'img',
            loadingMsg: '/static/home/<USER>/loading.gif',
            target: '#des_demo'
        });
        $('#jump_menu').change(function () {
            $('#select_s').attr('href', "@Url.Action("PiclistConten")?id=" + $('#jump_menu').val()+"&type=@ViewBag.type");
            $('.sample_demo').ajaxContent({
                event: 'click', //mouseover
                loaderType: 'img',
                loadingMsg: '/static/home/<USER>/loading.gif',
                target: '#des_demo'
            });
            $('#select_s').click();
        });
    });
</script>