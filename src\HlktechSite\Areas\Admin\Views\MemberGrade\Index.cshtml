﻿@model List<GradeModel>
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("会员级别")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li class="tips">@T("当会员符合某个级别后将自动升至该级别，请谨慎设置会员级别")</li>
            <li class="tips">@T("建议：一、级别应该是逐层递增，例如“级别2”所需经验值要高于“级别1”；二、设置的第一个级别所需经验值应为0；三、请填写完整的级别信息")</li>
        </ul>
    </div>
    @using (Html.BeginForm("UpdateGrade", "MemberGrade", FormMethod.Post, new Dictionary<String, Object> { { "id", "mg_form" }, { "name", "mg_form" }, { "enctype", "multipart/form-data" } }))
    {
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th class="align-center">@T("级别名称")</th>
                    <th class="align-left">@T("经验值")</th>
                    <th class="align-left">@T("操作")</th>
                </tr>
            </thead>
            <tbody id="mg_tbody">
                @for (var i = 1; i <= Model.Count; i++)
                {
                    <tr>
                        <td><input type="text" ds_type="verify" name="mg[@Model[i - 1].level][level_name]" value="@Model[i - 1].level_name" class="w120" /></td>
                        <td><input type="text" ds_type="verify" name="mg[@Model[i - 1].level][exppoints]" value="@Model[i - 1].exppoints" class="w60" @(i == 1 ? "readonly" : "") /></td>
                        @if (i == 1)
                        {
                            <td></td>
                        }
                        else
                        {
                            <td><a onclick="remove_tr($(this));" href="JavaScript:void(0);">删除</a></td>
                        }
                    </tr>
                }
            </tbody>
            <tbody>
                <tr>
                    <td colspan="3">
                        <a id="add_membergrade" class="btn-add-nofloat marginleft" href="JavaScript:void(0);"> <span>新增等级</span> </a>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="3"><a href="JavaScript:void(0);" class="btn" id="submitBtn"><span>提交</span></a></td>
                </tr>
            </tfoot>
        </table>
    }
    <script type="text/javascript" asp-location="Footer">
        $(function () {
            var i = @Model.Count + 1;
            var mg_tbody = '<tr>' +
                '<td><input type="text" ds_type="verify" name="mg[key][level_name]" value="" class="w120" /></td>' +
                '<td><input type="text" ds_type="verify" name="mg[key][exppoints]"  value="" class="w60" /></td>' +
                '<td><a onclick="remove_tr($(this));" href="JavaScript:void(0);">移除</a></td>' +
                '</tr>';

            $("#add_membergrade").click(function () {
                $('#mg_tbody > tr:last').after(mg_tbody.replace(/key/g, i));
                i++;
            });

            $('#submitBtn').click(function () {
                var result = true;
                var error = new Array();
                $("#mg_tbody").find("[ds_type='verify']").each(function () {
                    if (result) {
                        data = $(this).val();
                        if (!data) {
                            result = false;
                            //error.push('请将信息填写完整');
                            error = '请将信息填写完整';
                        }
                        //验证类型
                        if (result) {
                            var data_str = $(this).attr('data-param');
                            if (data_str) {
                                eval("data_str = " + data_str);
                                switch (data_str.type) {
                                    case 'int':
                                        result = (data = parseInt(data)) > 0 ? true : false;
                                        error = (result == false) ? (data_str.name + '应为整数') : '';
                                }
                            }
                        }
                    }
                });
                if (result) {
                    $('#mg_form').submit();
                } else {
                    layer.alert(error);
                }
            });
        })

        function remove_tr(o) {
            o.parents('tr:first').remove();
        }
    </script>
</div>