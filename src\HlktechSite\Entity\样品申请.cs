﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>样品申请</summary>
[Serializable]
[DataObject]
[Description("样品申请")]
[BindTable("DG_SampleApplication", Description = "样品申请", ConnName = "DG", DbType = DatabaseType.None)]
public partial class SampleApplication : ISampleApplication, IEntity<ISampleApplication>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _ComName;
    /// <summary>公司名称</summary>
    [DisplayName("公司名称")]
    [Description("公司名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ComName", "公司名称", "")]
    public String? ComName { get => _ComName; set { if (OnPropertyChanging("ComName", value)) { _ComName = value; OnPropertyChanged("ComName"); } } }

    private String? _ComPeople;
    /// <summary>企业人数</summary>
    [DisplayName("企业人数")]
    [Description("企业人数")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ComPeople", "企业人数", "")]
    public String? ComPeople { get => _ComPeople; set { if (OnPropertyChanging("ComPeople", value)) { _ComPeople = value; OnPropertyChanged("ComPeople"); } } }

    private String? _Website;
    /// <summary>企业官网</summary>
    [DisplayName("企业官网")]
    [Description("企业官网")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Website", "企业官网", "")]
    public String? Website { get => _Website; set { if (OnPropertyChanging("Website", value)) { _Website = value; OnPropertyChanged("Website"); } } }

    private String? _Turnover;
    /// <summary>年营业额</summary>
    [DisplayName("年营业额")]
    [Description("年营业额")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Turnover", "年营业额", "")]
    public String? Turnover { get => _Turnover; set { if (OnPropertyChanging("Turnover", value)) { _Turnover = value; OnPropertyChanged("Turnover"); } } }

    private String? _ComType;
    /// <summary>企业类型</summary>
    [DisplayName("企业类型")]
    [Description("企业类型")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ComType", "企业类型", "")]
    public String? ComType { get => _ComType; set { if (OnPropertyChanging("ComType", value)) { _ComType = value; OnPropertyChanged("ComType"); } } }

    private String? _Address;
    /// <summary>企业地址</summary>
    [DisplayName("企业地址")]
    [Description("企业地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Address", "企业地址", "")]
    public String? Address { get => _Address; set { if (OnPropertyChanging("Address", value)) { _Address = value; OnPropertyChanged("Address"); } } }

    private String? _Linkman;
    /// <summary>联系人</summary>
    [DisplayName("联系人")]
    [Description("联系人")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Linkman", "联系人", "")]
    public String? Linkman { get => _Linkman; set { if (OnPropertyChanging("Linkman", value)) { _Linkman = value; OnPropertyChanged("Linkman"); } } }

    private String? _Position;
    /// <summary>职位</summary>
    [DisplayName("职位")]
    [Description("职位")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Position", "职位", "")]
    public String? Position { get => _Position; set { if (OnPropertyChanging("Position", value)) { _Position = value; OnPropertyChanged("Position"); } } }

    private String? _Phone;
    /// <summary>电话</summary>
    [DisplayName("电话")]
    [Description("电话")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Phone", "电话", "")]
    public String? Phone { get => _Phone; set { if (OnPropertyChanging("Phone", value)) { _Phone = value; OnPropertyChanged("Phone"); } } }

    private String? _QQ;
    /// <summary>QQ</summary>
    [DisplayName("QQ")]
    [Description("QQ")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("QQ", "QQ", "")]
    public String? QQ { get => _QQ; set { if (OnPropertyChanging("QQ", value)) { _QQ = value; OnPropertyChanged("QQ"); } } }

    private String? _Email;
    /// <summary>邮箱</summary>
    [DisplayName("邮箱")]
    [Description("邮箱")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Email", "邮箱", "")]
    public String? Email { get => _Email; set { if (OnPropertyChanging("Email", value)) { _Email = value; OnPropertyChanged("Email"); } } }

    private String? _Fax;
    /// <summary>传真</summary>
    [DisplayName("传真")]
    [Description("传真")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Fax", "传真", "")]
    public String? Fax { get => _Fax; set { if (OnPropertyChanging("Fax", value)) { _Fax = value; OnPropertyChanged("Fax"); } } }

    private String? _ProModel;
    /// <summary>产品型号</summary>
    [DisplayName("产品型号")]
    [Description("产品型号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ProModel", "产品型号", "")]
    public String? ProModel { get => _ProModel; set { if (OnPropertyChanging("ProModel", value)) { _ProModel = value; OnPropertyChanged("ProModel"); } } }

    private String? _Demand;
    /// <summary>月需求量</summary>
    [DisplayName("月需求量")]
    [Description("月需求量")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Demand", "月需求量", "")]
    public String? Demand { get => _Demand; set { if (OnPropertyChanging("Demand", value)) { _Demand = value; OnPropertyChanged("Demand"); } } }

    private String? _Theme;
    /// <summary>申请主题</summary>
    [DisplayName("申请主题")]
    [Description("申请主题")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Theme", "申请主题", "")]
    public String? Theme { get => _Theme; set { if (OnPropertyChanging("Theme", value)) { _Theme = value; OnPropertyChanged("Theme"); } } }

    private String? _Describe;
    /// <summary>需求描述 </summary>
    [DisplayName("需求描述")]
    [Description("需求描述 ")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Describe", "需求描述 ", "")]
    public String? Describe { get => _Describe; set { if (OnPropertyChanging("Describe", value)) { _Describe = value; OnPropertyChanged("Describe"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISampleApplication model)
    {
        Id = model.Id;
        ComName = model.ComName;
        ComPeople = model.ComPeople;
        Website = model.Website;
        Turnover = model.Turnover;
        ComType = model.ComType;
        Address = model.Address;
        Linkman = model.Linkman;
        Position = model.Position;
        Phone = model.Phone;
        QQ = model.QQ;
        Email = model.Email;
        Fax = model.Fax;
        ProModel = model.ProModel;
        Demand = model.Demand;
        Theme = model.Theme;
        Describe = model.Describe;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "ComName" => _ComName,
            "ComPeople" => _ComPeople,
            "Website" => _Website,
            "Turnover" => _Turnover,
            "ComType" => _ComType,
            "Address" => _Address,
            "Linkman" => _Linkman,
            "Position" => _Position,
            "Phone" => _Phone,
            "QQ" => _QQ,
            "Email" => _Email,
            "Fax" => _Fax,
            "ProModel" => _ProModel,
            "Demand" => _Demand,
            "Theme" => _Theme,
            "Describe" => _Describe,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "ComName": _ComName = Convert.ToString(value); break;
                case "ComPeople": _ComPeople = Convert.ToString(value); break;
                case "Website": _Website = Convert.ToString(value); break;
                case "Turnover": _Turnover = Convert.ToString(value); break;
                case "ComType": _ComType = Convert.ToString(value); break;
                case "Address": _Address = Convert.ToString(value); break;
                case "Linkman": _Linkman = Convert.ToString(value); break;
                case "Position": _Position = Convert.ToString(value); break;
                case "Phone": _Phone = Convert.ToString(value); break;
                case "QQ": _QQ = Convert.ToString(value); break;
                case "Email": _Email = Convert.ToString(value); break;
                case "Fax": _Fax = Convert.ToString(value); break;
                case "ProModel": _ProModel = Convert.ToString(value); break;
                case "Demand": _Demand = Convert.ToString(value); break;
                case "Theme": _Theme = Convert.ToString(value); break;
                case "Describe": _Describe = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得样品申请字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>公司名称</summary>
        public static readonly Field ComName = FindByName("ComName");

        /// <summary>企业人数</summary>
        public static readonly Field ComPeople = FindByName("ComPeople");

        /// <summary>企业官网</summary>
        public static readonly Field Website = FindByName("Website");

        /// <summary>年营业额</summary>
        public static readonly Field Turnover = FindByName("Turnover");

        /// <summary>企业类型</summary>
        public static readonly Field ComType = FindByName("ComType");

        /// <summary>企业地址</summary>
        public static readonly Field Address = FindByName("Address");

        /// <summary>联系人</summary>
        public static readonly Field Linkman = FindByName("Linkman");

        /// <summary>职位</summary>
        public static readonly Field Position = FindByName("Position");

        /// <summary>电话</summary>
        public static readonly Field Phone = FindByName("Phone");

        /// <summary>QQ</summary>
        public static readonly Field QQ = FindByName("QQ");

        /// <summary>邮箱</summary>
        public static readonly Field Email = FindByName("Email");

        /// <summary>传真</summary>
        public static readonly Field Fax = FindByName("Fax");

        /// <summary>产品型号</summary>
        public static readonly Field ProModel = FindByName("ProModel");

        /// <summary>月需求量</summary>
        public static readonly Field Demand = FindByName("Demand");

        /// <summary>申请主题</summary>
        public static readonly Field Theme = FindByName("Theme");

        /// <summary>需求描述 </summary>
        public static readonly Field Describe = FindByName("Describe");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得样品申请字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>公司名称</summary>
        public const String ComName = "ComName";

        /// <summary>企业人数</summary>
        public const String ComPeople = "ComPeople";

        /// <summary>企业官网</summary>
        public const String Website = "Website";

        /// <summary>年营业额</summary>
        public const String Turnover = "Turnover";

        /// <summary>企业类型</summary>
        public const String ComType = "ComType";

        /// <summary>企业地址</summary>
        public const String Address = "Address";

        /// <summary>联系人</summary>
        public const String Linkman = "Linkman";

        /// <summary>职位</summary>
        public const String Position = "Position";

        /// <summary>电话</summary>
        public const String Phone = "Phone";

        /// <summary>QQ</summary>
        public const String QQ = "QQ";

        /// <summary>邮箱</summary>
        public const String Email = "Email";

        /// <summary>传真</summary>
        public const String Fax = "Fax";

        /// <summary>产品型号</summary>
        public const String ProModel = "ProModel";

        /// <summary>月需求量</summary>
        public const String Demand = "Demand";

        /// <summary>申请主题</summary>
        public const String Theme = "Theme";

        /// <summary>需求描述 </summary>
        public const String Describe = "Describe";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
