﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>在线客服</summary>
[Serializable]
[DataObject]
[Description("在线客服")]
[BindIndex("IX_DG_OnlineKeFu_OType", false, "OType")]
[BindTable("DG_OnlineKeFu", Description = "在线客服", ConnName = "DG", DbType = DatabaseType.None)]
public partial class OnlineKeFu : IOnlineKeFu, IEntity<IOnlineKeFu>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _LIds;
    /// <summary>语言Id集合</summary>
    [DisplayName("语言Id集合")]
    [Description("语言Id集合")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("LIds", "语言Id集合", "")]
    public String? LIds { get => _LIds; set { if (OnPropertyChanging("LIds", value)) { _LIds = value; OnPropertyChanged("LIds"); } } }

    private String? _OIds;
    /// <summary>所属平台集合。1为PC，2为移动端,多个以逗号分隔</summary>
    [DisplayName("所属平台集合")]
    [Description("所属平台集合。1为PC，2为移动端,多个以逗号分隔")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("OIds", "所属平台集合。1为PC，2为移动端,多个以逗号分隔", "")]
    public String? OIds { get => _OIds; set { if (OnPropertyChanging("OIds", value)) { _OIds = value; OnPropertyChanged("OIds"); } } }

    private String? _OName;
    /// <summary>客服名称</summary>
    [DisplayName("客服名称")]
    [Description("客服名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("OName", "客服名称", "")]
    public String? OName { get => _OName; set { if (OnPropertyChanging("OName", value)) { _OName = value; OnPropertyChanged("OName"); } } }

    private String? _ONumber;
    /// <summary>客服号码</summary>
    [DisplayName("客服号码")]
    [Description("客服号码")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ONumber", "客服号码", "")]
    public String? ONumber { get => _ONumber; set { if (OnPropertyChanging("ONumber", value)) { _ONumber = value; OnPropertyChanged("ONumber"); } } }

    private Int16 _OType;
    /// <summary>客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp</summary>
    [DisplayName("客服平台类型")]
    [Description("客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("OType", "客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp", "")]
    public Int16 OType { get => _OType; set { if (OnPropertyChanging("OType", value)) { _OType = value; OnPropertyChanged("OType"); } } }

    private Int32 _Location;
    /// <summary>客服所属平台,0为全部,1为PC,2为手机</summary>
    [DisplayName("客服所属平台")]
    [Description("客服所属平台,0为全部,1为PC,2为手机")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Location", "客服所属平台,0为全部,1为PC,2为手机", "")]
    public Int32 Location { get => _Location; set { if (OnPropertyChanging("Location", value)) { _Location = value; OnPropertyChanged("Location"); } } }

    private Int32 _Sort;
    /// <summary>排序</summary>
    [DisplayName("排序")]
    [Description("排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Sort", "排序", "")]
    public Int32 Sort { get => _Sort; set { if (OnPropertyChanging("Sort", value)) { _Sort = value; OnPropertyChanged("Sort"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IOnlineKeFu model)
    {
        Id = model.Id;
        LIds = model.LIds;
        OIds = model.OIds;
        OName = model.OName;
        ONumber = model.ONumber;
        OType = model.OType;
        Location = model.Location;
        Sort = model.Sort;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "LIds" => _LIds,
            "OIds" => _OIds,
            "OName" => _OName,
            "ONumber" => _ONumber,
            "OType" => _OType,
            "Location" => _Location,
            "Sort" => _Sort,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "LIds": _LIds = Convert.ToString(value); break;
                case "OIds": _OIds = Convert.ToString(value); break;
                case "OName": _OName = Convert.ToString(value); break;
                case "ONumber": _ONumber = Convert.ToString(value); break;
                case "OType": _OType = Convert.ToInt16(value); break;
                case "Location": _Location = value.ToInt(); break;
                case "Sort": _Sort = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得在线客服字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>语言Id集合</summary>
        public static readonly Field LIds = FindByName("LIds");

        /// <summary>所属平台集合。1为PC，2为移动端,多个以逗号分隔</summary>
        public static readonly Field OIds = FindByName("OIds");

        /// <summary>客服名称</summary>
        public static readonly Field OName = FindByName("OName");

        /// <summary>客服号码</summary>
        public static readonly Field ONumber = FindByName("ONumber");

        /// <summary>客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp</summary>
        public static readonly Field OType = FindByName("OType");

        /// <summary>客服所属平台,0为全部,1为PC,2为手机</summary>
        public static readonly Field Location = FindByName("Location");

        /// <summary>排序</summary>
        public static readonly Field Sort = FindByName("Sort");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得在线客服字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>语言Id集合</summary>
        public const String LIds = "LIds";

        /// <summary>所属平台集合。1为PC，2为移动端,多个以逗号分隔</summary>
        public const String OIds = "OIds";

        /// <summary>客服名称</summary>
        public const String OName = "OName";

        /// <summary>客服号码</summary>
        public const String ONumber = "ONumber";

        /// <summary>客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp</summary>
        public const String OType = "OType";

        /// <summary>客服所属平台,0为全部,1为PC,2为手机</summary>
        public const String Location = "Location";

        /// <summary>排序</summary>
        public const String Sort = "Sort";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
