﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>SEO多语言信息存放表</summary>
[Serializable]
[DataObject]
[Description("SEO多语言信息存放表")]
[BindIndex("IU_DG_SeoLan_SId_LId", true, "SId,LId")]
[BindTable("DG_SeoLan", Description = "SEO多语言信息存放表", ConnName = "DG", DbType = DatabaseType.None)]
public partial class SeoInfoLan : ISeoInfoLan, IEntity<ISeoInfoLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _SId;
    /// <summary>SeoId</summary>
    [DisplayName("SeoId")]
    [Description("SeoId")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("SId", "SeoId", "")]
    public Int32 SId { get => _SId; set { if (OnPropertyChanging("SId", value)) { _SId = value; OnPropertyChanged("SId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _SeoTitle;
    /// <summary>SEO标题</summary>
    [DisplayName("SEO标题")]
    [Description("SEO标题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("SeoTitle", "SEO标题", "", Master = true)]
    public String? SeoTitle { get => _SeoTitle; set { if (OnPropertyChanging("SeoTitle", value)) { _SeoTitle = value; OnPropertyChanged("SeoTitle"); } } }

    private String? _SeoKeywords;
    /// <summary>SEO关键词</summary>
    [DisplayName("SEO关键词")]
    [Description("SEO关键词")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("SeoKeywords", "SEO关键词", "text")]
    public String? SeoKeywords { get => _SeoKeywords; set { if (OnPropertyChanging("SeoKeywords", value)) { _SeoKeywords = value; OnPropertyChanged("SeoKeywords"); } } }

    private String? _SeoDescription;
    /// <summary>SEO描述</summary>
    [DisplayName("SEO描述")]
    [Description("SEO描述")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("SeoDescription", "SEO描述", "")]
    public String? SeoDescription { get => _SeoDescription; set { if (OnPropertyChanging("SeoDescription", value)) { _SeoDescription = value; OnPropertyChanged("SeoDescription"); } } }

    private String? _SeoType;
    /// <summary>类型</summary>
    [DisplayName("类型")]
    [Description("类型")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("SeoType", "类型", "")]
    public String? SeoType { get => _SeoType; set { if (OnPropertyChanging("SeoType", value)) { _SeoType = value; OnPropertyChanged("SeoType"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISeoInfoLan model)
    {
        Id = model.Id;
        SId = model.SId;
        LId = model.LId;
        SeoTitle = model.SeoTitle;
        SeoKeywords = model.SeoKeywords;
        SeoDescription = model.SeoDescription;
        SeoType = model.SeoType;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "SId" => _SId,
            "LId" => _LId,
            "SeoTitle" => _SeoTitle,
            "SeoKeywords" => _SeoKeywords,
            "SeoDescription" => _SeoDescription,
            "SeoType" => _SeoType,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "SId": _SId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "SeoTitle": _SeoTitle = Convert.ToString(value); break;
                case "SeoKeywords": _SeoKeywords = Convert.ToString(value); break;
                case "SeoDescription": _SeoDescription = Convert.ToString(value); break;
                case "SeoType": _SeoType = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static SeoInfoLan? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据SeoId、所属语言Id查找</summary>
    /// <param name="sId">SeoId</param>
    /// <param name="lId">所属语言Id</param>
    /// <returns>实体对象</returns>
    public static SeoInfoLan? FindBySIdAndLId(Int32 sId, Int32 lId)
    {
        if (sId < 0) return null;
        if (lId < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.SId == sId && e.LId == lId);

        return Find(_.SId == sId & _.LId == lId);
    }

    /// <summary>根据SeoId查找</summary>
    /// <param name="sId">SeoId</param>
    /// <returns>实体列表</returns>
    public static IList<SeoInfoLan> FindAllBySId(Int32 sId)
    {
        if (sId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.SId == sId);

        return FindAll(_.SId == sId);
    }
    #endregion

    #region 字段名
    /// <summary>取得SEO多语言信息存放表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>SeoId</summary>
        public static readonly Field SId = FindByName("SId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>SEO标题</summary>
        public static readonly Field SeoTitle = FindByName("SeoTitle");

        /// <summary>SEO关键词</summary>
        public static readonly Field SeoKeywords = FindByName("SeoKeywords");

        /// <summary>SEO描述</summary>
        public static readonly Field SeoDescription = FindByName("SeoDescription");

        /// <summary>类型</summary>
        public static readonly Field SeoType = FindByName("SeoType");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得SEO多语言信息存放表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>SeoId</summary>
        public const String SId = "SId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>SEO标题</summary>
        public const String SeoTitle = "SeoTitle";

        /// <summary>SEO关键词</summary>
        public const String SeoKeywords = "SeoKeywords";

        /// <summary>SEO描述</summary>
        public const String SeoDescription = "SeoDescription";

        /// <summary>类型</summary>
        public const String SeoType = "SeoType";
    }
    #endregion
}
