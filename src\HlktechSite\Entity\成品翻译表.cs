﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>成品翻译表</summary>
[Serializable]
[DataObject]
[Description("成品翻译表")]
[BindTable("DG_EndProductLan", Description = "成品翻译表", ConnName = "DG", DbType = DatabaseType.None)]
public partial class EndProductLan : IEndProductLan, IEntity<IEndProductLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>成品名称+规格名称</summary>
    [DisplayName("成品名称+规格名称")]
    [Description("成品名称+规格名称")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "成品名称+规格名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _AdvWord;
    /// <summary>成品广告词</summary>
    [DisplayName("成品广告词")]
    [Description("成品广告词")]
    [DataObjectField(false, false, true, 150)]
    [BindColumn("AdvWord", "成品广告词", "")]
    public String? AdvWord { get => _AdvWord; set { if (OnPropertyChanging("AdvWord", value)) { _AdvWord = value; OnPropertyChanged("AdvWord"); } } }

    private String? _Image;
    /// <summary>成品主图</summary>
    [DisplayName("成品主图")]
    [Description("成品主图")]
    [DataObjectField(false, false, true, 150)]
    [BindColumn("Image", "成品主图", "")]
    public String? Image { get => _Image; set { if (OnPropertyChanging("Image", value)) { _Image = value; OnPropertyChanged("Image"); } } }

    private String? _Content;
    /// <summary>成品内容</summary>
    [DisplayName("成品内容")]
    [Description("成品内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "成品内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _MobileContent;
    /// <summary>手机端成品描述</summary>
    [DisplayName("手机端成品描述")]
    [Description("手机端成品描述")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("MobileContent", "手机端成品描述", "text")]
    public String? MobileContent { get => _MobileContent; set { if (OnPropertyChanging("MobileContent", value)) { _MobileContent = value; OnPropertyChanged("MobileContent"); } } }

    private String? _Summary;
    /// <summary>简介</summary>
    [DisplayName("简介")]
    [Description("简介")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Summary", "简介", "")]
    public String? Summary { get => _Summary; set { if (OnPropertyChanging("Summary", value)) { _Summary = value; OnPropertyChanged("Summary"); } } }

    private String? _UsageScenarios;
    /// <summary>使用场景</summary>
    [DisplayName("使用场景")]
    [Description("使用场景")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("UsageScenarios", "使用场景", "")]
    public String? UsageScenarios { get => _UsageScenarios; set { if (OnPropertyChanging("UsageScenarios", value)) { _UsageScenarios = value; OnPropertyChanged("UsageScenarios"); } } }

    private String? _Specifications;
    /// <summary>参数规格</summary>
    [DisplayName("参数规格")]
    [Description("参数规格")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Specifications", "参数规格", "text")]
    public String? Specifications { get => _Specifications; set { if (OnPropertyChanging("Specifications", value)) { _Specifications = value; OnPropertyChanged("Specifications"); } } }

    private Boolean _Commend;
    /// <summary>成品推荐</summary>
    [DisplayName("成品推荐")]
    [Description("成品推荐")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Commend", "成品推荐", "")]
    public Boolean Commend { get => _Commend; set { if (OnPropertyChanging("Commend", value)) { _Commend = value; OnPropertyChanged("Commend"); } } }

    private Boolean _Shelf;
    /// <summary>成品是否上架</summary>
    [DisplayName("成品是否上架")]
    [Description("成品是否上架")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Shelf", "成品是否上架", "")]
    public Boolean Shelf { get => _Shelf; set { if (OnPropertyChanging("Shelf", value)) { _Shelf = value; OnPropertyChanged("Shelf"); } } }

    private Int32 _Sort;
    /// <summary>排序</summary>
    [DisplayName("排序")]
    [Description("排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Sort", "排序", "")]
    public Int32 Sort { get => _Sort; set { if (OnPropertyChanging("Sort", value)) { _Sort = value; OnPropertyChanged("Sort"); } } }

    private Int32 _GId;
    /// <summary>成品表Id</summary>
    [DisplayName("成品表Id")]
    [Description("成品表Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("GId", "成品表Id", "")]
    public Int32 GId { get => _GId; set { if (OnPropertyChanging("GId", value)) { _GId = value; OnPropertyChanged("GId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IEndProductLan model)
    {
        Id = model.Id;
        Name = model.Name;
        AdvWord = model.AdvWord;
        Image = model.Image;
        Content = model.Content;
        MobileContent = model.MobileContent;
        Summary = model.Summary;
        UsageScenarios = model.UsageScenarios;
        Specifications = model.Specifications;
        Commend = model.Commend;
        Shelf = model.Shelf;
        Sort = model.Sort;
        GId = model.GId;
        LId = model.LId;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "AdvWord" => _AdvWord,
            "Image" => _Image,
            "Content" => _Content,
            "MobileContent" => _MobileContent,
            "Summary" => _Summary,
            "UsageScenarios" => _UsageScenarios,
            "Specifications" => _Specifications,
            "Commend" => _Commend,
            "Shelf" => _Shelf,
            "Sort" => _Sort,
            "GId" => _GId,
            "LId" => _LId,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "AdvWord": _AdvWord = Convert.ToString(value); break;
                case "Image": _Image = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "MobileContent": _MobileContent = Convert.ToString(value); break;
                case "Summary": _Summary = Convert.ToString(value); break;
                case "UsageScenarios": _UsageScenarios = Convert.ToString(value); break;
                case "Specifications": _Specifications = Convert.ToString(value); break;
                case "Commend": _Commend = value.ToBoolean(); break;
                case "Shelf": _Shelf = value.ToBoolean(); break;
                case "Sort": _Sort = value.ToInt(); break;
                case "GId": _GId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得成品翻译表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>成品名称+规格名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>成品广告词</summary>
        public static readonly Field AdvWord = FindByName("AdvWord");

        /// <summary>成品主图</summary>
        public static readonly Field Image = FindByName("Image");

        /// <summary>成品内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>手机端成品描述</summary>
        public static readonly Field MobileContent = FindByName("MobileContent");

        /// <summary>简介</summary>
        public static readonly Field Summary = FindByName("Summary");

        /// <summary>使用场景</summary>
        public static readonly Field UsageScenarios = FindByName("UsageScenarios");

        /// <summary>参数规格</summary>
        public static readonly Field Specifications = FindByName("Specifications");

        /// <summary>成品推荐</summary>
        public static readonly Field Commend = FindByName("Commend");

        /// <summary>成品是否上架</summary>
        public static readonly Field Shelf = FindByName("Shelf");

        /// <summary>排序</summary>
        public static readonly Field Sort = FindByName("Sort");

        /// <summary>成品表Id</summary>
        public static readonly Field GId = FindByName("GId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得成品翻译表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>成品名称+规格名称</summary>
        public const String Name = "Name";

        /// <summary>成品广告词</summary>
        public const String AdvWord = "AdvWord";

        /// <summary>成品主图</summary>
        public const String Image = "Image";

        /// <summary>成品内容</summary>
        public const String Content = "Content";

        /// <summary>手机端成品描述</summary>
        public const String MobileContent = "MobileContent";

        /// <summary>简介</summary>
        public const String Summary = "Summary";

        /// <summary>使用场景</summary>
        public const String UsageScenarios = "UsageScenarios";

        /// <summary>参数规格</summary>
        public const String Specifications = "Specifications";

        /// <summary>成品推荐</summary>
        public const String Commend = "Commend";

        /// <summary>成品是否上架</summary>
        public const String Shelf = "Shelf";

        /// <summary>排序</summary>
        public const String Sort = "Sort";

        /// <summary>成品表Id</summary>
        public const String GId = "GId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
