﻿@model HlktechSite.Entity.OnlineKeFu
<style asp-location="true">
    .page {
        min-height: 410px;
        height: 100%;
    }

    .html {
        background-color: #FFF;
    }

    .rowform {
        overflow: inherit;
    }
</style>
<div class="page">
    <form id="ProductModel_form" method="post" action='' name="adminForm">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required w120"><label class="validation" for="nav_title">@T("名称")</label></td>
                    <td><input type="text" name="OName" id="OName" value="@Model.OName" class="w200"></td>
                    <td></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("所有语言")</td>
                    <td class="vatop rowform">
                        <div id="demo1" style=" width: 280px;float: left;"></div>
                        <input type="hidden" id="LIds" name="LIds" value="@Model.LIds" />
                    </td>
                </tr>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required w120"><label class="validation" for="nav_title">@T("号码")</label></td>
                    <td><input type="text" name="ONumber" id="ONumber" value="@Model.ONumber" class="w200"></td>
                    <td></td>
                </tr>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required w120">@T("客服平台类型")</td>
                    <td class="vatop rowform">
                        <select name="OType" id="OType">
                            <!option value="0" @(Model.OType.ToString() == "0" ? "selected" : "")>QQ</!option>
                            <!option value="1" @(Model.OType.ToString() == "1" ? "selected" : "")>@T("阿里旺旺")</!option>
                            <!option value="2" @(Model.OType.ToString() == "2" ? "selected" : "")>Skype</!option>
                            <!option value="3" @(Model.OType.ToString() == "3" ? "selected" : "")>Whatsapp</!option>
                        </select>
                    </td>
                </tr>
                 <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required w120">@T("客服所属设备平台")</td>
                    <td class="vatop rowform">
                        <select name="Location" id="Location">
                            <!option value="0" @(Model.Location==0 ? "selected" : "")>@T("全部")</!option>
                            <!option value="1" @(Model.Location==1 ? "selected" : "")>@T("仅PC端")</!option>
                            <!option value="2" @(Model.Location==2 ? "selected" : "")>@T("仅手机端")</!option>
                            <!option value="3" @(Model.Location==3 ? "selected" : "")>@T("关闭")</!option>
                        </select>
                    </td>
                </tr>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required w120"><label class="validation" for="nav_title">@T("排序")</label></td>
                    <td><input type="text" name="Sort" id="Sort" value="@Model.Sort" class="w200"></td>
                    <td></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="2"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script src="~/static/admin/js/xm-select.js"></script>
<script type="text/javascript">
    var demo1 = xmSelect.render({
        el: '#demo1',
        paging: true,
        pageSize: 10,
        filterable: true,
        pageEmptyShow: false,
        clickClose: true,
        data: $.parseJSON('@Html.Raw(ViewBag.List)'),
        on: function (data) {
            if (data.arr.length > 0) {
                var text = "";
                for (var i = 0; i < data.arr.length; i++) {
                    text += ","+data.arr[i].value;
                }
                $("#LIds").val(text);
            } else {
                $("#LIds").val("");
            }
        }
    });
</script>