﻿//using DG.Npoi.Attributes;

//namespace HlktechSite.DTO
//{
//    [Sheet(SheetName = "操作日志", AutoColumnWidthEnabled = true)]
//    public class LogExport
//    {
//        /// <summary> 
//        /// 日志Id
//        /// </summary> 
//        [Column(Title = "日志Id")]
//        public string Id { get; set; }

//        /// <summary> 
//        /// 类别
//        /// </summary> 
//        [Column(Title = "类别")]
//        public string Category { get; set; }

//        /// <summary> 
//        /// 操作
//        /// </summary> 
//        [Column(Title = "操作")]
//        public string Action { get; set; }

//        /// <summary> 
//        /// 是否成功
//        /// </summary> 
//        [Column(Title = "是否成功")]
//        public bool Success { get; set; }

//        /// <summary> 
//        /// 详细信息
//        /// </summary> 
//        [Column(Title = "详细信息")]
//        public string Remark { get; set; }

//        /// <summary> 
//        /// 用户名
//        /// </summary> 
//        [Column(Title = "用户名")]
//        public string UserName { get; set; }

//        /// <summary> 
//        /// IP 地址
//        /// </summary> 
//        [Column(Title = "IP 地址")]
//        public string CreateIP { get; set; }

//        /// <summary> 
//        /// 物理地址
//        /// </summary> 
//        [Column(Title = "物理地址")]
//        public string PhysicalAddress { get; set; }

//        /// <summary> 
//        /// 创建时间
//        /// </summary> 
//        [Column(Title = "创建时间")]
//        public string CreateTime { get; set; }

//    }
//}
