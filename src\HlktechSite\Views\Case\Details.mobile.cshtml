﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";
    
    var Solution = Model.SolutionModel as Case;
    var Titles = ViewBag.Titles as String;

    Html.AppendTitleParts(Solution.Name + DG.Setting.Current.PageTitleSeparator + Titles + DG.Setting.Current.PageTitleSeparator + "Hi_Link" + DG.Setting.Current.PageTitleSeparator + "<PERSON><PERSON>sky");
}

<h2 class="journalism-h2">@Solution.Name</h2>

<p class="journalism-msg">@T("时间")：@Solution.CreateTime.ToString("yyyy/MM/dd HH:mm")    @("作者")：@Solution.CreateUser</p>

<div class="journalism-detail-con">
    @Html.Raw(Solution.Content)
</div>

<p class="flip-over">
    @if (Model.previous != null)
    {
        <span>@T("上一篇")：<a href="@Url.DGAction("Details",new { Id=Model.previous.Id})">@Model.previous.Name</a></span>
    }
    else
    {
        <span>@T("上一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
    }
    @if (Model.Nex != null)
    {
        <span>@T("下一篇")：<a href="@Url.DGAction("Details",new { Id=Model.Nex.Id})">@Model.Nex.Name</a></span>
    }
    else
    {
        <span>@T("下一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
    }
</p>