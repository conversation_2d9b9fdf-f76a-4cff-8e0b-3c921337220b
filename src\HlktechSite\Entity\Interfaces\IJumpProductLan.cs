﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>成品跳转表多语言信息存放表</summary>
public partial interface IJumpProductLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>成品Id</summary>
    Int32 JId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>产品名称</summary>
    String? Name { get; set; }

    /// <summary>推文链接</summary>
    String? InfoUrl { get; set; }

    /// <summary>广告词</summary>
    String? AdWord { get; set; }
    #endregion
}
