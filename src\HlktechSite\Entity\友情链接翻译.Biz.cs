using DG.Entity;

using DH.Helpers;

using NewLife;
using NewLife.Data;

using Pek;

using XCode;

namespace HlktechSite.Entity {
    /// <summary>友情链接翻译</summary>
    public partial class FriendLinksLan : CubeEntityBase<FriendLinksLan>
    {
        #region 对象操作
        static FriendLinksLan()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(FId));

            // 过滤器 UserModule、TimeModule、IPModule
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正

            // 检查唯一索引
            // CheckExist(isNew, nameof(FId), nameof(LId));
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化FriendLinksLan[友情链接翻译]数据……");

        //    var entity = new FriendLinksLan();
        //    entity.Id = 0;
        //    entity.FId = 0;
        //    entity.LId = 0;
        //    entity.Title = "abc";
        //    entity.FType = 0;
        //    entity.Url = "abc";
        //    entity.Pic = "abc";
        //    entity.Sort = 0;
        //    entity.Enabled = 0;
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化FriendLinksLan[友情链接翻译]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static FriendLinksLan FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据友情链接Id、所属语言Id查找</summary>
        /// <param name="fId">友情链接Id</param>
        /// <param name="lId">所属语言Id</param>
        /// <returns>实体对象</returns>
        public static FriendLinksLan FindByFIdAndLId(Int32 fId, Int32 lId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.FId == fId && e.LId == lId);

            return Find(_.FId == fId & _.LId == lId);
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="fId">友情链接Id</param>
        /// <param name="lId">所属语言Id</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<FriendLinksLan> Search(Int32 fId, Int32 lId, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (fId >= 0) exp &= _.FId == fId;
            if (lId >= 0) exp &= _.LId == lId;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.Url.Contains(key) | _.Pic.Contains(key);

            return FindAll(exp, page);
        }

        /// <summary>
        /// 根据友情链接ID集合删除数据
        /// </summary>
        /// <param name="Ids">ID集合</param>
        public static void DelByFIds(String Ids)
        {
            if (Delete(_.FId.In(Ids.Trim(','))) > 0)
                Meta.Cache.Clear("");
        }

        /// <summary>
        /// 通过角色Id和语言Id获取翻译数据
        /// </summary>
        /// <param name="FId">友情链接Id</param>
        /// <param name="lId">语言Id</param>
        /// <param name="IsGetDefault">是否获取默认数据</param>
        /// <returns></returns>
        public static (String Title, String Pic, String Url, short FType, int Sort) FindByFIdAndLId(Int32 FId, Int32 lId, Boolean IsGetDefault = true)
        {
            if (FId <= 0 || lId <= 0) return ("", "", "", 0, 0);

            if (Meta.Session.Count < 1000)
            {
                var model = Meta.Cache.Find(e => e.FId == FId && e.LId == lId);
                if (model == null)
                {
                    if (!IsGetDefault)
                    {
                        return ("", "", "", 0, 0);
                    }
                    else
                    {
                        return FindNameAndRemark(FId, model);
                    }
                }
                return (model.Name, UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), model.Pic.IsNotNullAndWhiteSpace() ? model.Pic : ""), model.Url, model.FType, model.Sort);
            }
            var exp = new WhereExpression();
            exp = _.FId == FId & _.LId == lId;

            var m = Find(exp);
            if (m == null)
            {
                if (!IsGetDefault)
                {
                    return ("", "", "", 0, 0);
                }
                else
                {
                    return FindNameAndRemark(FId, m);
                }
            }
            return (m.Name, UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), m.Pic.IsNotNullAndWhiteSpace() ? m.Pic : ""), m.Url, m.FType, m.Sort);
        }

        /// <summary>
        /// 获取翻译数据
        /// </summary>
        /// <param name="FId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private static (String Title, String Pic, String Url, short FType, int Sort) FindNameAndRemark(Int32 FId, FriendLinksLan model)
        {
            var r = FriendLinks.FindById(FId);

            if (model == null)
            {
                return (r.Name, UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), r.Pic.IsNotNullAndWhiteSpace() ? r.Pic : ""), r.Url, r.FType, r.Sort);
            }
            else
            {
                var Title = model.Name.IsNullOrWhiteSpace() ? r.Name : model.Name;
                var Pic = model.Pic.IsNullOrWhiteSpace() ? r.Pic : model.Pic;
                var Url = model.Url.IsNullOrWhiteSpace() ? r.Url : model.Url;
                var FType = model.FType;
                var Sort = model.Sort;
                return (Title, UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Pic.IsNotNullAndWhiteSpace() ? Pic : ""), Url, FType, Sort);
            }
        }

        /// <summary>
        /// 根据FId获取所属语言数据
        /// </summary>
        /// <param name="FId">友情链接Id</param>
        /// <returns></returns>
        public static IList<FriendLinksLan> FindAllByFId(Int32 FId)
        {
            if (FId <= 0) return new List<FriendLinksLan>();

            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.FId == FId);

            return FindAll(_.FId == FId);
        }


        // Select Count(Id) as Id,Category From FriendLinksLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<FriendLinksLan> _CategoryCache = new FieldCache<FriendLinksLan>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}