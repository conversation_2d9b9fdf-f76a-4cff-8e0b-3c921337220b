@using NewLife.Collections;
@{
    Layout = "_Root.Head";

    Html.AppendCssFileParts(ResourceLocation.Head, "/static/plugins/bootstrap/css/bootstrap.min.css");

    Html.AppendHeadCustomParts("<meta name=\"applicable-device\" content=\"mobile\">");

    Html.AppendCssFileParts("~/css/mobile/common.css");

    Html.AppendScriptParts(ResourceLocation.Head, "/static/js/jquery-1.12.2.min.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/md5.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/static/plugins/layer/layer.js");
    var SiteSettings = SiteSettingInfo.SiteSettings;
    var pages = new PageParameter()
    {
        PageIndex = 1,
        PageSize = 6,
        OrderBy = "DisplayOrder",
    };
    var ModelList = SolutionCategory.Searchs("", pages);
    var returnUrl = WebHelper2.GetRawUrlStr(Context.Request);
    var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

    var cdn = CDN.GetCDN();

    var localizationSettings = LocalizationSettings.Current;
}

<nav style="height:51px;position:relative;z-index: 700;">
    <div class="yemei" style="position: fixed; left: 0; right: 0; top: 0;box-shadow: 0px 2px 8px 0px rgba(177, 173, 173, 0.2);">
        <p style="margin:0px;height:51px;vertical-align:middle;background-color:#fff;text-align:right;">
            <a href="@Url.DGAction("Index", "CubeHome", IsHtml:true)"><img src="@(cdn)/images/logo.png" class="logo" style="margin-top: 17px;float:left; max-width: 64px; margin-left: 4%;"></a>
            <a id="language" class="search-btn" aria-expanded="false" href="javascript:;"><img src="@(cdn)/images/changelanguage.png" alt="Alternate Text" /></a>
            <a id="search" class="search-btn" aria-expanded="false" href="@Url.DGAction("Index","Search")"><img src="@(cdn)/images/mobile-search.png" alt="Alternate Text" /></a>
            <button type="button" id="cut" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">@T("切换导航")</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
        </p>
    </div>
    <ul class="nav nav-pills" style="display:none;box-shadow:rgba(177, 173, 173, 0.3) 4px 2px 4px 1px;position: fixed; left: 0px; right: 0px; top: 51px;background-color: #fff;">
        <li role="presentation"><a class="title1" href="@Url.DGAction("Index","CubeHome", IsHtml:true)">@T("首页")</a></li>
        <li role="presentation"><a class="title2" href="@Url.DGAction("Index","Product", IsHtml:true)">@T("产品中心")</a></li>
        <li role="presentation"><a class="title3" href="@Url.DGAction("Index","Solution", IsHtml:true)">@T("解决方案")</a></li>
        <li role="presentation"><a class="title4" href="@Url.DGAction("Index","Case", IsHtml:true)">@T("客户案例")</a></li>
        <li role="presentation"><a class="title5" href="@Url.DGAction("Index","Agent", IsHtml:true)">@T("代理招商")</a></li>
        <li role="presentation"><a class="title6" href="@Url.DGAction("Index","Contact", IsHtml:true)">@T("联系我们")</a></li>
        <li role="presentation"><a class="title7" href="@Url.DGAction("AboutUs","CubeHome", IsHtml:true)">@T("关于我们1")</a></li>
        <li role="presentation"><a class="title8" href="//shop.hlktech.com/mobile">@T("在线购买")</a></li>
        <li role="presentation"><a class="title9" href="@Url.DGAction("Index","Journalism", IsHtml:true)">@T("新闻资讯")</a></li>
        <li role="presentation"><a class="title10" href="//ask.hlktech.com">@T("技术支持中心")</a></li>
    </ul>
    <ul class="changelanguage" style="display:none;box-shadow:rgba(177, 173, 173, 0.3) 4px 2px 4px 1px;position: fixed; left: 0px; right: 0px; top: 51px;background-color: #fff;">
        @foreach (var lang in Languagelist)
        {
            @* <li role="presentation"><a class="title1" href='@Url.RouteUrl("ChangeLanguage", new { langid = lang.Id, returnUrl })' title="@lang.Name">@lang.DisplayName</a></li> *@

            <li role="presentation"><a class="title1" href='/changeLanguage/@(lang.Id)?returnUrl=@(System.Web.HttpUtility.UrlEncode(returnUrl, System.Text.Encoding.UTF8))'>@lang.DisplayName</a></li>
        }
    </ul>
</nav>


@RenderBody()

<div class="com-footer">
    <p class="com-footer-top">
        <img src="@(cdn)/images/logo.png" alt="Alternate Text" />
        <span>@T("深圳市海凌科电子有限公司1")</span>
    </p>
    <div class="com-footer-menu">
        <dl>
            <dd>
                <ul>
                    <li class="com-footer-menu-title">@T("关于我们1")</li>
                    <li><a href="//h.hlktech.com/mobile/download">@T("下载中心")</a></li>
                    <li><a href="@Url.DGAction("index","Contact")">@T("联系我们")</a></li>
                    <li><a href="//h.hlktech.com/search?SearchType=2">@T("开放工单")</a></li>
                    <li><a href="//h.hlktech.com/UserCenter/AddOrderIndex">@T("提交工单")</a></li>
                    <li><a href="//h.hlktech.com/UserCenter/AddOrderIndex">@T("意见反馈")</a></li>
                    <li><a href="@Url.DGAction("Index","Case")">@T("应用场景")</a></li>
                </ul>
            </dd>
            <dd>
                <ul>
                    <li class="com-footer-menu-title">@T("产品服务1")</li>
                    @*<li><a>@T("智能硬件开发")</a></li>
                        <li><a>@T("物联网开发")</a></li>
                        <li><a>@T("大数据分析")</a></li>
                        <li><a>@T("软件开发")</a></li>*@
                    <li><a href="//h.hlktech.com/mobile/download">@T("资料下载")</a></li>
                    <li><a href="@Url.DGAction("Index","Apply")">@T("定制申请")</a></li>
                </ul>
            </dd>
            <dd>
                <ul>
                    <li class="com-footer-menu-title">@T("合作伙伴")</li>
                    @{
                        var FriendLinksList = FriendLinks.Searchs(-1, "", pages).Select(x => new FriendLinks { Url = x.Url, Name = localizationSettings.IsEnable ? FriendLinksLan.FindByFIdAndLId(x.Id, language.Id)?.Name : x.Name }); ;
                        foreach (var item in FriendLinksList)
                        {
                            <li><a href="@item.Url">@item.Name</a></li>
                        }
                    }
                </ul>
            </dd>
            <dd>
                <ul>
                    <li class="com-footer-menu-title">@T("解决方案")</li>
                    @foreach (var item in ModelList)
                    {
                        <li><a href="@Url.DGAction("Index","Solution",new { CID=item.Id})">@item.Name</a></li>
                    }
                </ul>
            </dd>
        </dl>
    </div>
    <div class="com-footer-contact">
        <h2>@T("联系我们")</h2>
        <p>@T("电话") : 0755-23152658；0755-83575196；4008850221；</p>
        <p>@T("传真") : 0755-83575189</p>
        <p>QQ : 2851395231 2851395234</p>
        <p>@T("地址 : 深圳龙华民治留仙大道24号彩悦大厦西大门三楼")</p>
        <div style="height: 20px;"></div>
        <p>
            @T("武汉分公司： 武汉极思灵创科技有限公司")
        </p>
        <p>
            @T("地址： 湖北省武汉市洪山区街道口樱花大厦A座503")
        </p>
        <p>@T("电话"): 027-87222329；4008850221；</p>
        <hr>
        <p style="text-align:center;">
            @T("版权所有：深圳海凌科电子科技有限公司")
        </p>
        @T("备案号2")
        <p style="text-align:center;">
            <img src="@(cdn)/@SiteSettingInfo.SiteSettings.SiteLogoWx" alt="Alternate Text" />
        </p>
    </div>
</div>
<div id="backtop" class="fixbtn backtop">
    <img class="icon" src="@(cdn)/images/rocket.png" />
</div>
<div id="kefubtn" class="fixbtn kefubtn">
    <img class="icon" src="@(cdn)/images/kffloat.png" />
    @* 图标地址 https://www.iconfont.cn/collections/detail?spm=a313x.7781069.0.da5a778a4&cid=18745 *@
    <div class="panel" style="display:none">
        @await Component.InvokeAsync("KF", new { LId = language.Id,isMobile = true})
    </div>
</div>
<script type="text/javascript">
    $("#cut").click(function () {
        $(".nav-pills").slideToggle(200);
    });

    $(document).mouseup(function (e) {
        var _con = $('#cut');
        var _con2 = $('.nav-pills li');
        if (!_con.is(e.target) && _con.has(e.target).length === 0 && !_con2.is(e.target) && _con2.has(e.target).length === 0) {
            $(".nav-pills").slideUp(200);
        }
        _con = $('#language');
        _con2 = $('.changelanguage li');
        if (!_con.is(e.target) && _con.has(e.target).length === 0 && !_con2.is(e.target) && _con2.has(e.target).length === 0) {
            $(".changelanguage").slideUp(200);
        }
    });

    $(window).scroll(function () {
        if ($(document).scrollTop() > 0) {
            $("#backtop").css("opacity", "1");
            $("#backtop").css("display", "block");
        } else {
            $("#backtop").fadeOut(500);
        }
    });
    
    $("#kefubtn").click(function () {
        $("#kefubtn .panel").slideToggle(200);
    });
    //点击页面其他部分 关闭#kefubtn .panel
    $(document).click(function (e) {
        var _con = $('#kefubtn');
        var _con2 = $('#kefubtn .panel');
        if (!_con.is(e.target) && _con.has(e.target).length === 0 && !_con2.is(e.target) && _con2.has(e.target).length === 0) {
            $("#kefubtn .panel").slideUp(200);
        }
    });

    $("#backtop").click(function () {
        $('html,body').animate({ scrollTop: '0px' }, 500);
    });
    $("#language").click(() => {
        $(".changelanguage").slideToggle(200);
    })
</script>