<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<LangVersion>latest</LangVersion>
		<TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>8afefab6-8771-4dce-aa69-13322d265bcf</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<AssemblyTitle>海凌科官网</AssemblyTitle>
		<Description>武汉极思灵创制作的海凌科官网系统</Description>
		<Authors>丁川</Authors>
		<Company>武汉极思灵创科技有限公司</Company>
		<Copyright>版权所有(C) 武汉极思灵创科技有限公司 2019-2025</Copyright>
		<Version>1.0.2020.0830</Version>
		<FileVersion>1.0.2020.0830</FileVersion>
		<AssemblyVersion>1.0.*</AssemblyVersion>
		<Deterministic>false</Deterministic>
		<OutputPath>..\..\BinWeb</OutputPath>
		<DebugType>pdbonly</DebugType>
		<Optimize>true</Optimize>
		<DefineConstants>TRACE</DefineConstants>
		<GenerateRuntimeConfigDevFile>true</GenerateRuntimeConfigDevFile>
		<SatelliteResourceLanguages>en</SatelliteResourceLanguages>
		<!--允许你指定要在生成和发布过程中为哪些语言保留附属资源程序集-->

		<!--混合打包，如启用则下面的混合打包规则也要启用-->
		<!--<RazorCompileOnBuild>true</RazorCompileOnBuild>
        <RazorCompileOnPublish>true</RazorCompileOnPublish>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <PreserveCompilationReferences>true</PreserveCompilationReferences>-->

		<!--不打包模板文件-->
		<RazorCompileOnBuild>false</RazorCompileOnBuild>
		<MvcRazorCompileOnPublish>false</MvcRazorCompileOnPublish>

		<!--将此参数设置为true以获取从NuGet缓存复制到项目输出的dll。-->
		<CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>

		<NoWarn>$(NoWarn);1591</NoWarn>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)'=='Debug'">
		<DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
		<DebugType>full</DebugType>
		<DebugSymbols>true</DebugSymbols>
	</PropertyGroup>

	<ItemGroup>
		<Content Update="wwwroot\**\*.*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<!--打包模板文件夹规则，混合编译，部分预编译，部分动态编译，此处如没有设置规则，则动态编译、混合编译无效-->
	<!--<ItemGroup>
    <MvcRazorFilesToCompile Include="Views\**\*.cshtml;EmailTemplates\**\*.cshtml" Exclude="wwwroot\themes\**\*.cshtml;" />
  </ItemGroup>-->

	<ItemGroup>
		<!-- 我们复制整个\App_Data目录。 但是我们忽略了JSON文件和数据保护密钥  -->
		<Content Include="App_Data\**" CopyToPublishDirectory="PreserveNewest" Exclude="App_Data\*.json" />
		<Content Remove="App_Data\*.json" />
		<Content Update="App_Data\DataProtectionKeys\*.xml" CopyToPublishDirectory="Never" />

		<Compile Remove="Plugins\**;Themes\**" />
		<Content Remove="Plugins\**;Themes\**" />
		<EmbeddedResource Remove="Plugins\**;Themes\**" />
		<None Remove="Plugins\**;Themes\**" />

		<Content Include="Plugins\bin\placeholder.txt" CopyToPublishDirectory="PreserveNewest" />
		<None Include="Plugins\**" CopyToPublishDirectory="PreserveNewest" />

		<Content Include="Themes\**" CopyToPublishDirectory="PreserveNewest" Exclude="Themes\**\*.config;Themes\**\*.cshtml;Themes\**\*.json" />
		<None Include="Themes\**" CopyToPublishDirectory="PreserveNewest" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="DH.NCore" Version="4.11.2025.331-beta0808" />
		<PackageReference Include="DH.SLazyCaptcha" Version="4.0.2025.323-beta1144" />
		<PackageReference Include="DH.NRedis.Extensions" Version="4.11.2025.329-beta0412" />
		<PackageReference Include="DH.SignalR" Version="4.11.2025.323-beta1229" />
		<PackageReference Include="DG.DSCube" Version="8.9.2025.3310070" />
		<PackageReference Include="DG.Utils" Version="8.9.2025.3310070" />
		<PackageReference Include="DG.Web.Framework" Version="8.9.2025.3310070" />
		<PackageReference Include="DH.SearchEngine" Version="4.0.2025.33100068" />
		<PackageReference Include="Jering.Javascript.NodeJS" Version="7.0.0" />
		<PackageReference Include="Grpc.AspNetCore" Version="2.70.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
		<PackageReference Include="DH.NStardust" Version="4.11.2025.314-beta1133" />
		<PackageReference Include="DH.NAgent" Version="4.11.2025.331-beta0327" />
        <PackageReference Include="DG.QiNiu.Extensions" Version="8.9.2025.3310070" />
		<PackageReference Include="DH.WebHook" Version="4.0.2025.33100068" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
	</ItemGroup>

	<ItemGroup>
		<!-- 此设置解决了vs2019中websdk中此更新引起的问题
    https://github.com/aspnet/websdk/commit/7e6b193ddcf1eec5c0a88a9748c626775555273e#diff-edf5a48ed0d4aa5a4289cb857bf46a04
    因此，我们恢复了标准配置行为（没有副本到输出目录）
     为了避免在发布过程中出现“ Duplicate dll”错误。
     我们还可以根据以下条件使用“ ExcludeConfigFilesFromBuildOutput” https://github.com/aspnet/AspNetCore/issues/14017 -->
		<Content Update="**\*.config;**\*.json" CopyToOutputDirectory="Never" CopyToPublishDirectory="PreserveNewest" />
	</ItemGroup>

	<ItemGroup>
		<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
	</ItemGroup>

	<ItemGroup>
		<Protobuf Include="GRPC\Protos\greet.proto" GrpcServices="Server" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="Settings\*.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <None Update="Run.cmd">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
	<ItemGroup>
	  <PackageReference Update="Microsoft.SourceLink.GitHub" Version="8.0.0" />
	</ItemGroup>

  <PropertyGroup>
    <ServerGarbageCollection>false</ServerGarbageCollection>
    <!--- ServerGarbageCollection ： 服务器垃圾收集 ：不会让内存无限增长 -->
    <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
    <!--- ServerGarbageCollection ： 并发垃圾收集 ：不会让内存无限增长 -->
  </PropertyGroup>

</Project>
