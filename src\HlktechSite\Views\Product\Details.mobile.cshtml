﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    Html.AppendCssFileParts("~/css/mobile/prodetails.css");
    Html.AppendCssFileParts("~/css/mobile/swiper-wrapper.css");
    Html.AppendCssFileParts("~/css/mobile/summarize.css");

    Html.AppendScriptParts(ResourceLocation.Head, "/js/swiper.min.js");

    var Product = Model.Product as Goods;
    var Titles = ViewBag.Titles as String;

    Html.AppendTitleParts(Product.ProductModelName + DG.Setting.Current.PageTitleSeparator + Titles + DG.Setting.Current.PageTitleSeparator + Product.Name + DG.Setting.Current.PageTitleSeparator + "Hi_Link");

    var cdn = CDN.GetCDN();
    var ShopUrl = Model.ShopUrl as List<String>;
    var TaoBaoUrl1 = Model.TaoBaoUrl1 as List<String>;
}
<div class="swiper-container" data-space-between='10' data-pagination='.swiper-pagination' data-autoplay="1000">
    <div class="swiper-wrapper">
        @foreach (var item in Model.ImgList as IEnumerable<HlktechSite.Entity.GoodsImages>)
        {
            <div class="swiper-slide"><img src="@item.Url" alt=""></div>
        }
    </div>
    @{
        var count = (Model.ImgList as IEnumerable<HlktechSite.Entity.GoodsImages>).Count();
    }
    <span class="subscript"><i>i</i>/@(count++)</span>
</div>

<div class="pro-detail-msg">
    <h2>@Model.Product.Name</h2>
    <i>@Model.Product.AdvWord</i>
    <i>@T("浏览次数") : <b>@Model.Product.Clicks</b>@T("次")</i>
    <p>@Model.Product.Summary</p>
    <div>
        <a href="@Url.DGAction("Index","Apply")">@T("样机申请")</a>
        <a class="data-download" href="#c-id3">@T("资料下载")</a>
        <a href="//ask.hlktech.com" target="_blank">@T("技术支持")</a>
        @if (Product.MobileGouUrl.IsNullOrEmpty())
        {
            <a class="tobuy">@T("在线购买")</a>
        }
        else
        {
            <a href="@Product.MobileGouUrl" class="tobuy">@T("在线购买")</a>
        }
    </div>
</div>

<p class="pro-type-menu">
    <a c-id="1" class="selected" href="javascript:;">@T("概述特点")</a>
    <a c-id="2" href="javascript:;">@T("参数规格")</a>
    <a c-id="3" href="javascript:;">@T("资料下载")</a>
    <a c-id="4" href="javascript:;">@T("知识问答")</a>
    <a c-id="5" href="javascript:;">@T("解决方案")</a>
    <a c-id="6" href="javascript:;">@T("购买渠道")</a>
</p>

<div class="pro-content">
    <div id="c-id1" style="display:block">
        @Html.Raw(Model.Product.MobileContent)
    </div>
    <div id="c-id2">
        @Html.Raw(Model.Product.Specifications)
    </div>
    <div id="c-id3">
        <ul>
            <li>
                <span>@T("开发资料")</span>
                <p>

                    @foreach (var item in Model.Development)
                    {
                        <i>
                            @item.resource_name
                            <a href="http://h.hlktech.com@(item.resource_url)"><img src="@(cdn)/images/down.png" /></a>
                        </i>
                    }
                </p>
            </li>
            <li>
                <span>@T("软件应用")</span>
                <p>
                    @foreach (var item in Model.Application)
                    {
                        <i>
                            @item.resource_name
                            <a href="http://h.hlktech.com@(item.resource_url)"><img src="@(cdn)/images/down.png" /></a>
                        </i>
                    }
                </p>
            </li>
            <li>
                <span>@T("通用软件")</span>
                <p>
                    @foreach (var item in Model.GeneralSoftware)
                    {
                        <i>
                            @item.resource_name
                            <a href="http://h.hlktech.com@(item.resource_url)"><img src="@(cdn)/images/down.png" /></a>
                        </i>
                    }
                </p>
            </li>
            <li>
                <span>@T("常见问题")</span>
                <p>
                    @*@foreach (var item in Model.Application)
                        {
                        <i>
                        @item.resource_name
                        <a href="@item.resource_url"><img src="@(cdn)/images/down.png" /></a>
                        </i>
                        }*@
                </p>
            </li>
        </ul>
    </div>
    <div id="c-id4">
        <ul>
            @foreach (var item in Model.knowledgeList)
            {
                <li><a href="@("http://h.hlktech.com/Knowledge/KnowledgeDetails/"+(item.Id)+".html")"><p>@item.Name</p><span><img src="@(cdn)/images/time.png" alt="Alternate Text" />@item.CreateTime</span><i>@T("查看详情")></i></a></li>
            }
        </ul>
    </div>
    <div id="c-id5">
        <ul>
            @foreach (var item in Model.SolutionList)
            {
                <li><a href="@Url.DGAction("Details","Solution",new { Id=item.Id})"><p>@item.Name</p><span><img src="@(cdn)/images/time.png" alt="Alternate Text" />@item.CreateTime</span><i>@T("查看详情")></i></a></li>
            }
        </ul>
    </div>
    <div id="c-id6">
        <ul>
            <li>
                <p>
                    @("在线购买")
                </p>
                <span>
                    @if (language.UniqueSeoCode == "cn")
                    {
                        if (ShopUrl.Any())
                        {
                            foreach (var item in ShopUrl)
                            {
                                <a href="@item">
                                    <img src="@(cdn)/images/gfsc.png" />
                                    <Strong style="color: red;">@T("官方商城直达下单")</Strong>
                                </a>
                            }
                        }
                        else
                        {
                            <a href="@T("商城链接")">
                                <img src="@(cdn)/images/gfsc.png" />
                                @T("官方商城")
                            </a>
                        }

                        @* if (TaoBaoUrl1.Any())
                        {
                            foreach (var item in TaoBaoUrl1)
                            {
                                <a href="@item" target="_blank">
                                    <img src="@(cdn)/images/tbsc.png" />
                                    <Strong style="color: red;">@T("淘宝直达下单")</Strong>
                                </a>
                            }
                        }
                        else
                        {
                            <a href="https://mall.jd.com/index-12293539.html" target="_blank">
                                <img src="@(cdn)/images/jdimg.png" />
                                @T("淘宝店铺")
                            </a>
                        } *@
                        <a href="https://mall.jd.com/index-12293539.html" target="_blank">
                            <img src="@(cdn)/images/jdimg.png" />
                            @T("京东店铺")
                        </a>
                        <a href="https://hilink.tmall.com/">
                            <img src="@(cdn)/images/tmsc.png" />@T("天猫商城")
                        </a>
                        <a href="https://hi-link.taobao.com/">
                            <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")1
                        </a>
                        <a href="https://hlktech.taobao.com/">
                            <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")2
                        </a>
                        <a href="https://shop57596328.taobao.com/">
                            <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")3
                        </a>
                        <a href="https://shop311490340.taobao.com/">
                            <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")4
                        </a>
                    }
                    else
                    {
                        <a href="https://www.aliexpress.com/store/911797719" target="_blank">
                            <img src="@(cdn)images/aliexpress.png" style="width: 30px;" />Aliexpress
                        </a>
                    }
                </span>
            </li>
            <li>
                <p>
                    @T("大客户通道")
                </p>
                <span>
                    <i>@T("样品申请")<small>[@T("会2个工作日内回复")]</small><a href="@Url.DGAction("Index","Apply")">@T("立即申请样品")</a></i>
                    <i>@T("批量采购")<small>[@T("联系所属省份的大客户经理")]</small><a href="@Url.DGAction("Index","Contact")">@T("查看联系方式")</a></i>
                    <i>@T("定制申请")<small>[@T("会2个工作日内回复")]</small><a href="@Url.DGAction("Index","Apply")">@T("立即申请定制")</a></i>
                </span>
            </li>
        </ul>
    </div>
</div>

<script asp-location="Footer">
    var mySwiper = new Swiper('.swiper-container', {
        loop: true,
        autoplay: 3000,
        pagination: ".swiper-pagination",
        autoplayDisableOnInteraction: false,
        onTouchEnd: function (swiper) {
            $(".subscript i").text(swiper.activeIndex);
        },
        onSlideChangeEnd: function (swiper) {
            $(".subscript i").text((swiper.activeIndex > 5 ? "1" : swiper.activeIndex));
        }
    })

    $(".pro-type-menu a").click(function () {
        $(".pro-type-menu a").removeClass("selected");
        $(this).addClass("selected");
        $(".pro-content>div").hide();
        $(".pro-content #c-id" + $(this).attr("c-id")).show();
    });

    $(".data-download").click(() => {
        $(".pro-type-menu a[c-id=3]").click();
    })

    $(".tobuy").click(() => {
        $(".pro-type-menu a[c-id=6]").click();
    })
</script>