using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;

using NewLife;
using NewLife.Data;

using Pek;

using XCode;
using XCode.Cache;

namespace HlktechSite.Entity {
    /// <summary>成品表</summary>
    public partial class EndProducts : CubeEntityBase<EndProducts>
    {
        #region 对象操作
        static EndProducts()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(CId));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }

        /// <summary>验证并修补数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 建议先调用基类方法，基类方法会做一些统一处理
            base.Valid(isNew);

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化EndProducts[成品表]数据……");

        //    var entity = new EndProducts();
        //    entity.Id = 0;
        //    entity.Name = "abc";
        //    entity.AdvWord = "abc";
        //    entity.MId = "abc";
        //    entity.CId = 0;
        //    entity.Cid1 = 0;
        //    entity.Cid2 = 0;
        //    entity.Cid3 = 0;
        //    entity.Image = "abc";
        //    entity.Content = "abc";
        //    entity.MobileContent = "abc";
        //    entity.Summary = "abc";
        //    entity.UsageScenarios = "abc";
        //    entity.Specifications = "abc";
        //    entity.Clicks = 0;
        //    entity.Commend = true;
        //    entity.Shelf = true;
        //    entity.Sort = 0;
        //    entity.PcGouUrl = "abc";
        //    entity.MobileGouUrl = "abc";
        //    entity.CreateUser = "abc";
        //    entity.CreateUserID = 0;
        //    entity.CreateTime = DateTime.Now;
        //    entity.CreateIP = "abc";
        //    entity.UpdateUser = "abc";
        //    entity.UpdateUserID = 0;
        //    entity.UpdateTime = DateTime.Now;
        //    entity.UpdateIP = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化EndProducts[成品表]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public string Images { get; set; }
        #endregion

        #region 扩展查询
        /// <summary>根据成品分类ID查找</summary>
        /// <param name="cId">成品分类ID</param>
        /// <returns>实体列表</returns>
        public static IList<EndProducts> FindAllByCId(Int32 cId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.CId == cId);

            return FindAll(_.CId == cId);
        }

        /// <summary>根据一级分类ID查找</summary>
        /// <param name="cid1">一级分类ID</param>
        /// <returns>实体列表</returns>
        public static IList<EndProducts> FindAllByCid1(Int32 cid1)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Cid1 == cid1);

            return FindAll(_.Cid1 == cid1);
        }

        /// <summary>根据二级分类ID查找</summary>
        /// <param name="cid2">二级分类ID</param>
        /// <returns>实体列表</returns>
        public static IList<EndProducts> FindAllByCid2(Int32 cid2)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Cid2 == cid2);

            return FindAll(_.Cid2 == cid2);
        }

        /// <summary>根据三级分类ID查找</summary>
        /// <param name="cid3">三级分类ID</param>
        /// <returns>实体列表</returns>
        public static IList<EndProducts> FindAllByCid3(Int32 cid3)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Cid3 == cid3);

            return FindAll(_.Cid3 == cid3);
        }

        /// <summary>
        /// 根据ID集合删除数据
        /// </summary>
        /// <param name="Ids">ID集合</param>
        public static void DelByIds(String Ids)
        {
            if (Delete(_.Id.In(Ids)) > 0)
                Meta.Cache.Clear("");
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="mId">产品型号Id集合，以逗号区分</param>
        /// <param name="cId">成品分类ID</param>
        /// <param name="cid1">一级分类ID</param>
        /// <param name="cid2">二级分类ID</param>
        /// <param name="cid3">三级分类ID</param>
        /// <param name="start">更新时间开始</param>
        /// <param name="end">更新时间结束</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<EndProducts> Search(String mId, Int32 cId, Int32 cid1, Int32 cid2, Int32 cid3, DateTime start, DateTime end, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (!mId.IsNullOrEmpty()) exp &= _.MId == mId;
            if (cId >= 0) exp &= _.CId == cId;
            if (cid1 >= 0) exp &= _.Cid1 == cid1;
            if (cid2 >= 0) exp &= _.Cid2 == cid2;
            if (cid3 >= 0) exp &= _.Cid3 == cid3;
            exp &= _.UpdateTime.Between(start, end);
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.AdvWord.Contains(key) | _.Image.Contains(key) | _.Content.Contains(key) | _.MobileContent.Contains(key) | _.Summary.Contains(key) | _.UsageScenarios.Contains(key) | _.Specifications.Contains(key) | _.PcGouUrl.Contains(key) | _.MobileGouUrl.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        // Select Count(Id) as Id,MId From DG_Product Where CreateTime>'2020-01-24 00:00:00' Group By MId Order By Id Desc limit 20
        static readonly FieldCache<EndProducts> _MIdCache = new FieldCache<EndProducts>(nameof(MId))
        {
            //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        };

        /// <summary>获取产品型号Id集合列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        /// <returns></returns>
        public static IDictionary<String, String> GetMIdList() => _MIdCache.FindAllName();

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="name">产品名</param>
        /// <param name="commend">是否推荐</param>
        /// <param name="page"></param>
        /// <param name="CId"></param>
        /// <param name="CId1"></param>
        /// <param name="CId2"></param>
        /// <param name="CId3"></param>
        /// <returns></returns>
        public static IEnumerable<EndProducts> Searchs(string name, int commend, int CId, int CId1, int CId2, int CId3, PageParameter page)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                IEnumerable<EndProducts> list;

                list = Meta.Cache.FindAll(x => (name.IsNullOrWhiteSpace() || x.Name.ToLower().Contains(name.ToLower())));
                if (commend != 0)
                {
                    list = list.Where(e => e.Commend == (commend == 1));
                }

                if (CId > 0)
                {
                    list = list.Where(e => e.CId == CId);
                }
                if (CId1 > 0)
                {
                    list = list.Where(e => e.Cid1 == CId1);
                }
                if (CId2 > 0)
                {
                    list = list.Where(e => e.Cid2 == CId2);
                }
                if (CId3 > 0)
                {
                    list = list.Where(e => e.Cid3 == CId3);
                }
                page.TotalCount = list.Count();

                list = list.OrderByDescending(e => e.Id).Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize);
                return list;
            }

            var exp = new WhereExpression();
            if (name.IsNotNullAndWhiteSpace())
            {
                exp &= _.Name.Contains(name);
            }
            if (CId >= 0)
            {
                exp &= _.CId == CId;
            }
            if (CId1 >= 0)
            {
                exp &= _.Cid1 == CId1;
            }

            if (CId1 >= 0)
            {
                exp &= _.Cid1 == CId1;
            }
            if (CId2 >= 0)
            {
                exp &= _.Cid2 == CId2;
            }
            if (CId3 >= 0)
            {
                exp &= _.Cid3 == CId3;
            }

            return FindAll(exp, page);
        }


        /// <summary>
        /// 根据标题查询
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static EndProducts FindByName(String name)
        {
            if (name.IsNullOrEmpty()) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

            return Find(_.Name == name);
        }

        /// <summary>
        /// 前台分页查询
        /// </summary>
        /// <param name="Key"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        public static IEnumerable<EndProducts> SearchQ(String Key, PageParameter page)
        {
            if (Meta.Session.Count < 1000)
            {
                var list1 = FindAllWithCache();
                if (Key.IsNullOrWhiteSpace())
                {
                    list1 = (IList<EndProducts>)list1.Where(x => x.Name.Contains(Key));
                }

                page.TotalCount = list1.Count();
                var list = list1.OrderByDescending(x => x.CreateTime).Skip(0).Take(page.PageSize);
                return list;
            }
            else
            {
                var exp = new WhereExpression();
                if (Key.IsNullOrWhiteSpace())
                {
                    exp &= _.Name.Contains(Key);
                }
                return FindAll(exp, page).OrderByDescending(x => x.CreateTime);
            }

        }

        /// <summary>
        /// 查询所有
        /// </summary>
        /// <returns></returns>
        public static IList<EndProducts> GetAll()
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return FindAllWithCache();

            // 单对象缓存
            return FindAll();
        }
        /// <summary>
        /// 获取最大的排序
        /// </summary>
        /// <returns></returns>
        public static EndProducts GetMaxSort()
        {
            if (Meta.Session.Count < 1000)
            {
                return FindAllWithCache().OrderByDescending(x => x.Sort).Skip(0).Take(1).FirstOrDefault();
            }
            else
            {
                return FindAll(new WhereExpression(), new PageParameter { Desc = true, Sort = _.Sort, PageSize = 1 }).FirstOrDefault();
            }
        }
        #endregion

        #region 业务操作
        #endregion
    }
}