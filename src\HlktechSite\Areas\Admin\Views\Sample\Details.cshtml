﻿@model SampleApplication
<style asp-location="true">
    .page {
        min-height: 415px
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("定制申请详情")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Details",new { Id=Model.Id})" class="current"><span>@T("定制申请详情")</span></a></li>
            </ul>
        </div>
    </div>
    <form id="goods_class_form" enctype="multipart/form-data" method="post">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("公司名称")</td>
                    <td class="vatop rowform">@Model.ComName</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("企业人数")</td>
                    <td class="vatop rowform">@Model.ComPeople</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("企业官网")</td>
                    <td class="vatop rowform">@Model.Website</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("年营业额")</td>
                    <td class="vatop rowform">@Model.Turnover</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("企业类型")</td>
                    <td class="vatop rowform">@Model.ComType</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("企业地址")</td>
                    <td class="vatop rowform">@Model.Address</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("联系人")</td>
                    <td class="vatop rowform">@Model.Linkman</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("职位")</td>
                    <td class="vatop rowform">@Model.Position</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("电话")</td>
                    <td class="vatop rowform">@Model.Phone</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("QQ")</td>
                    <td class="vatop rowform">@Model.QQ</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("邮箱")</td>
                    <td class="vatop rowform">@Model.Email</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("传真")</td>
                    <td class="vatop rowform">@Model.Fax</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("产品型号")</td>
                    <td class="vatop rowform">@Model.ProModel</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("月需求量")</td>
                    <td class="vatop rowform">@Model.Demand</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("申请主题")</td>
                    <td class="vatop rowform">@Model.Theme</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("需求描述")</td>
                    <td class="vatop rowform">@Model.Describe</td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
        </table>
    </form>
</div>