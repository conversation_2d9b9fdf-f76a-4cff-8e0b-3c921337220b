﻿@{
}
<div class="goods-gallery add-step2">
    <a class='sample_demo' id="select_submit" href="/index.php/admin/Goodsalbum/pic_list.html?item=goods" style="display:none;">提交</a>
    <div class="nav">
        <span class="l">
            用户相册 >
            全部图片
        </span>
        <span class="r">
            <select name="jumpMenu" id="jumpMenu" style="width:100px;">
                <option value="0" style="width:80px;">请选择</option>
                @foreach (var item in ViewBag.ListXC)
                {
                    <option style="width:80px;" value="@item.Id">@item.Name</option>
                }
            </select>
        </span>
    </div>
    <ul class="list">

        @foreach (var item in ViewBag.list)
        {

            <li onclick="insert_img('@item.Id', '@item.Cover');">
                <a href="JavaScript:void(0);"><img src="@item.Cover" /></a>
            </li>
        }
    </ul>
    <div class="pagination">
        <ul class="pagination">
            @*<li class="disabled"><span>&laquo;</span></li> <li class="active"><span>1</span></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=2">2</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=3">3</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=4">4</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=5">5</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=6">6</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=7">7</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=8">8</a></li>
                <li class="disabled"><span>...</span></li><li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=11">11</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=12">12</a></li>
                <li><a href="/index.php/admin/Goodsalbum/pic_list.html?item=goods&amp;page=2">&raquo;</a></li>*@
            @Html.Raw(ViewBag.Str)
        </ul>
    </div>
</div>

<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script asp-location="Footer">
    $(document).ready(function () {
        $('ul.pagination li a').ajaxContent({
            event: 'click', //mouseover
            loaderType: 'img',
            loadingMsg: '/static/home/<USER>/loading.gif',
            target: '#demo'
        });
        $('#jumpMenu').change(function () {
            $('#select_submit').attr('href', "@Url.Action("Piclist")?id=" + $('#jumpMenu').val());
            $('.sample_demo').ajaxContent({
                event: 'click', //mouseover
                loaderType: 'img',
                loadingMsg: '/static/home/<USER>/loading.gif',
                target: '#demo'
            });
            $('#select_submit').click();
        });
    });
</script>