﻿@{
    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .layui-tab-title {
        width: 95%;
        margin: 0 auto;
    }

    .layui-tab.layui-tab-brief.Lan {
        box-shadow: 0 0 5px #AAA inset;
        box-shadow: 0 0 5px #AAA;
        padding-bottom: 15px;
        min-height: 380px;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("成品管理")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("新增")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("Add", "ChengPinJump", FormMethod.Post, new { id = "form1", enctype = "multipart/form-data" }))
    {
        <div class="layui-tab layui-tab-brief Lan" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content" style="height: 100px;">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td colspan="2" class="required"><label class="gc_name validation" for="Name">@T("产品名称"):</label></td>
                            </tr>
                            <tr class="noborder">
                                <td class="vatop rowform"><input type="text" maxlength="20" value="" name="Name" id="Name" class="txt"></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2"><label class="gc_name" for="InfoUrl">@T("推文链接"):</label></td>
                            </tr>
                            <tr class="noborder">
                                <td class="vatop rowform"><input type="text" value="" name="InfoUrl" id="InfoUrl" class="txt"></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2"><label class="gc_name" for="AdWord">@T("广告词"):</label></td>
                            </tr>
                            <tr class="noborder">
                                <td class="vatop rowform"><input type="text" value="" name="AdWord" id="AdWord" class="txt"></td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td colspan="2" class="required"><label class="gc_name validation" for="[@item.Id].Name">@T("产品名称"):</label></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform"><input type="text" value="" name="[@item.Id].Name" id="[@item.Id].Name" class="txt"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td colspan="2"><label class="gc_name" for="[@item.Id].InfoUrl">@T("推文链接"):</label></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform"><input type="text" value="" name="[@item.Id].InfoUrl" id="[@item.Id].InfoUrl" class="txt"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td colspan="2"><label class="gc_name" for="[@item.Id].AdWord">@T("广告词"):</label></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform"><input type="text" value="" name="[@item.Id].AdWord" id="[@item.Id].AdWord" class="txt"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>

        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="Content">@T("内容"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><textarea value="" name="Content" id="Content" class="txt"></textarea></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="AndroidPaths">@T("国内Android下载地址"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="" name="AndroidPaths" id="AndroidPaths" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="AndroidPaths1">@T("国外Android下载地址"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="" name="AndroidPaths1" id="AndroidPaths1" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="IosPaths">@T("IOS下载地址"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="" name="IosPaths" id="IosPaths" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr>
                    <td colspan="2"><label for="pic">APP Logo:</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <span class="type-file-box">
                            <input type='text' name='textfield' id='textfield1' class='type-file-text' />
                            <input type='button' name='button' id='button1' value='上传' class='type-file-button' />
                            <input name="pic" type="file" class="type-file-file" id="pic" size="30" hidefocus="true" ds_type="change_pic">
                        </span>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    }
</div>

<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script type="text/javascript" asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;

        $('#form1').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                Name: {
                    required: true
                }
            },
            messages: {
                Name: {
                    required: '@T("产品名称不能为空")'
                }
            }
        });
    })
</script>