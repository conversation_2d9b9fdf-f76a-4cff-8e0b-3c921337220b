﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>在线客服</summary>
public partial class OnlineKeFuModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>语言Id集合</summary>
    public String? LIds { get; set; }

    /// <summary>所属平台集合。1为PC，2为移动端,多个以逗号分隔</summary>
    public String? OIds { get; set; }

    /// <summary>客服名称</summary>
    public String? OName { get; set; }

    /// <summary>客服号码</summary>
    public String? ONumber { get; set; }

    /// <summary>客服平台类型。0为QQ，1为阿里旺旺，2为Skype,3为Whatsapp</summary>
    public Int16 OType { get; set; }

    /// <summary>客服所属平台,0为全部,1为PC,2为手机</summary>
    public Int32 Location { get; set; }

    /// <summary>排序</summary>
    public Int32 Sort { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IOnlineKeFu model)
    {
        Id = model.Id;
        LIds = model.LIds;
        OIds = model.OIds;
        OName = model.OName;
        ONumber = model.ONumber;
        OType = model.OType;
        Location = model.Location;
        Sort = model.Sort;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
