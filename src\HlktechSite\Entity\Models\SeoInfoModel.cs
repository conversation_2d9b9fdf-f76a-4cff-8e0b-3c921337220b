﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>SEO信息存放表</summary>
public partial class SeoInfoModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>SEO标题</summary>
    public String? SeoTitle { get; set; }

    /// <summary>SEO关键词</summary>
    public String? SeoKeywords { get; set; }

    /// <summary>SEO描述</summary>
    public String? SeoDescription { get; set; }

    /// <summary>SEO类型</summary>
    public String? SeoType { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISeoInfo model)
    {
        Id = model.Id;
        SeoTitle = model.SeoTitle;
        SeoKeywords = model.SeoKeywords;
        SeoDescription = model.SeoDescription;
        SeoType = model.SeoType;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
