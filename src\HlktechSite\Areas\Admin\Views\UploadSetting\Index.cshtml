﻿@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("上传设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("默认图片")</span></a></li>
                <li><a href="@Url.Action("Upload_Type")"><span>@T("上传设置")</span></a></li>
                <li><a href="@Url.Action("Upload_Type1")"><span>@T("私有上传设置")</span></a></li>
                <li><a href="@Url.Action("CDNSetting")"><span>@T("CDN设置")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("UploadThumb", "UploadSetting", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
    {
        <div class="ncap-form-default">
            <dl>
                <dt>@T("默认图标头像")</dt>
                <dd>
                    <span class="type-file-show">
                        <img class="show_image" src="/static/admin/images/preview.png">
                        <div class="type-file-preview"><img src="@Model.default_user_portrait"></div>
                    </span>
                    <span class="type-file-box">
                        <input type='text' name='textfield' id='textfield4' class='type-file-text' /><input type='button' name='button' id='button1' value='@T("上传")' class='type-file-button' />
                        <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                    </span>
                    <p class="notic">@T("128px * 128px")</p>
                </dd>
            </dl>
            <dl>
                <dt></dt>
                <dd><input class="btn" type="submit" value="@T("提交")" /></dd>
            </dl>
        </div>
    }
</div>
<script asp-location="Footer">
    $(function () {
        $("#default_user_portrait").change(function () {
            $("#textfield4").val($("#default_user_portrait").val());
        });
    })
</script>