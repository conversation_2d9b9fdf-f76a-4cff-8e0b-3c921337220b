.text-link a {
	color: #555;
}
.text-link a:hover {
	color: #00aaff;
	text-decoration: none;
}
.user-detail-count {
	color: #ff8800;
	font-size: 22px;
	line-height: 1;
}
.home-page {
	min-width: 1080px;
	background: #f3f4f5;
}
.home-page .ny-panel {
	padding: 0;
	margin: 0;
}
/* 20180127改版 */
.home-container {
	padding: 24px;
}
.user-info-row {
	height: 56px;
	margin-top: 8px;
	margin-bottom: 24px;
	line-height: 56px;
	padding-left: 76px;
	background: url(img/user_avatar.png) no-repeat left center;
}
.home-user-name {
	color: #272829;
	font-size: 22px;
}
.user-cps-icon {
	display: inline-block;
	width: 26px;
	height: 28px;
	vertical-align: middle;
	margin-left: 16px;
}
.user-cps-icon.not-active {
	background-image: url(img/home-not-icon.png);
}
.user-cps-icon.platinum {
	background-image: url(img/home-platinum-icon.png);
}
.user-cps-icon.lifelong {
	background-image: url(img/home-lifelong-icon.png);
}

.user-safety-icon {
	position: relative;
	display: inline-block;
	width: 16px;
	height: 16px;
	background: no-repeat center;
	vertical-align: middle;
	margin-left: 5px;
}
.user-auth-reminder {
	position: absolute;
	left: -100px;
	top: 24px;
	width: 216px;
	height: 24px;
	line-height: 24px;
	text-align: center;
	color: #fff;
	background: #2d3438;
}
.icon-auth:hover .user-auth-reminder {
	display: block;
}
.user-auth-reminder:before {
	content: "";
	position: absolute;
	left: 47%;
	top: -12px;
	border: 6px solid transparent;
	border-bottom-color: #2d3438;
}
.icon-auth {
	background-image: url(img/home_icon_safety_verify.png);
	margin-left: 10px;
}
.icon-auth.active {
	background-image: url(img/home_icon_safety_verify_active.png);
}
.icon-mobile {
	background-image: url(img/home_icon_safety_mobile.png);
}
.icon-mobile.active {
	background-image: url(img/home_icon_safety_mobile_active.png);
}
.icon-email {
	background-image: url(img/home_icon_safety_email.png);
}
.icon-email.active {
	background-image: url(img/home_icon_safety_email_active.png);
}
.icon-protection {
	background-image: url(img/home_icon_safety_protect.png);
}
.icon-protection.active {
	background-image: url(img/home_icon_safety_protect_active.png);
}
.home-deck {
	margin-bottom: 24px;
}
.home-module {
	float: left;
	padding: 0 24px 32px 24px;
	background: #fff;
}
.home-module.width-60per {
	width: 66.25%;
}
.home-module.width-30per {
	width: 32.5%;
	margin-left: 1.25%;
}
.home-module.width-30per:first-child {
	margin-left: 0;
}
.home-module-title {
	padding-top: 24px;
	font-size: 16px;
	color: #a0a2a3;
}
.home-deck-1 .home-module {
	height: 240px;
}
.home-user-money {
	width: 50%;
	cursor: pointer;
}
.user-money-title {
	font-size: 12px;
	color: #636566;
}
.user-money-amount {
	margin-top: 14px;
	font-size: 22px;
}
.home-user-coupon {
	border-left: 1px solid #e1e4e6;
	padding-left: 20px;
}
.home-user-charge {
	width: 96px;
	font-size: 14px;
}
.user-expense {
	position: relative;
	z-index: 1;
	height: 184px;
}
.user-expense-chart {
	height: 100%;
}
.user-expense-view {
	position: absolute;
	left: 60%;
	z-index: 2;
	max-width: 140px;
	font-size: 14px;
	color: #262829;
}
.user-expense-title {
	left: 101px;
	top: 89px;
	margin-left: -28px;
}
.user-expense-faker-cover {
	width: 135px;
	height: 135px;
	left: 27%;
	top: 22px;
	z-index: 10;
	cursor: ew-resize;
	background: rgba(0,0,0,0.1);
}
.user-expense-cost {
	top: 70px;
}
.user-expense-income {
	top: 140px;
}
.home-todo-list {
	margin-top: 24px;
}
.home-todo-list li {
	height: 44px;
	line-height: 44px;
	background: #f7f9fa;
	border: 1px solid #f7f9fa;
	font-size: 12px;
	margin-top: 6px;
	color: #636566;
}
.home-todo-list .todo-item {
	color: #636566;
}
.home-todo-list li:hover {
	border-color: #00aaff;
	background: #fff;
}
.home-todo-list li:first-child {
	margin-top: 0;
}
.home-todo-list a {
	display: block;
	width: 100%;
	height: 100%;
	text-decoration: none;
	padding-left: 15px;
	padding-right: 17px;
}
.home-todo-list li span {
	vertical-align: top;
}
.todo-count {
	margin-right: 10px;
	font-size: 20px;
	color: #272829;
}
.home-todo-list a {
	font-size: 12px;
	float:right;
}
.home-deck-2 .home-module {
	height: 176px;
}
.home-alert-list {
	width: 100%;
	margin-top: 24px;
}
.home-alert-list li {
	float: left;
	width: 32.5%;
	margin-left: 1.25%;
	height: 80px;
	line-height: 80px;
	background: #f7f9fa;
	border: 1px solid #f7f9fa;
}
.home-alert-list li:first-child {
	margin-left: 0;
}
.home-alert-list li:hover {
	background: #fff;
	border-color: #00aaff;
}
.home-alert-list li:hover .alert-link {
	color: #00aaff;
}
.home-alert-list a {
	display: block;
	width: 100%;
	height: 100%;
	padding: 0 24px 0 40px;
	text-decoration: none;
}
.alert-count {
	font-size: 28px;
}
.alert-unit {
	font-size: 14px;
}
.alert-link {
	color: #636566;
}
.home-recommend {
	padding-top: 24px;
}
.qrcode-module {
	padding-right: 0;
}
.home-qrcode {
	width: 120px;
	height: 120px;
	border: 1px solid #e1e4e6;
	background: url(img/home_qrcode.jpg) no-repeat center;
}
.home-recommend-right {
	margin-left: 5%;
	width: 54%;
}
.home-recommend-info {
	padding-top: 10px;
	line-height: 2em;
	color: #636566;
}
.home-deck-3 .home-module {
	height: 345px;
}
.home-product-list {
	margin-top: 20px;
}
.home-product-list:first-child {
	margin-top: 0;
}
.home-product-list li {
	position: relative;
	border: 1px solid #fff;
	float: left;
	width: 32.5%;
	margin-left: 1.25%;
	height: 72px;
	padding: 15px 0 0 63px;
	background: no-repeat 16px center;
}
.home-product-list li:first-child {
	margin-left: 0;
}
.home-product-list li:hover .home-product-title {
	color: #00aaff;
}
.home-product-list li:hover {
	border-color: #00aaff;
}
.home-product-list li:hover .home-product-buy {
	display: block;
}
.home-product-list li.disabled,
.home-product-list li.disabled:hover {
	border-color: #fff;
	cursor: default;
}
.home-product-list li.disabled .home-product-title,
.home-product-list li.disabled .home-product-subtitle {
	color: #ccc;
}
.home-product-list li.server {
	background-image: url(img/home_icon_server.png);
}
.home-product-list li.server:hover {
	background-image: url(img/home_icon_server_active.png);
}
.home-product-list li.host {
	background-image: url(img/home_icon_host.png);
}
.home-product-list li.host:hover {
	background-image: url(img/home_icon_host_active.png);
}
.home-product-list li.idc {
	background-image: url(img/home_icon_idc.png);
}
.home-product-list li.idc:hover {
	background-image: url(img/home_icon_idc_active.png);
}
.home-product-list li.slb {
	background-image: url(img/home_icon_slb.png);
}
.home-product-list li.slb:hover {
	background-image: url(img/home_icon_slb_active.png);
}
.home-product-list li.ssl {
	background-image: url(img/home_icon_ssl.png);
}
.home-product-list li.ssl:hover {
	background-image: url(img/home_icon_ssl_active.png);
}
.home-product-list li.domain {
	background-image: url(img/home_icon_domain.png);
}
.home-product-list li.domain:hover {
	background-image: url(img/home_icon_domain_active.png);
}
.home-product-list li.database {
	background-image: url(img/home_icon_database.png);
}
.home-product-list li.database:hover {
	background-image: url(img/home_icon_database_active.png);
}
.home-product-list li.oss {
	background-image: url(img/home_icon_oss.png);
}
.home-product-list li.oss:hover {
	background-image: url(img/home_icon_oss_active.png);
}
.home-product-list li.cdn {
	background-image: url(img/home_icon_cdn.png);
}
.home-product-list li.cdn:hover {
	background-image: url(img/home_icon_cdn_active.png);
}
.home-product-buy {
	display: none;
	position: absolute;
	right: 0;
	top: 0;
	z-index: 1;
	height: 72px;
	width: 56px;
	background: url(img/home_icon_purchase.png) no-repeat center;
}
.home-product-buy:hover {
	background-image: url(img/home_icon_purchase_blue.png);
}
.home-product-link-left {
	display: block;
	width: 100%;
	height: 100%;
}
.home-product-link-left:hover {
	text-decoration: none;
}
.home-product-title {
	font-size: 14px;
	color: #272829;
}
.home-product-subtitle {
	width: 72%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	color: #a0a2a3;
}
.home-news-list {
	margin-top: 20px;
}
.home-news-list li {
	line-height: 48px;
	border-bottom: 1px dashed #e3e5e5;
}
.home-news-list li:first-child {
	border-top:1px dashed #e3e5e5;
}
.home-news-list li:hover .notice-content {
	transition: padding 0.3s ease;
	padding-left: 16px;
	background-color: #edf7fc;
}
.home-news-list li:hover .notice-content span {
	color: #00aaff;
}
.home-news-list li a {
	display: block;
	width: 100%;
	height: 100%;
	text-decoration: none;
}
.list-decoration-point {
	position: relative;
	display: inline-block;
	width: 20px;
}
.list-decoration-point:after {
	content: "";
	position: absolute;
	left: 0;
	top: -6px;
	z-index: 10;
	width: 6px;
	height: 6px;
	background: #00aaff;
}
.notice-content a {
	display: inline-block;
	width: 68%;
	overflow: hidden;
	line-height: 1;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.notice-title {
	color: #272829;
}