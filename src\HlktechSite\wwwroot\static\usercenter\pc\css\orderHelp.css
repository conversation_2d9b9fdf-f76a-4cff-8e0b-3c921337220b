.menu-box {
	position: relative;
	display: inline-block;
	border: 1px solid #e1e4e6;
	width: 24%;
	/*background-color: #F5F9FA;*/
}

.menu-box-title {
	border-bottom: 1px solid #e1e4e6;
	padding-left: 10%;
	height: 41px;
	line-height: 40px;
	font-size: 14px;
	color: #636566;
	font-weight: bold;
	background: #f7f9fa;
}
.menu-box li {
	position: relative;
	border-bottom: 1px solid #e1e4e6;
	height: 40px;
	line-height: 40px;
	padding-left: 5%;
	margin: 0 5%;
	color: #333;
}
.menu-box li:last-child {
	border-bottom: none;
}
.menu-item i {
	width: 0;
	height: 0;
	border-style: solid;
	_border-style: dotted;
	border-width: 4px;
	border-color: transparent transparent transparent #666;
	position: absolute;
	right: 0;
	top: 18px;
}
.menu-item a {
	display: block;
	color: #333;
	margin-right: 15px;
	white-space: nowrap;
	overflow: hidden;
}
.menu-item a:hover {
	text-decoration: none;
	color: #00aaff;
	cursor: pointer;
}
.menu-box li:hover {
	background-color: #fff;
}
.menu-box li:hover i {
	border-left-color: #00aaff;
}

li.menu-active {
	background-color: #fff;
}
li.menu-active a {
	color: #00aaff;
}
li.menu-active i {
	border-left-color: #00aaff;
}

.menu-list {
	max-height: 693px;
	overflow: hidden;
}
.menu-list.open li:last-child {
	border-bottom: 1px solid #e1e4e6;
}
.menu-list.open {
	max-height: none;
	padding-bottom: 7px;
}
.show-box-btn {
	position: absolute;
	bottom: 0;
	left: 0;
	border-top: 0;
	width: 100%;
	height: 46px;
	line-height: 46px;
	text-align: center;
	cursor: pointer;
	background: #fff;
	color: #00aaff;
}
.show-box-btn:before {
	display: block;
	content: " ";
	position: absolute;
	left: 50%;
	margin-left: 45px;
	top: 50%;
	margin-top: -4px;
	width: 12px;
	height: 8px;
	background: url(img/menu_close.png) no-repeat center;
	background-size: 100%;
}
.show-box-btn.open:before {
	background: url(img/menu_open.png) no-repeat center;
	background-size: 100%;
}
.content-box {
	padding-left: 22px;
	width: 76%;
	min-height: 629px;
}

.content-box-inner {
	height: 100%;
	border: 1px solid #e1e4e6;
}
.question-list-ul {
	/*min-height: 399px;*/
}
.search-box {
	display: block;
	position: relative;
	margin: 0 19px 15px;
	border-bottom: 1px solid #e1e4e6;
	padding-top: 16px;
	padding-left: 10px;
	height: 65px;
}
.search-group-title,
.search-group-input,
.btn-primary-search {
	height: 38px;
	line-height: 38px;
	color: #333;
}
.search-group-title {
	width: 160px;
}
.search-group-input {
	width: 350px;
}
.btn-primary-search {
	width: 110px;
}
.btn-primary-search.btn-primary[disabled]{

	background:#7fddff url(img/btn_icon_search.png) no-repeat 30px center;
}

.fuzzy-query {
	display: none;
	position: absolute;
	left: 10px;
	top: 48px;
	width: 220px;
	border:1px solid #d6d6d6;
	border-top: none;
	background-color: #fff;
	z-index: 10;
}
.fuzzy-query li {
	padding-left: 14px;
	height: 30px;
	line-height: 30px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	font-size: 12px;
	color: #666;
}
.fuzzy-query li:hover {
	background-color: #f0f0f0;
}
.fuzzy-query li span {
	color: #333;
}
.serach-container-wrapper {
	position: relative;
	text-align:right
}
	.serach-container-wrapper .pull-left{
		text-align:left;
	display:inline-block;
		float:none !important;
	}

	.serach-container-wrapper .fuzzy-query {
		z-index: 999;
		left: 0;
		top: 32px;
	}
.question-box {
	padding: 0 30px;
}
.question-box {
	padding-bottom: 0;
}
.question-tip {
	font-size: 14px;
	line-height: 42px;
	color: #a0a2a3;
}
.question-list {
	display: block;
	padding-bottom: 60px;
}

.question-list.special {
	padding-bottom: 30px;
}
.question-list li {
	padding-left: 22px;
	height: 40px;
	line-height: 40px;
	background: url(img/question_list_li_bg.png) no-repeat 0 center;
}
.question-list li:hover {
	background: url(img/question_list_li_bg_active.png) no-repeat 0 center;
}
.question-list li a {
	display: inline-block;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 12px;
	color: #666666;
}
.question-list li:hover a {
	color: #00aaff;
}
.no-helpe-span {
	font-size: 12px;
	color: #00aaff;
}
.btn-send-workorder {
	margin-left: 20px;
	width: 140px;
	text-align: center;
}
.order-add-wrap{
	float: left;
	margin-left: 30px;
}
.order-add {
	margin: 0 19px;
	border-top: 1px solid #e1e4e6;
	padding: 30px 0;
	text-align: center;
}
.btn-primary-search {
	text-indent: 15px;
	background-position: 30px center;
	color: #fff;
}
.btn-primary-search, .btn-primary-search:focus, .btn-primary-search:active, .btn-primary-search:focus:active {
	background-position: 30px center;
}
.index-box {
	position: relative;
	width: 100%;
	height: 400px;
}
.index-box .index-panel{
	position: absolute;
	left: 20%;
	top: 20%;
	padding-left: 210px;
	padding-top: 40px;
	height: 287px;
	background: url(img/certificate-bg.png) no-repeat left center;
}
.index-box p {
	line-height: 30px;
	font-size: 14px;
	color: #00aaff;
}
hr {
	margin-left: 20px;
}

/* 搜索页样式 begin*/
.search-list {
	margin-top: 20px;
}
.search-list a {
	display: block;
	padding-left: 20px;
	margin-top: 10px;
}
.search-list a:hover {
	text-decoration: none;
}
.search-list p {
	margin: 0;
	line-height: 25px;
	font-size: 12px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}
.search-list p.search-list-content {
	color: #999;
}
.search-add {
	margin: 20px 0 20px 20px;
	height: 80px;
	line-height: 80px;
	text-align: center;
	background-color: #F5F9FA;
	color: #333;
}
.search-add a {
	vertical-align: middle;
}
.btn-primary-high {
	width: 88px;
	height: 32px;
	line-height: 32px!important;
	font-size: 14px;
}
/*重置分页样式*/
/*.pager-wrapper {
	padding-left: 10px;
	margin-top: 20px;
}
.pager-wrapper .pager-count {
	display: none;
}
.pager-wrapper .ny-pagination {
	float: left!important;
}*/

/*搜索框*/
.serach-container {
	position: relative;
	margin-bottom: 16px;
	border: 1px solid #d6d6d6;
	height: 32px;
	width: 220px;
	display: inline-block;
}
}
.search-btn {
	position: absolute;
	right: 5px;
	top: 0;
	border: none;
	width: 30px;
	height: 30px;
	background: url(img/small_search_icon.png) no-repeat center;
	cursor: pointer;
	outline: none;
}
.serach-input {
	border: none;
	padding-left: 14px;
	height: 30px;
	width: 186px;
	line-height: 30px;
	outline: none;
}

