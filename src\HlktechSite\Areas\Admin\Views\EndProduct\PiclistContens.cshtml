﻿@{
}


<div class="goods-gallery add-step2">
    <a class="sample_demo" id="select_s" href="" style="display:none;">提交</a>
    <div class="nav">
        <span class="l">
            用户相册 >
            全部图片
        </span><span class="r">
                   <select name="jump_menu" id="jump_menu" style="width:100px;">
                       <option value="0" style="width:80px;">请选择</option>
                       @foreach (var item in ViewBag.ListXC)
                       {
                           <option style="width:80px;" value="@item.Id">@item.Name</option>
                       }
                   </select>
        </span>
    </div>
    <ul class="list">
        @foreach (var item in ViewBag.list)
        {
            if (ViewBag.type == "Pc")
            {
                <li onclick="insert_editor1('@item.Cover');">
                    <a href="JavaScript:void(0);"><img src="@item.Cover" /></a>
                </li>
            }
            else if (ViewBag.type == "Mobile")
            {
                <li onclick="insert_mobile_img('@item.Cover');">
                    <a href="JavaScript:void(0);"><img src="@item.Cover" /></a>
                </li>
            }

        }
    </ul>
    <div class="pagination"><ul class="pagination">
    @Html.Raw(ViewBag.Str)
</ul></div>
</div>
<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script asp-location="Footer">
    $(document).ready(function () {
        $('.EndProducts-gallery .pagination li a').ajaxContent({
            event: 'click', //mouseover
            loaderType: 'img',
            loadingMsg: '/static/home/<USER>/loading.gif',
            target: '#des_demos'
        });
        $('#jump_menu').change(function () {
            $('#select_s').attr('href', "@Url.Action("PiclistContens")?id=" + $('#jump_menu').val()+"&type=@ViewBag.type");
            $('.sample_demo').ajaxContent({
                event: 'click', //mouseover
                loaderType: 'img',
                loadingMsg: '/static/home/<USER>/loading.gif',
                target: '#des_demos'
            });
            $('#select_s').click();
        });
    });
</script>