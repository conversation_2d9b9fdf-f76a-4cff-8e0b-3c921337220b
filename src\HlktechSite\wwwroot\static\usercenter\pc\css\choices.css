.workorder-main {
	padding-top: 27px;
}
.workorder-type-container {
	padding-right: 5px;
}
.workorder-type-line {
	position: relative;
	z-index: 10;
	margin-bottom: 24px;
	border: 1px solid #F4F4F4;
}
.workorder-type-line-title {
	box-sizing: content-box;
	position: absolute;
	top: 0;
	bottom: 0;
	z-index: 10;
	width: 1em;
	padding: 20px 1em;
	background-color: #eef1f3;
	line-height: 1;
	font-size: 14px;
	color: #555;
}
.workorder-type-tech,
.workorder-type-others {
	padding-top: 30px;
}
.workorder-type-account {
	padding-top: 25px;
}
.workorder-type-line-titlepad {
	margin-left: 3em;
}
.workorder-type-item {
	float: left;
	width: 33%;
	height: 152px;
	line-height: 1;
}
.workorder-type-item-inner {
	position: relative;
}
.workorder-type-item-inner .workorder-img-container {
	position: absolute;
	width: 56px;
	height: 56px;
	overflow: hidden;
	left: 10px;
	top: 20%;
}
.workorder-type-item-iconpad {
	margin-left: 24px;
	padding-left: 84px;
}
.workorder-iconpad-money {
	background-image: url(img/icon_workorder_money.png);
}
.workorder-iconpad-compact {
	background-image: url(img/icon_workorder_compact.png);
}
.workorder-iconpad-account {
	background-image: url(img/icon_workorder_account.png);
}
.workorder-iconpad-net {
	background-image: url(img/icon_workorder_net.png);
}
.workorder-iconpad-general {
	background-image: url(img/icon_workorder_general.png);
}
.workorder-iconpad-presales {
	background-image: url(img/icon_workorder_presales.png);
}
.workorder-iconpad-record {
	background-image: url(img/icon_workorder_record.png);
}
.workorder-iconpad-activity {
	background-image: url(img/icon_workorder_activity.png);
}
.workorder-type-item-title {
	padding-top: 25px;
	font-size: 16px;
	color: #545454;
}
.workorder-type-item-text {
	max-height: 36px;
	overflow: hidden;
	margin-top: 12px;
	line-height: 1.5em;
	font-size: 12px;
	color: #999;
}
.workorder-type-button-submit,
.workorder-type-button-submit:active,
.workorder-type-button-submit:focus {
	display: inline-block;
	margin-top: 16px;
	margin-bottom: 23px;
	padding: 5px;
	border: 1px solid #FF8F17;
	border-radius: 2px;
	white-space: nowrap;
	font-size: 12px;
	color: #FF8400;
}
.workorder-type-button-submit:hover {
	background: #FF8F17;
	text-decoration: none;
	color: white;
}
.buy-diagnosis {
	height: auto;
	font-size: 12px;
	margin-top: 16px;
	margin-bottom: 23px;
	padding: 6px;
	line-height: 1;
}


/*新版本4.0*/
.select-container-title {
	margin-bottom: 7px;
	color: #00aaff;
	line-height: 46px;
	font-size: 14px;
	font-weight: bold;
}
.select-item {
	position: relative;
	float: left;
	margin-left: 2%;
	margin-bottom: 24px;
	border: 1px solid #e1e4e6;
	padding: 32px 16px 0 90px;
	height: 120px;
	width: 32%;
}
.select-item:nth-child(3n+1) {
	margin-left: 0;
}
.select-item:hover {
	text-decoration: none;
	border-color: #00aaff;
	
}
.select-item-icon {
	position: absolute;
	left: 20px;
	top: 31px;
	width: 51px;
	height: 56px;
	background: url(img/select_icon_01.png) no-repeat 0 0;
}
.select-item:hover .select-item-icon{
	background-position: -51px 0;
}
.select-item-title {
	margin-bottom: 0;
	line-height: 30px;
	font-size: 18px;
	color: #262829;
}
.select-item-intro {
	margin-bottom: 0;
 	line-height: 22px;
 	font-size: 12px;
 	color: #a0a2a3;
}
/*搜索框*/
.serach-container{
	position: relative;
	margin-bottom: 16px;
	border: 1px solid #d6d6d6;
	height: 32px;
	width: 426px;
}
.search-btn {
	position: absolute;
	right: 5px;
	top: 0;
	border: none;
	width: 30px;
	height: 30px;
	background: url(img/small_search_icon.png) no-repeat center;
	cursor: pointer;
	outline: none;
}
.serach-input {
	border: none;
	padding-left: 14px;
	height: 30px;
	width: 380px;
	line-height: 30px;
	outline: none;
}
.feedback-link {
	float: right;
	padding-left: 20px;
	line-height: 32px;
	font-size: 14px;
	color: #00aaff;
	background: url(img/feed_back_icon.png) no-repeat 0 center;
}



.ProModel {
	padding: 5px 10px;
	border: 1px solid #848895;
	color: #848895;
	margin-right: 15px;
	margin-bottom: 15px;
	display: inline-block;
}

	.ProModel:hover {
		color: #0067ac;
		text-decoration: inherit;
		border: 1px solid #0067ac;
	}