﻿@using DG.Entity
@{
    Layout = null;

    var SiteSettings = SiteSettingInfo.SiteSettings;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>App下载</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">

    <link rel="stylesheet" href="~/layuiadmin/layui/css/layui.css" media="all" asp-append-version="true">
    <style>
        .layui-main {
            max-width: 750px;
            text-align: center;
            margin: 0 auto;
        }

        #background {
            position: fixed;
            top: 0;
        @*left: 0;*@ width: 100%;
            max-width: 750px;
            height: 100%;
            overflow: hidden;
            background-color: #211f1f;
            display: none\8;
        }

            #background .bg-photo {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: none;
                overflow: hidden;
                -webkit-background-size: cover !important;
                -moz-background-size: cover !important;
                -o-background-size: cover !important;
                background-size: cover !important;
            }

            #background .bg-photo-1 {
                background: url('/images/NG101.png') no-repeat center center;
            }

        #background-ie {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #211f1f;
        }

        .dgbottom {
            position: fixed;
            bottom: 20%;
            max-width: 750px;
        }

        #android img {
            width: 90%;
        }

        #ios img {
            width: 90%;
        }
    </style>
</head>
<body>
    <div class="layui-main">
        <div id="background">
            <div class="bg-photo bg-photo-1" style="display: block;">
            </div>
        </div>
        <div class="layui-row dgbottom">
            <div class="layui-col-xs6">
                <a id="android" href="@SiteSettings.AndriodDownLoad"><img src="/images/Android1.png" /></a>
            </div>
            <div class="layui-col-xs6" style="text-align: center;">
                <a id="ios" href="@SiteSettings.IOSDownLoad"><img src="/images/ios2.png" /></a>
            </div>
        </div>
    </div>
    <script src="~/layuiadmin/layui/layui.js"></script>
</body>
</html>
<script>
    layui.use(['layer', 'jquery'], function () {
        var layer = layui.layer
            , $ = layui.jquery;

        $(document).ready(function () {
            $("#android").mouseover(function () {
                $(this).find('img').attr("src", "/images/Android2.png");
            }).mouseout(function () {
                $(this).find('img').attr("src", "/images/Android1.png");
            });
            $("#ios").mouseover(function () {
                $(this).find('img').attr("src", "/images/ios1.png");
            }).mouseout(function () {
                $(this).find('img').attr("src", "/images/ios2.png");
            });
        });

    });
</script>