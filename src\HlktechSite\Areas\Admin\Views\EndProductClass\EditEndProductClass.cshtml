﻿@model HlktechSite.Entity.EndProductClass
@{
    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .page {
        min-height: 415px
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #419DFD !important;
    }

        .layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #419DFD !important;
        }

    .layui-tab-content {
        padding: 0px !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品分类")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("编辑")</span></a></li>
            </ul>
        </div>
    </div>

    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点>操作提示")"></h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("商品类型关系到产品发布时，产品规格的添加，没有产品类型的产品分类将不能添加产品规格。")</li>
            <li>@T("默认勾选'关联到子分类'将产品类型附加到该子分类，如子分类不同于上级分类的类型，可以取消勾选并单独对子分类的特定类型进行编辑选择。")</li>
        </ul>
    </div>


    <form id="goods_class_form" name="goodsClassForm" enctype="multipart/form-data" method="post">
        <input type="hidden" name="Id" value="@Model.Id" />


        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content" style="height: 100px;">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td colspan="2" class="required"><label class="gc_name validation" for="Name">@T("分类名称"):</label></td>
                            </tr>
                            <tr class="noborder">
                                <td class="vatop rowform"><input type="text" maxlength="20" value="@Model.Name" name="Name" id="Name" class="txt"></td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        //var modelLan = HlktechSite.Entity.NavigationLan.FindByNIdAndLId(Model.Id, item.Id, false);
                        var modelLan = HlktechSite.Entity.EndProductClassLan.FindByCIdAndLId(Model.Id, item.Id, false);
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td colspan="2" class="required"><label class="gc_name validation" for="[@item.Id].Name">@T("分类名称"):</label></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform"><input type="text" value="@modelLan" name="[@item.Id].Name" id="[@item.Id].Name" class="txt"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>

        @*<input type="hidden" name="ParentId" id="ParentId" value="@Model.ParentId" />*@
        <table class="ds-default-table">
            <tbody>
                @*<tr class="noborder">
                        <td colspan="2" class="required"><label class="gc_name validation" for="Name">@T("分类名称"):</label></td>
                    </tr>
                    <tr class="noborder">
                        <td class="vatop rowform"><input type="text" maxlength="20" value="@Model.Name" name="Name" id="Name" class="txt"></td>
                        <td class="vatop tips"></td>
                    </tr>*@
                <tr>
                    <td colspan="2" class="required"><label for="DisplayOrder">@T("排序"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="@Model.DisplayOrder" name="DisplayOrder" id="DisplayOrder" class="txt"></td>
                    <td class="vatop tips">@T("数字范围为0~255，数字越小越靠前")</td>
                </tr>
                <tr>
                    <td colspan="2" class="required"><label for="parent_id">@T("上级分类"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <select name="ParentId" id="ParentId">
                            <option value="0">@T("不更改所属分类（更改下拉）")</option>
                            @foreach (var item in (IEnumerable<HlktechSite.Entity.EndProductClass>)ViewBag.Plist)
                            {
                                if (Model.ParentId == item.Id && Model.ParentId != 0)
                                {
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }

                                }
                                else
                                {
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                }
                            }
                        </select>
                    </td>
                    <td class="vatop tips"><span style="color:#ff0000">@T("注意：不要把顶级分类整体移动到其它分类下")；</span>@T("如果选择上级分类，那么新增的分类则为被选择上级分类的子分类")</td>
                </tr>

            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
@*<script type="text/javascript" src="/static/plugins/mlselection.js" charset="utf-8"></script>
    <script type="text/javascript" src="/static/plugins/jquery.mousewheel.js"></script>*@
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;

    })

    $(function () {

        $('#goods_class_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().prev().find('td:first'));
            },
            rules: {
                Name: {
                    required: true,
                    remote: {
                        url: "@Url.Action("ExistName")",
                        type: 'get',
                        data: {
                            Key: function () {
                                return $('#gc_name').val();
                            },
                            Id: '@Model.Id'
                        }
                    }
                },
                DisplayOrder: {
                    number: true
                }
            },
            messages: {
                Name: {
                    required: '@T("分类名称不能为空")',
                    remote: '@T("该分类名称已经存在了，请您换一个")'
                },
                DisplayOrder: {
                    number: '@T("分类排序仅能为数字")'
                }
            }
        });
    });
</script> 