﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>成品跳转表</summary>
public partial class JumpProductModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>产品名称</summary>
    public String? Name { get; set; }

    /// <summary>推文链接</summary>
    public String? InfoUrl { get; set; }

    /// <summary>内容</summary>
    public String? Content { get; set; }

    /// <summary>国内Android下载地址</summary>
    public String? AndroidPaths { get; set; }

    /// <summary>国外Android下载地址</summary>
    public String? AndroidPaths1 { get; set; }

    /// <summary>IOS下载地址</summary>
    public String? IosPaths { get; set; }

    /// <summary>App Logo</summary>
    public String? AppLogo { get; set; }

    /// <summary>京东链接</summary>
    public String? JdUrl { get; set; }

    /// <summary>淘宝链接</summary>
    public String? TbUrl { get; set; }

    /// <summary>拼多多链接</summary>
    public String? PddUrl { get; set; }

    /// <summary>广告词</summary>
    public String? AdWord { get; set; }

    /// <summary>点击数</summary>
    public Int32 Clicks { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IJumpProduct model)
    {
        Id = model.Id;
        Name = model.Name;
        InfoUrl = model.InfoUrl;
        Content = model.Content;
        AndroidPaths = model.AndroidPaths;
        AndroidPaths1 = model.AndroidPaths1;
        IosPaths = model.IosPaths;
        AppLogo = model.AppLogo;
        JdUrl = model.JdUrl;
        TbUrl = model.TbUrl;
        PddUrl = model.PddUrl;
        AdWord = model.AdWord;
        Clicks = model.Clicks;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
