﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace AIAddress.Entity;

/// <summary>地址处理</summary>
public partial interface IAddress
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>业务员</summary>
    String? Salesman { get; set; }

    /// <summary>部门名称</summary>
    String? Department { get; set; }

    /// <summary>收货联系人</summary>
    String? Receiving { get; set; }

    /// <summary>收货地址</summary>
    String? Delivery { get; set; }

    /// <summary>Lat</summary>
    Double Lat { get; set; }

    /// <summary>Lng</summary>
    Double Lng { get; set; }

    /// <summary>Detail</summary>
    String? Detail { get; set; }

    /// <summary>Town</summary>
    String? Town { get; set; }

    /// <summary>PhoneNum</summary>
    String? PhoneNum { get; set; }

    /// <summary>CityCode</summary>
    String? CityCode { get; set; }

    /// <summary>Province</summary>
    String? Province { get; set; }

    /// <summary>Person</summary>
    String? Person { get; set; }

    /// <summary>ProvinceCode</summary>
    String? ProvinceCode { get; set; }

    /// <summary>Text</summary>
    String? Text { get; set; }

    /// <summary>County</summary>
    String? County { get; set; }

    /// <summary>City</summary>
    String? City { get; set; }

    /// <summary>CountyCode</summary>
    String? CountyCode { get; set; }

    /// <summary>TownCode</summary>
    String? TownCode { get; set; }

    /// <summary>是否请求</summary>
    Boolean Process { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
