﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>友情链接翻译</summary>
[Serializable]
[DataObject]
[Description("友情链接翻译")]
[BindIndex("IU_DG_FriendLinksLan_FId_LId", true, "FId,LId")]
[BindTable("DG_FriendLinksLan", Description = "友情链接翻译", ConnName = "DG", DbType = DatabaseType.None)]
public partial class FriendLinksLan : IFriendLinksLan, IEntity<IFriendLinksLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _FId;
    /// <summary>友情链接Id</summary>
    [DisplayName("友情链接Id")]
    [Description("友情链接Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("FId", "友情链接Id", "")]
    public Int32 FId { get => _FId; set { if (OnPropertyChanging("FId", value)) { _FId = value; OnPropertyChanged("FId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>友情链接标题</summary>
    [DisplayName("友情链接标题")]
    [Description("友情链接标题")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Name", "友情链接标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private Int16 _FType;
    /// <summary>类型。0为文字，1为图片。冗余字段</summary>
    [DisplayName("类型")]
    [Description("类型。0为文字，1为图片。冗余字段")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("FType", "类型。0为文字，1为图片。冗余字段", "")]
    public Int16 FType { get => _FType; set { if (OnPropertyChanging("FType", value)) { _FType = value; OnPropertyChanged("FType"); } } }

    private String? _Url;
    /// <summary>友情链接地址</summary>
    [DisplayName("友情链接地址")]
    [Description("友情链接地址")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Url", "友情链接地址", "")]
    public String? Url { get => _Url; set { if (OnPropertyChanging("Url", value)) { _Url = value; OnPropertyChanged("Url"); } } }

    private String? _Pic;
    /// <summary>友情链接图片</summary>
    [DisplayName("友情链接图片")]
    [Description("友情链接图片")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Pic", "友情链接图片", "")]
    public String? Pic { get => _Pic; set { if (OnPropertyChanging("Pic", value)) { _Pic = value; OnPropertyChanged("Pic"); } } }

    private Int32 _Sort;
    /// <summary>友情链接排序</summary>
    [DisplayName("友情链接排序")]
    [Description("友情链接排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Sort", "友情链接排序", "")]
    public Int32 Sort { get => _Sort; set { if (OnPropertyChanging("Sort", value)) { _Sort = value; OnPropertyChanged("Sort"); } } }

    private Int32 _Enabled;
    /// <summary>是否启用</summary>
    [DisplayName("是否启用")]
    [Description("是否启用")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Enabled", "是否启用", "")]
    public Int32 Enabled { get => _Enabled; set { if (OnPropertyChanging("Enabled", value)) { _Enabled = value; OnPropertyChanged("Enabled"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IFriendLinksLan model)
    {
        Id = model.Id;
        FId = model.FId;
        LId = model.LId;
        Name = model.Name;
        FType = model.FType;
        Url = model.Url;
        Pic = model.Pic;
        Sort = model.Sort;
        Enabled = model.Enabled;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "FId" => _FId,
            "LId" => _LId,
            "Name" => _Name,
            "FType" => _FType,
            "Url" => _Url,
            "Pic" => _Pic,
            "Sort" => _Sort,
            "Enabled" => _Enabled,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "FId": _FId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "FType": _FType = Convert.ToInt16(value); break;
                case "Url": _Url = Convert.ToString(value); break;
                case "Pic": _Pic = Convert.ToString(value); break;
                case "Sort": _Sort = value.ToInt(); break;
                case "Enabled": _Enabled = value.ToInt(); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得友情链接翻译字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>友情链接Id</summary>
        public static readonly Field FId = FindByName("FId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>友情链接标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>类型。0为文字，1为图片。冗余字段</summary>
        public static readonly Field FType = FindByName("FType");

        /// <summary>友情链接地址</summary>
        public static readonly Field Url = FindByName("Url");

        /// <summary>友情链接图片</summary>
        public static readonly Field Pic = FindByName("Pic");

        /// <summary>友情链接排序</summary>
        public static readonly Field Sort = FindByName("Sort");

        /// <summary>是否启用</summary>
        public static readonly Field Enabled = FindByName("Enabled");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得友情链接翻译字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>友情链接Id</summary>
        public const String FId = "FId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>友情链接标题</summary>
        public const String Name = "Name";

        /// <summary>类型。0为文字，1为图片。冗余字段</summary>
        public const String FType = "FType";

        /// <summary>友情链接地址</summary>
        public const String Url = "Url";

        /// <summary>友情链接图片</summary>
        public const String Pic = "Pic";

        /// <summary>友情链接排序</summary>
        public const String Sort = "Sort";

        /// <summary>是否启用</summary>
        public const String Enabled = "Enabled";
    }
    #endregion
}
