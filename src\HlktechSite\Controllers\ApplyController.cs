﻿using DG.Web.Framework;

using DH.Helpers;
using DH.WebHook;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek.Helpers;
using Pek.Models;

namespace HlktechSite.Controllers;

public class ApplyController : DGBaseControllerX
{
    /// <summary>
    /// 定制申请首页
    /// </summary>
    /// <returns></returns>
    public IActionResult Index()
    {
        return DGView(viewName: "Index", true);
    }

    /// <summary>
    /// 定制申请接口
    /// </summary>
    /// <param name="comName">公司名称</param>
    /// <param name="business">主营业务</param>
    /// <param name="job">申请人职务</param>
    /// <param name="email">邮箱</param>
    /// <param name="comUrl">公司网址</param>
    /// <param name="comlinkman">公司联系人</param>
    /// <param name="phone">手机号</param>
    /// <param name="type">定制类型</param>
    /// <param name="predict">年均需求量</param>
    /// <param name="model">基于的产品类型</param>
    /// <param name="purchase">首批采购量</param>
    /// <param name="demand">定制需求</param>
    /// <param name="setting">应用的项目背景</param>
    /// <param name="verification">验证码</param>
    /// <returns></returns>
    public IActionResult Apply(String comName, String business, String job, String email, String comUrl, String comlinkman, String phone, int type, String purchase, String model, String predict, String demand, String setting, String verification)
    {
        if (comName.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("公司名称不能为空") });
        if (business.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("主营业务不能为空") });
        if (job.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("申请人职务不能为空") });
        if (email.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("邮箱不能为空") });
        if (!ValidateHelper.IsEmail(email)) return Json(new DResult { msg = GetResource("请输入正确的邮箱") });
        if (comUrl.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("公司网址不能为空") });
        if (comlinkman.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("公司联系人不能为空") });
        if (phone.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("手机号不能为空") });
        if (CurrentLanguage == "cn")
            if (!ValidateHelper.IsMobile(phone)) return Json(new DResult { msg = GetResource("请输入正确的手机号") });
        if (setting.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("定制需求不能为空") });
        if (verification.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("应用的项目背景不能为空") });
        if (verification.IsNullOrWhiteSpace()) return Json(new DResult { msg = "验证码不能为空" });
        var ybbcode = HttpContext.Session.GetString("ybbcode");
        if (ybbcode.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("验证码过期,请点击验证码图片刷新") });
        if (verification.ToLower() != ybbcode.ToLower()) return Json(new DResult { msg = GetResource("验证码错误") });

        var apply = new Customization();
        apply.ComName = comName;
        apply.Business = business;
        apply.Job = job;
        apply.Email = email;
        apply.ComUrl = comUrl;
        apply.Linkman = comlinkman;
        apply.Phone = phone;
        apply.Type = type;
        apply.Demand = demand;
        apply.Setting = setting;
        if (type == 1)
        {
            apply.Predict = predict;
            apply.Model = model;
            apply.Purchase = purchase;
        }
        apply.Insert();

        string template1 = $"#### {(type == 0 ? "软件" : "产品")}定制申请通知:\n" +
            $"1. **公司信息:**\n" +
            $" + 公司名称: {comName}  \n" +
            $" + 公司联系人: {comlinkman}  \n" +
            $" + 公司网址: [{comUrl}]({comUrl})  \n" +
            $" + 主营业务: {business}  \n" +
            $"\n2. **申请人信息:**\n" +
            $" + 申请人职务: {job}  \n" +
            $" + 申请人电话: {phone}  \n" +
            $" + 申请人邮箱: {email}  \n\n";

        if (type == 1)
        {
            template1 += $"\n5. **定制产品详情:**\n" +
                $" + 基于产品型号:{(model.IsNullOrWhiteSpace() ? "未指定" : model)}   \n" +
                $" + 首批采购量:{(purchase.IsNullOrWhiteSpace() ? "未指定" : purchase)}   \n" +
                $" + 预计年均需求量:{(predict.IsNullOrWhiteSpace() ? "未指定" : predict)}   \n";
        }

        string template2 = $"\n3. **定制需求:**   \n\n" +
            $"``` txt\n" +
            $"{demand} \n" +
            $"```\n" +
            $"\n4. **定制背景:**  \n\n" +
            $"``` txt\n" +
            $"{setting}  \n" +
            $"```\n";

        DingTalkRobot.OapiRobotMarkDown($"消息提醒<br />", template1+template2, new List<string>(), false);
        //DingTalkRobot.OapiRobotText(template, new List<string>(), false);

        return Json(new DResult { success = true, msg = GetResource("提交成功") });
    }

    /// <summary>
    /// 样品申请接口
    /// </summary>
    /// <param name="ComName">公司名称</param>
    /// <param name="ComPeople">企业人数</param>
    /// <param name="Website">企业官网</param>
    /// <param name="Turnover">年营业额</param>
    /// <param name="ComType">企业类型</param>
    /// <param name="Address">企业地址</param>
    /// <param name="Linkman">联系人</param>
    /// <param name="Position">职位</param>
    /// <param name="Phone">电话</param>
    /// <param name="QQ">QQ</param>
    /// <param name="Email">邮箱</param>
    /// <param name="Fax">传真</param>
    /// <param name="ProModel">产品型号</param>
    /// <param name="Demand">月需求量</param>
    /// <param name="Theme">申请主题</param>
    /// <param name="Describe">需求描述</param>
    /// <param name="verification">验证码</param>
    /// <returns></returns>
    public IActionResult Sample(String ComName, String ComPeople, String Website, String Turnover, String ComType, String Address, String Linkman, String Position, String Phone, String QQ, String Email, String Fax, String ProModel, String Demand, String Theme, String Describe, String verification)
    {
        if (ComName.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("公司名称不能为空") });
        if (Linkman.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("联系人不能为空") });
        if (Phone.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("联系人手机号不能为空") });
        if (CurrentLanguage == "cn")
            if (!ValidateHelper.IsMobile(Phone)) return Json(new DResult { msg = GetResource("请输入正确的手机号") });
        if (Email.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("联系人邮箱不能为空") });
        if (!ValidateHelper.IsEmail(Email)) return Json(new DResult { msg = GetResource("请输入正确的联系人邮箱") });
        if (ProModel.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("产品型号不能为空") });
        if (Theme.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("申请主题不能为空") });
        if (Describe.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("需求描述不能为空") });
        if (verification.IsNullOrWhiteSpace()) return Json(new DResult { msg = "验证码不能为空" });
        var ybbcode = HttpContext.Session.GetString("ybbcode");
        if (ybbcode.IsNullOrWhiteSpace()) return Json(new DResult { msg = GetResource("验证码过期,请点击验证码图片刷新") });
        if (verification.ToLower() != ybbcode.ToLower()) return Json(new DResult { msg = GetResource("验证码错误") });

        var sample = new SampleApplication();
        sample.ComName = ComName;
        sample.ComPeople = ComPeople;
        sample.Website = Website;
        sample.Turnover = Turnover;
        sample.ComType = ComType;
        sample.Linkman = Linkman;
        sample.Address = Address;
        sample.Position = Position;
        sample.Phone = Phone;
        sample.QQ = QQ;
        sample.Email = Email;
        sample.Fax = Fax;
        sample.ProModel = ProModel;
        sample.Demand = Demand;
        sample.Theme = Theme;
        sample.Fax = Fax;
        sample.Describe = Describe;

        sample.Insert();

        string template = $"#### 样品申请通知:\n" +
            $"1. **公司信息:**\n" +
            $" + 公司名称: {ComName}  \n" +
            $" + 联系人: {Linkman}  \n" +
            $" + 职位: {(Position.IsNullOrWhiteSpace()?"未注明":Position)}  \n" +
            $" + 电话: {Phone}   \n" +
            $" + 企业官网: [{Website}]({Website})  \n" +
            $" + 邮箱: {Email}  \n" +
            $"\n2. **样品需求信息:**\n" +
            $" + 产品型号: {ProModel}  \n" +
            $" + 月需求量: {(Demand.IsNullOrWhiteSpace() ? "未指定" : Demand)}   \n" +
            $"\n3. **申请主题:** {Theme}  \n" +
            $"\n4. **需求描述:** {Describe}  \n\n";

        DingTalkRobot.OapiRobotMarkDown($"消息提醒<br />", template, new List<string>(), false);
        return Json(new DResult { success = true, msg = GetResource("提交成功") });
    }

}
