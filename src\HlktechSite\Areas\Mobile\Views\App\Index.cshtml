﻿@model AppManagers
@{
    var id = (Int32)ViewBag.Id;
    var modelAppManagersLan = AppManagersLan.FindItemByJIdAndlId(id, language.Id, true);
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover" />
    <title>@(modelAppManagersLan.Name) @T("APP下载") - @T("海凌科电子")</title>
    <meta name="description" content="@T("海凌科下载中心为您提供WIFI模块,电源模块,蓝牙模块,人脸识别模块,语音识别模块,3G模块,4G模块,串口服务器,GPRS模块,工业级路由器等产品的资料下载")" />
    <meta name="keywords" content="@(modelAppManagersLan.Name) @T("APP下载")" />
    <link href="~/css/weui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>
    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/lay/modules/jquery.js"></script>
    <script src="~/static/usercenter/pc/js/jquery-1.12.2.min.js"></script>
    <link href="~/iconfont/iconfont.css" rel="stylesheet" />
    <style>
        .pageBox {
            margin: auto;
            width: 100%;
            max-width: 435px;
            height: 100vh;
            position: relative;
            top: 0;
            left: 0;
            background-image: url('/images/app_donwload_bg.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            overflow: hidden;
        }

        .pageImg {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .logo {
            position: absolute;
            top: 21%;
            left: 35.5%;
            right: 0;
            max-width: 14vh;
            color: #2e7ff5;
            font-size: 1.7rem;
            text-align: center;
            border-radius: 13px;
            filter: drop-shadow(0 0 0.3rem skyblue);
        }

        .appName {
            position: absolute;
            top: 43%;
            left: 0;
            right: 0;
            color: #2e7ff5;
            font-size: 1.7rem;
            text-align: center;
        }

        .text {
            position: absolute;
            top: 47%;
            left: 15%;
            right: 0;
            width: 70%;
            color: #2e7ff5;
            font-size: .8rem;
            text-align: left;
            text-indent: 10px;
            text-shadow: 0 0 2px rgb(199, 234, 248);
        }

        a {
            color: white !important;
            font-size: 18px;
            display: block;
            margin-top: 3px;
        }

        .DonwloadBox {
            position: absolute;
            bottom: 13%;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-evenly;
            /* border: 2px solid red; */
        }

            .DonwloadBox > div {
                width: 150px;
                border-radius: 13px;
                border: 2px solid white;
                padding: 0.5rem;
                margin: 20px 0;
                text-align: center;
                position: relative;
                display: flex;
                justify-content: center;
            }

            /* .DonwloadBox > div:last-child {
                margin: 20px 0 0 0;
            } */

        .v {
            position: absolute;
            margin-top: 50px;
            color: #f7f7f7;
            /* border: 1px solid white; */
        }

        .message {
            position: absolute;
            bottom: 8%;
            width: 100%;
            text-align: center;
            font-size: 12px;
            color: #fefeff;
        }

        .update {
            position: absolute;
            bottom: 5%;
            width: 100%;
            text-align: center;
            font-size: 12px;
            color: #fefeff;
        }

    </style>
</head>
<body>
    <div class="pageBox">
        <div style="text-align: center; margin-top: 2rem;">
            @if (!Model.AppLogo.IsNullOrWhiteSpace() && @Model?.AppLogo != null)
            {
                <img class="logo" src="@CDN.GetCDN()/@Model?.AppLogo"/>
            }
        </div>
        <div class="appName">@modelAppManagersLan.Name</div>

        <div class="text" style="margin-top: 5px;">
            @modelAppManagersLan.Content
        </div>
     
        <div class="DonwloadBox">
            @if (@Model?.IosPaths.IsNullOrWhiteSpace() == false && @Model?.IosPaths != null)
            {
            <div>
                <img src="~/images/ios_logo.png" style="width: 26px;margin-right: 12px;" /> 
                <a href="@Model?.IosPaths">@T("AppStore")</a>
                <div class="v" style="margin-left: 2px;">@T("版本号")：@Model?.IosVersion</div>
            </div>
            }
            @if (@Model?.AndroidPaths.IsNullOrWhiteSpace() == false || @Model?.AndroidPaths1.IsNullOrWhiteSpace() == false || @Model?.ApkFilePath.IsNullOrWhiteSpace() == false)
            {
                    @if (language.UniqueSeoCode == "cn")
                    {
                        @if (@Model?.AndroidPaths.IsNullOrWhiteSpace() == false)
                        {
                        <div>
                            <img src="~/images/android_logo.png" style="width: 26px;margin-right: 10px;" />
                            <a id="dowload1" href="@Model?.AndroidPaths">@T("安卓下载")</a>
                            <div class="v" style="margin-left: 5px;">@T("版本号")：@Model?.AndroidVersion</div>
                        </div>
                        }
                        else if (@Model?.ApkFilePath.IsNullOrWhiteSpace() == false)
                        {
                        <div>
                            <img src="~/images/android_logo.png" style="width: 26px;margin-right: 10px;" />
                            <a id="dowload2" href="/@Model?.ApkFilePath">@T("安卓下载")</a>
                            <div class="v" style="margin-left: 5px;">@T("版本号")：@Model?.AndroidVersion</div>
                        </div>
                        }
                        @if (@Model?.AndroidPaths1.IsNullOrWhiteSpace() == false)
                        { 
                        <div>
                            <img src="~/images/android_logo.png" style="width: 26px;margin-right: 10px;" />
                            <a id="dowload3" href="@Model?.AndroidPaths1">@T("Google Play")</a>
                            <div class="v" style="margin-left: 5px;">@T("版本号")：@Model?.AndroidVersion</div>
                        </div>
                        }
                    }
                    else
                    {
                        @if (@Model?.AndroidPaths1.IsNullOrWhiteSpace() == false)
                        {
                        <div style="margin-bottom: 0;">
                            <img src="~/images/android_logo.png" style="width: 26px;margin-right: 10px;" />
                            <a id="dowload4" href="@Model?.AndroidPaths1">@T("Google Play")</a>
                            <div class="v" style="margin-left: 5px;">@T("版本号")：@Model?.AndroidVersion</div>
                        </div>
                        }
                        @if (@Model?.AndroidPaths.IsNullOrWhiteSpace() == false)
                        {
                        <div>
                            <img src="~/images/android_logo.png" style="width: 26px;margin-right: 10px;" />
                            <a id="dowload5" href="@Model?.AndroidPaths">@T("安卓下载")</a>
                            <div class="v" style="margin-left: 5px;">@T("版本号")：@Model?.AndroidVersion</div>
                        </div>
        
                        }
                        else if (@Model?.ApkFilePath.IsNullOrWhiteSpace() == false)
                        {
                        <div>
                            <img src="~/images/android_logo.png" style="width: 26px;margin-right: 10px;" />
                            <a id="dowload6" href="/@Model?.ApkFilePath">@T("安卓下载")</a>
                            <div class="v" style="margin-left: 5px;">@T("版本号")：@Model?.AndroidVersion</div>
                        </div>
                        }
                    }
            }

        </div>

        <div class="message"><span style="letter-spacing: 1px;">@T("开发者:深圳市海凌科电子有限公司")</span> </div>

        <div class="update">
            @T("发布日期：")@Model?.UpdateTime.ToFullString().Split(" ")[0]
        </div>
    </div>
</body>
</html>