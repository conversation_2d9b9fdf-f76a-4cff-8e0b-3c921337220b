@model IEnumerable<OnlineKeFu>
    @{
    var cdn = CDN.GetCDN();
    }
    <div class="fixed-bar">
        <div class="wide-bar">
            <div class="consult-box">
                <div class="consult-header clearfix">
                    <h3 class="consult-title">@T("在线客服")<span class="icon-times-circle-o"><img
                                src="@(CDN.GetCDN())/images/x.png" alt="Alternate Text" /></span></h3>
                </div>
                <ul class="consult-list">
                    <li class="clearfix">
                        <a id="open-ai-service" href="javascript:;" lay-on="test-iframe-handle">
                            <img style="width: 28px;height: 28px;" border="0" src="@(cdn)/images/kffloat.png" alt="AI"
                                title="点击开始AI聊天">
                            <span>@T("deepseek客服")</span>
                        </a>
                    </li>
                    @foreach (var item in Model)
                    {
                    if (item.Location != 0 && item.Location != 1) continue; //非平台下客服不加载
                    switch (item.OType)
                    {
                    case 0:
                    <li class="clearfix">
                        <a target="_blank"
                            href="http://wpa.qq.com/msgrd?v=3&amp;uin=@(item.ONumber)&amp;site=qq&amp;menu=yes">
                            <img border="0" src="@(cdn)/images/JS_qq.png" alt="QQ" title="点击开始QQ交谈/留言">
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;

                    case 1:
                    <li class="clearfix">
                        <a target="_blank"
                            href="https://www.taobao.com/go/market/webww/ww.php?ver=3&amp;touid=@(item.ONumber)&amp;siteid=cntaobao&amp;status=1&amp;charset=utf-8">
                            <img border="0" src="@(cdn)/images/ww.gif" alt="旺旺" title="点击开始旺旺交谈/留言">
                        </a>
                    </li>
                    break;

                    case 2:
                    <li class="clearfix">
                        <a rel="nofollow" href="skype:@(item.ONumber)?chat" target="_blank">
                            <img src="@(cdn)/images/skype.png" alt="Skype" title="点击开始Skype交谈/留言" />
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;

                    case 3:
                    var number = item.ONumber?.TrimStart("+").Replace(" ", "").Trim() ?? "";
                    //Whatsapp
                    <li class="clearfix">
                        <a rel="nofollow" href="https://api.whatsapp.com/send?phone=@(number)&text=Hello">
                            <img style="width:20px;height:20px;" src="@(cdn)/images/whatsapp.png" alt="Whatsapp"
                                title="点击开始Whatsapp交谈/留言" />
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;
                    }
                    }
                </ul>
            </div>
        </div>
    </div>


    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>

    <style>
        .layui-layer-title {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            justify-content: space-between;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        .layui-layer-title {
            background: #dbdbdb;
            color: #000;
            font-size: 20px;
            padding: 10px;
            display: flex;
            align-items: center;
        }
        .layui-layer-content {
            padding: 0;
        }
        #layui-layer-iframe1,
        #layui-layer-iframe2,
        #layui-layer-iframe3,
        #layui-layer-iframe4,
        #layui-layer-iframe5,
        #layui-layer-iframe6,
        #layui-layer-iframe7,
        #layui-layer-iframe8 {
            width: 100%;
        }
        .layui-layer-iframe {
            position: fixed !important;
            top: 15% !important;
            background-color: #eeeeee !important;
            border-radius: 10px !important;
        }
        .layui-layer-btn {
            justify-content: center;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }
         @* deepseek客服右上角关闭按钮 *@
        .layui-layer-btn a{
            text-decoration: none !important;
        }
       .layui-layer-btn0{
         position: absolute;
         top: 14px;
         right: 20px;
         cursor: pointer;
         font-weight: bolder
       }

    </style>
    <script asp-location="Footer">
        $(() => {
            $(".icon-times-circle-o").click(function () {
                $(this).parents().find(".fixed-bar").css("right", "-135px");
                $(".show-fixed-bar").addClass("comeOut");
            });
            $(".show-fixed-bar").click(() => {
                $(".icon-times-circle-o").parents().find(".fixed-bar").css("right", "0px");
                $(".show-fixed-bar").removeClass("comeOut");
            })
        })
        document.getElementById('open-ai-service').addEventListener('click', () => {
            layer.open({
                type: 2,
                title: '@T("deepseek客服")',
                area: ['500px', '700px'],
                fixed: false,
                shadeClose: true,
                btn: ['<i class="layui-icon layui-icon-close"></i>'],
                content: 'https://kf.hlktech.com/AIChat',
                success: function(layero, index) {
                    console.log('deepseek客服客服窗口加载成功');
                    // 延迟执行，确保iframe完全加载
                    setTimeout(() => {
                        onSend(layero, index);
                    }, 100);
                },
                end: function() {
                    console.log('deepseek客服客服窗口已关闭');
                }
            });
        })
    function onSend(layero, index) {
        console.log('111 开始设置iframe监听器');

        // 多种方式获取iframe
        let iframe = null;

        // 方式1: 通过layero参数获取
        if (layero) {
            iframe = layero.find('iframe')[0];
            console.log('通过layero获取iframe:', iframe);
        }

        if (!iframe) {
            console.error('未找到iframe元素');
            return;
        }

        console.log('222 找到iframe，准备设置监听器');

        // 检查iframe是否已经加载完成
        function setupIframeListener() {
            console.log('333 开始设置iframe内容监听器');
            try {
                console.log('444 尝试访问iframe内容');

                // 获取iframe内部文档
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                console.log('555 获取到iframeDoc:', iframeDoc);

                if (!iframeDoc || iframeDoc.readyState !== 'complete') {
                    console.log('666 iframe文档未完全加载，等待中...');
                    setTimeout(setupIframeListener, 500);
                    return;
                }

                console.log('777 iframe文档已完全加载');

                // 多种选择器尝试获取输入框
                const inputSelectors = [
                    'input[type="text"]',
                    'textarea',
                    '.message-input',
                    '#message',
                    '.chat-input',
                    '[placeholder*="消息"]',
                    '[placeholder*="message"]'
                ];

                let messageInput = null;
                for (const selector of inputSelectors) {
                    messageInput = iframeDoc.querySelector(selector);
                    if (messageInput) {
                        console.log('888 找到输入框:', selector, messageInput);
                        break;
                    }
                }

                if (!messageInput) {
                    console.log('999 未找到消息输入框，尝试的选择器:', inputSelectors);
                    return;
                }

                // 多种选择器尝试获取发送按钮
                const sendSelectors = [
                    '.send',
                    '#send',
                    'button[type="submit"]',
                    '.send-btn',
                    '.btn-send',
                    '[onclick*="send"]',
                    'button:contains("发送")',
                    'button:contains("Send")'
                ];

                let sendButton = null;
                for (const selector of sendSelectors) {
                    sendButton = iframeDoc.querySelector(selector);
                    if (sendButton) {
                        console.log('AAA 找到发送按钮:', selector, sendButton);
                        break;
                    }
                }

                // 监听输入框的回车键事件
                messageInput.addEventListener('keydown', function (e) {
                    console.log('BBB 键盘事件:', e.key);
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault(); // 阻止默认换行行为
                        console.log('CCC 检测到回车键');

                        // 如果有发送按钮则点击发送
                        if (sendButton) {
                            sendButton.click();
                            console.log('DDD 通过回车键触发发送按钮');
                        } else {
                            console.log('EEE 未找到发送按钮，尝试触发表单提交');
                            // 尝试触发表单提交
                            const form = messageInput.closest('form');
                            if (form) {
                                form.submit();
                            }
                        }
                    }
                });

                console.log('FFF 消息发送监听已激活');

            } catch (error) {
                console.error('GGG 无法访问iframe内容（跨域限制）:', error);
                // 尝试使用postMessage通信
                setupPostMessageCommunication();
            }
        }

        // 如果iframe已经加载完成，直接设置监听器
        if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
            console.log('iframe已加载完成，直接设置监听器');
            setupIframeListener();
        } else {
            // 否则等待加载完成
            console.log('等待iframe加载完成');
            iframe.onload = setupIframeListener;
            // 备用方案：定时检查
            setTimeout(setupIframeListener, 1000);
        }

        // PostMessage通信方案（跨域情况）
        function setupPostMessageCommunication() {
            console.log('HHH 设置PostMessage通信');
            window.addEventListener('message', function (event) {
                console.log('event :>> ', event);
                if (event.origin !== 'https://kf.hlktech.com') return;

                if (event.data.type === 'SEND_MESSAGE') {
                    console.log('III 收到发送消息事件:', event.data);
                }
            });

            // 向iframe发送设置请求
            if (iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'SETUP_SEND_LISTENER'
                }, 'https://kf.hlktech.com');
            }
        }
    }
</script>