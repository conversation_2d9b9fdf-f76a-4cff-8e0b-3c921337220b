@model IEnumerable<OnlineKeFu>
    @{
    var cdn = CDN.GetCDN();
    }
    <div class="fixed-bar">
        <div class="wide-bar">
            <div class="consult-box">
                <div class="consult-header clearfix">
                    <h3 class="consult-title">@T("在线客服")<span class="icon-times-circle-o"><img
                                src="@(CDN.GetCDN())/images/x.png" alt="Alternate Text" /></span></h3>
                </div>
                <ul class="consult-list">
                    <li class="clearfix">
                        <a id="open-ai-service" href="javascript:;" lay-on="test-iframe-handle">
                            <img style="width: 28px;height: 28px;" border="0" src="@(cdn)/images/kffloat.png" alt="AI"
                                title="点击开始AI聊天">
                            <span>@T("deepseek客服")</span>
                        </a>
                    </li>
                    @foreach (var item in Model)
                    {
                    if (item.Location != 0 && item.Location != 1) continue; //非平台下客服不加载
                    switch (item.OType)
                    {
                    case 0:
                    <li class="clearfix">
                        <a target="_blank"
                            href="http://wpa.qq.com/msgrd?v=3&amp;uin=@(item.ONumber)&amp;site=qq&amp;menu=yes">
                            <img border="0" src="@(cdn)/images/JS_qq.png" alt="QQ" title="点击开始QQ交谈/留言">
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;

                    case 1:
                    <li class="clearfix">
                        <a target="_blank"
                            href="https://www.taobao.com/go/market/webww/ww.php?ver=3&amp;touid=@(item.ONumber)&amp;siteid=cntaobao&amp;status=1&amp;charset=utf-8">
                            <img border="0" src="@(cdn)/images/ww.gif" alt="旺旺" title="点击开始旺旺交谈/留言">
                        </a>
                    </li>
                    break;

                    case 2:
                    <li class="clearfix">
                        <a rel="nofollow" href="skype:@(item.ONumber)?chat" target="_blank">
                            <img src="@(cdn)/images/skype.png" alt="Skype" title="点击开始Skype交谈/留言" />
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;

                    case 3:
                    var number = item.ONumber?.TrimStart("+").Replace(" ", "").Trim() ?? "";
                    //Whatsapp
                    <li class="clearfix">
                        <a rel="nofollow" href="https://api.whatsapp.com/send?phone=@(number)&text=Hello">
                            <img style="width:20px;height:20px;" src="@(cdn)/images/whatsapp.png" alt="Whatsapp"
                                title="点击开始Whatsapp交谈/留言" />
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;
                    }
                    }
                </ul>
            </div>
        </div>
    </div>


<link href="@Url.Content("~/static/plugins/js/layui/css/layui.css")" rel="stylesheet"/>
    <script src="~/static/plugins/js/layui/layui.js"></script>

    <style>
        .layui-layer-title {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            justify-content: space-between;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        .layui-layer-title {
            background: #dbdbdb;
            color: #000;
            font-size: 20px;
            padding: 10px;
            display: flex;
            align-items: center;
        }
        .layui-layer-content {
            padding: 0;
        }
        #layui-layer-iframe1,
        #layui-layer-iframe2,
        #layui-layer-iframe3,
        #layui-layer-iframe4,
        #layui-layer-iframe5,
        #layui-layer-iframe6,
        #layui-layer-iframe7,
        #layui-layer-iframe8 {
            width: 100%;
        }
        .layui-layer-iframe {
            position: fixed !important;
            background-color: #eeeeee !important;
            border-radius: 10px !important;
        }

        /* 确保遮罩层显示 */
        .layui-layer-shade {
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            position: fixed;
        }
        .layui-layer-btn {
            justify-content: center;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }
         @* deepseek客服右上角关闭按钮 *@
        .layui-layer-btn a{
            text-decoration: none !important;
        }
       .layui-layer-btn0{
         position: absolute;
         top: 14px;
         right: 20px;
         cursor: pointer;
         font-weight: bolder
       }

    </style>
    <script asp-location="Footer">
        $(() => {
            $(".icon-times-circle-o").click(function () {
                $(this).parents().find(".fixed-bar").css("right", "-135px");
                $(".show-fixed-bar").addClass("comeOut");
            });
            $(".show-fixed-bar").click(() => {
                $(".icon-times-circle-o").parents().find(".fixed-bar").css("right", "0px");
                $(".show-fixed-bar").removeClass("comeOut");
            })
        })
        document.getElementById('open-ai-service').addEventListener('click', () => {
            layer.open({
                type: 2,
                title: '@T("deepseek客服")',
                area: ['500px', '700px'],
                shade: 0.6,  // 遮罩透明度和颜色
                shadeClose: true,
                btn: ['<i class="layui-icon layui-icon-close"></i>'],
                content: 'https://kf.hlktech.com/AIChat',
                success: function(layero, index) {
                    console.log('deepseek客服客服窗口加载成功');
                    var iframe = layero.find('iframe')[0];
                    console.log('找到iframe:', iframe);

                    // 打开第三方对话窗口,监听回车事件 添加触发回车发送消息方法
                    setupEnterKeyListener(iframe);
                },
                end: function() {
                    console.log('deepseek客服客服窗口已关闭');
                }
            });
        })

        // 设置回车键监听器函数
        function setupEnterKeyListener(iframe) {
            if (!iframe) {
                console.error('iframe元素不存在');
                return;
            }

            // 等待iframe加载完成
            function waitForIframeLoad() {
                try {
                    // 检查iframe是否加载完成
                    if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                        console.log('iframe已加载完成，开始设置监听器');
                        setupKeyListener();
                    } else {
                        console.log('等待iframe加载...');
                        // 如果连续等待超过10秒，切换到跨域方案
                        if (!waitForIframeLoad.attempts) {
                            waitForIframeLoad.attempts = 0;
                        }
                        waitForIframeLoad.attempts++;

                        if (waitForIframeLoad.attempts > 20) { // 10秒后切换
                            console.log('等待超时，切换到跨域通信方案');
                            setupPostMessageFallback();
                            return;
                        }

                        setTimeout(waitForIframeLoad, 500);
                    }
                } catch (error) {
                    console.log('检查iframe状态时出错（可能是跨域）:', error);
                    console.log('立即切换到跨域通信方案');
                    setupPostMessageFallback();
                }
            }

            // 设置键盘监听器
            function setupKeyListener() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    console.log('获取到iframe文档:', iframeDoc);

                    // 根据AIChat.html的结构，输入框ID是'mes'
                    const messageInput = iframeDoc.getElementById('mes');
                    console.log('找到输入框:', messageInput);

                    if (!messageInput) {
                        console.error('未找到消息输入框 #mes');
                        return;
                    }

                    // 根据AIChat.html的结构，发送按钮class是'send'
                    const sendButton = iframeDoc.querySelector('.send');
                    console.log('找到发送按钮:', sendButton);

                    if (!sendButton) {
                        console.error('未找到发送按钮 .send');
                        return;
                    }

                    // 监听输入框的键盘事件
                    messageInput.addEventListener('keydown', function(event) {
                        console.log('检测到按键:', event.key);

                        // 检测回车键且不是Shift+回车（避免换行）
                        if (event.key === 'Enter' && !event.shiftKey) {
                            event.preventDefault(); // 阻止默认的换行行为

                            // 检查输入框是否有内容
                            const message = messageInput.value.trim();
                            if (message.length > 0) {
                                console.log('回车发送消息:', message);

                                // 触发发送按钮的点击事件
                                sendButton.click();

                                // 也可以手动触发事件
                                const clickEvent = new Event('click', {
                                    bubbles: true,
                                    cancelable: true
                                });
                                sendButton.dispatchEvent(clickEvent);

                                console.log('已触发发送按钮点击');
                            } else {
                                console.log('输入框为空，不发送消息');
                            }
                        }
                    });

                    console.log('✅ 回车发送监听器设置成功');

                    // 向iframe发送设置完成的消息
                    iframe.contentWindow.postMessage({
                        type: 'ENTER_LISTENER_SETUP',
                        success: true
                    }, 'https://kf.hlktech.com');

                } catch (error) {
                    console.error('❌ 设置监听器失败（可能是跨域限制）:', error);

                    // 跨域情况下的备用方案
                    setupPostMessageFallback();
                }
            }

            // 跨域备用方案：使用postMessage通信
            function setupPostMessageFallback() {
                console.log('🔄 使用postMessage跨域通信方案');

                // 监听来自iframe的消息
                const messageHandler = function(event) {
                    console.log('📨 收到消息 origin:', event.origin, 'data:', event.data);

                    // 允许的域名列表
                    const allowedOrigins = [
                        'https://kf.hlktech.com',
                        'http://kf.hlktech.com',
                        'https://ask.hlktech.com'
                    ];

                    if (!allowedOrigins.includes(event.origin)) {
                        console.log('❌ 消息来源不被允许:', event.origin);
                        return;
                    }

                    if (event.data.type === 'ENTER_KEY_PRESSED') {
                        console.log('✅ iframe报告回车键被按下:', event.data.message);
                    }

                    if (event.data.type === 'iframeLoaded') {
                        console.log('✅ iframe已加载完成');
                        // iframe加载完成后，发送设置请求
                        setTimeout(() => {
                            sendSetupRequest();
                        }, 1000);
                    }

                    if (event.data.type === 'ENTER_LISTENER_SETUP_COMPLETE') {
                        console.log('✅ iframe内回车监听器设置完成');
                    }
                };

                // 移除可能存在的旧监听器
                window.removeEventListener('message', messageHandler);
                window.addEventListener('message', messageHandler);

                // 发送设置请求的函数
                function sendSetupRequest() {
                    try {
                        console.log('📤 向iframe发送设置请求');
                        iframe.contentWindow.postMessage({
                            type: 'SETUP_ENTER_LISTENER'
                        }, '*'); // 使用通配符，因为可能有多个域名
                    } catch (error) {
                        console.error('❌ 发送消息失败:', error);
                    }
                }

                // 立即尝试发送设置请求
                sendSetupRequest();

                // 备用：延迟发送
                setTimeout(sendSetupRequest, 2000);
                setTimeout(sendSetupRequest, 5000);
            }

            // 由于跨域限制，直接使用postMessage方案
            console.log('🚀 直接启用跨域通信方案');
            setupPostMessageFallback();

            // 备用：如果是同域，也尝试直接访问
            iframe.onload = function() {
                console.log('iframe onload事件触发');
                setTimeout(() => {
                    try {
                        if (iframe.contentDocument) {
                            console.log('检测到同域，尝试直接设置监听器');
                            setupKeyListener();
                        }
                    } catch (e) {
                        console.log('确认为跨域，继续使用postMessage方案');
                    }
                }, 1000);
            };
        }
    window.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            console.log('回车发送消息');
        }
    });


</script>