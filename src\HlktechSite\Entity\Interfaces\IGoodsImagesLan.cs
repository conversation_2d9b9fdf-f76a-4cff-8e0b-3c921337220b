﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>多语言商品图片</summary>
public partial interface IGoodsImagesLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>商品ID</summary>
    Int32 GId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>商品图片</summary>
    String? Url { get; set; }

    /// <summary>排序</summary>
    Int32 Sort { get; set; }

    /// <summary>是否默认主图</summary>
    Boolean IsDefault { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
