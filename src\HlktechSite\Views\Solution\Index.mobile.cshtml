﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    if (Model.CId == 0)
    {
        Html.AppendTitleParts(T("解决方案").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        var modelSolutionCategory = Model.Model as SolutionCategory;
        Html.AppendTitleParts(modelSolutionCategory.Name + DG.Setting.Current.PageTitleSeparator + T("解决方案").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
}

<div class="top">
    <img src="@(CDN.GetCDN())/images/CaseBanner.png" />
        <h2>@T("智能制造 创造未来")</h2>
        <P>
            @T("缩短产品上市时间提高生产效率和灵活性")
        </P>
</div>


<div class="input-group seach-div">
    <input class="form-control" type="text" id="key" name="key" value="@Model.key" /> 
    <a href="javascript:;" onclick="seach()">@T("搜索")</a>
</div>

<ul class="type-menu">
    <li class="@(Model.CId == 0 ? "selected" : "")"><a href="@Url.DGAction("Index")">@T("全部")</a></li>
    @foreach (var item in Model.CategoryList)
    {
        <li class="@(Model.CId == item.Id ? "selected" : "")">
            <a id="@item.Id" href="@Url.DGAction("List", new { CId = item.Id})">@item.Name</a>
        </li>
    }
</ul>


<ul class="rows-list">
    @foreach (var item in Model.SolutionList)
    {
        <li><a href="@(item.Url==""||item.Url==null?Url.DGAction("Details",new { Id=item.Id}):item.Url)"><img src="@(CDN.GetCDN())/@item.Pic" alt="@item.Name" /><b>@item.Name</b><i>@item.Summary</i></a></li>
    }
</ul>

<div class="paging" style="text-align: center;">
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>


<script asp-location="Footer">
    var cid = @Model.CId;

    $("#key").keydown(function (e) {
        if (e.which == 13) {
            if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#key").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { CId = Model.CId})?key=" + $("#key").val();
        }
        }
    });

    function seach(){
        if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#key").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { CId = Model.CId})?key=" + $("#key").val();
        }
    }

</script>