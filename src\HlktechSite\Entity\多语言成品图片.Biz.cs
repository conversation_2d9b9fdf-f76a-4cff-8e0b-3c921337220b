using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;
using DH.SearchEngine;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>多语言成品图片</summary>
    public partial class EndProductImagesLan : CubeEntityBase<EndProductImagesLan>
    {
        #region 对象操作
        static EndProductImagesLan()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(GId));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }

        /// <summary>验证并修补数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 建议先调用基类方法，基类方法会做一些统一处理
            base.Valid(isNew);

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化EndProductImagesLan[多语言成品图片]数据……");

        //    var entity = new EndProductImagesLan();
        //    entity.Id = 0;
        //    entity.GId = 0;
        //    entity.LId = 0;
        //    entity.Url = "abc";
        //    entity.Sort = 0;
        //    entity.IsDefault = true;
        //    entity.CreateUser = "abc";
        //    entity.CreateUserID = 0;
        //    entity.CreateTime = DateTime.Now;
        //    entity.CreateIP = "abc";
        //    entity.UpdateUser = "abc";
        //    entity.UpdateUserID = 0;
        //    entity.UpdateTime = DateTime.Now;
        //    entity.UpdateIP = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化EndProductImagesLan[多语言成品图片]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static EndProductImagesLan FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据商品ID查找</summary>
        /// <param name="gId">商品ID</param>
        /// <returns>实体列表</returns>
        public static IList<EndProductImagesLan> FindAllByGId(Int32 gId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.GId == gId);

            return FindAll(_.GId == gId);
        }

        /// <summary>
        /// 根据ID集合删除数据
        /// </summary>
        /// <param name="Ids">ID集合</param>
        public static void DelByGIds(String Ids)
        {
            if (Delete(_.GId.In(Ids)) > 0)
                Meta.Cache.Clear("");
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="gId">商品ID</param>
        /// <param name="start">更新时间开始</param>
        /// <param name="end">更新时间结束</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<EndProductImagesLan> Search(Int32 gId, DateTime start, DateTime end, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (gId >= 0) exp &= _.GId == gId;
            exp &= _.UpdateTime.Between(start, end);
            if (!key.IsNullOrEmpty()) exp &= _.Url.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        /// <summary>根据商品ID及排序查找</summary>
        /// <param name="gId">商品ID</param>
        /// <param name="sort"></param>
        /// <param name="LId"></param>
        /// <returns>实体列表</returns>
        public static IList<EndProductImagesLan> FindAllByGIdAndSortAndLId(Int32 gId, int sort, int LId)
        {
            // 实体缓存
            if (gId <= 0 || sort < 0 || LId <= 0) return new List<EndProductImagesLan>();
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.GId == gId && e.Sort == sort && e.LId == LId);

            return FindAll(_.GId == gId & _.Sort == sort & _.LId == LId);
        }

        /// <summary>根据商品ID及排序查找</summary>
        /// <param name="gId">商品ID</param>
        /// <param name="LId"></param>
        /// <returns>实体列表</returns>
        public static IList<EndProductImagesLan> FindAllByGIdAndLId(Int32 gId, int LId)
        {
            // 实体缓存
            if (gId <= 0 || LId <= 0) return new List<EndProductImagesLan>();
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.GId == gId && e.LId == LId);

            return FindAll(_.GId == gId & _.LId == LId);
        }
        #endregion

        #region 业务操作
        #endregion
    }
}