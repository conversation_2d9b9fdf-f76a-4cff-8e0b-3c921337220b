﻿@model SingleArticle
@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    Html.AppendCssFileParts("~/css/mobile/agent.css");

    Html.AppendTitleParts(T("销售网络").Text + DG.Setting.Current.PageTitleSeparator + T("代理招商").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}

<h2 class="agent-title">
    @Model.Name
</h2>

<div class="agent-con">
    <img src="@(CDN.GetCDN())/images/map.png" />

    @Html.Raw(Model.MobileContent)

</div>
