﻿using DG.Cube.BaseControllers;
using HlktechSite.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;

namespace HlktechSite.Areas.Mobile.Controllers;

/// <summary>
/// App下载控制器
/// </summary>
[MobileArea]
public class AppController : BaseMobileController {

    /// <summary>App下载页</summary>
    /// <returns></returns>
    public IActionResult Index(Int32 Id)
    {
        ViewBag.Id = Id;

        if (Id <= 0)
        {
            return Content(GetResource("参数有误"));
        }

        var model = AppManagers.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("内容不存在"));
        }

        if (model.AndroidPaths.IsNullOrWhiteSpace() && model.AndroidPaths1.IsNullOrWhiteSpace() && model.IosPaths.IsNullOrWhiteSpace() && model.ApkFilePath.IsNullOrWhiteSpace())
        {
            return Content(GetResource("暂无下载数据"));
        }

        return View(model);
    }
}
