﻿using System.ComponentModel;
using System.Dynamic;

using DG.Cube.BaseControllers;
using DG.Cube.Models;

using DH;
using DH.Core.Domain.Localization;
using DH.Entity;
using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Log;

using Pek;
using Pek.Helpers;
using Pek.Models;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers;
using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>商品分类</summary>
[DisplayName("成品分类")]
[Description("用于商品分类的管理，最多只有3级")]
[AdminArea]
[DHMenu(82,ParentMenuName = "ProductMenu", CurrentMenuUrl = "~/{area}/EndProductClass", CurrentMenuName = "EndProductClassList", CurrentIcon = "&#xe652;", LastUpdate = "20240125")]
public class EndProductClassController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 82;

    /// <summary>
    /// 商品分类列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("商品分类列表")]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var list = EndProductClass.FindAllByLevel(0);
        foreach (var item in list)
        {
            var Model = EndProductClass.FindAllByParentId(item.Id);
            if (Model.Count() != 0)
            {
                item.subset = true;
            }
        }
        viewModel.list = list;
        return View(viewModel);
    }

    /// <summary>
    /// 获取商品分类表下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取商品分类表下级数据")]
    public IActionResult GetSubordinateData(string Id)
    {
        var zList = new List<Hierarchy>();
        if (Id.IsNullOrWhiteSpace())
        {
            return Json(new { });
        }
        var list = EndProductClass.FindAllByParentId(Id.ToInt());
        foreach (var item in list)
        {
            var model = new Hierarchy();
            model.gc_name = item.Name;
            model.gc_id = item.Id;
            model.gc_parent_id = item.ParentId;
            var exc = EndProductClass.FindAllByParentId(item.Id);
            if (exc.Count() > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.gc_sort = item.DisplayOrder;
            model.deep = item.Level;
            zList.Add(model);
        }
        return Json(new { zList });
    }

    /// <summary>
    /// 模糊查询产品分类数据
    /// </summary>
    /// <param name="Key"></param>
    /// <returns></returns>
    [DHAuthorize]
    [DisplayName("模糊查询产品分类数据")]
    public IActionResult GetlikeName(string Key)
    {
        if (Key.IsNullOrWhiteSpace())
        {
            return Json(new List<EndProductClass>());
        }
        var List1 = EndProductClass.FindAllByLikeName(Key.SafeString().Trim());
        var list = new List<Xmsekect>();

        foreach (var item in List1)
        {
            var model = new Xmsekect();
            model.name = item.Name;
            model.value = item.Id;
            list.Add(model);
        }
        return Json(list);
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = EndProductClass.FindById(Id);

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = EndProductClass.FindByName(value);
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.DisplayOrder = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }

    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();

        var list = EndProductClass.FindByIds(Ids);
        var dellist = new List<EndProductClass>();

        res = DeleteArticleCategory(res, dellist, list);

        if (!res.msg.IsNullOrWhiteSpace())
        {
            return Json(res);
        }

        if (dellist.Delete(true) > 0)
            EndProductClass.Meta.Cache.Clear("");
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }


    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private DResult DeleteArticleCategory(DResult res, IList<EndProductClass> dellist, IEnumerable<EndProductClass> list)
    {
        if (list.Count() > 0)
        {
            foreach (var item in list)
            {
                //var listKnowledge = Article.FindAllByAId(item.Id);
                //if (listKnowledge.Count > 0)
                //{
                //    res.msg = String.Format(GetResource("选中的{0}有关联文章数据 不允许被删除"), item.Name);
                //    return res;
                //}
                //else
                //{
                //    dellist.Add(item);
                //    var childlist = EndProductClass.FindAllByParentId(item.Id);
                //    res = DeleteArticleCategory(res, dellist, childlist);
                //}
                dellist.Add(item);
                var childlist = EndProductClass.FindAllByParentId(item.Id);
                res = DeleteArticleCategory(res, dellist, childlist);
            }
        }
        return res;
    }

    /// <summary>
    /// 打开编辑页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("打开编辑产品分类页面")]
    public IActionResult EditEndProductClass(int Id)
    {
        var Model = EndProductClass.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }

        var List = new List<EndProductClass>();
        var live1 = EndProductClass.FindAllByLevel(0); //1级数据
        GetCategoryList(live1, List);
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        ViewBag.Plist = List;
        return View(Model);
    }


    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IEnumerable<EndProductClass> levelList, IList<EndProductClass> list)
    {
        if (levelList.Count() > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = EndProductClass.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 编辑产品分类
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Name"></param>
    /// <param name="DisplayOrder"></param>
    /// <param name="ParentId"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("编辑产品分类")]
    public IActionResult EditEndProductClass(int Id, string Name, int DisplayOrder, int ParentId)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("分类名称不能为空！") });
        }
        var Model = EndProductClass.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
        }
        using (var tran1 = DataDictionary.Meta.CreateTrans())
        {
            var EXIT = EndProductClass.FindByName(Name);
            if (EXIT != null && EXIT.Id != Id)
            {
                return Prompt(new PromptModel { Message = GetResource("分类名称已存在！") });
            }
            Model.DisplayOrder = DisplayOrder.ToDGShort();
            Model.Name = Name.SafeString().Trim();
            if (ParentId != Model.ParentId)
            {
                Model.ParentId = ParentId;
                var Pmodel = EndProductClass.FindById(ParentId);
                if (Pmodel == null)
                {
                    Model.Level = 0;
                    Model.ParentIdList = Model.Id.ToString();
                }
                else
                {
                    if (Pmodel.Level >= 2)
                    {
                        return Prompt(new PromptModel { Message = GetResource("分类最多存在三级,创建失败！") });
                    }
                    Model.Level = Pmodel.Level + 1;
                    Model.ParentIdList = Pmodel.ParentIdList + "," + Model.Id;
                }

            }
            Model.Update();

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var list = EndProductClassLan.FindAllByCId(Model.Id);

                foreach (var item in Languagelist)
                {
                    var ex = list.Find(x => x.LId == item.Id);
                    if (ex != null)
                    {
                        ex.Name = (GetRequest($"[{item.Id}].Name")).SafeString().Trim();
                        ex.Update();
                    }
                    else
                    {
                        ex = new EndProductClassLan();
                        ex.Name = (GetRequest($"[{item.Id}].Name")).SafeString().Trim();
                        if (ex.Name.IsNullOrWhiteSpace())
                        {
                            continue;
                        }
                        ex.LId = item.Id;
                        ex.CId = Model.Id;
                        ex.Insert();
                    }
                }
            }
            tran1.Commit();
        }
        EndProductClassLan.Meta.Cache.Clear("");
        EndProductClass.Meta.Cache.Clear("");
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 查询名称是否存在
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("查询名称是否存在")]
    public IActionResult ExistName(string Key, int Id)
    {
        XTrace.WriteLine("获取到的打印key==" + Key);
        XTrace.WriteLine("获取到的打印Id==" + Id);
        if (Key.IsNullOrWhiteSpace())
        {
            return Json(false);
        }
        if (Id != 0)
        {
            var EXIST = EndProductClass.FindByName(Key);
            if (EXIST != null && EXIST.Id != Id)
            {
                return Json(false);
            }
        }
        else
        {
            var EXIST = EndProductClass.FindByName(Key);
            if (EXIST != null)
            {
                return Json(false);
            }
        }
        return Json(true);
    }


    /// <summary>
    /// 打开新增产品分类页面
    /// </summary>
    /// <param name="parent_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("打开新增产品分类页面")]
    public IActionResult AddEndProductClass(int parent_id)
    {

        var List = new List<EndProductClass>();
        var live1 = EndProductClass.FindAllByLevel(0);
        GetCategoryList(live1, List);
        ViewBag.Plist = List;

        var DisplayOrder = 0;
        if (parent_id != 0)
        {
            DisplayOrder = (Int32)EndProductClass.FindMax("DisplayOrder", EndProductClass._.ParentId == parent_id);
        }
        else
        {
            DisplayOrder = (Int32)EndProductClass.FindMax("DisplayOrder");
        }

        ViewBag.DisplayOrder = DisplayOrder + 1;
        ViewBag.ParentId = parent_id;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View();
    }

    /// <summary>
    /// 新增产品分类
    /// </summary>
    /// <param name="Name"></param>
    /// <param name="DisplayOrder"></param>
    /// <param name="ParentId"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增产品分类")]
    public IActionResult AddEndProductClass(string Name, short DisplayOrder, int ParentId)
    {

        XTrace.WriteLine("编辑产品分类Name==" + Name);
        XTrace.WriteLine("编辑产品分类DisplayOrder==" + DisplayOrder);
        XTrace.WriteLine("编辑产品分类ParentId==" + ParentId);
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("分类名称不能为空！") });
        }

        var exit = EndProductClass.FindByName(Name.SafeString().Trim());
        if (exit != null)
        {
            return Prompt(new PromptModel { Message = GetResource("分类名称已存在！") });
        }
        var Pmodel = EndProductClass.FindById(ParentId);
        if (Pmodel != null)
        {
            if (Pmodel.Level >= 2)
            {
                return Prompt(new PromptModel { Message = GetResource("分类最多存在三级,创建失败！") });
            }
        }
        using (var tran1 = EndProductClassLan.Meta.CreateTrans())
        {
            var Model = new EndProductClass();
            Model.Name = Name.SafeString().Trim();
            Model.DisplayOrder = DisplayOrder;
            Model.ParentId = ParentId;
            Model.Insert();
            if (Pmodel == null)
            {
                Model.Level = 0;
                Model.ParentIdList = Model.Id.ToString();
            }
            else
            {
                Model.Level = Pmodel.Level + 1;
                Model.ParentIdList = Pmodel.ParentIdList + "," + Model.Id;
            }
            Model.Update();

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var List = new List<EndProductClassLan>();
                foreach (var item in Languagelist)
                {
                    var ex = new EndProductClassLan();
                    ex.Name = (GetRequest($"[{item.Id}].Name")).SafeString().Trim();
                    if (ex.Name.IsNullOrWhiteSpace())
                    {
                        continue;
                    }
                    ex.CId = Model.Id;
                    ex.LId = item.Id;
                    List.Add(ex);
                }
                List.Save();
            }
            tran1.Commit();
        }
        EndProductClassLan.Meta.Cache.Clear("");
        EndProductClass.Meta.Cache.Clear("");
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }


    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("查询子集数据")]
    public IActionResult Querytheson(int gc_id, int deep)
    {
        XTrace.WriteLine("进入到商品查询子集数据接收值gc_id==" + gc_id);
        XTrace.WriteLine("进入到商品查询子集数据接收值deep==" + deep);

        var lict = EndProductClass.FindAllByParentId(gc_id).Select(x => new { gc_id = x.Id, gc_name = x.Name });
        return Json(lict);
    }
}
