﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>成品常用分类</summary>
[Serializable]
[DataObject]
[Description("成品常用分类")]
[BindTable("DG_EndProductClassStaple", Description = "成品常用分类", ConnName = "DG", DbType = DatabaseType.None)]
public partial class EndProductClassStaple : IEndProductClassStaple, IEntity<IEndProductClassStaple>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _Counter;
    /// <summary>计数器。使用次数计数</summary>
    [DisplayName("计数器")]
    [Description("计数器。使用次数计数")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Counter", "计数器。使用次数计数", "")]
    public Int32 Counter { get => _Counter; set { if (OnPropertyChanging("Counter", value)) { _Counter = value; OnPropertyChanged("Counter"); } } }

    private String? _Name;
    /// <summary>常用分类名称</summary>
    [DisplayName("常用分类名称")]
    [Description("常用分类名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Name", "常用分类名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private Int32 _Cid1;
    /// <summary>一级分类ID</summary>
    [DisplayName("一级分类ID")]
    [Description("一级分类ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId_1", "一级分类ID", "")]
    public Int32 Cid1 { get => _Cid1; set { if (OnPropertyChanging("Cid1", value)) { _Cid1 = value; OnPropertyChanged("Cid1"); } } }

    private Int32 _Cid2;
    /// <summary>二级分类ID</summary>
    [DisplayName("二级分类ID")]
    [Description("二级分类ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId_2", "二级分类ID", "")]
    public Int32 Cid2 { get => _Cid2; set { if (OnPropertyChanging("Cid2", value)) { _Cid2 = value; OnPropertyChanged("Cid2"); } } }

    private Int32 _Cid3;
    /// <summary>三级分类ID</summary>
    [DisplayName("三级分类ID")]
    [Description("三级分类ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId_3", "三级分类ID", "")]
    public Int32 Cid3 { get => _Cid3; set { if (OnPropertyChanging("Cid3", value)) { _Cid3 = value; OnPropertyChanged("Cid3"); } } }

    private Boolean _TypeId;
    /// <summary>类型Id。成品分类是否联动</summary>
    [DisplayName("类型Id")]
    [Description("类型Id。成品分类是否联动")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("TypeId", "类型Id。成品分类是否联动", "")]
    public Boolean TypeId { get => _TypeId; set { if (OnPropertyChanging("TypeId", value)) { _TypeId = value; OnPropertyChanged("TypeId"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IEndProductClassStaple model)
    {
        Id = model.Id;
        Counter = model.Counter;
        Name = model.Name;
        Cid1 = model.Cid1;
        Cid2 = model.Cid2;
        Cid3 = model.Cid3;
        TypeId = model.TypeId;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Counter" => _Counter,
            "Name" => _Name,
            "Cid1" => _Cid1,
            "Cid2" => _Cid2,
            "Cid3" => _Cid3,
            "TypeId" => _TypeId,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Counter": _Counter = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Cid1": _Cid1 = value.ToInt(); break;
                case "Cid2": _Cid2 = value.ToInt(); break;
                case "Cid3": _Cid3 = value.ToInt(); break;
                case "TypeId": _TypeId = value.ToBoolean(); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得成品常用分类字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>计数器。使用次数计数</summary>
        public static readonly Field Counter = FindByName("Counter");

        /// <summary>常用分类名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>一级分类ID</summary>
        public static readonly Field Cid1 = FindByName("Cid1");

        /// <summary>二级分类ID</summary>
        public static readonly Field Cid2 = FindByName("Cid2");

        /// <summary>三级分类ID</summary>
        public static readonly Field Cid3 = FindByName("Cid3");

        /// <summary>类型Id。成品分类是否联动</summary>
        public static readonly Field TypeId = FindByName("TypeId");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得成品常用分类字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>计数器。使用次数计数</summary>
        public const String Counter = "Counter";

        /// <summary>常用分类名称</summary>
        public const String Name = "Name";

        /// <summary>一级分类ID</summary>
        public const String Cid1 = "Cid1";

        /// <summary>二级分类ID</summary>
        public const String Cid2 = "Cid2";

        /// <summary>三级分类ID</summary>
        public const String Cid3 = "Cid3";

        /// <summary>类型Id。成品分类是否联动</summary>
        public const String TypeId = "TypeId";
    }
    #endregion
}
