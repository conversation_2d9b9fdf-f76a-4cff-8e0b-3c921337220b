﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>解决方案翻译</summary>
[Serializable]
[DataObject]
[Description("解决方案翻译")]
[BindIndex("IU_DG_SolutionLan_CId_LId", true, "CId,LId")]
[BindTable("DG_SolutionLan", Description = "解决方案翻译", ConnName = "DG", DbType = DatabaseType.None)]
public partial class SolutionLan : ISolutionLan, IEntity<ISolutionLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _CId;
    /// <summary>CaseId</summary>
    [DisplayName("CaseId")]
    [Description("CaseId")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId", "CaseId", "")]
    public Int32 CId { get => _CId; set { if (OnPropertyChanging("CId", value)) { _CId = value; OnPropertyChanged("CId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>解决方案标题</summary>
    [DisplayName("解决方案标题")]
    [Description("解决方案标题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "解决方案标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _Summary;
    /// <summary>简介</summary>
    [DisplayName("简介")]
    [Description("简介")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Summary", "简介", "")]
    public String? Summary { get => _Summary; set { if (OnPropertyChanging("Summary", value)) { _Summary = value; OnPropertyChanged("Summary"); } } }

    private String? _Pic;
    /// <summary>解决方案主图</summary>
    [DisplayName("解决方案主图")]
    [Description("解决方案主图")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Pic", "解决方案主图", "")]
    public String? Pic { get => _Pic; set { if (OnPropertyChanging("Pic", value)) { _Pic = value; OnPropertyChanged("Pic"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISolutionLan model)
    {
        Id = model.Id;
        CId = model.CId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
        Summary = model.Summary;
        Pic = model.Pic;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "CId" => _CId,
            "LId" => _LId,
            "Name" => _Name,
            "Content" => _Content,
            "Summary" => _Summary,
            "Pic" => _Pic,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "CId": _CId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "Summary": _Summary = Convert.ToString(value); break;
                case "Pic": _Pic = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得解决方案翻译字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>CaseId</summary>
        public static readonly Field CId = FindByName("CId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>解决方案标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>简介</summary>
        public static readonly Field Summary = FindByName("Summary");

        /// <summary>解决方案主图</summary>
        public static readonly Field Pic = FindByName("Pic");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得解决方案翻译字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>CaseId</summary>
        public const String CId = "CId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>解决方案标题</summary>
        public const String Name = "Name";

        /// <summary>内容</summary>
        public const String Content = "Content";

        /// <summary>简介</summary>
        public const String Summary = "Summary";

        /// <summary>解决方案主图</summary>
        public const String Pic = "Pic";
    }
    #endregion
}
