﻿@{
    Html.AppendCssFileParts("~/css/apply.css");
}

<div class="ApplyCon">
    <div class="row datum-title">
        <div class="col-md-5">
            @T("欢迎使用海凌科服务支持")
        </div>
        <div class="col-md-6">
            <img src="@(CDN.GetCDN())/images/DatumLOgo.png" />
        </div>
    </div>
    <ul>
        <li class="selected" data-type="software">@T("软件定制申请")</li>
        <li data-type="product">@T("产品定制申请")</li>
        <li data-type="Sample">@T("样品申请") </li>
        <li><a href="@Url.DGAction("index","Agent")">@T("代理商申请") </a></li>
    </ul>

    <div class="fillIn">
        <form id="apply">
            <p class="title">@T("海凌科物联网专注工业物联网通讯领域，为客户定制联网领域产品，合作方式多样。")&nbsp;&nbsp;<span style="color: red;">@T("电话/微信"): 17786528309</span></p>
            <div class="row">
                <div class="col-md-6">
                    <p>
                        @T("公司名称")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="comName" name="comName" class="form-control" aria-describedby="basic-addon1" hint="@T("公司名称不能为空")">
                    </div>
                    <p>
                        @T("主营业务")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="business" name="business" class="form-control" aria-describedby="basic-addon1" hint="@T("主营业务不能为空")">
                    </div>
                    <p>
                        @T("申请人职务")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="job" name="job" class="form-control" aria-describedby="basic-addon1" hint="@T("申请人职务不能为空")">
                    </div>
                    <p>
                        @T("申请人邮箱")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="email" name="email" class="form-control" aria-describedby="basic-addon1" hint="@T("申请人邮箱不能为空")" )">
                    </div>
                    <p style="display:none">
                        @T("定制基于产品型号")
                    </p>
                    <div class="sub-input input-group" style="display:none">
                        <input type="text" id="model" name="model" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p style="display:none">
                        @T("定制产品首批采购量")
                    </p>
                    <div class="sub-input input-group" style="display:none">
                        <input type="text" id="purchase" name="purchase" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                </div>
                <div class="col-md-6">
                    <p>
                        @T("公司网址")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="comUrl" name="comUrl" class="form-control" aria-describedby="basic-addon1" hint="@T("公司网址不能为空")">
                    </div>
                    <p>
                        @T("公司联系人")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="comlinkman" name="comlinkman" class="form-control" aria-describedby="basic-addon1" hint="@T("公司联系人不能为空")">
                    </div>
                    <p>
                        @T("申请人电话")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="phone" name="phone" class="form-control" aria-describedby="basic-addon1" hint="@T("申请人电话不能为空")">
                    </div>
                    <p>
                        @T("定制类型")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <select name="type">
                            <option value="0">@T("解决方案")</option>
                            <option value="1" style="display:none">@T("产品")</option>
                        </select>
                    </div>
                    <p style="display:none">
                        @T("定制产品预计年均需求量")
                    </p>
                    <div class="sub-input input-group" style="display:none">
                        <input type="text" id="predict" name="predict" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                </div>
            </div>
            <p>
                @T("产品定制需求")
                <i>*</i>
            </p>
            <textarea class="form-control" id="demand" name="demand"  value="" hint="@T("产品定制需求不能为空")"></textarea>
            <p>
                @T("定制产品所应用项目背景")
                <i>*</i>
            </p>
            <textarea class="form-control" id="setting" name="setting"  value="" hint="@T("定制产品所应用项目背景不能为空")"></textarea>

        </form>
        <form id="apply2">
            <p class="title">
                @T("温馨提示：因资源有限，样品借测或送样，仅针对较大的公司或项目，无法保证全部审批通过，敬请理解。您也可以通过线上平台购买样品测试，（成品7天/模块15天）内测试不合适可退货、退款。")
            </p>
            <div class="row">
                <div class="col-md-6">
                    <p>
                        @T("公司名称")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="comName2" name="comName" class="form-control" aria-describedby="basic-addon1" hint="@T("公司名称不能为空")">
                    </div>
                    <p>
                        @T("企业人数")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="business2" name="ComPeople" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("企业官网")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Website2" name="Website" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("年营业额")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Turnover2" name="Turnover" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("企业类型")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="ComType2" name="ComType" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("传真")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Fax2" name="Fax" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("月需求量")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Demand2" name="Demand" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("申请主题")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Theme2" name="Theme" class="form-control" aria-describedby="basic-addon1" hint="@T("申请主题不能为空")">
                    </div>
                </div>
                <div class="col-md-6">
                    <p>
                        @T("企业地址")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Address2" name="Address" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("联系人")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Linkman2" name="Linkman" class="form-control" aria-describedby="basic-addon1" hint="@T("联系人不能为空")">
                    </div>
                    <p>
                        @T("职位")
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Position2" name="Position" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("电话")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Phone2" name="Phone" class="form-control" aria-describedby="basic-addon1" hint="@T("申请人电话不能为空")">
                    </div>
                    <p>
                        QQ
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="QQ2" name="QQ" class="form-control" aria-describedby="basic-addon1" hint="">
                    </div>
                    <p>
                        @T("邮箱")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="Email2" name="Email" class="form-control" aria-describedby="basic-addon1" hint="@T("申请人邮箱不能为空")">
                    </div>
                    <p>
                        @T("产品型号")
                        <i>*</i>
                    </p>
                    <div class="sub-input input-group">
                        <input type="text" id="ProModel2" name="ProModel" class="form-control" aria-describedby="basic-addon1" hint="@T("产品型号不能为空")">
                    </div>
                </div>
            </div>
            <p>
                @T("需求描述")
                <i>*</i>
            </p>
            <textarea class="form-control" id="Describe" name="Describe"  value="" hint="@T("需求描述不能为空")"></textarea>
        </form>
        <p>
            @T("验证码")
            <i>*</i>
        </p>
        <div class="sub-input input-group">
            <input type="text" id="verification" name="verification" class="form-control" aria-describedby="basic-addon1" hint="@T("验证码不能为空")">
            <img src="@DG.Setting.Current.CaptChaUrl" />
            <a href="javascript:;" id="submit">@T("提交问题")</a>
        </div>
    </div>
</div>

<script type="text/javascript">
    var Ereg = /^([a-zA-Z]|[0-9])(\w|\-)+@@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
    var Preg = /^[1][3,4,5,7,8][0-9]{9}$/;
    $(function () {
        $(".ApplyCon li").click(function () {
            $(".selected").removeClass("selected");
            $(this).addClass("selected");
            if ($(this).data("type") == "software") {
                $("#apply2").hide();
                $("#apply").show();
                $("[name=type] option:eq(0)").show().attr("selected", true);
                $("[name=type] option:eq(1)").hide();
                $("#model,#purchase,#predict").parent().prev().hide();
                $("#model,#purchase,#predict").val("").parent().hide();
            }
            else if ($(this).data("type") == "product") {
                $("#apply2").hide();
                $("#apply").show();
                $("[name=type] option:eq(1)").show().attr("selected", true);
                $("[name=type] option:eq(0)").hide();
                $("#model,#purchase,#predict").parent().prev().show();
                $("#model,#purchase,#predict").parent().show();
            } else if ($(this).data("type") == "Sample") {
                $("#apply").hide();
                $("#apply2").show();
            }
        });
    });
    $("#submit").click(() => {
        var popup = true;

        var formname = ($(".selected").data("type") == "Sample" ? "apply2" : "apply");

        $(`#${formname} input,#${formname} textarea`).each(function () {
            if (!this.value && popup&&$(this).attr("hint")) {
                window.location.href = `#${$(this).attr("id")}`;
                $("html").scrollTop($("html").scrollTop()-300);
                alert($(this).attr("hint"));
                popup = !popup;
            }
        })
        if (popup) {
            if (formname == "apply2")
            {
                if (!(Ereg.test($(`#${formname} [name=Email]`).val()))) {
                    window.location.href = `#${$("[name=Email]").attr("id")}`;
                    $("html").scrollTop($("html").scrollTop() - 300);
                    alert("@T("请输入正确的邮箱")");
                    return;
                }
                @if (language.UniqueSeoCode == "cn")
                {
                    <text>
                    if (!(Preg.test($(`#${formname} [name=Phone]`).val()))) {
                       window.location.href = `#${$("[name=Phone]").attr("id")}`;
                       $("html").scrollTop($("html").scrollTop() - 300);
                       alert("@T("请输入正确的手机号")");
                       return;
                   }
                   </text>
                }

            }
            else {
                if (!(Ereg.test($(`#${formname} [name=email]`).val()))) {
                    window.location.href = `#${$("[name=email]").attr("id")}`;
                    $("html").scrollTop($("html").scrollTop() - 300);
                    alert("@T("请输入正确的邮箱")");
                    return;
                }
                @if (language.UniqueSeoCode == "cn") 
                {
                    <text>
                    if (!(Preg.test($(`#${formname} [name=phone]`).val()))) {
                        window.location.href = `#${$("[name=phone]").attr("id")}`;
                        $("html").scrollTop($("html").scrollTop() - 300);
                        alert("@T("请输入正确的手机号")");
                        return;
                    }
                    </text>
                }
            }

            if (!$("[name=verification]").val()) {
                window.location.href = `#${$("[name=verification]").attr("id")}`;
                    $("html").scrollTop($("html").scrollTop() - 300);
                    alert("@T("验证码不能为空")");
                    return;
                }
            $.post((formname == "apply"?"@Url.DGAction("Apply")":"@Url.DGAction("Sample")"), $.param({ "verification": $("[name=verification]").val() }) + "&" + $(`#${formname}`).serialize(), (res) => {
                alert(res.msg);
                if (res.success) {
                    setTimeout(() => {
                        window.location.href = "@Url.DGAction("Index", "CubeHome")";
                    }, 1000)
                } else {
                    $(".fillIn img").attr('src','@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()))
                }
            })
        }

    });
    $(".fillIn img").click(function () {
        $(".fillIn img").attr('src','@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()))
    })
</script>