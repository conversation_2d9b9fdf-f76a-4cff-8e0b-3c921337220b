﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>成品常用分类</summary>
public partial interface IEndProductClassStaple
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>计数器。使用次数计数</summary>
    Int32 Counter { get; set; }

    /// <summary>常用分类名称</summary>
    String? Name { get; set; }

    /// <summary>一级分类ID</summary>
    Int32 Cid1 { get; set; }

    /// <summary>二级分类ID</summary>
    Int32 Cid2 { get; set; }

    /// <summary>三级分类ID</summary>
    Int32 Cid3 { get; set; }

    /// <summary>类型Id。成品分类是否联动</summary>
    Boolean TypeId { get; set; }
    #endregion
}
