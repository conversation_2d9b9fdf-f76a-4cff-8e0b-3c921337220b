﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>SEO信息存放表</summary>
[Serializable]
[DataObject]
[Description("SEO信息存放表")]
[BindIndex("IU_DG_Seo_SeoType", true, "SeoType")]
[BindTable("DG_Seo", Description = "SEO信息存放表", ConnName = "DG", DbType = DatabaseType.None)]
public partial class SeoInfo : ISeoInfo, IEntity<ISeoInfo>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _SeoTitle;
    /// <summary>SEO标题</summary>
    [DisplayName("SEO标题")]
    [Description("SEO标题")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("SeoTitle", "SEO标题", "")]
    public String? SeoTitle { get => _SeoTitle; set { if (OnPropertyChanging("SeoTitle", value)) { _SeoTitle = value; OnPropertyChanged("SeoTitle"); } } }

    private String? _SeoKeywords;
    /// <summary>SEO关键词</summary>
    [DisplayName("SEO关键词")]
    [Description("SEO关键词")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("SeoKeywords", "SEO关键词", "")]
    public String? SeoKeywords { get => _SeoKeywords; set { if (OnPropertyChanging("SeoKeywords", value)) { _SeoKeywords = value; OnPropertyChanged("SeoKeywords"); } } }

    private String? _SeoDescription;
    /// <summary>SEO描述</summary>
    [DisplayName("SEO描述")]
    [Description("SEO描述")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("SeoDescription", "SEO描述", "")]
    public String? SeoDescription { get => _SeoDescription; set { if (OnPropertyChanging("SeoDescription", value)) { _SeoDescription = value; OnPropertyChanged("SeoDescription"); } } }

    private String? _SeoType;
    /// <summary>SEO类型</summary>
    [DisplayName("SEO类型")]
    [Description("SEO类型")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("SeoType", "SEO类型", "")]
    public String? SeoType { get => _SeoType; set { if (OnPropertyChanging("SeoType", value)) { _SeoType = value; OnPropertyChanged("SeoType"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISeoInfo model)
    {
        Id = model.Id;
        SeoTitle = model.SeoTitle;
        SeoKeywords = model.SeoKeywords;
        SeoDescription = model.SeoDescription;
        SeoType = model.SeoType;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "SeoTitle" => _SeoTitle,
            "SeoKeywords" => _SeoKeywords,
            "SeoDescription" => _SeoDescription,
            "SeoType" => _SeoType,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "SeoTitle": _SeoTitle = Convert.ToString(value); break;
                case "SeoKeywords": _SeoKeywords = Convert.ToString(value); break;
                case "SeoDescription": _SeoDescription = Convert.ToString(value); break;
                case "SeoType": _SeoType = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static SeoInfo? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据SEO类型查找</summary>
    /// <param name="seoType">SEO类型</param>
    /// <returns>实体对象</returns>
    public static SeoInfo? FindBySeoType(String? seoType)
    {
        if (seoType == null) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.SeoType.EqualIgnoreCase(seoType));

        return Find(_.SeoType == seoType);
    }
    #endregion

    #region 字段名
    /// <summary>取得SEO信息存放表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>SEO标题</summary>
        public static readonly Field SeoTitle = FindByName("SeoTitle");

        /// <summary>SEO关键词</summary>
        public static readonly Field SeoKeywords = FindByName("SeoKeywords");

        /// <summary>SEO描述</summary>
        public static readonly Field SeoDescription = FindByName("SeoDescription");

        /// <summary>SEO类型</summary>
        public static readonly Field SeoType = FindByName("SeoType");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得SEO信息存放表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>SEO标题</summary>
        public const String SeoTitle = "SeoTitle";

        /// <summary>SEO关键词</summary>
        public const String SeoKeywords = "SeoKeywords";

        /// <summary>SEO描述</summary>
        public const String SeoDescription = "SeoDescription";

        /// <summary>SEO类型</summary>
        public const String SeoType = "SeoType";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
