﻿using System.ComponentModel;

using DH.Core.Infrastructure;
using DH.Services.Jobs;
using DH.SignalR;
using DH.SignalR.Dtos;

using Microsoft.AspNetCore.SignalR;

using NewLife.Log;

namespace HlktechSite.Jobs;

/// <summary>
/// 测试SignalR定时发送消息作业参数
/// </summary>
public class QuartzJobArgument {

}

/// <summary>测试SignalR定时发送消息服务</summary>
[DisplayName("测试SignalR定时发送消息")]
[Description("测试执行SignalR定时发送消息")]
[CronJob("QuartzJob", "0 */5 * * * ? *", Enable = true)]
public class QuartzJobService : CubeJobBase<QuartzJobArgument> {
    private readonly ITracer _tracer;

    /// <summary>实例化测试执行SignalR定时发送消息</summary>
    /// <param name="tracer"></param>
    public QuartzJobService(ITracer tracer) => _tracer = tracer;

    /// <summary>执行作业</summary>
    /// <param name="argument"></param>
    /// <returns></returns>
    protected override async Task<String> OnExecute(QuartzJobArgument argument)
    {
        using var span = _tracer?.NewSpan("QuartzJob", argument);

        var _notifyHub = EngineContext.Current.Resolve<IHubContext<NotifyHub, IClientNotifyHub>>();
        var model = new NotifyConnectsData()
        {
            UserId = -999
        };
        await _notifyHub.Clients.All.OnNotify(model);

        return "OK";
    }
}

///// <summary>
///// 测试执行SignalR定时发送消息
///// </summary>
//[DisallowConcurrentExecution]
//public class QuartzJobService : JobBase
//{
//    ///// <summary>
//    ///// 获取重复执行间隔时间，单位：分钟
//    ///// </summary>
//    //public override int? GetIntervalInMinutes()
//    //{
//    //    return 1;
//    //}

//    /// <summary>
//    /// 获取重复执行间隔时间，单位：秒
//    /// </summary>
//    public override int? GetIntervalInSeconds()
//    {
//        return 60;
//    }

//    /// <summary>
//    /// 执行
//    /// </summary>
//    public override async Task Execute(IJobExecutionContext context)
//    {
//        //var _notifyHub = EngineContext.Current.Resolve<IHubContext<NotifyHub, IClientNotifyHub>>();
//        //var model = new NotifyConnectsData()
//        //{
//        //    UserId = -999
//        //};
//        //await _notifyHub.Clients.All.OnNotify(model);
//        await Task.FromResult(0);
//    }
//}
