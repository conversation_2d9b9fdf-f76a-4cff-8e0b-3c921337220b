﻿@model HlktechSite.Entity.SeoInfo;
@{
    var seoContent = ViewBag.Content as SeoInfo;

    var localizationSettings = LocalizationSettings.Current;
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("SEO设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("首页")</span></a></li>
                <li><a href="@Url.Action("Journalism")"><span>@T("新闻")</span></a></li>
                <li><a href="@Url.Action("Case")"><span>@T("案例")</span></a></li>
                <li><a href="@Url.Action("Solution")"><span>@T("解决方案")</span></a></li>
                <li><a href="@Url.Action("Product")" class="current"><span>@T("商品")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="提示相关设置操作时应注意的要点">操作提示</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>插入的变量必需包括花括号“{}”，比如 {sitename}，当应用范围不支持该变量时，该变量将不会在前台显示(变量后边的分隔符也不会显示)，留空为系统默认设置，SEO自定义支持手写。</li>
            <li>站点名称 {sitename}，（应用范围：全站）</li>
            <li>名称 {name}，（应用范围：新闻标题名称、案例名称、解决方案名称、产品名称）</li>
            <li>分类名称 {product_class}，（应用范围：商品分类页）</li>
            <li>名称 {shopname}，（应用范围：页）</li>
            <li>关键词 {key}，（应用范围：商品关键词、文章关键词、关键词）</li>
            <li>简单描述 {description}，（应用范围：商品描述、文章摘要、关键词）</li>
        </ul>
    </div>
    <form action="@Url.Action("Product")" method="post" enctype="multipart/form-data">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief" style="margin-bottom:0">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">标准</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content" style="padding-bottom: 0px;">
                <div class="layui-tab-item layui-show">
                    <div class="ncap-form-default" style="padding-bottom: 0px;">
                        <dl>
                            <dt>商品</dt>
                            <dd>
                                <span><a>{sitename}{product_class}{name}{description}</a></span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>标题</dt>
                            <dd>
                                <input id="SEO_title" name="SEO_title" value="@Model.SeoTitle" class="w300" type="text">
                            </dd>
                        </dl>
                        <dl>
                            <dt>关键字</dt>
                            <dd>
                                <input id="SEO_keywords" name="SEO_keywords" value="@Model.SeoKeywords" class="w300" type="text" maxlength="200">
                            </dd>
                        </dl>
                        <dl>
                            <dt>描述</dt>
                            <dd>
                                <input id="SEO_description" name="SEO_description" value="@Model.SeoDescription" class="w300" type="text" maxlength="200">
                            </dd>
                        </dl>
                        <dl>
                            <dt>商品内容</dt>
                            <dd>
                            </dd>
                        </dl>
                        <dl>
                            <dt>标题</dt>
                            <dd>
                                <input id="SEO_Content_title" name="SEO_Content_title" value="@seoContent.SeoTitle" class="w300 valid" type="text" aria-invalid="false">
                            </dd>
                        </dl>
                        <dl>
                            <dt>关键字</dt>
                            <dd>
                                <input id="SEO_Content_keywords" name="SEO_Content_keywords" value="@seoContent.SeoKeywords" class="w300 valid" type="text" aria-invalid="false">
                            </dd>
                        </dl>
                        <dl>
                            <dt>描述</dt>
                            <dd>
                                <input id="SEO_Content_description" name="SEO_Content_description" value="@seoContent.SeoDescription" class="w300 valid" type="text" aria-invalid="false">
                            </dd>
                        </dl>
                    </div>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var SeoLan = SeoInfoLan.FindBySIdAndLId(Model.Id, item.Id) ?? new SeoInfoLan();
                        var SeoLanContent = SeoInfoLan.FindBySIdAndLId(seoContent.Id, item.Id) ?? new SeoInfoLan();
                        <div class="layui-tab-item">
                            <div class="ncap-form-default" style="padding-bottom: 0px;">
                                <dl>
                                    <dt>商品</dt>
                                    <dd>
                                        <span><a>{sitename}{product_class}{name}{description}</a></span>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>标题</dt>
                                    <dd>
                                        <input id="SEO_title" name="[@item.Id]SEO_title" value="@SeoLan.SeoTitle" class="w300" type="text">
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>关键字</dt>
                                    <dd>
                                        <input id="SEO_keywords" name="[@item.Id]SEO_keywords" value="@SeoLan.SeoKeywords" class="w300" type="text" maxlength="200">
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>描述</dt>
                                    <dd>
                                        <input id="SEO_description" name="[@item.Id]SEO_description" value="@SeoLan.SeoDescription" class="w300" type="text" maxlength="200">
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>商品内容</dt>
                                    <dd>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>标题</dt>
                                    <dd>
                                        <input id="[@item.Id]SEO_Content_title" name="[@item.Id]SEO_Content_title" value="@SeoLanContent.SeoTitle" class="w300 valid" type="text" aria-invalid="false">
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>关键字</dt>
                                    <dd>
                                        <input id="[@item.Id]SEO_Content_keywords" name="[@item.Id]SEO_Content_keywords" value="@SeoLanContent.SeoKeywords" class="w300 valid" type="text" aria-invalid="false">
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>描述</dt>
                                    <dd>
                                        <input id="[@item.Id]SEO_Content_description" name="[@item.Id]SEO_Content_description" value="@SeoLanContent.SeoDescription" class="w300 valid" type="text" aria-invalid="false">
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    }
                }
            </div>
        </div>
        <div class="ncap-form-default" style="padding-bottom: 0px;padding-top: 0px;">
            <dl>
                <dt></dt>
                <dd>
                    <input class="btn" type="submit" value="提交">
                </dd>
            </dl>
        </div>
    </form>
</div>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;
    })
</script>