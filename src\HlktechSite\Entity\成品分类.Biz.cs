using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;
using DH.Core.Domain.Localization;

using DH.Core.Infrastructure;
using DH.SearchEngine;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>成品分类</summary>
    public partial class EndProductClass : CubeEntityBase<EndProductClass>
    {
        #region 对象操作
        static EndProductClass()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(Counter));

            // 过滤器 UserModule、TimeModule、IPModule
        }

        /// <summary>验证并修补数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 建议先调用基类方法，基类方法会做一些统一处理
            base.Valid(isNew);

            // 在新插入数据或者修改了指定字段时进行修正
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化EndProductClass[成品分类]数据……");

        //    var entity = new EndProductClass();
        //    entity.Id = 0;
        //    entity.Counter = 0;
        //    entity.Name = "abc";
        //    entity.Cid1 = 0;
        //    entity.Cid2 = 0;
        //    entity.Cid3 = 0;
        //    entity.TypeId = true;
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化EndProductClass[成品分类]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        ///是否存在子集
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public bool subset { get; set; } = false;

        /// <summary>
        /// 翻译名称
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public String? LngName => Extends.Get(nameof(LngName), k =>
        {
            var localizationSettings = LocalizationSettings.Current;

            return localizationSettings.IsEnable ? EndProductClassLan.FindByCIdAndLId(Id, CurrentLanguage.Id)?.Name : Name;
        });
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static EndProductClass FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>
        /// 查询所有
        /// </summary>
        /// <returns></returns>
        public static IList<EndProductClass> GetAll()
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return FindAllWithCache();

            // 单对象缓存
            return FindAll();
        }

        /// <summary>根据当前层级查找</summary>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IEnumerable<EndProductClass> FindAllByLevel(Int32 level)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Level == level).OrderBy(x => x.DisplayOrder);

            return FindAll(_.Level == level).OrderBy(x => x.DisplayOrder);
        }

        /// <summary>根据所属父级Id查找</summary>
        /// <param name="parentId">所属父级Id</param>
        /// <returns>实体列表</returns>
        public static IEnumerable<EndProductClass> FindAllByParentId(Int32 parentId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ParentId == parentId).OrderBy(x => x.DisplayOrder);

            return FindAll(_.ParentId == parentId).OrderBy(x => x.DisplayOrder);
        }


        /// <summary>模糊查询分类名称</summary>
        /// <returns>实体列表</returns>
        public static IList<EndProductClass> FindAllByLikeName(string Key)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Name.Contains(Key));

            return FindAll(_.Name.Contains(Key));
        }

        /// <summary>根据编号、当前层级查找</summary>
        /// <param name="id">编号</param>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IList<EndProductClass> FindAllByIdAndLevel(Int32 id, Int32 level)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Id == id && e.Level == level);

            return FindAll(_.Id == id & _.Level == level);
        }

        /// <summary>根据名称查找</summary>
        /// <param name="name">设备DeviceName</param>
        /// <returns>实体对象</returns>
        public static EndProductClass FindByName(String name)
        {
            if (name.IsNullOrWhiteSpace()) return null;

            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

            return Find(_.Name == name);
        }

        /// <summary>
        /// 根据列表Ids获取列表
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static IList<EndProductClass> FindByIds(String ids)
        {
            if (ids.IsNullOrWhiteSpace()) return new List<EndProductClass>();

            ids = ids.Trim(',');

            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => ids.SplitAsInt(",").Contains(x.Id));
            }

            return FindAll(_.Id.In(ids.Split(',')));
        }

        /// <summary>
        /// 根据ID获取下面所有的产品分类及自己
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public static IList<EndProductClass> FindChildrenById(Int32 Id)
        {
            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => x.Id == Id || x.ParentId == Id);
            }

            return FindAll(_.Id == Id | _.ParentId == Id);
        }
        #endregion

        #region 高级查询

        // Select Count(Id) as Id,Category From DG_ProductClassStaple Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<EndProductClass> _CategoryCache = new FieldCache<EndProductClass>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}