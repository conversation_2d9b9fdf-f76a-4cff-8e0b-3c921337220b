﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    Html.AppendCssFileParts("~/css/mobile/index.css");
}

<div class="Milky-Way">
    <canvas id="c" height="291"></canvas>
    <p>@T("智能制造 创造未来")</p>
    <span>@T("缩短产品上市时间提高生产效率和灵活性")</span>
</div>

<div class="solve">
    <p>@T("一站式解决方案")</p>
    <span>@T("提供从PCBA设计到后台SaaS包括APP小程序等快速落地的一站式解决方案")</span>
    <div class="solve-one">
        <p><span><img src="@(CDN.GetCDN())/images/mk.png"></span><i>@T("智能模块")</i><b>@T("提供WIFI/蓝牙/NB-IOT/Lora/Cat1模组")</b></p>
        <p><span><img src="@(CDN.GetCDN())/images/q.png"></span><i>@T("智能APP")</i><b>@T("APP开发和维护，管理智能硬件，享受方便快捷的智能生活")</b></p>
    </div>
    <div>
        <p><span><img src="@(CDN.GetCDN())/images/sj.png"></span><i>@T("智能产品")</i><b>@T("海凌科提供一站式产品智能解决方案")</b></p>
        <p><span><img src="@(CDN.GetCDN())/images/yun.png"></span><i>@T("智能云服务")</i><b>@T("对接各大IOT云平台，包括天猫/飞燕/涂鸦/华为/小米等合作伙伴")</b></p>
    </div>
    <div>
        <p><span><img src="@(CDN.GetCDN())/images/solution5.png"></span><i>@T("大数据开发")</i><b>@T("接入平台的智能硬件进行数据分析和呈现，结合用户行为进行深度分析")</b></p>
        <p><span><img src="@(CDN.GetCDN())/images/solution6.png"></span><i>@T("开放生态平台")</i><b>@T("为合作伙伴提供信息系统，数据开放服务帮助合伙伴开发个性化增值服务")</b></p>
    </div>
</div>

<div class="recommended">
    <p>@T("推荐产品")</p>
    <span>@T("智慧赋能 打造高效快速的产品落地服务")</span>
    <ul>
        @foreach (var item in Model.productRecomList)
        {
            <li><a href="@Url.DGAction("Details","Product",new { Id=item.Id})"><img src="@(CDN.GetCDN())@item.Image" alt="Alternate Text" /><b>@item.Name</b><i>@item.AdvWord</i></a></li>
        }
    </ul>
</div>

<div class="scenario">
    <p>@T("应用场景")</p>
    <span>@T("海凌科针对物联网设备连接需求，广泛地应用于智能家居市场，物联网市场，工业控制等应用场景")</span>
    <ul>
        <li>
            <img src="@(CDN.GetCDN())/images/t1.jpg" alt="Alternate Text" />
            <p>@T("智能制造")</p>
        </li>
        <li>
            <img src="@(CDN.GetCDN())/images/t2.jpg" alt="Alternate Text" />
            <p>@T("智慧物流")</p>
        </li>
        <li>
            <img src="@(CDN.GetCDN())/images/t3.jpg" alt="Alternate Text" />
            <p>@T("智能产品")</p>
        </li>
        <li>
            <img src="@(CDN.GetCDN())/images/t4.jpg" alt="Alternate Text" />
            <p>@T("电源模块")</p>
        </li>
        <li>
            <img src="@(CDN.GetCDN())/images/t5.jpg" alt="Alternate Text" />
            <p>@T("智能安防")</p>
        </li>
        <li>
            <img src="@(CDN.GetCDN())/images/t6.jpg" alt="Alternate Text" />
            <p>@T("智慧农业")</p>
        </li>
    </ul>
</div>

<div class="case">
    <p>@T("相关案例")</p>
    <span>@T("一站式智能控制解决方案提供商")</span>
    <ul>
        @foreach (var item in Model.caseRecomList)
        {
            <li><a href="@Url.DGAction("details","case",new { Id=item.Id})"><img src="@(CDN.GetCDN())/images/xgaltp.png" alt="Alternate Text" /><b>@item.Name</b>@*<i>用途：手持设备/远程控制/消费类电子/物联网应用/工业控制</i>*@</a></li>
        }
    </ul>
</div>

<div class="news">
    <p>@T("新闻中心")</p>
    <span>@T("热点/痛点/观点 连点成线，物联大事件脉络尽在掌握")</span>
    <div>
        <p>
            <span id="0" class="selected">@T("行业新闻")</span>
            <span id="1">@T("公司新闻")</span>
            <span id="2">@T("知识问答")</span>
        </p>
        <ul>
            @foreach (var item in Model.ArticleList)
            {
                <li><a href="@Url.DGAction("Details","Journalism",new { Id=item.Id})"><i>@item.Name </i><i>@item.CreateTime.ToString("yyyy-MM-dd")</i></a></li>
            }
        </ul>
        <ul>
            @foreach (var item in Model.IndustryList)
            {
                <li><a href="@Url.DGAction("Details","Journalism",new { Id=item.Id})"><i>@item.Name</i><i>@item.CreateTime.ToString("yyyy-MM-dd")</i></a></li>
            }
        </ul>
        <ul>
            @foreach (var item in Model.Solutionlist as IEnumerable<Solution>)
            {
                <li><a href="@(item.Url==""||item.Url==null?Url.DGAction("Details","Solution",new { Id=item.Id}):item.Url)"><i>@item.Name</i><i>@item.CreateTime.ToString("yyyy-MM-dd")</i></a></li>
            }
        </ul>
    </div>
</div>

<script src="~/js/dat.gui.min.js"></script>
<script src="~/js/mobile/index.js"></script>
<script type="text/javascript"  asp-location="Footer">
    $(function () {
        $(".news div ul:not(.news div ul:eq(0))").hide();

        $(".news p span").click(function () {
            $(".news p span").removeClass("selected");
            $(this).addClass("selected");
            $(".news div ul").hide();
            $(".news div ul:eq(" + $(this).attr("id") + ")").show();
        });
    })
</script>

