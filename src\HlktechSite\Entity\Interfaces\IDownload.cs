﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>下载文件</summary>
public partial interface IDownload
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>下载文件名称</summary>
    String? Name { get; set; }

    /// <summary>开发资料 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    String? Development { get; set; }

    /// <summary>应用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    String? Application { get; set; }

    /// <summary>通用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    String? GeneralSoftware { get; set; }

    /// <summary>产品型号Id，以逗号分隔开</summary>
    String? MIds { get; set; }

    /// <summary>下载分类Id</summary>
    Int32 DId { get; set; }

    /// <summary>点击数</summary>
    Int32 Clicks { get; set; }

    /// <summary>排序</summary>
    Int16 DisplayOrder { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
