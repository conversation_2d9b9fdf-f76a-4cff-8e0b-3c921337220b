﻿@{
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();

    var localizationSettings = LocalizationSettings.Current;
}
@model HlktechSite.Entity.AlbumPic
<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>商品管理</h3>
                <h5></h5>
            </div>
            <ul class="add-goods-step">
                <li>
                    <i class="iconfont">&#xe600;</i>
                    <h6>STEP.1</h6>
                    <h2>选择商品分类</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li>
                    <i class="icon iconfont">&#xe731;</i>
                    <h6>STEP.2</h6>
                    <h2>填写商品详情</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li class="current">
                    <i class="icon iconfont">&#xe6a2;</i>
                    <h6>STEP.3</h6>
                    <h2>上传商品图片</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li>
                    <i class="icon iconfont">&#xe64d;</i>
                    <h6>STEP.4</h6>
                    <h2>商品发布成功</h2>
                </li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>
    <form method="post" id="goods_image" action="@Url.Action("CreateGoodsthrees")">
        <input type="hidden" name="commonid" value="123">
        <div class="dssc-form-goods-pic">

            <div class="container">
                <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                    @if (localizationSettings.IsEnable)
                    {
                        <ul class="layui-tab-title">
                            <li data="" class="layui-this">标准 </li>
                            @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                            {
                                <li data="@item.Id" class="LId">@item.DisplayName</li>
                            }
                        </ul>
                    }
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="dssc-goodspic-list">
                                <div class="title">
                                    <h3></h3>
                                </div>
                                <ul dstype="ul0">
                                    <li class="dssc-goodspic-upload">
                                        <div class="upload-thumb">
                                            <img src="@(Model.Cover.IsNullOrWhiteSpace()?"~/uploads/common/default_goods_image.jpg":UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Model.Cover))" dstype="file_00">
                                            <input type="hidden" name="img[0][0][name]" value="@Model.Id" dstype="file_00">
                                            <input type="hidden" name="img[0][0][Id]" value="@Model.Id" dsId="file_00">
                                        </div>
                                        <div class="show-default selected" dstype="file_00">
                                            <p>
                                                <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="img[0][0][default]" value="1">
                                            </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                        </div>
                                        <div class="show-sort">
                                            排序：
                                            <input name="img[0][0][sort]" type="text" class="text" value="0" size="1" maxlength="1">
                                        </div>
                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span><input type="file" hidefocus="true" size="1" class="input-file" name="file_00" id="file_00"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                            </a>
                                        </div>
                                    </li>
                                    <li class="dssc-goodspic-upload">
                                        <div class="upload-thumb">
                                            <img src="~/uploads/common/default_goods_image.jpg" dstype="file_01">
                                            <input type="hidden" name="img[0][1][name]" value="" dstype="file_01">
                                            <input type="hidden" name="img[0][1][Id]" value="" dsId="file_01">
                                        </div>
                                        <div class="show-default" dstype="file_01">
                                            <p>
                                                <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="img[0][1][default]" value="0">
                                            </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                        </div>
                                        <div class="show-sort">
                                            排序：
                                            <input name="img[0][1][sort]" type="text" class="text" value="1" size="1" maxlength="1">

                                        </div>
                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span><input type="file" hidefocus="true" size="1" class="input-file" name="file_01" id="file_01"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                            </a>
                                        </div>
                                    </li>
                                    <li class="dssc-goodspic-upload">
                                        <div class="upload-thumb">
                                            <img src="~/uploads/common/default_goods_image.jpg" dstype="file_02">
                                            <input type="hidden" name="img[0][2][name]" value="" dstype="file_02">
                                            <input type="hidden" name="img[0][2][Id]" value="" dsId="file_02">
                                        </div>
                                        <div class="show-default" dstype="file_02">
                                            <p>
                                                <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="img[0][2][default]" value="0">
                                            </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                        </div>
                                        <div class="show-sort">
                                            排序：
                                            <input name="img[0][2][sort]" type="text" class="text" value="2" size="1" maxlength="1">
                                        </div>
                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span><input type="file" hidefocus="true" size="1" class="input-file" name="file_02" id="file_02"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                            </a>
                                        </div>
                                    </li>
                                    <li class="dssc-goodspic-upload">
                                        <div class="upload-thumb">
                                            <img src="~/uploads/common/default_goods_image.jpg" dstype="file_03">
                                            <input type="hidden" name="img[0][3][name]" value="" dstype="file_03">
                                            <input type="hidden" name="img[0][3][Id]" value="" dsId="file_03">
                                        </div>
                                        <div class="show-default" dstype="file_03">
                                            <p>
                                                <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="img[0][3][default]" value="0">
                                            </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                        </div>
                                        <div class="show-sort">
                                            排序：
                                            <input name="img[0][3][sort]" type="text" class="text" value="3" size="1" maxlength="1">
                                        </div>
                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span><input type="file" hidefocus="true" size="1" class="input-file" name="file_03" id="file_03"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                            </a>
                                        </div>
                                    </li>
                                    <li class="dssc-goodspic-upload">
                                        <div class="upload-thumb">
                                            <img src="~/uploads/common/default_goods_image.jpg" dstype="file_04">
                                            <input type="hidden" name="img[0][4][name]" value="" dstype="file_04">
                                            <input type="hidden" name="img[0][4][Id]" value="" dsId="file_04">
                                        </div>
                                        <div class="show-default" dstype="file_04">
                                            <p>
                                                <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="img[0][4][default]" value="0">
                                            </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                        </div>
                                        <div class="show-sort">
                                            排序：
                                            <input name="img[0][4][sort]" type="text" class="text" value="4" size="1" maxlength="1">
                                        </div>
                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span><input type="file" hidefocus="true" size="1" class="input-file" name="file_04" id="file_04"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                            </a>
                                        </div>
                                    </li>
                                </ul>
                                <div class="dssc-select-album">
                                    <a class="dssc-btn" href="@Url.Action("Piclistmultiple")" dstype="select-0"><i class="iconfont">&#xe72a;</i>从图片空间选择</a>
                                    <a href="javascript:void(0);" dstype="close_album" class="dssc-btn ml5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                </div>
                                <div dstype="album-0"></div>
                            </div>
                        </div>
                        @if (localizationSettings.IsEnable)
                        {
                            @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                            {
                                var CoverLan = (List<HlktechSite.Entity.AlbumPic>)ViewBag.lanCover;
                                var ModelLan = CoverLan.Where(x => x.LId == item.Id).FirstOrDefault();
                                if (ModelLan == null)
                                {
                                    ModelLan = new AlbumPic();
                                }
                                <div class="layui-tab-item">
                                    <div class="dssc-goodspic-list">
                                        <div class="title">
                                            <h3></h3>
                                        </div>
                                        <ul dstype="ul0@(item.Id)">
                                            <li class="dssc-goodspic-upload">
                                                <div class="upload-thumb">
                                                    <img src="@(ModelLan.Cover.IsNullOrWhiteSpace()?"~/uploads/common/default_goods_image.jpg":ModelLan.Cover)" dstype="file_00@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][0][name]" value="@ModelLan.Id" dstype="file_00@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][0][Id]" value="@ModelLan.Id" dsId="file_00">
                                                </div>
                                                <div class="show-default selected" dstype="file_00">
                                                    <p>
                                                        <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="[@item.Id].img[0][0][default]" value="1">
                                                    </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                </div>
                                                <div class="show-sort">
                                                    排序：
                                                    <input name="[@item.Id].img[0][0][sort]" type="text" class="text" value="0" size="1" maxlength="1">
                                                </div>
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span><input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].file_00" id="file_00@(item.Id)"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                                    </a>
                                                </div>
                                            </li>
                                            <li class="dssc-goodspic-upload">
                                                <div class="upload-thumb">
                                                    <img src="~/uploads/common/default_goods_image.jpg" dstype="file_01@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][1][name]" value="" dstype="file_01@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][1][Id]" value="" dsId="file_01@(item.Id)">
                                                </div>
                                                <div class="show-default" dstype="file_01@(item.Id)">
                                                    <p>
                                                        <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="[@item.Id].img[0][1][default]" value="0">
                                                    </p><a href="javascript:void(0)" dstype="del@(item.Id)" class="del" title="移除">X</a>
                                                </div>
                                                <div class="show-sort">
                                                    排序：
                                                    <input name="[@item.Id].img[0][1][sort]" type="text" class="text" value="1" size="1" maxlength="1">

                                                </div>
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span><input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].file_01" id="file_01@(item.Id)"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                                    </a>
                                                </div>
                                            </li>
                                            <li class="dssc-goodspic-upload">
                                                <div class="upload-thumb">
                                                    <img src="~/uploads/common/default_goods_image.jpg" dstype="file_02@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][2][name]" value="" dstype="file_02@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][2][Id]" value="" dsId="file_02">
                                                </div>
                                                <div class="show-default" dstype="file_02@(item.Id)">
                                                    <p>
                                                        <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="[@item.Id].img[0][2][default]" value="0">
                                                    </p><a href="javascript:void(0)" dstype="del@(item.Id)" class="del" title="移除">X</a>
                                                </div>
                                                <div class="show-sort">
                                                    排序：
                                                    <input name="[@item.Id].img[0][2][sort]" type="text" class="text" value="2" size="1" maxlength="1">
                                                </div>
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span><input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].file_02" id="file_02@(item.Id)"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                                    </a>
                                                </div>
                                            </li>
                                            <li class="dssc-goodspic-upload">
                                                <div class="upload-thumb">
                                                    <img src="~/uploads/common/default_goods_image.jpg" dstype="file_03@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][3][name]" value="" dstype="file_03@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][3][Id]" value="" dsId="file_03">
                                                </div>
                                                <div class="show-default" dstype="file_03@(item.Id)">
                                                    <p>
                                                        <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="[@item.Id].img[0][3][default]" value="0">
                                                    </p><a href="javascript:void(0)" dstype="del@(item.Id)" class="del" title="移除">X</a>
                                                </div>
                                                <div class="show-sort">
                                                    排序：
                                                    <input name="[@item.Id].img[0][3][sort]" type="text" class="text" value="3" size="1" maxlength="1">
                                                </div>
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span><input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].file_03" id="file_03@(item.Id)"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                                    </a>
                                                </div>
                                            </li>
                                            <li class="dssc-goodspic-upload">
                                                <div class="upload-thumb">
                                                    <img src="~/uploads/common/default_goods_image.jpg" dstype="file_04@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][4][name]" value="" dstype="file_04@(item.Id)">
                                                    <input type="hidden" name="[@item.Id].img[0][4][Id]" value="" dsId="file_04">
                                                </div>
                                                <div class="show-default" dstype="file_04@(item.Id)">
                                                    <p>
                                                        <i class="iconfont">&#xe64d;</i>默认主图                <input type="hidden" name="[@item.Id].img[0][4][default]" value="0">
                                                    </p><a href="javascript:void(0)" dstype="del@(item.Id)" class="del" title="移除">X</a>
                                                </div>
                                                <div class="show-sort">
                                                    排序：
                                                    <input name="[@item.Id].img[0][4][sort]" type="text" class="text" value="4" size="1" maxlength="1">
                                                </div>
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span><input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].file_04" id="file_04@(item.Id)"></span><p><i class="iconfont">&#xe733;</i>上传</p>
                                                    </a>
                                                </div>
                                            </li>
                                        </ul>
                                        <div class="dssc-select-album">
                                            <a class="dssc-btn" href="@Url.Action("Piclistmultiple")" dstype="select-0@(item.Id)"><i class="iconfont">&#xe72a;</i>从图片空间选择</a>
                                            <a href="javascript:void(0);" dstype="close_album@(item.Id)" class="dssc-btn ml5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                        </div>
                                        <div dstype="album-0@(item.Id)"></div>
                                    </div>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
            <div class="sidebar">
                <div class="alert alert-info alert-block" id="uploadHelp">
                    <div class="faq-img"></div>
                    <h4>上传要求：</h4><ul>
                        <li>1. 请使用jpg\jpeg\png等格式、单张大小不超过1M的正方形图片。</li>
                        <li>2. 上传图片最大尺寸将被保留为1280像素。</li>
                        <li>3. 每种颜色最多可上传5张图片或从图片空间中选择已有的图片，上传后的图片也将被保存在店铺图片空间中以便其它使用。</li>
                        <li>4. 通过更改排序数字修改商品图片的排列显示顺序。</li>
                        <li>5. 图片质量要清晰，不能虚化，要保证亮度充足。</li>
                        <li>6. 操作完成后请点下一步，否则无法在网站生效。</li>
                    </ul><h4>建议:</h4><ul><li>1. 主图为白色背景正面图。</li><li>2. 排序依次为正面图-&gt;背面图-&gt;侧面图-&gt;细节图。</li></ul>
                </div>
            </div>
        </div>
        <div class="bottom tc hr32"><input type="submit" class="btn" value="下一步，确认商品发布" /></div>
    </form>
    <script src="/static/plugins/ajaxfileupload.js"></script>
    <script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
    <script src="/static/home/<USER>/sellergoods_add_step3.js"></script>

    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>
    <script asp-location="Footer">
        layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
            var $ = layui.jquery,
                form = layui.form,
                layer = layui.layer,
                upload = layui.upload,
                layer = layui.layer,
                element = layui.element;
        })

        var DEFAULT_GOODS_IMAGE = "/uploads/common/default_goods_image.jpg";
        var ADMINSITEURL = "/@adminarea";
        var HOMESITEROOT = "/";
        var BASESITEURL = "/@adminarea";
        var ADMINSITEROOT = "/@adminarea";
        var Querytheson = "@Url.Action("Querytheson", "ProductCategory")";
        var CreateImg = "@Url.Action("UploadImg")";
        var CreateImgs = "@Url.Action("UploadImg","SpaceCategory",new { type=1})";

        var lid = "";
        var select = "";
        var del = "";
        var album = "";
        $(function () {

            $('a[dstype=select-0]').ajaxContent({
                event: 'click', //mouseover
                loaderType: "img",
                loadingMsg: '@(DHSetting.Current.CurDomainUrl)/images/loading.gif',
                target: 'div[dstype="album-0"]'
            }).click(function () {
                $(this).hide();
                $(this).next().show();
            });

            $(".layui-tab-title").on("click", "li", function () {
                console.log($(this).attr("data"));
                lid = $(this).attr("data");
                select = "select-0" + lid;
                album = "album-0" + lid;
                del = "del" + lid;

                console.log(select);
                console.log(album);

                $('a[dstype="' + select + '"]').ajaxContent({
                    event: 'click', //mouseover
                    loaderType: "img",
                    loadingMsg: '@(DHSetting.Current.CurDomainUrl)/images/loading.gif',
                    target: 'div[dstype="' + album + '"]'
                }).click(function () {
                    $(this).hide();
                    $(this).next().show();
                });

            })





            /* ajax 打开图片空间 */
            //$('a[dstype="select-0"]').ajaxContent({
            //    event: 'click', //mouseover
            //    loaderType: "img",
            //    loadingMsg: '/static/admin/images/loading.gif',
            //    target: 'div[dstype="album-0"]'
            //}).click(function () {
            //    $(this).hide();
            //    $(this).next().show();
            //});
        });

        // 选择默认主图&&删除
        function selectDefaultImage($this) {
            // 默认主题
            $this.click(function () {
                $(this).parents('ul:first').find('.show-default').removeClass('selected').find('input').val('0');
                $(this).addClass('selected').find('input').val('1');
            });
            // 删除
            $this.parents('li:first').find('a[dstype="' + del + '"]').click(function () {
                $this.unbind('click').removeClass('selected').find('input').val('0');
                $this.prev().find('input').val('').end().find('img').attr('src', DEFAULT_GOODS_IMAGE);
            });
        }

        // 从图片空间插入主图
        function insert_img(name, src, color_id) {
            color_id = color_id + lid;
            var $_thumb = $('ul[dstype="ul' + color_id + '"]').find('.upload-thumb');
            $_thumb.each(function () {
                if ($(this).find('input').val() == '') {
                    $(this).find('img').attr('src', src);
                    $(this).find('input').val(name);
                    selectDefaultImage($(this).next());      // 选择默认主图
                    return false;
                }
            });
        }

        function insert_imgs(id, name, src, color_id) {
            color_id = color_id + lid;
            var $_thumb = $('ul[dstype="ul' + color_id + '"]').find('.upload-thumb');
            $_thumb.each(function () {
                if ($(this).find('input:eq(0)').val() == '') {
                    $(this).find('img').attr('src', src);
                    $(this).find('input:eq(0)').val(name);
                    $(this).find('input:eq(1)').val(id);
                    $(this).find('input:eq(2)').val(name);

                    selectDefaultImage($(this).next());      // 选择默认主图
                    return false;
                }
            });
        }
    </script>
</div>

