﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    if (Model.type == 0)
    {
        Html.AppendTitleParts(T("客户案例").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        var modelCaseCategory = Model.Model as CaseCategory;
        Html.AppendTitleParts(modelCaseCategory.Name + DG.Setting.Current.PageTitleSeparator + T("客户案例").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
}

<div class="top">
    <img src="@(CDN.GetCDN())/images/CaseBanner.png" />
    <h2>@T("客户案例")</h2>
    <P>
        @T("打造全新物联网行业解决方案，满足客户个性化需求")
    </P>
</div>

<div class="input-group seach-div">
    <input class="form-control" type="text" id="key" name="key" value="@Model.key" />
    <a href="javascript:;" onclick="seach()">@T("搜索")</a>
</div>

<ul class="type-menu">
    <li class="@(Model.type == 0 ? "selected" : "")"><a href="@Url.DGAction("Index")">@T("全部")</a></li>
    @foreach (var item in Model.CaseTypelist as IEnumerable<CaseCategory>)
    {
        <li class="@(Model.type == item.Id ? "selected" : "")">
            <a id="@item.Id" href="@Url.DGAction("List", new { Type = item.Id})">@item.Name</a>
        </li>
    }
</ul>

<ul class="rows-list">
    @foreach (var item in Model.SolutionList)
    {
        <li><a href="@Url.DGAction("Details",new { Id=item.Id})"><img src="@(CDN.GetCDN())@item.Pic" alt="Alternate Text" /><b>@item.Name</b><i>@item.Summary</i></a></li>
    }
</ul>

<div class="paging" style="text-align: center;">
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>

<script asp-location="Footer">
    var cid = @Model.type;

    $("#key").keydown(function (e) {
        if (e.which == 13) {
            if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#key").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { Type = Model.type})?key=" + $("#key").val();
        }
        }
    });

      function seach(){
        if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#key").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { Type = Model.type})?key=" + $("#key").val();
        }
    }

</script>