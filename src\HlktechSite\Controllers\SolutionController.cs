﻿using System.Dynamic;

using DG.Cube;
using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;

using HlktechSite.DTO;
using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;

namespace HlktechSite.Controllers;

/// <summary>
/// 解决方案控制器
/// </summary>
public class SolutionController : DGBaseControllerX
{
    /// <summary>
    /// 解决方案首页
    /// </summary>
    /// <returns></returns>
    public IActionResult Index(string key, int CId = 0, int p = 1)
    {
        key = key.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 9,
            RetrieveTotalCount = true
        };
        IEnumerable<Solution> SolutionList;
        IEnumerable<SolutionCategory> SolutionTypelist;

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
            SolutionTypelist = SolutionCategoryLan.FindAllByLevel(WorkingLanguage.Id).Select(x => new SolutionCategory
            {
                Id = x.CId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.solutionCategory.Name : x.Name
            });
        else
            SolutionTypelist = SolutionCategory.FindAllByLevel(0);
        viewModel.SolutionTypelist = SolutionTypelist;

        if (localizationSettings.IsEnable)
        {
            SolutionList = SolutionLan.Searchs(key, pages, CId, WorkingLanguage.Id).Select(x => new Solution
            {
                Id = x.CId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.SolutionModel?.Name : x.Name,
                Url = x.SolutionModel?.Url.SafeString(),
                Pic = x.Pic.IsNullOrWhiteSpace() ? x.SolutionModel?.Pic : x.Pic,
                Summary = x.Summary.IsNullOrWhiteSpace() ? x.SolutionModel?.Summary : x.Summary,
                CreateTime = x.SolutionModel.CreateTime
            });
        }
        else
        {
            SolutionList = Solution.Search(key, CId, pages);
        }

        viewModel.SolutionList = SolutionList;
        viewModel.CId = CId;
        viewModel.key = key;
        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("解决方案"), IsLast = true });
        ViewBag.Locations = navigations;
        viewModel.CategoryList = SolutionCategory.GetAll();
        viewModel.Model = SolutionTypelist.FirstOrDefault(e => e.Id == CId);

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "Index", "Solution", "", dic, "p");

        return DGView(viewModel, true);
    }

    /// <summary>
    /// 解决方案栏目
    /// </summary>
    /// <returns></returns>
    public IActionResult List(string key, int CId = 0, int p = 1)
    {
        var model = SolutionCategory.FindById(CId);
        if (model == null)
        {
            return View404();
        }

        key = key.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 9,
            RetrieveTotalCount = true
        };
        IEnumerable<Solution> SolutionList;
        IEnumerable<SolutionCategory> SolutionTypelist;

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
            SolutionTypelist = SolutionCategoryLan.FindAllByLevel(WorkingLanguage.Id).Select(x => new SolutionCategory
            {
                Id = x.CId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.solutionCategory.Name : x.Name
            });
        else
            SolutionTypelist = SolutionCategory.FindAllByLevel(0);

        if (SolutionTypelist == null || !SolutionTypelist.Any())
        {
            return View404();
        }

        viewModel.SolutionTypelist = SolutionTypelist;

        if (localizationSettings.IsEnable)
        {
            SolutionList = SolutionLan.Searchs(key, pages, CId, WorkingLanguage.Id).Select(x => new Solution
            {
                Id = x.CId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.SolutionModel?.Name : x.Name,
                Url = x.SolutionModel?.Url.SafeString(),
                Pic = x.Pic.IsNullOrWhiteSpace() ? x.SolutionModel?.Pic : x.Pic,
                Summary = x.Summary.IsNullOrWhiteSpace() ? x.SolutionModel?.Summary : x.Summary,
                CreateTime = x.SolutionModel.CreateTime
            });
        }
        else
        {
            SolutionList = Solution.Search(key, CId, pages);
        }

        viewModel.SolutionList = SolutionList;
        viewModel.CId = CId;
        viewModel.key = key;
        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("解决方案"), IsLast = true });
        ViewBag.Locations = navigations;
        viewModel.CategoryList = SolutionCategory.GetAll();
        viewModel.Model = SolutionTypelist.FirstOrDefault(e => e.Id == CId);

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
        dic.TryAdd("CId", CId.ToString());

        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "List", "Solution", "", dic, "p");

        return DGView(viewModel, "Index", true);
    }

    /// <summary>
    /// 解决方案详情
    /// </summary>
    /// <returns></returns>
    public IActionResult Details(int Id)
    {
        dynamic viewModel = new ExpandoObject();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var Model = SolutionLan.FindByCIdAndLId(Id, WorkingLanguage.Id);
            if (Model == null)
            {
                return View404();
            }
            var caseModel = new Solution
            {
                Name = Model.Name.IsNullOrWhiteSpace() ?Model.SolutionModel?.Name : Model.Name,
                Summary = Model.Summary.IsNullOrWhiteSpace() ? Model.SolutionModel?.Summary : Model.Summary,
                Content = (Model.Content.SafeString().IsNullOrWhiteSpace() ? Model.SolutionModel?.Content : Model.Content),
                CreateTime = Model.SolutionModel.CreateTime
            };
            viewModel.SolutionModel = caseModel;
            viewModel.previous = Case.Findprevious(Model.SolutionModel.CreateTime);
            viewModel.Nex = Case.FindNext(Model.SolutionModel.CreateTime);
            ViewBag.Titles = SolutionCategoryLan.FindNameByCIdAndlId(Model.SolutionModel.CId, WorkingLanguage.Id);
            return DGView(viewModel, true);
        }
        var SolutionModel = Solution.FindById(Id);
        if (SolutionModel == null)
        {
            return View404();
        }
        SolutionModel.Content = SolutionModel.Content;
        viewModel.SolutionModel = SolutionModel;
        viewModel.previous = Solution.Findprevious(SolutionModel.CreateTime);
        viewModel.Nex = Solution.FindNext(SolutionModel.CreateTime);
        ViewBag.Titles = SolutionCategory.FindById(SolutionModel.CId)?.Name;
        return DGView(viewModel, true);
    }



}
