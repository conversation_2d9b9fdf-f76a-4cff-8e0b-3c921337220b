﻿@model List<MenuTree>
@{
    Layout = null;
    var user = ViewBag.User as IUser ?? User.Identity as IUser;
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
    var returnUrl = WebHelper2.GetRawUrlStr(Context.Request);
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>@T(NewLife.Common.SysConfig.Current.DisplayName)</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="~/static/admin/css/admin.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/static/plugins/js/jqueryui/jquery-ui.min.css" asp-append-version="true">
    <script src="~/static/plugins/jquery214.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/jquery.validate.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/jquery.cookie.js" asp-append-version="true"></script>
    <script src="~/static/plugins/common.js" asp-append-version="true"></script>
    <script src="~/static/admin/js/admin.js" asp-append-version="true"></script>
    <script src="~/static/plugins/js/jqueryui/jquery-ui.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/js/jqueryui/jquery.ui.datepicker-zh-CN.js" asp-append-version="true"></script>
    <script src="~/static/plugins/perfect-scrollbar.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/layer/layer.js" asp-append-version="true"></script>
    <script type="text/javascript">
        var HOMESITEURL = "@DHSetting.Current.CurDomainUrl";
        var ADMINSITEURL = "/@adminarea";
    </script>
</head>
<body>
    <div id="append_parent"></div>
    <div id="ajaxwaitid"></div>

    <div class="admincp-header">
        <div class="logo">
            <img src="~/static/admin/images/backlogo.png" />
        </div>
        <div class="navbar">
            <ul class="fl" style="float:left;">
                @foreach (var menu in Model)
                {
                    <li id="<EMAIL>()">
                        <a href="javascript:void(0)" onclick="openItem('@menu.Name.ToLower()')">@menu.DisplayName</a>
                    </li>
                }
            </ul>
            <ul class="fr" style="float:right">
                <li>
                    <span>@T("您好,")@user.Name</span>
                    <div class="sub-meun">
                        <dl>
                            <dd><a href="@Url.Action("ModifyPw", "Home", new { area = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName } )" target="main-frame"><i class="iconfont">&#xe67b;</i>@T("修改密码")</a></dd>
                            <dd><a href="javascript:dsLayerConfirm('@Url.Action("Index", "Logout", new { area = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName, ReturnState = 1, r = Url.Action("Index", "Login", new { area = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName }) } )','@T("您确定退出吗?")', '@(DG.Setting.Current.LogoutAll ? "logout" : "")')"><i class="iconfont">&#xe70c;</i>@T("安全退出")</a></dd>
                        </dl>
                    </div>
                </li>
                <li>
                    <span>Language</span>
                    <div class="sub-meun">
                        <dl>
                            @foreach (var item in languagelist)
                            {
                                @*<dd><a href='@Url.RouteUrl("ChangeLanguage", new { langid = item.Id, returnUrl }, WebHelper2.CurrentRequestProtocol)' target="main-frame">@item.DisplayName</a></dd>*@
                                @*<dd><a href='@Url.RouteUrl("DashBoard", new { langid = item.Id, returnUrl }, WebHelper2.CurrentRequestProtocol)' target="main-frame">@item.DisplayName</a></dd>*@
                                <dd><a href='@Url.Action("DashBoard", new { lang = item.UniqueSeoCode })'>@item.DisplayName</a></dd>
                            }
                        </dl>
                    </div>
                </li>
                <li><a href="javascript:dsLayerConfirm('@Url.Action("ClearCache", "Home", new { area = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName } )','@T("确定清理全部缓存?")')" target="main-frame">@T("清除缓存")</a></li>
                <li><a href="/" target="_blank">@T("访问首页")</a></li>
            </ul>
        </div>
    </div>

    <div class="admincp-container">
        <div class="admincp-container-left">
            <div id="mainMenu">
                @{ 
                    var i = 0;
                }
                @foreach (var menu in Model)
                {
                    i++;
                <ul id="<EMAIL>()" @(i == 1 ? Html.Raw("") : Html.Raw("style=\"display:none\"") )>
                    @if (menu.Children != null && menu.Children.Count > 0)
                    {
                        @foreach (var item in menu.Children)
                        {
                            var url = item.Url.Replace($"/{adminarea}/", "");
                            var urlArray = url.Split('/');

                            var action = "";
                            var controller = "";

                            if (urlArray.Length == 1)
                            {
                                action = "index";
                            }
                            else
                            {
                                action = urlArray[1];
                            }
                            controller = urlArray[0];

                            <li id="left_@(menu.Name.ToLower())@(controller)@(action)"><a href="javascript:void(0)" onclick="openItem('@action,@controller,@menu.Name.ToLower()')"><i class="iconfont">@Html.Raw(item.Icon)</i>@item.DisplayName</a></li>
                        }
                    }
                </ul>
                }
            </div>

        </div>
        <div class="admincp-container-right">
            <div class="top-border"></div>
            <iframe src="@Url.Action("Index", "Main")" id="main-frame" name="main-frame" style="overflow: visible; width: 100%; height:100%;" frameborder="0" scrolling="yes" onload="window.parent"></iframe>
        </div>
    </div>
    <script>
        $(function () {
            $('#welcome,dashboard,dashboard').addClass('active');
            if ($.cookie('now_location_controller') != null) {
                openItem($.cookie('now_location_action') + ',' + $.cookie('now_location_controller') + ',' + $.cookie('now_location_module'));
            } else {
                $('#mainMenu>ul').first().css('display', 'block');
                //第一次进入后台时，默认定到欢迎界面
                $('#item_welcome').addClass('selected');
                $('#workspace').attr('src', ADMINSITEURL + '@Url.Action("Index", "Main")');
            }
            $('#iframe_refresh').click(function () {
                var fr = document.frames ? document.frames("workspace") : document.getElementById("workspace").contentWindow;
                fr.location.reload();
            });
        });

        function openItem(args) {
            spl = args.split(',');
            action = spl[0];
            try {
                controller = spl[1];
                module = spl[2];
            }
            catch (ex) {
            }
            if (typeof (controller) == 'undefined') {
                var module = args;
            }
            //顶部导航样式处理
            $('.actived').removeClass('actived');
            $('#nav_' + module).addClass('actived');
            //清除左侧样式
            $('.selected').removeClass('selected');

            //show
            $('#mainMenu ul').css('display', 'none');
            $('#sort_' + module).css('display', 'block');
            if (typeof (controller) == 'undefined') {
                //顶部菜单事件
                html = $('#sort_' + module + '>li').first().html();
                str = html.match(/openItem\('(.*)'\)/ig);
                arg = str[0].split("'");
                spl = arg[1].split(',');
                action = spl[0];
                controller = spl[1];
                module = spl[2];
                first_obj = $('#sort_' + module + '>li').first();
                $(first_obj).addClass('selected');
            } else {
                //左侧菜单事件
                //location
                $.cookie('now_location_module', module);
                $.cookie('now_location_controller', controller);
                $.cookie('now_location_action', action);
                $("#left_" + module + controller + action).addClass('selected');

            }

            if (action == "index") {
                src = ADMINSITEURL + '/' + controller;
            }
            else {
                src = ADMINSITEURL + '/' + controller + '/' + action;
            }
            
            $('#main-frame').attr('src', src);
        }
    </script>
</body>
</html>