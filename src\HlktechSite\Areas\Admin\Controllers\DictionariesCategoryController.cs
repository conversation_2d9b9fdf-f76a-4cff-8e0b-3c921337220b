﻿using DG.Cube.BaseControllers;
using DG.Cube.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek;
using Pek.Helpers;
using Pek.Iot;
using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>字典分类</summary>
[DisplayName("字典分类")]
[Description("用于字典分类的管理")]
[AdminArea]
[DHMenu(49,ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/DictionariesCategory", CurrentMenuName = "DictionariesCategoryList", CurrentIcon = "&#xe652;", LastUpdate = "20240125")]
public class DictionariesCategoryController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 49;

    /// <summary>
    /// 字典分类列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("字典分类列表")]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var list = DictionariesCategory.FindAllByLevel(0);
        foreach (var item in list)
        {
            var Model = DictionariesCategory.FindAllByParentId(item.Id);
            if (Model.Count != 0)
            {
                item.subset = true;
            }
        }
        viewModel.list = list;
        return View(viewModel);
    }

    /// <summary>
    /// 获取字典分类表下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取字典分类表下级数据")]
    public IActionResult GetSubordinateData(Int32 Id)
    {
        var zList = new List<Hierarchy>();
        if (Id == 0)
        {
            return Json(new { });
        }

        var list = DictionariesCategory.FindAllByParentId(Id.ToInt());
        foreach (var item in list)
        {
            var model = new Hierarchy();
            model.gc_name = item.Name;
            model.gc_id = item.Id;
            model.gc_parent_id = item.ParentId;
            var exc = DictionariesCategory.FindAllByParentId(item.Id);
            if (exc.Count > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.gc_sort = item.DisplayOrder;
            model.deep = item.Level;
            model.IsSystem = item.IsSystem;
            zList.Add(model);
        }
        return Json(new { zList });
    }

    /// <summary>
    /// 打开修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("打开修改页面")]
    public IActionResult EditDictionariesCategory(Int32 Id)
    {
        dynamic viewModel = new ExpandoObject();
        var Model = DictionariesCategory.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        var List = new List<DictionariesCategory>();
        var live1 = DictionariesCategory.FindAllByLevel(0);
        GetCategoryList(live1, List);
        viewModel.Plist = List;
        viewModel.Model = Model;
        return View(viewModel);
    }


    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IList<DictionariesCategory> levelList, IList<DictionariesCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = DictionariesCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取是否存在该名称")]
    public IActionResult GetByName(String gc_name, Int32 gc_id)
    {
        var Model = DictionariesCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }

    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <returns></returns>
    public IActionResult GetByNames(String gc_name)
    {
        var Model = DictionariesCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }

    /// <summary>
    /// 修改字典分类
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="gc_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <param name="code_Issystem"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改字典分类")]
    public IActionResult EditDictionariesCategory(String gc_name, Int32 gc_parent_id, Int32 gc_id, Int32 DisplayOrder, int code_Issystem)
    {
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("字典名称不能为空") });
        }
        var Model = DictionariesCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("该下载分类名称已存在") });
        }
        if (gc_parent_id == gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("父级栏目不能为本省") });
        }
        var models = DictionariesCategory.FindById(gc_id);
        if (models == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
        }
        models.Name = gc_name.SafeString().Trim();
        models.ParentId = gc_parent_id.ToInt();
        models.DisplayOrder = DisplayOrder.ToShort();
        models.IsSystem = code_Issystem == 1 ? true : false;
        var pmodel = DictionariesCategory.FindById(gc_parent_id);
        if (pmodel == null)
        {
            models.ParentIdList = gc_id.ToString();
            models.Level = 0;
        }
        else
        {
            models.ParentIdList = pmodel.ParentIdList + "," + gc_id.ToString();
            models.Level = pmodel.Level + 1;
        }
        if (models.Level > 2)
        {
            return Prompt(new PromptModel { Message = GetResource("分类不能超过三层") });
        }
        models.Update();

        //循环修改子集的父级Id集合
        var zList = DictionariesCategory.FindAllByParentId(gc_id);
        foreach (var item in zList)
        {
            item.ParentIdList = models.ParentIdList + "," + item.Id;
            var slist = DictionariesCategory.FindAllByParentId(item.Id);
            foreach (var row in slist)
            {
                item.ParentIdList = item.ParentIdList + "," + row.Id;
            }
            slist.Save();
        }
        zList.Save();
        DictionariesCategory.Meta.Cache.Clear("");//清楚缓存

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 字典分类页面打开
    /// </summary>
    /// <param name="parent_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("字典分类页面打开")]
    public IActionResult AddDictionariesCategory(Int32 parent_id = 0)
    {
        dynamic viewModel = new ExpandoObject();
        var List = new List<DictionariesCategory>();
        viewModel.ParentId = parent_id;
        var live1 = DictionariesCategory.FindAllByLevel(0);
        GetCategoryList(live1, List);
        viewModel.Plist = List;

        int DisplayOrder;
        if (parent_id != 0)
        {
            DisplayOrder = (Int32)DictionariesCategory.FindMax("DisplayOrder", DictionariesCategory._.ParentId == parent_id);
        }
        else
        {
            DisplayOrder = (Int32)DictionariesCategory.FindMax("DisplayOrder");
        }

        ViewBag.DisplayOrder = DisplayOrder + 1;
        return View(viewModel);
    }

    /// <summary>
    /// 字典分类新增
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <param name="code_Issystem"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("字典分类新增")]
    public IActionResult AddDictionariesCategory(String gc_name, Int32 gc_parent_id, Int16 DisplayOrder, int code_Issystem)
    {
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("字典名称不能为空") });
        }

        gc_name = gc_name.SafeString().Trim();
        var model = DictionariesCategory.FindByName(gc_name);
        if (model != null)
        {
            return Prompt(new PromptModel { Message = GetResource("字典分类名称已存在") });
        }

        var Pmodel = DictionariesCategory.FindById(gc_parent_id);
        if (Pmodel != null)
        {
            if (Pmodel.Level >= 2)
            {
                return Prompt(new PromptModel { Message = GetResource("字典分类最多存在三级,创建失败！") });
            }
        }

        var Model = new DictionariesCategory();
        Model.Name = gc_name;
        Model.ParentId = gc_parent_id;
        Model.DisplayOrder = DisplayOrder;
        Model.IsSystem = code_Issystem == 1 ? true : false;
        Model.Insert();

        if (Pmodel == null)
        {
            Model.Level = 0;
            Model.ParentIdList = Model.Id.ToString();
        }
        else
        {
            Model.Level = Pmodel.Level + 1;
            Model.ParentIdList = Pmodel.ParentIdList + "," + Model.Id;
        }
        Model.Update();

        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除分类")]
    public IActionResult Delete(String Ids)
    {
        var res = new DResult();

        var list = DictionariesCategory.FindByIds(Ids);
        var dellist = new List<DictionariesCategory>();

        res = DeleteDictionariesCategory(res, dellist, list);

        if (!res.msg.IsNullOrWhiteSpace())
        {
            return Json(res);
        }

        if (dellist.Delete(true) > 0)
            DictionariesCategory.Meta.Cache.Clear("");
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private DResult DeleteDictionariesCategory(DResult res, IList<DictionariesCategory> dellist, IList<DictionariesCategory> list)
    {
        if (list.Count > 0)
        {
            foreach (var item in list)
            {
                if (item.IsSystem == true)
                {
                    res.msg = String.Format(GetResource("选中的{0}是系统级字典分类， 不允许被删除"), item.Name);
                    return res;
                }



                var listKnowledge = DataDictionary.FindAllByDId(item.Id);
                if (listKnowledge.Count > 0)
                {
                    res.msg = String.Format(GetResource("选中的{0}有关联字典数据 不允许被删除"), item.Name);
                    return res;
                }
                else
                {
                    dellist.Add(item);
                    var childlist = DictionariesCategory.FindAllByParentId(item.Id);
                    res = DeleteDictionariesCategory(res, dellist, childlist);
                }
            }
        }
        return res;
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = DictionariesCategory.FindById(Id);

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = DictionariesCategory.FindByName(value);
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.DisplayOrder = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }

}
