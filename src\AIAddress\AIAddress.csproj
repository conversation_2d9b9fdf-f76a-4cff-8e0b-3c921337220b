﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PublishAot>false</PublishAot>
    <InvariantGlobalization>true</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Entity\Config\**" />
    <Compile Remove="Entity\Log\**" />
    <EmbeddedResource Remove="Entity\Config\**" />
    <EmbeddedResource Remove="Entity\Log\**" />
    <None Remove="Entity\Config\**" />
    <None Remove="Entity\Log\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Baidu.AI" Version="4.15.16" />
    <PackageReference Include="DH.Common" Version="4.0.2025.42800070" />
    <PackageReference Include="DH.MiniExcel" Version="4.1.2025.318-beta0914" />
    <PackageReference Include="DH.NCode" Version="4.11.2025.423-beta0319" />
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Entity\" />
  </ItemGroup>

</Project>
