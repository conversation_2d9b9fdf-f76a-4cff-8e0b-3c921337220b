window._bd_share_main.F.module("component/animate",function(e,t,n){var r,i=r=i||{version:"1.5.2.2"};i.guid="$BAIDU$",i.$$=window[i.guid]=window[i.guid]||{global:{}},i.fx=i.fx||{},i.lang=i.lang||{},i.lang.guid=function(){return"TANGRAM$"+i.$$._counter++},i.$$._counter=i.$$._counter||1,i.lang.Class=function(){this.guid=i.lang.guid(),!this.__decontrolled&&(i.$$._instances[this.guid]=this)},i.$$._instances=i.$$._instances||{},i.lang.Class.prototype.dispose=function(){delete i.$$._instances[this.guid];for(var e in this)typeof this[e]!="function"&&delete this[e];this.disposed=!0},i.lang.Class.prototype.toString=function(){return"[object "+(this.__type||this._className||"Object")+"]"},window.baiduInstance=function(e){return i.$$._instances[e]},i.lang.isString=function(e){return"[object String]"==Object.prototype.toString.call(e)},i.isString=i.lang.isString,i.lang.Event=function(e,t){this.type=e,this.returnValue=!0,this.target=t||null,this.currentTarget=null},i.lang.Class.prototype.fire=i.lang.Class.prototype.dispatchEvent=function(e,t){i.lang.isString(e)&&(e=new i.lang.Event(e)),!this.__listeners&&(this.__listeners={}),t=t||{};for(var n in t)e[n]=t[n];var n,r,s=this,o=s.__listeners,u=e.type;e.target=e.target||(e.currentTarget=s),u.indexOf("on")&&(u="on"+u),typeof s[u]=="function"&&s[u].apply(s,arguments);if(typeof o[u]=="object")for(n=0,r=o[u].length;n<r;n++)o[u][n]&&o[u][n].apply(s,arguments);return e.returnValue},i.lang.Class.prototype.on=i.lang.Class.prototype.addEventListener=function(e,t,n){if(typeof t!="function")return;!this.__listeners&&(this.__listeners={});var r,i=this.__listeners;e.indexOf("on")&&(e="on"+e),typeof i[e]!="object"&&(i[e]=[]);for(r=i[e].length-1;r>=0;r--)if(i[e][r]===t)return t;return i[e].push(t),n&&typeof n=="string"&&(i[e][n]=t),t},i.lang.inherits=function(e,t,n){var r,i,s=e.prototype,o=new Function;o.prototype=t.prototype,i=e.prototype=new o;for(r in s)i[r]=s[r];return e.prototype.constructor=e,e.superClass=t.prototype,typeof n=="string"&&(i.__type=n),e.extend=function(t){for(var n in t)i[n]=t[n];return e},e},i.inherits=i.lang.inherits,i.object=i.object||{},i.extend=i.object.extend=function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},i.fx.Timeline=function(e){i.lang.Class.call(this),this.interval=16,this.duration=500,this.dynamic=!0,i.object.extend(this,e)},i.lang.inherits(i.fx.Timeline,i.lang.Class,"baidu.fx.Timeline").extend({launch:function(){var e=this;return e.dispatchEvent("onbeforestart"),typeof e.initialize=="function"&&e.initialize(),e["btime"]=(new Date).getTime(),e["etime"]=e["btime"]+(e.dynamic?e.duration:0),e["pulsed"](),e},"pulsed":function(){var e=this,t=(new Date).getTime();e.percent=(t-e["btime"])/e.duration,e.dispatchEvent("onbeforeupdate");if(t>=e["etime"]){typeof e.render=="function"&&e.render(e.transition(e.percent=1)),typeof e.finish=="function"&&e.finish(),e.dispatchEvent("onafterfinish"),e.dispose();return}typeof e.render=="function"&&e.render(e.transition(e.percent)),e.dispatchEvent("onafterupdate"),e["timer"]=setTimeout(function(){e["pulsed"]()},e.interval)},transition:function(e){return e},cancel:function(){this["timer"]&&clearTimeout(this["timer"]),this["etime"]=this["btime"],typeof this.restore=="function"&&this.restore(),this.dispatchEvent("oncancel"),this.dispose()},end:function(){this["timer"]&&clearTimeout(this["timer"]),this["etime"]=this["btime"],this["pulsed"]()}}),i.object.each=function(e,t){var n,r,i;if("function"==typeof t)for(r in e)if(e.hasOwnProperty(r)){i=e[r],n=t.call(e,i,r);if(n===!1)break}return e},i.dom=i.dom||{},i.dom.g=function(e){return e?"string"==typeof e||e instanceof String?document.getElementById(e):!e.nodeName||e.nodeType!=1&&e.nodeType!=9?null:e:null},i.g=i.G=i.dom.g,i.dom._g=function(e){return i.lang.isString(e)?document.getElementById(e):e},i._g=i.dom._g,i.dom.getDocument=function(e){return e=i.dom.g(e),e.nodeType==9?e:e.ownerDocument||e.document},i.dom.getComputedStyle=function(e,t){e=i.dom._g(e);var n=i.dom.getDocument(e),r;if(n.defaultView&&n.defaultView.getComputedStyle){r=n.defaultView.getComputedStyle(e,null);if(r)return r[t]||r.getPropertyValue(t)}return""},i.dom._styleFixer=i.dom._styleFixer||{},i.dom._styleFilter=i.dom._styleFilter||[],i.dom._styleFilter.filter=function(e,t,n){for(var r=0,s=i.dom._styleFilter,o;o=s[r];r++)if(o=o[n])t=o(e,t);return t},i.string=i.string||{},i.string.toCamelCase=function(e){return e.indexOf("-")<0&&e.indexOf("_")<0?e:e.replace(/[-_][^-_]/g,function(e){return e.charAt(1).toUpperCase()})},i.dom.getStyle=function(e,t){var n=i.dom;e=n.g(e),t=i.string.toCamelCase(t);var r=e.style[t]||(e.currentStyle?e.currentStyle[t]:"")||n.getComputedStyle(e,t);if(!r||r=="auto"){var s=n._styleFixer[t];s&&(r=s.get?s.get(e,t,r):i.dom.getStyle(e,s))}if(s=n._styleFilter)r=s.filter(t,r,"get");return r},i.getStyle=i.dom.getStyle,i.dom.setStyle=function(e,t,n){var r=i.dom,s;e=r.g(e),t=i.string.toCamelCase(t);if(s=r._styleFilter)n=s.filter(t,n,"set");return s=r._styleFixer[t],s&&s.set?s.set(e,n,t):e.style[s||t]=n,e},i.setStyle=i.dom.setStyle,r.undope=!0;var s=function(e,t,n,s,o){var e=r.g(e);if(!e)return;var u=new i.fx.Timeline({duration:n||400}),a={};r.object.each(t,function(t,n){var i=parseInt(r.dom.getStyle(e,n)),s=parseInt(t),o=s-i;a[n]={gap:o,start:i,end:s}}),u.transition=function(e){return e*(2-e)},u.render=function(n){r.object.each(t,function(t,i){var s=a[i];r.dom.setStyle(e,i,n*s.gap+s.start+"px")}),o&&o(n)},u.finish=function(){r.object.each(t,function(t,n){var i=a[n];r.dom.setStyle(e,n,i.end+"px")}),s&&s()},u.launch()};t.animate=s});