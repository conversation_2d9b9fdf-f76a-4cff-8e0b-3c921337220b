﻿@{
}
<style asp-location="true">
    .type-file-preview {
        z-index: 99999
    }
</style>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("字典管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("编辑")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("UpdateDataDictionary", "DataDictionary", FormMethod.Post, new { id = "form1", enctype = "multipart/form-data" }))
    {
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required">@T("所属字典分类")</td>
                    <td class="vatop rowform">
                        <select name="gc_class_id" id="gc_parent_id">
                            <option value="">@T("请选择")...</option>
                            @foreach (var item in Model.Plist)
                            {
                                if (Model.Model.DId == item.Id)
                                {
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }

                                }
                                else
                                {
                                    @*<option value="@item.Id">@item.Name</option>*@
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                }
                            }
                        </select>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("字典值")</td>
                    <td class="vatop rowform"><input type="text" name="code_title" id="code_title" value="@Model.Model.Name" class="w200" /></td>
                    <td class="vatop tips"><input type="hidden" name="Id" value="@Model.Model.Id" /></td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("字典值标识")</td>
                    <td class="vatop rowform"><input type="text" name="code_type" id="code_type" readonly value="@Model.Model.CodeType" class="w200" /></td>
                    <td class="vatop tips">标识值建议使用英文且同一分类下惟一</td>
                </tr>

                <tr class="noborder">
                    <td class="required">@T("状态")</td>
                    <td class="vatop rowform onoff">
                        <label for="code_show1" class="cb-enable  @(Model.Model.Status?"selected":"")"><span>@T("启用")</span></label>
                        <label for="code_show2" class="cb-disable @(!Model.Model.Status?"selected":"")"><span>@T("不启用")</span></label>
                        <input id="code_show1" name="code_status" @(Model.Model.Status ? "checked" : "") value="1" type="radio">
                        <input id="code_show2" name="code_status" @(!Model.Model.Status ? "checked" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("排序")</td>
                    <td class="vatop rowform"><input type="text" name="code_sort" id="code_sort" value="@Model.Model.Sort" class="w200" /></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("字典描述")</td>
                    <td class="vatop rowform" colspan="2"><textarea name="code_remark" id="code_remark" style="width:90%;height:100px">@Model.Model.Content</textarea></td>
                </tr>

            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    }
</div>
@*<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>*@
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>

<script src="~/static/admin/js/xm-select.js"></script>
<script type="text/javascript" asp-location="Footer">
    $(function () {
      
        $('#code_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                code_title: {
                    required: true
                },
                code_type: {
                    required: true
                },
                code_sort: {
                    number: true,
                    range: [0, 255]
                }
            },
            messages: {
                code_title: {
                    required: '@T("标题名称不能为空")'
                },
                code_type: {
                    required: '@T("字典值类型不能为空")'
                },
                code_sort: {
                    number: '@T("排序只能为数字")',
                    range: '@T("数字范围为0~255，数字越小越靠前")'
                }
            }
        });

    });
</script>
