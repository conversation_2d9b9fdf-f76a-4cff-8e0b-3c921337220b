﻿using DG.Utils.Infrastructure;
using DG.Web.Framework.Routing;

using DH;
using DH.Core.Domain.Localization;

namespace HlktechSite.Common.Routing;

public partial class AreaRouteProvider : BaseRouteProvider, IRouteProvider
{
    #region 方法

    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpoints">路由构造器</param>
    public void RegisterRoutes(IEndpointRouteBuilder endpoints)
    {
        var lang = GetLanguageRoutePattern();

        // 对路由进行重新排序，以便最常用的路由排在最前面。 它可以提高性能
        var UrlSuffix = String.Empty;
        if (DG.Setting.Current.IsAllowUrlSuffix)
        {
            UrlSuffix = ".{urlsuffix:suffix=" + DG.Setting.Current.UrlSuffix + "}";
        }

        if (!DHSetting.Current.IsInstalled) return;

        var pattern = $"{lang}/";

        // 推文/APP下载
        endpoints.MapControllerRoute(name: "areaRouteFProduct",
            pattern: "{area:exists}/FProduct" + UrlSuffix, new { controller = "FProduct", action = "Index", area = "Mobile" });
        endpoints.MapControllerRoute(name: "areaRoutedownloadfdetail",
            pattern: "{area:exists}/FProduct/FDetail/{id}" + UrlSuffix, new { controller = "FProduct", action = "FDetail", area = "Mobile" });

        endpoints.MapControllerRoute(name: "areaRouteFProductTui",
            pattern: "{area:exists}/FProduct/Tui/{Id}" + UrlSuffix, new { controller = "FProduct", action = "Tui", area = "Mobile" });
        endpoints.MapControllerRoute(name: "areaRouteFProductApp",
            pattern: "{area:exists}/FProduct/App/{Id}" + UrlSuffix, new { controller = "FProduct", action = "App", area = "Mobile" });

        endpoints.MapControllerRoute(name: "areaRouteApp",
            pattern: "{area:exists}/App/{id}" + UrlSuffix, new { controller = "App", action = "Index", area = "Mobile" });

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpoints.MapControllerRoute(name: "areaRouteFProduct",
            pattern: pattern + "{area:exists}/FProduct" + UrlSuffix, new { controller = "FProduct", action = "Index", area = "Mobile" });
            endpoints.MapControllerRoute(name: "areaRoutedownloadfdetail",
                pattern: pattern + "{area:exists}/FProduct/FDetail/{id}" + UrlSuffix, new { controller = "FProduct", action = "FDetail", area = "Mobile" });

            endpoints.MapControllerRoute(name: "areaRouteFProductTui",
                pattern: pattern + "{area:exists}/FProduct/Tui/{Id}" + UrlSuffix, new { controller = "FProduct", action = "Tui", area = "Mobile" });
            endpoints.MapControllerRoute(name: "areaRouteFProductApp",
                pattern: pattern + "{area:exists}/FProduct/App/{Id}" + UrlSuffix, new { controller = "FProduct", action = "App", area = "Mobile" });

            endpoints.MapControllerRoute(name: "areaRouteApp",
            pattern: pattern + "{area:exists}/App/{id}" + UrlSuffix, new { controller = "App", action = "Index", area = "Mobile" });
        }
    }

    #endregion

    #region 属性

    /// <summary>
    /// 获取路由提供者的优先级
    /// </summary>
    public int Priority
    {
        get { return 1; }
    }

    #endregion
}
