﻿using DG.Web.Framework;


using HlktechSite.DTO;

using Microsoft.AspNetCore.Mvc;
using Pek.Seo;
using System.Dynamic;

namespace HlktechSite.Controllers;

/// <summary>
/// 联系我们控制器
/// </summary>
public class ContactController : DGBaseControllerX
{
    /// <summary>
    /// 联系我们首页
    /// </summary>
    /// <returns></returns>
    [DHSitemap(IsUse = true, SType = SiteMap.其他)]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("联系我们"), IsLast = true });
        viewModel.Locations = navigations;
        return DGView(viewModel,true);
    }
}
