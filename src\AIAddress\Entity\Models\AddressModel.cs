﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace AIAddress.Entity;

/// <summary>地址处理</summary>
public partial class AddressModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>业务员</summary>
    public String? Salesman { get; set; }

    /// <summary>部门名称</summary>
    public String? Department { get; set; }

    /// <summary>收货联系人</summary>
    public String? Receiving { get; set; }

    /// <summary>收货地址</summary>
    public String? Delivery { get; set; }

    /// <summary>Lat</summary>
    public Double Lat { get; set; }

    /// <summary>Lng</summary>
    public Double Lng { get; set; }

    /// <summary>Detail</summary>
    public String? Detail { get; set; }

    /// <summary>Town</summary>
    public String? Town { get; set; }

    /// <summary>PhoneNum</summary>
    public String? PhoneNum { get; set; }

    /// <summary>CityCode</summary>
    public String? CityCode { get; set; }

    /// <summary>Province</summary>
    public String? Province { get; set; }

    /// <summary>Person</summary>
    public String? Person { get; set; }

    /// <summary>ProvinceCode</summary>
    public String? ProvinceCode { get; set; }

    /// <summary>Text</summary>
    public String? Text { get; set; }

    /// <summary>County</summary>
    public String? County { get; set; }

    /// <summary>City</summary>
    public String? City { get; set; }

    /// <summary>CountyCode</summary>
    public String? CountyCode { get; set; }

    /// <summary>TownCode</summary>
    public String? TownCode { get; set; }

    /// <summary>是否请求</summary>
    public Boolean Process { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAddress model)
    {
        Id = model.Id;
        Salesman = model.Salesman;
        Department = model.Department;
        Receiving = model.Receiving;
        Delivery = model.Delivery;
        Lat = model.Lat;
        Lng = model.Lng;
        Detail = model.Detail;
        Town = model.Town;
        PhoneNum = model.PhoneNum;
        CityCode = model.CityCode;
        Province = model.Province;
        Person = model.Person;
        ProvinceCode = model.ProvinceCode;
        Text = model.Text;
        County = model.County;
        City = model.City;
        CountyCode = model.CountyCode;
        TownCode = model.TownCode;
        Process = model.Process;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
