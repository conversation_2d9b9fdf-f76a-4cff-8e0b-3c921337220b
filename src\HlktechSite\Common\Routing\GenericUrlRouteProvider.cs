﻿using DG.Utils.Infrastructure;
using DG.Web.Framework.Routing;

using DH;
using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;

namespace HlktechSite.Common {
    /// <summary>
    /// 代表提供一般路由的供应商
    /// </summary>
    public partial class GenericUrlRouteProvider : BaseRouteProvider, IRouteProvider
    {
        #region 方法

        /// <summary>
        /// 注册路由
        /// </summary>
        /// <param name="endpoints">路由构造器</param>
        public void RegisterRoutes(IEndpointRouteBuilder endpoints)
        {
            var lang = GetLanguageRoutePattern();

            // 默认路由
            endpoints.MapControllerRoute(
                    "Default",
                    "{controller=CubeHome}/{action=Index}/{id?}"
                    );

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable) // 是否启用多语言
            {
                endpoints.MapControllerRoute(
                    "MuliDefault",
                    lang + "/{controller=CubeHome}/{action=Index}/{id?}"
                    );
            }

            endpoints.MapControllerRoute(
                    name: "api",
                    pattern: "{controller}/{id?}");

            if (!DHSetting.Current.IsInstalled) return;

            var genericPattern = $"{lang}/{{SeName}}";
            var pattern = $"{lang}/";

            endpoints.MapDynamicControllerRoute<SlugRouteTransformer>(genericPattern);            

            // 通用网址
            endpoints.MapControllerRoute(
                name: "GenericUrl",
                pattern: "{GenericSeName}",
                new { controller = "Common", action = "GenericUrl" });
            if (localizationSettings.IsEnable) // 是否启用多语言
            {
                endpoints.MapControllerRoute(
                name: "GenericUrl",
                pattern: pattern + "{GenericSeName}",
                new { controller = "Common", action = "GenericUrl" });
            }

            endpoints.MapControllerRoute("Product", genericPattern, new { controller = "UserAgreement", action = "Index" });
        }

        #endregion

        #region 属性

        /// <summary>
        /// 获取路由提供者的优先级
        /// </summary>
        public int Priority
        {
            // 它应该是最后一条路由。 我们没有将其设置为-int.MaxValue，因此可以将其覆盖（如果需要）
            get { return -999999; }
        }

        #endregion
    }
}
