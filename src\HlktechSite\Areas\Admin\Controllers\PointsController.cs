﻿using System.ComponentModel;

using DG.Cube.BaseControllers;

using DH.Entity;
using DH.Models;

using Microsoft.AspNetCore.Mvc;

using Pek.Models;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>积分管理</summary>
[DisplayName("积分管理")]
[Description("用于设置积分管理")]
[AdminArea]
[DHMenu(50,ParentMenuName = "Members", CurrentMenuUrl = "~/{area}/Points", CurrentMenuName = "Pointssetting", CurrentIcon = "&#xe6f5;", LastUpdate = "20240125")]
public class PointsController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 50;

    /// <summary>
    /// 积分规则设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("积分规则设置")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 积分规则设置
    /// </summary>
    /// <param name="points_reg">会员注册</param>
    /// <param name="points_login">会员每天登录</param>
    /// <param name="points_comments">订单商品评论</param>
    /// <param name="points_signin">签到</param>
    /// <param name="points_invite">邀请注册</param>
    /// <param name="points_rebate">返利比例</param>
    /// <param name="points_orderrate">消费额与赠送积分比例</param>
    /// <param name="points_ordermax">每订单最多赠送积分</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("积分规则设置")]
    public IActionResult Index(Int32 points_reg, Int32 points_login, Int32 points_comments, Int32 points_signin, Int32 points_invite, Int32 points_rebate, Int32 points_orderrate, Int32 points_ordermax)
    {
        var model = SiteSettingInfo.SiteSettings;
        model.PointsReg = points_reg;
        model.PointsLogin = points_login;
        model.PointsComments = points_comments;
        model.PointsSignin = points_signin;
        model.PointsInvite = points_invite;
        model.PointsRebate = points_rebate;
        model.PointsOrderrate = points_orderrate;
        model.PointsOrdermax = points_ordermax;
        SiteSettingInfo.SaveChanges();

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
    }
}