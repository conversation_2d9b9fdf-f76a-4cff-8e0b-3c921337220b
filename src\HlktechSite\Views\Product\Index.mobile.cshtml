﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    if (Model.CId == 0)
    {
        Html.AppendTitleParts(T("产品中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        NewLife.Log.XTrace.WriteLine("直接输出实体");
        var modelProductCategory = Model.Model as ProductCategory;
        Html.AppendTitleParts(modelProductCategory.Name + DG.Setting.Current.PageTitleSeparator + T("产品中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }

    var cdn = CDN.GetCDN();
}

<div class="top">
    <img src="@(cdn)/images/cpzt2.jpg" />
    <h2>@T("产品中心")</h2>
    <P>
        @T("缩短产品上市时间提高生产效率和灵活性")
    </P>
</div>


<div class="input-group seach-div">
    <input class="form-control" type="text" id="key" name="key" value="@Model.Key" />
    <a href="javascript:;" onclick="seach()">@T("搜索")</a>
</div>

<div class="pro-nav">
    <a class="@((Model.CId==0) ? "selected productType" : "productType")" href="@Url.RouteUrl("Product")">@T("全部")</a>
    @foreach (var item in (Model.ProTypelist as IEnumerable<ProductCategory>))
    {
        <a class="@(Model.CId == item.Id ? "selected productType" : "productType")" id="@item.Id" href="@Url.DGAction("List", "Product", new { CId = item.Id})">@item.Name</a>
    }
</div>

<div class="second" CID="@Model.CId" childId="@Model.ChildId">
    <ul>
    </ul>
</div>

<ul class="rows-list">
    @foreach (var item in Model.list as IEnumerable<Goods>)
    {
        <li><a href="@Url.DGAction("Details","Product",new {Id =item.Id})"><img src="@item.Image" /><b title="@item.Name">@item.Name</b><i>@item.AdvWord</i></a></li>
    }
</ul>

<div class="paging" style="text-align: center;">
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>


<script asp-location="Footer">
    var cid = @Model.CId;
    
    $("#key").keydown(function (e) {
        if (e.which == 13) {
            if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#key").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { CId = Model.CId})?key=" + $("#key").val();
        }
        }
    });

    function seach(){
        if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#key").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { CId = Model.CId})?key=" + $("#key").val();
        }
    }
    $(function () {
        if ($(".pro-nav>div").length == 0) {
            $(".pro-nav").height(27);
        }
        $(".productType").click(function () {
            $(".pro-nav div").hide();
            $(".pro-nav a").removeClass("selected");
            $(this).addClass("selected");
            $(this).next().show();
            if ($(this).next().find("li").length == 0) {
                window.location.href = "@Url.DGAction("Index")?CId=" + $(this).attr("id");
            }
        });
        if ($(".second").attr("CID")) {
            refresh($(".second").attr("CID"), $(".second").attr("childId"));
        }
        //$(".pro-nav a").click(function () {
        //    refresh($(this).attr("id"));
        //})
    })

    function refresh(CID, ChildId) {
        console.log(ChildId);
        $.post("@Url.DGAction("GetSecond")", { CID: CID }, (res) => {
            if (res.success) {
                var h = "";
                for (var i = 0; i < res.data.length; i++) {
                    h += `<li><a class="${(ChildId == res.data[i].Id?"selected":"")}" href="@(Url.DGAction("Index","Product"))?1=1&CId=${res.data[i].Id}">${res.data[i].Name}</a></li>`;
                }
                $(".second ul").html(h);
                $(".second ul li").length > 0 ? $(".second").show() : $(".second").hide();
            }
        })
    }

</script>