﻿@{
    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .page {
        min-height: 415px
    }
    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #419DFD !important;
    }

        .layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #419DFD !important;
        }

    .layui-tab-content {
        padding: 0px !important;
    }
</style>
<div class="page">

    <form id="navigation_form" method="post">

        <div class="layui-tab layui-tab-brief  Lan" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td class="required w120"><label class="validation" for="nav_title">标题</label></td>
                                <td><input type="text" name="nav_title" id="nav_title" value="" class="w200" /></td>
                            </tr>
                            <tr class="noborder">
                                <td>导航链接</td>
                                <td><input type="text" name="nav_url" id="nav_url" value="" class="w200" /></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {

                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td class="required w120"><label class="" for="nav_title">标题</label></td>
                                        <td><input type="text" name="[@item.Id].navtitle" value="" class="w200" /></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td>导航链接</td>
                                        <td><input type="text" name="[@item.Id].navurl" value="" class="w200" /></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>
        <table class="ds-default-table">
            <tbody>
                @*<tr class="noborder">
                    <td class="required w120"><label class="validation" for="nav_title">标题</label></td>
                    <td><input type="text" name="nav_title" id="nav_title" value="" class="w200" /></td>
                </tr>
                <tr class="noborder">
                    <td>导航链接</td>
                    <td><input type="text" name="nav_url" id="nav_url" value="" class="w200" /></td>
                </tr>*@
                <tr class="noborder">
                    <td class="required w120">导航位置</td>
                    <td>
                        <label class="radio-label">
                            <i class="radio-common ">
                                <input type="radio" value="header" name="nav_location">
                            </i>
                            <span>头部</span>
                        </label>
                        <label class="radio-label">
                            <i class="radio-common ">
                                <input type="radio" value="middle" name="nav_location">
                            </i>
                            <span>中部</span>
                        </label>
                        <label class="radio-label">
                            <i class="radio-common selected">
                                <input type="radio" value="footer" name="nav_location" checked="checked">
                            </i>
                            <span>底部</span>
                        </label>
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">新窗口打开</td>
                    <td>
                        <div class="onoff">
                            <label for="sms_login_show1" class="cb-enable selected">是</label>
                            <label for="sms_login_show0" class="cb-disable ">否</label>
                            <input id="sms_login_show1" name="nav_new_open" value="1" type="radio" checked="checked">
                            <input id="sms_login_show0" name="nav_new_open" value="0" type="radio">
                        </div>
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">排序</td>
                    <td><input type="text" name="nav_sort" id="nav_sort" value="@ViewBag.Sort" class="w200" /></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="提交" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>

<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script  asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;

    })
</script>
<script type="text/javascript" asp-location="Footer">
    $(function () {
        $('#navigation_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                nav_title: {
                    required: true
                },
                nav_url: {
                    url: true
                },
                nav_sort: {
                    number: true,
                    range: [0, 255]
                }
            },
            messages: {
                nav_title: {
                    required: '必填'
                },
                nav_url: {
                    url: '请输入有效的链接'
                },
                nav_sort: {
                    number: '请输入数字',
                    range: '请输入0-255之间的数'
                }
            }
        });

    });

</script>