﻿@using Microsoft.AspNetCore.Html
@using NewLife.Log
@using Pek.Helpers
@{
    var SiteSettings = SiteSettingInfo.SiteSettings;

    var pages = new PageParameter()
            {
                PageIndex = 1,
                PageSize = 6
            };

    var localizationSettings = LocalizationSettings.Current;

    IEnumerable<SolutionCategory> SolutionTypelist;
    if (localizationSettings.IsEnable)
        SolutionTypelist = SolutionCategoryLan.FindAllByLevel(language.Id).Select(x => new SolutionCategory
                {
                    Id = x.CId,
                    Name = x.Name.IsNullOrWhiteSpace() ? x.solutionCategory.Name : x.Name
                });
    else
        SolutionTypelist = SolutionCategory.FindAllByLevel(0);

    var FriendLinksList = FriendLinks.Searchs(-1, "", pages).Select(x => new FriendLinks { Url = x.Url, Name = localizationSettings.IsEnable ? FriendLinksLan.FindByFIdAndLId(x.Id, language.Id)?.Name : x.Name });

    var ProCList = ProductCategory.FindAllByLevel(0);

    var returnUrl = WebHelper2.GetRawUrlStr(Context.Request);
    var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

    var cdn = CDN.GetCDN();

    var Domain = DHWeb.GetSiteUrl();
    XTrace.WriteLine("当前域名：" + Domain);
    IHtmlContent icp;
    if (Domain.Contains("hlktech.cn"))
    {
        // if (language.UniqueSeoCode == "cn")
        // {
            
        // }
        // else
        // {
        //     icp = Html.Raw("");
        // }
        icp = Html.Raw("：<a style=\"color: inherit!important;\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\">粤ICP备19157503号-7</a>");
    }
    else
    {
        icp = Html.Raw(T("：粤ICP备19157503号 - 2").Value);
    }
}
<div class="tck" id="tck1" style="display: none;">
    <div class="tckgf">
        <input type="text" class="form-control" id="usr" placeholder="@T("请输入关键字")" style="float: left;width: 360px;height: 40px;background-color: #ffffff;border-radius:10px 0 0 10px;border:2px solid #4e6ef2;float: left;">
        <div class="fdjk">
            <img src="@(cdn)/images/fdj.png" class="fdj">
        </div>
        <div class="yyhz">
            <a class="yyhzchange" href="javascript:;"><img src="@(cdn)/images/yy.png" class="dq"></a>
            <dl>
                @foreach (var lang in Languagelist)
                {
                        <dd><a href='@Url.RouteUrl("ChangeLanguage", new { langid = lang.Id, returnUrl })' title="@lang.Name">@lang.DisplayName</a></dd>
                }
            </dl>
            <a class="qhyy">language</a>
        </div>
    </div>
    <div class="tckgf2">
        <div class="goumaiguifan">
            <a href="javascript:;" class="tckwz">@T("快速购买")</a>
        </div>
        <div style="display: inline-block;flex: 1; ">
            @if (language.UniqueSeoCode == "cn")
            {
                    <div class="goumaiguifan">
                        <img src="@(cdn)/images/gfsc.png" class="gfsc">
                        <a href="@T("商城链接")" class="tckwz" target="_blank" style="margin-left: 4px;">@T("官方商城")</a>
                    </div>
                //<div class="goumaiguifan">
                //    <img src="@(cdn)/images/tmsc.png" class="gfsc">
                //    <a href="//hilink.tmall.com/" target="_blank" class="tckwz" style="margin-left: 4px;">@T("天猫商城")</a>
                //</div>
                    <div class="goumaiguifan">
                        <img src="@(cdn)/images/tbsc.png" class="gfsc">
                        <a href="//hi-link.taobao.com/" target="_blank" class="tckwz" style="margin-left: 4px;">@T("淘宝商城")1</a>
                    </div>
                //<div class="goumaiguifan">
                //    <img src="@(cdn)/images/tbsc.png" class="gfsc">
                //    <a href="//hlktech.taobao.com/" target="_blank" class="tckwz" style="margin-left: 4px;">@T("淘宝商城")2</a>
                //</div>
                //<div class="goumaiguifan">
                //    <img src="@(cdn)/images/tbsc.png" class="gfsc">
                //    <a href="//shop57596328.taobao.com/" target="_blank" class="tckwz" style="margin-left: 4px;">@T("淘宝商城")3</a>
                //</div>
            }
            else
            {
                    <div class="goumaiguifan">
                        <img src="@(cdn)images/aliexpress.png" class="gfsc" style="width: 30px;">
                        <a href="https://www.aliexpress.com/store/911797719" target="_blank" class="tckwz" style="margin-left: 4px;">Aliexpress</a>
                    </div>
            }
        </div>
    </div>
    <div class="ct"></div>
    <div class="tckgf3">
        <div class="goumaiguifan">
            <a href="javascript:;" class="tckwz">@T("新闻中心")</a>
        </div>
        <div style="display: inline-block;flex: 1; ">
            <div class="goumaiguifan">
                <a href="@Url.DGAction("Index", "Solution")" class="tckwz">@T("解决方案")</a>
            </div>
            <div class="goumaiguifan">
                <a href="@Url.DGAction("Index","Case")" class="tckwz">@T("客户案例")</a>
            </div>
            <div class="goumaiguifan">
                <a href="//ask.hlktech.com" target="_blank" class="tckwz">@T("技术支持中心")</a>
            </div>
            <div class="goumaiguifan">
                <a href="@Url.DGAction("AboutUs","CubeHome")" class="tckwz" style="margin-left: -7px;">@T("关于我们1")</a>
            </div>
        </div>
    </div>
    <div class="ct">
    </div>
    <div class="tckgf3 product_center">
        <div class="goumaiguifan">
            <a href="javascript:;" class="tckwz">@T("产品中心")</a>
        </div>
        <div style="display: inline-block;flex: 1; ">
            @foreach (var item in ProCList)
            {
                var modellan = ProductCategoryLan.FindByCIdAndLId(item.Id, language.Id, true);
                    <div class="goumaiguifan">
                        <a href="@Url.DGAction("Index","Product",new {CId=item.Id })" class="tckwz">@modellan</a>
                    </div>
            }
        </div>
    </div>
</div>
<!-- 底部 -->
<div class="footer-box">
    <div class="footer-container">
        <div class="footer-service clearfix" style="display:flex;">
            <div class="footer-service-item footer-icon-001" style="flex:1;">
                <a href="@Url.DGAction("Index","CubeHome")">
                    <img src="@(cdn)/images/logo.png" style="float:left;" class="yeweilogo">
                </a>
                <span style="float: left;margin-left: 12px;" class="lianjie">@T("深圳市海凌科电子有限公司1")</span>
            </div>
            <div class="footer-service-item footer-icon-003" style="flex:1;text-align:right;float:none;line-height: 36px;">
                <a href="//ask.hlktech.com" target="_blank" class="lianjie" style="margin-right:43px;">@T("技术支持")</a>
                <a href="//h.hlktech.com/mobile/download" target="_blank" class="lianjie">@T("资料下载")</a>
            </div>
        </div>
        <div class="footer-line"></div>
        <div class="footer-links clearfix">
            @T("底部左下角联系方式")
            <div class="xian">
            </div>
            @T("关于我们")

            @T("产品服务")
            <dl class="footer-article-item" style="padding-left: 30px;">
                <dt>@T("合作伙伴")</dt>

                @foreach (var item in FriendLinksList)
                {
                        <dd>
                            <a href="@item.Url" target="_blank">@item.Name</a>
                        </dd>
                }
            </dl>
            <dl class="footer-article-item" style="padding-left: 30px;">
                <dt>@T("解决方案")</dt>
                @foreach (var item in SolutionTypelist)
                {
                        <dd>
                            <a href="@Url.DGAction("List", "Solution", new { CId = item.Id})">@item.Name</a>
                        @*<a href="@Url.RouteUrl("Index", "Solution", new { CID=item.Id})">@item.Name</a>*@
                        </dd>
                }
            </dl>
            <dl class="footer-contact-item">

                <div class="footer-ewm">
                    <img style="width:113px;height:113px" src="@(cdn)/@SiteSettings.SiteLogoWx" alt="">
                    <p>@T("关注微信订阅号")</p>
                </div>
            </dl>
        </div>
        <p>@T("联系电话")：0755-23152658 @T("版权所有：深圳海凌科电子科技有限公司") @T("备案号")@(icp) @T("	底部技术支持")</p>
        <p style="display: none;">@Html.Raw(DG.Setting.Current.Statistical)</p>
    </div>
</div>

<div class="backToTop" style="position: fixed; right: 30px; bottom: 80px; height: 50px; cursor: pointer; border-radius: 40px;z-index: 100; background-color: #fff; opacity: 0; width: 50px; text-align: center; box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.40); -webkit-box-shadow: -1px 5px 17px -8px rgba(0,0,0,0.40); -moz-box-shadow: -1px 5px 17px -8px">
    <img src="@(cdn)/images/rocket.png" style=" width: 40px; margin-top: 5px; display: inline-block;">
</div>

@await Component.InvokeAsync("KF", new { LId = language.Id })

<div class="show-fixed-bar">
    <img src="@(cdn)/images/show-fixed-bar.png" alt="Alternate Text" />
</div>

<script asp-location="Footer">
    $(function () {
        $("#stg1").click(function () {
            $("#tck1").toggle();
        });

        $(document).mouseup(function (e) {
            var _con = $('#tck1');
            var _con2 = $('#stg1');
            if (!_con.is(e.target) && _con.has(e.target).length === 0 && !_con2.is(e.target) && _con2.has(e.target).length === 0) {
                $("#tck1").hide();
                $(".yyhzchange").removeClass("IsGray");
                $(".yyhzchange").parent().find("dl").slideUp(100);
            }
            var con3 = $(".yyhzchange,.qhyy");
            var con4 = $(".yyhzchange").parent().find("dl")
            if (!con3.is(e.target) && con3.has(e.target).length === 0 && !con4.is(e.target) && con4.has(e.target).length === 0) {
                $(".yyhzchange").removeClass("IsGray");
                $(".yyhzchange").parent().find("dl").slideUp(100);
            }

        });

        $(".backToTop").click(function () {
            $('html,body').animate({ scrollTop: '0px' }, 500);
        });
        $(window).scroll(function () {
            if ($(document).scrollTop() > 0) {
                $(".backToTop").css("opacity", "1");
                $(".backToTop").css("display", "block");
            } else {
                $(".backToTop").fadeOut(500);
            }
        });

        $(".yyhzchange,.qhyy").click(function () {
            if ($(".yyhzchange").hasClass("IsGray")) {
                $(".yyhzchange").removeClass("IsGray");
            }
            else {
                $(".yyhzchange").addClass("IsGray");
            }
            $(".yyhzchange").parent().find("dl").slideToggle(100);
        });

        $(".fdjk").click(() => {
            window.location.href = "@Url.DGAction("Index", "Search")" + ($("#usr").val()?"?Key_Word=" + $("#usr").val():"");
        });
        $("#usr").keyup(function (e) {
            if (e.keyCode == 13) {
                $(".fdjk").click();
            }
        })
    })
</script>