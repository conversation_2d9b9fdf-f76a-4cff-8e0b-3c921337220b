﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>页面导航</summary>
public partial interface INavigation
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>页面导航标题</summary>
    String? Name { get; set; }

    /// <summary>页面导航链接</summary>
    String? Url { get; set; }

    /// <summary>页面导航位置。header头部，middle中部，footer底部</summary>
    String? Location { get; set; }

    /// <summary>是否以新窗口打开</summary>
    Boolean NewOpen { get; set; }

    /// <summary>页面导航排序</summary>
    Int32 Sort { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
