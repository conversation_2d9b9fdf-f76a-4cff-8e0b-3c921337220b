﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>成品表</summary>
[Serializable]
[DataObject]
[Description("成品表")]
[BindIndex("IX_DG_EndProduct_CId", false, "CId")]
[BindIndex("IX_DG_EndProduct_CId_1", false, "CId_1")]
[BindIndex("IX_DG_EndProduct_CId_2", false, "CId_2")]
[BindIndex("IX_DG_EndProduct_CId_3", false, "CId_3")]
[BindIndex("IX_DG_EndProduct_MId", false, "MId")]
[BindTable("DG_EndProduct", Description = "成品表", ConnName = "DG", DbType = DatabaseType.None)]
public partial class EndProducts : IEndProducts, IEntity<IEndProducts>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>成品名称+规格名称</summary>
    [DisplayName("成品名称+规格名称")]
    [Description("成品名称+规格名称")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "成品名称+规格名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _AdvWord;
    /// <summary>成品广告词</summary>
    [DisplayName("成品广告词")]
    [Description("成品广告词")]
    [DataObjectField(false, false, true, 150)]
    [BindColumn("AdvWord", "成品广告词", "")]
    public String? AdvWord { get => _AdvWord; set { if (OnPropertyChanging("AdvWord", value)) { _AdvWord = value; OnPropertyChanged("AdvWord"); } } }

    private String? _MId;
    /// <summary>产品型号Id集合，以逗号区分</summary>
    [DisplayName("产品型号Id集合")]
    [Description("产品型号Id集合，以逗号区分")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("MId", "产品型号Id集合，以逗号区分", "")]
    public String? MId { get => _MId; set { if (OnPropertyChanging("MId", value)) { _MId = value; OnPropertyChanged("MId"); } } }

    private Int32 _CId;
    /// <summary>成品分类ID</summary>
    [DisplayName("成品分类ID")]
    [Description("成品分类ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId", "成品分类ID", "")]
    public Int32 CId { get => _CId; set { if (OnPropertyChanging("CId", value)) { _CId = value; OnPropertyChanged("CId"); } } }

    private Int32 _Cid1;
    /// <summary>一级分类ID</summary>
    [DisplayName("一级分类ID")]
    [Description("一级分类ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId_1", "一级分类ID", "")]
    public Int32 Cid1 { get => _Cid1; set { if (OnPropertyChanging("Cid1", value)) { _Cid1 = value; OnPropertyChanged("Cid1"); } } }

    private Int32 _Cid2;
    /// <summary>二级分类ID</summary>
    [DisplayName("二级分类ID")]
    [Description("二级分类ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId_2", "二级分类ID", "")]
    public Int32 Cid2 { get => _Cid2; set { if (OnPropertyChanging("Cid2", value)) { _Cid2 = value; OnPropertyChanged("Cid2"); } } }

    private Int32 _Cid3;
    /// <summary>三级分类ID</summary>
    [DisplayName("三级分类ID")]
    [Description("三级分类ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId_3", "三级分类ID", "")]
    public Int32 Cid3 { get => _Cid3; set { if (OnPropertyChanging("Cid3", value)) { _Cid3 = value; OnPropertyChanged("Cid3"); } } }

    private String? _Image;
    /// <summary>成品主图</summary>
    [DisplayName("成品主图")]
    [Description("成品主图")]
    [DataObjectField(false, false, true, 150)]
    [BindColumn("Image", "成品主图", "")]
    public String? Image { get => _Image; set { if (OnPropertyChanging("Image", value)) { _Image = value; OnPropertyChanged("Image"); } } }

    private String? _Content;
    /// <summary>成品内容</summary>
    [DisplayName("成品内容")]
    [Description("成品内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "成品内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _MobileContent;
    /// <summary>手机端成品描述</summary>
    [DisplayName("手机端成品描述")]
    [Description("手机端成品描述")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("MobileContent", "手机端成品描述", "text")]
    public String? MobileContent { get => _MobileContent; set { if (OnPropertyChanging("MobileContent", value)) { _MobileContent = value; OnPropertyChanged("MobileContent"); } } }

    private String? _Summary;
    /// <summary>简介</summary>
    [DisplayName("简介")]
    [Description("简介")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Summary", "简介", "")]
    public String? Summary { get => _Summary; set { if (OnPropertyChanging("Summary", value)) { _Summary = value; OnPropertyChanged("Summary"); } } }

    private String? _UsageScenarios;
    /// <summary>使用场景</summary>
    [DisplayName("使用场景")]
    [Description("使用场景")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("UsageScenarios", "使用场景", "")]
    public String? UsageScenarios { get => _UsageScenarios; set { if (OnPropertyChanging("UsageScenarios", value)) { _UsageScenarios = value; OnPropertyChanged("UsageScenarios"); } } }

    private String? _Specifications;
    /// <summary>参数规格</summary>
    [DisplayName("参数规格")]
    [Description("参数规格")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Specifications", "参数规格", "text")]
    public String? Specifications { get => _Specifications; set { if (OnPropertyChanging("Specifications", value)) { _Specifications = value; OnPropertyChanged("Specifications"); } } }

    private Int32 _Clicks;
    /// <summary>成品点击数量</summary>
    [DisplayName("成品点击数量")]
    [Description("成品点击数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Clicks", "成品点击数量", "")]
    public Int32 Clicks { get => _Clicks; set { if (OnPropertyChanging("Clicks", value)) { _Clicks = value; OnPropertyChanged("Clicks"); } } }

    private Boolean _Commend;
    /// <summary>成品推荐</summary>
    [DisplayName("成品推荐")]
    [Description("成品推荐")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Commend", "成品推荐", "")]
    public Boolean Commend { get => _Commend; set { if (OnPropertyChanging("Commend", value)) { _Commend = value; OnPropertyChanged("Commend"); } } }

    private Boolean _Shelf;
    /// <summary>成品是否上架</summary>
    [DisplayName("成品是否上架")]
    [Description("成品是否上架")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Shelf", "成品是否上架", "")]
    public Boolean Shelf { get => _Shelf; set { if (OnPropertyChanging("Shelf", value)) { _Shelf = value; OnPropertyChanged("Shelf"); } } }

    private Int32 _Sort;
    /// <summary>排序</summary>
    [DisplayName("排序")]
    [Description("排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Sort", "排序", "")]
    public Int32 Sort { get => _Sort; set { if (OnPropertyChanging("Sort", value)) { _Sort = value; OnPropertyChanged("Sort"); } } }

    private String? _PcGouUrl;
    /// <summary>PC购买Url</summary>
    [DisplayName("PC购买Url")]
    [Description("PC购买Url")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("PcGouUrl", "PC购买Url", "")]
    public String? PcGouUrl { get => _PcGouUrl; set { if (OnPropertyChanging("PcGouUrl", value)) { _PcGouUrl = value; OnPropertyChanged("PcGouUrl"); } } }

    private String? _MobileGouUrl;
    /// <summary>移动购买Url</summary>
    [DisplayName("移动购买Url")]
    [Description("移动购买Url")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("MobileGouUrl", "移动购买Url", "")]
    public String? MobileGouUrl { get => _MobileGouUrl; set { if (OnPropertyChanging("MobileGouUrl", value)) { _MobileGouUrl = value; OnPropertyChanged("MobileGouUrl"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IEndProducts model)
    {
        Id = model.Id;
        Name = model.Name;
        AdvWord = model.AdvWord;
        MId = model.MId;
        CId = model.CId;
        Cid1 = model.Cid1;
        Cid2 = model.Cid2;
        Cid3 = model.Cid3;
        Image = model.Image;
        Content = model.Content;
        MobileContent = model.MobileContent;
        Summary = model.Summary;
        UsageScenarios = model.UsageScenarios;
        Specifications = model.Specifications;
        Clicks = model.Clicks;
        Commend = model.Commend;
        Shelf = model.Shelf;
        Sort = model.Sort;
        PcGouUrl = model.PcGouUrl;
        MobileGouUrl = model.MobileGouUrl;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "AdvWord" => _AdvWord,
            "MId" => _MId,
            "CId" => _CId,
            "Cid1" => _Cid1,
            "Cid2" => _Cid2,
            "Cid3" => _Cid3,
            "Image" => _Image,
            "Content" => _Content,
            "MobileContent" => _MobileContent,
            "Summary" => _Summary,
            "UsageScenarios" => _UsageScenarios,
            "Specifications" => _Specifications,
            "Clicks" => _Clicks,
            "Commend" => _Commend,
            "Shelf" => _Shelf,
            "Sort" => _Sort,
            "PcGouUrl" => _PcGouUrl,
            "MobileGouUrl" => _MobileGouUrl,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "AdvWord": _AdvWord = Convert.ToString(value); break;
                case "MId": _MId = Convert.ToString(value); break;
                case "CId": _CId = value.ToInt(); break;
                case "Cid1": _Cid1 = value.ToInt(); break;
                case "Cid2": _Cid2 = value.ToInt(); break;
                case "Cid3": _Cid3 = value.ToInt(); break;
                case "Image": _Image = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "MobileContent": _MobileContent = Convert.ToString(value); break;
                case "Summary": _Summary = Convert.ToString(value); break;
                case "UsageScenarios": _UsageScenarios = Convert.ToString(value); break;
                case "Specifications": _Specifications = Convert.ToString(value); break;
                case "Clicks": _Clicks = value.ToInt(); break;
                case "Commend": _Commend = value.ToBoolean(); break;
                case "Shelf": _Shelf = value.ToBoolean(); break;
                case "Sort": _Sort = value.ToInt(); break;
                case "PcGouUrl": _PcGouUrl = Convert.ToString(value); break;
                case "MobileGouUrl": _MobileGouUrl = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static EndProducts? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据产品型号Id集合查找</summary>
    /// <param name="mId">产品型号Id集合</param>
    /// <returns>实体列表</returns>
    public static IList<EndProducts> FindAllByMId(String? mId)
    {
        if (mId == null) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.MId.EqualIgnoreCase(mId));

        return FindAll(_.MId == mId);
    }
    #endregion

    #region 字段名
    /// <summary>取得成品表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>成品名称+规格名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>成品广告词</summary>
        public static readonly Field AdvWord = FindByName("AdvWord");

        /// <summary>产品型号Id集合，以逗号区分</summary>
        public static readonly Field MId = FindByName("MId");

        /// <summary>成品分类ID</summary>
        public static readonly Field CId = FindByName("CId");

        /// <summary>一级分类ID</summary>
        public static readonly Field Cid1 = FindByName("Cid1");

        /// <summary>二级分类ID</summary>
        public static readonly Field Cid2 = FindByName("Cid2");

        /// <summary>三级分类ID</summary>
        public static readonly Field Cid3 = FindByName("Cid3");

        /// <summary>成品主图</summary>
        public static readonly Field Image = FindByName("Image");

        /// <summary>成品内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>手机端成品描述</summary>
        public static readonly Field MobileContent = FindByName("MobileContent");

        /// <summary>简介</summary>
        public static readonly Field Summary = FindByName("Summary");

        /// <summary>使用场景</summary>
        public static readonly Field UsageScenarios = FindByName("UsageScenarios");

        /// <summary>参数规格</summary>
        public static readonly Field Specifications = FindByName("Specifications");

        /// <summary>成品点击数量</summary>
        public static readonly Field Clicks = FindByName("Clicks");

        /// <summary>成品推荐</summary>
        public static readonly Field Commend = FindByName("Commend");

        /// <summary>成品是否上架</summary>
        public static readonly Field Shelf = FindByName("Shelf");

        /// <summary>排序</summary>
        public static readonly Field Sort = FindByName("Sort");

        /// <summary>PC购买Url</summary>
        public static readonly Field PcGouUrl = FindByName("PcGouUrl");

        /// <summary>移动购买Url</summary>
        public static readonly Field MobileGouUrl = FindByName("MobileGouUrl");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得成品表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>成品名称+规格名称</summary>
        public const String Name = "Name";

        /// <summary>成品广告词</summary>
        public const String AdvWord = "AdvWord";

        /// <summary>产品型号Id集合，以逗号区分</summary>
        public const String MId = "MId";

        /// <summary>成品分类ID</summary>
        public const String CId = "CId";

        /// <summary>一级分类ID</summary>
        public const String Cid1 = "Cid1";

        /// <summary>二级分类ID</summary>
        public const String Cid2 = "Cid2";

        /// <summary>三级分类ID</summary>
        public const String Cid3 = "Cid3";

        /// <summary>成品主图</summary>
        public const String Image = "Image";

        /// <summary>成品内容</summary>
        public const String Content = "Content";

        /// <summary>手机端成品描述</summary>
        public const String MobileContent = "MobileContent";

        /// <summary>简介</summary>
        public const String Summary = "Summary";

        /// <summary>使用场景</summary>
        public const String UsageScenarios = "UsageScenarios";

        /// <summary>参数规格</summary>
        public const String Specifications = "Specifications";

        /// <summary>成品点击数量</summary>
        public const String Clicks = "Clicks";

        /// <summary>成品推荐</summary>
        public const String Commend = "Commend";

        /// <summary>成品是否上架</summary>
        public const String Shelf = "Shelf";

        /// <summary>排序</summary>
        public const String Sort = "Sort";

        /// <summary>PC购买Url</summary>
        public const String PcGouUrl = "PcGouUrl";

        /// <summary>移动购买Url</summary>
        public const String MobileGouUrl = "MobileGouUrl";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
