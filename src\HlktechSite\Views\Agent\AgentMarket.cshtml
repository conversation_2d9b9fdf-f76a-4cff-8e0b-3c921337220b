﻿@model SingleArticle
@{
    Layout = "~/Views/Shared/_Root.cshtml";

    Html.AppendCssFileParts("~/css/agent.css");

    Html.AppendTitleParts(T("销售网络").Text + DG.Setting.Current.PageTitleSeparator + T("代理招商").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}

<div class="agent-top">
    <img src="@(CDN.GetCDN())/images/Agent.png" />
    <div>
        <h2>@Model.Name</h2>
        <P>@T("海凌科电子在全国进行招商代理是为了充分整合代理商的资源优势")</P>
    </div>
</div>

<div class="navigation-div">
    <p class="navigation">
        @await Component.InvokeAsync("Location", new { model = ViewBag.Locations })
    </p>
</div>


<div class="agent-con">
    <img src="@(CDN.GetCDN())/images/map.png" />

    @Html.Raw(Model.Content)

</div>
