﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>友情链接翻译</summary>
public partial class FriendLinksLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>友情链接Id</summary>
    public Int32 FId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>友情链接标题</summary>
    public String? Name { get; set; }

    /// <summary>类型。0为文字，1为图片。冗余字段</summary>
    public Int16 FType { get; set; }

    /// <summary>友情链接地址</summary>
    public String? Url { get; set; }

    /// <summary>友情链接图片</summary>
    public String? Pic { get; set; }

    /// <summary>友情链接排序</summary>
    public Int32 Sort { get; set; }

    /// <summary>是否启用</summary>
    public Int32 Enabled { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IFriendLinksLan model)
    {
        Id = model.Id;
        FId = model.FId;
        LId = model.LId;
        Name = model.Name;
        FType = model.FType;
        Url = model.Url;
        Pic = model.Pic;
        Sort = model.Sort;
        Enabled = model.Enabled;
    }
    #endregion
}
