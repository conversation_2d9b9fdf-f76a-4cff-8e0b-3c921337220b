﻿using System.ComponentModel;
using System.Diagnostics;

using DH.Services.Jobs;

using NewLife;
using NewLife.Log;
using Pek.Systems;

namespace HlktechSite.Jobs;

/// <summary>
/// 清理内存作业参数
/// </summary>
public class FreeMemoryArgument {
    /// <summary>
    /// 内存占比
    /// </summary>
    public Double Percentage { get; set; }

    /// <summary>
    /// 当前应用已用内存
    /// </summary>
    public Double GCMemory { get; set; }
}

/// <summary>清理内存服务</summary>
[DisplayName("清理内存")]
[Description("定时判断内存占比来清理内存，Percentage为百分比的值，不带%；GCMemory单位为MB,先判断是否低于总内存剩余百分比，再判断是否当前应用内存是否超出，为0时不判断。格式如{\"Percentage\": 10,\"GCMemory\": 1000}")]
[CronJob("FreeMemory", "0 */5 * * * ? *", Enable = false)]
public class FreeMemoryService : CubeJobBase<FreeMemoryArgument> {
    private readonly ITracer _tracer;

    /// <summary>实例化测试执行SignalR定时发送消息</summary>
    /// <param name="tracer"></param>
    public FreeMemoryService(ITracer tracer) => _tracer = tracer;

    /// <summary>执行作业</summary>
    /// <param name="argument"></param>
    /// <returns></returns>
    protected override async Task<String> OnExecute(FreeMemoryArgument argument)
    {
        using var span = _tracer?.NewSpan("FreeMemory", argument);

        await MachineInfo.RegisterAsync();

        var mi = MachineInfo.Current ?? new MachineInfo();
        mi.Refresh();

        var TotalMemory = mi.Memory / 1024 / 1024;
        var AvailableMemory = mi.AvailableMemory / 1024 / 1024;

        var usedMemory = GetUsedMemory() / (1024 * 1024);
        var virtualMemory = GetVirtualMemory() / (1024 * 1024);

        var b = ((Double)AvailableMemory / TotalMemory) * 100;

        XTrace.WriteLine($"当前服务器资源  内存总量:{TotalMemory}MB  可用内存：{AvailableMemory}MB  占比：{b}%  当前应用已用内存: {usedMemory} MB  当前应用虚拟内存: {virtualMemory} MB");

        if (argument.Percentage > 0)
        {
            if (b < argument.Percentage)
            {
                XTrace.WriteLine($"当前内存占比{b}小于{argument.Percentage}，开始释放内存");
                NativeHelper.FreeMemory();
            }
            else
            {
                XTrace.WriteLine($"当前内存占比{b}大于{argument.Percentage}，不释放内存");
            }
        }

        if (argument.GCMemory > 0)
        {
            if (argument.GCMemory < usedMemory)
            {
                XTrace.WriteLine($"当前应用已用内存{usedMemory}MB大于{argument.GCMemory}MB，开始释放内存");
                NativeHelper.FreeMemory();
            }
            else
            {
                XTrace.WriteLine($"当前应用已用内存{usedMemory}MB小于{argument.GCMemory}MB，不释放内存");
            }
        }

        return "OK";
    }

    static long GetUsedMemory()
    {
        Process currentProcess = Process.GetCurrentProcess();
        return currentProcess.WorkingSet64; // 以字节为单位
    }

    static long GetVirtualMemory()
    {
        Process currentProcess = Process.GetCurrentProcess();
        return currentProcess.VirtualMemorySize64; // 以字节为单位
    }
}
