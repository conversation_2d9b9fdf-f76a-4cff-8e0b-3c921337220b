﻿@{
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
    var content = Model.Content as String;
    content = content.SafeString().ToUnicodeString();
}
@model HlktechSite.Entity.Goods
<style asp-location="true">
    .type-file-preview {
        z-index: 99999
    }

    .dssc-form-goods-pic .sidebar {
        width: auto !important;
        float: none !important;
    }

    .dssc-form-goods-pic .container {
        float: none !important;
    }

    #edui1 {
        /* width:50%!important;*/
    }

    div#goods_body {
        width: 900px !important;
    }
</style>
<link rel="stylesheet" href="/static/plugins/perfect-scrollbar.min.css">
<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<script src="~/static/admin/js/xm-select.js"></script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("修改")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("EditGoods", "Goods", FormMethod.Post, new { id = "form1", enctype = "multipart/form-data" }))
    {
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("商品名称+规格名称")</td>
                    <td class="vatop rowform"><input type="text" name="article_title" id="article_title" value="@Model.Name" class="" /></td>
                    <td></td>
                </tr>
                <tr>
                    <td class="required w120">@T("商品广告词")</td>
                    <td><textarea name="AdvWord">@Model.AdvWord</textarea></td>
                    <td></td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("商品主图:") </td>
                    <td class="vatop rowform">

                        <span class="type-file-show">
                            @if (!Model.Image.IsNullOrWhiteSpace())
                            {
                                <img class="show_image" src="/static/admin/images/preview.png">
                                <div class="type-file-preview"><img src="@Model.Image"></div>
                            }
                        </span>
                        <span class="type-file-box">
                            <input type='text' name='textfield' id='textfield4' class='type-file-text' />
                            <input type='button' id="fileupload1" name="fileupload1" value='@T("上传")' class='type-file-button' />
                            <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                        </span>
                    </td>
                    <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                </tr>
                <tr>
                    <td>@T("所属产品型号")</td>
                    <td>
                        <div id="demo1" style="width:50%"></div>
                    </td>
                    <td></td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("所属分类")</td>
                    <td class="vatop" colspan="2">
                        <div class="wrapper_search">
                            <div class="wp_sort">
                                <div id="dataLoading" class="wp_data_loading" style="display: none;">
                                    <div class="data_loading">@T("加载中")...</div>
                                </div>

                                <div id="class_div" class="wp_sort_block">
                                    <div class="sort_list">
                                        <div class="wp_category_list">
                                            <div id="class_div_1" class="category_list ps-container">
                                                <ul>
                                                    @foreach (var item in ViewBag.list1 as IList<HlktechSite.Entity.ProductCategory>)
                                                    {
                                                        <li class="" dstype="selClass" data-param="{gcid:@item.Id,deep:@item.Level+1,tid:1}"> <a class="@(item.Id == Model.Cid1?"classDivClick":"")" href="javascript:void(0)"><i class="iconfont"></i>@item.Name</a></li>
                                                    }
                                                </ul>
                                                <div class="ps-scrollbar-x-rail" style="left: 0px; bottom: 3px; width: 284px; display: none;"><div class="ps-scrollbar-x" style="left: 0px; width: 0px;"></div></div><div class="ps-scrollbar-y-rail" style="top: 0px; right: 3px; height: 264px; display: inherit;"><div class="ps-scrollbar-y" style="top: 0px; height: 217px;"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="sort_list">
                                        <div class="wp_category_list">
                                            <div id="class_div_2" class="category_list ps-container" style="">
                                                <ul>
                                                    @foreach (var item in ViewBag.list2 as IList<HlktechSite.Entity.ProductCategory>)
                                                    {
                                                        <li class="" dstype="selClass" data-param="{gcid:@item.Id,deep:@item.Level+1,tid:1}"> <a class="@(item.Id == Model.Cid2?"classDivClick":"")" href="javascript:void(0)"><i class="iconfont"></i>@item.Name</a></li>
                                                    }
                                                </ul>
                                                <div class="ps-scrollbar-x-rail" style="left: 0px; bottom: 3px; width: 15px; display: none;"><div class="ps-scrollbar-x" style="left: 0px; width: 0px;"></div></div><div class="ps-scrollbar-y-rail" style="top: 0px; right: 3px; height: 264px; display: none;"><div class="ps-scrollbar-y" style="top: 0px; height: 0px;"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="sort_list sort_list_last">
                                        <div class="wp_category_list">
                                            <div id="class_div_3" class="category_list ps-container" style="">
                                                <ul>
                                                    @foreach (var item in ViewBag.list3 as IList<HlktechSite.Entity.ProductCategory>)
                                                    {
                                                        <li class="" dstype="selClass" data-param="{gcid:@item.Id,deep:@item.Level+1,tid:1}"> <a class="@(item.Id == Model.Cid3?"classDivClick":"")" href="javascript:void(0)"><i class="iconfont"></i>@item.Name</a></li>
                                                    }
                                                </ul>
                                                <div class="ps-scrollbar-x-rail" style="left: 0px; bottom: 3px; width: 15px; display: none;"><div class="ps-scrollbar-x" style="left: 0px; width: 0px;"></div></div><div class="ps-scrollbar-y-rail" style="top: 0px; right: 3px; height: 264px; display: none;"><div class="ps-scrollbar-y" style="top: 0px; height: 0px;"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert">
                                <dl class="hover_tips_cont">
                                    <dt id="commodityspan" style="display: none;"><span style="color:#F00;">@T("请选择商品类别")</span></dt>
                                    <dt id="commoditydt" style="" class="current_sort">@T("您当前选择的商品类别是")：</dt>
                                    <dd id="commoditydd"><i class="iconfont"></i>@*DIY硬件*@</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="wp_confirm">

                            <input type="hidden" name="commonid" value="" />
                            <input type="hidden" name="class_id" id="class_id" value="@Model.CId" />
                        </div>
                    </td>
                </tr>

                <tr class="noborder">
                    <td class="required">@T("商品描述")</td>

                    <td colspan="2">
                        <dd id="dsProductDetails">
                            <div class="tabs">
                                <ul class="ui-tabs-nav">
                                    <li class="ui-tabs-selected"><a href="#panel-1"><i class="iconfont">&#xe60c;</i> @T("电脑端")</a></li>
                                    <li class="selected"><a href="#panel-2"><i class="iconfont">&#xe60e;</i>@T("手机端")</a></li>
                                </ul>
                                <div id="panel-1" class="ui-tabs-panel">
                                    <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                                    <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                                    <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                                    <script type="text/javascript">
                                        var ue = UE.getEditor('goods_body', {
                                            toolbars: [[
                                                'fullscreen', 'source', '|', 'undo', 'redo', '|',
                                                'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                                                'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                                                'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                                                'directionalityltr', 'directionalityrtl', 'indent', '|',
                                                'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
                                                'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                                                'emotion', 'map', 'gmap', 'insertcode', 'template', '|',
                                                'horizontal', 'date', 'time', 'spechars', '|',
                                                'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',
                                                'searchreplace', 'help', 'drafts', 'charts'
                                            ]],
                                        });
                                        if ("") {
                                            ue.ready(function () {
                                                this.setContent('@Html.Raw(content)');
                                            })
                                        }

                                    </script> <textarea name="goods_body" id="goods_body"></textarea>
                                    <div class="hr8">

                                        <a class="dssc-btn mt5" dstype="show_desc" href="/index.php/admin/Goodsalbum/pic_list.html?item=des"><i class="iconfont">&#xe72a;</i>T("")插入相册图片</a> <a href="javascript:void(0);" dstype="del_desc" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>T("")关闭相册</a>
                                    </div>
                                    <p id="des_demo"></p>
                                </div>
                                <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                    <div class="dssc-mobile-editor">
                                        <div class="pannel">
                                            <div class="size-tip"><span dstype="img_count_tip">图片总数得超过<em>20</em>张</span><i>|</i><span dstype="txt_count_tip">文字不得超过<em>5000</em>字</span></div>
                                            <div class="control-panel" dstype="mobile_pannel">
                                            </div>
                                            <div class="add-btn">
                                                <ul class="btn-wrap">
                                                    <li>
                                                        <a href="javascript:void(0);" dstype="mb_add_img">
                                                            <i class="iconfont">&#xe72a;</i>
                                                            <p>图片</p>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:void(0);" dstype="mb_add_txt">
                                                            <i class="iconfont">&#xe8ed;</i>
                                                            <p>文字</p>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="explain">
                                            <dl>
                                                <dt>1、基本要求：</dt>
                                                <dd>（1）手机详情总体大小：图片+文字，图片不超过20张，文字不超过5000字；</dd>
                                                <dd>建议：所有图片都是本宝贝相关的图片。</dd>
                                            </dl><dl>
                                                <dt>2、图片大小要求：</dt>
                                                <dd>（1）建议使用宽度480 ~ 620像素、高度小于等于960像素的图片；</dd>
                                                <dd>（2）格式为：JPG\JEPG\GIF\PNG；</dd>
                                                <dd>举例：可以上传一张宽度为480，高度为960像素，格式为JPG的图片。</dd>
                                            </dl><dl>
                                                <dt>3、文字要求：</dt>
                                                <dd>（1）每次插入文字不能超过500个字，标点、特殊字符按照一个字计算；</dd>
                                                <dd>（2）请手动输入文字，不要复制粘贴网页上的文字，防止出现乱码；</dd>
                                                <dd>（3）以下特殊字符&ldquo;&lt;&rdquo;、&ldquo;&gt;&rdquo;、&ldquo;&quot;&rdquo;、&ldquo;\&rdquo;会被替换为空。</dd>
                                                <dd>建议：不要添加太多的文字，这样看起来更清晰。</dd>
                                            </dl>
                                        </div>
                                    </div>
                                    <div class="dssc-mobile-edit-area" dstype="mobile_editor_area">
                                        <div dstype="mea_img" class="dssc-mea-img" style="display: none;"></div>
                                        <div class="dssc-mea-text" dstype="mea_txt" style="display: none;">
                                            <p id="meat_content_count" class="text-tip"></p>
                                            <textarea class="textarea valid" dstype="meat_content" name="mobile"></textarea>
                                            <div class="button"><a class="dssc-btn dssc-btn-blue" dstype="meat_submit" href="javascript:void(0);">确认</a><a class="dssc-btn ml10" dstype="meat_cancel" href="javascript:void(0);">取消</a></div>
                                            <a class="text-close" dstype="meat_cancel" href="javascript:void(0);">X</a>
                                        </div>
                                    </div>
                                    <input name="m_body" autocomplete="off" type="hidden" value=''>
                                </div>
                                <div class="dssc-upload-btn">
                                    <a href="javascript:void(0);">
                                        <span>
                                            <input type="file" hidefocus="true" size="1" class="input-file" name="add_album" id="add_album" multiple="multiple">
                                        </span>
                                        <p><i class="iconfont" data_type="0" dstype="add_album_i">&#xe733;</i>图片上传</p>
                                    </a>
                                </div>
                            </div>
                        </dd>
                    </td>

                    @*<td class="vatop rowform" colspan="2"><textarea name="article_content" id="article_content" style="width:100%;"></textarea></td>*@
                </tr>
                <tr>
                    <td class="required">@T("上传商品图片:")</td>
                    <td colspan="1">
                        <div class="dssc-form-goods-pic">
                            <div class="container">
                                <div class="dssc-goodspic-list">
                                    <div class="title">
                                        <h3></h3>
                                    </div>
                                    <ul dstype="ul0">
                                        <li class="dssc-goodspic-upload">
                                            <div class="upload-thumb">
                                                <img src="@(ViewBag.Img0.Url == null?"/uploads/common/default_goods_image.jpg":ViewBag.Img0.Url)" dstype="file_00">
                                                <input type="hidden" name="img[0][0][name]" value="" dstype="file_00">
                                                <input type="hidden" name="img[0][0][Id]" value="" dsId="file_00">
                                            </div>
                                            <div class="show-default @(ViewBag.Img0.IsDefault?"selected":"") " dstype="file_00">
                                                <p>
                                                    <i class="iconfont"></i>默认主图                <input type="hidden" name="img[0][0][default]" value="1">
                                                </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                            </div>
                                            <div class="show-sort">
                                                排序：
                                                <input name="img[0][0][sort]" type="text" class="text" value="0" size="1" maxlength="1">
                                            </div>
                                            <div class="dssc-upload-btn">
                                                <a href="javascript:void(0);">
                                                    <span><input type="file" hidefocus="true" size="1" data="@Model.Id" class="input-file" name="file_00" id="file_00"></span><p><i class="iconfont"></i>上传</p>
                                                </a>
                                            </div>
                                        </li>
                                        <li class="dssc-goodspic-upload">
                                            <div class="upload-thumb">
                                                <img src="@(ViewBag.Img1.Url == null?"/uploads/common/default_goods_image.jpg":ViewBag.Img1.Url)" dstype="file_01">
                                                <input type="hidden" name="img[0][1][name]" value="" dstype="file_01">
                                                <input type="hidden" name="img[0][1][Id]" value="" dsId="file_01">
                                            </div>
                                            <div class="show-default @(ViewBag.Img1.IsDefault?"selected":"") " dstype="file_01">
                                                <p>
                                                    <i class="iconfont"></i>默认主图                <input type="hidden" name="img[0][1][default]" value="0">
                                                </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                            </div>
                                            <div class="show-sort">
                                                排序：
                                                <input name="img[0][1][sort]" type="text" class="text" value="1" size="1" maxlength="1">
                                            </div>
                                            <div class="dssc-upload-btn">
                                                <a href="javascript:void(0);">
                                                    <span><input type="file" hidefocus="true" size="1" data="@Model.Id" class="input-file" name="file_01" id="file_01"></span><p><i class="iconfont"></i>上传</p>
                                                </a>
                                            </div>
                                        </li>
                                        <li class="dssc-goodspic-upload">
                                            <div class="upload-thumb">
                                                <img src="@(ViewBag.Img2.Url == null?"/uploads/common/default_goods_image.jpg":ViewBag.Img2.Url)" dstype="file_02">
                                                <input type="hidden" name="img[0][2][name]" value="" dstype="file_02">
                                                <input type="hidden" name="img[0][2][Id]" value="" dsId="file_02">
                                            </div>
                                            <div class="show-default @(ViewBag.Img2.IsDefault?"selected":"") " dstype="file_02">
                                                <p>
                                                    <i class="iconfont"></i>默认主图                <input type="hidden" name="img[0][2][default]" value="0">
                                                </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                            </div>
                                            <div class="show-sort">
                                                排序：
                                                <input name="img[0][2][sort]" type="text" class="text" value="2" size="1" maxlength="1">
                                            </div>
                                            <div class="dssc-upload-btn">
                                                <a href="javascript:void(0);">
                                                    <span><input type="file" hidefocus="true" size="1" data="@Model.Id" class="input-file" name="file_02" id="file_02"></span><p><i class="iconfont"></i>上传</p>
                                                </a>
                                            </div>
                                        </li>
                                        <li class="dssc-goodspic-upload">
                                            <div class="upload-thumb">
                                                <img src="@(ViewBag.Img3.Url == null?"/uploads/common/default_goods_image.jpg":ViewBag.Img3.Url)" dstype="file_03">
                                                <input type="hidden" name="img[0][3][name]" value="" dstype="file_03">
                                                <input type="hidden" name="img[0][2][Id]" value="" dsId="file_03">

                                            </div>
                                            <div class="show-default @(ViewBag.Img3.IsDefault?"selected":"") " dstype="file_03">
                                                <p>
                                                    <i class="iconfont"></i>默认主图                <input type="hidden" name="img[0][3][default]" value="0">
                                                </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                            </div>
                                            <div class="show-sort">
                                                排序：
                                                <input name="img[0][3][sort]" type="text" class="text" value="3" size="1" maxlength="1">
                                            </div>
                                            <div class="dssc-upload-btn">
                                                <a href="javascript:void(0);">
                                                    <span><input type="file" hidefocus="true" size="1" data="@Model.Id" class="input-file" name="file_03" id="file_03"></span><p><i class="iconfont"></i>上传</p>
                                                </a>
                                            </div>
                                        </li>
                                        <li class="dssc-goodspic-upload">
                                            <div class="upload-thumb">
                                                <img src="@(ViewBag.Img4.Url == null?"/uploads/common/default_goods_image.jpg":ViewBag.Img4.Url)" dstype="file_04">
                                                <input type="hidden" name="img[0][4][name]" value="" dsId="file_04">
                                            </div>
                                            <div class="show-default @(ViewBag.Img4.IsDefault?"selected":"") " dstype="file_04">
                                                <p>
                                                    <i class="iconfont"></i>默认主图                <input type="hidden" name="img[0][4][default]" value="0">
                                                </p><a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                            </div>
                                            <div class="show-sort">
                                                排序：
                                                <input name="img[0][4][sort]" type="text" class="text" value="4" size="1" maxlength="1">
                                            </div>
                                            <div class="dssc-upload-btn">
                                                <a href="javascript:void(0);">
                                                    <span><input type="file" hidefocus="true" size="1" class="input-file" data="@Model.Id" name="file_04" id="file_04"></span><p><i class="iconfont"></i>上传</p>
                                                </a>
                                            </div>
                                        </li>
                                    </ul>
                                    <div class="dssc-select-album">
                                        <a class="dssc-btn selected" href="/index.php/admin/Goodsalbum/pic_list.html?item=goods_image&amp;color_id=0" dstype="select-0" style="display: inline-block;"><i class="iconfont"></i>从图片空间选择</a>
                                        <a href="javascript:void(0);" dstype="close_album" class="dssc-btn ml5" style="display: none;"><i class="iconfont"></i>关闭相册</a>
                                    </div>
                                    <div dstype="album-0"></div>
                                </div>
                            </div>
                            <div class="sidebar">
                                <div class="alert alert-info alert-block" id="uploadHelp">
                                    <div class="faq-img"></div>
                                    <h4>上传要求：</h4><ul>
                                        <li>1. 请使用jpg\jpeg\png等格式、单张大小不超过1M的正方形图片。</li>
                                        <li>2. 上传图片最大尺寸将被保留为1280像素。</li>
                                        <li>3. 每种颜色最多可上传5张图片或从图片空间中选择已有的图片，上传后的图片也将被保存在店铺图片空间中以便其它使用。</li>
                                        <li>4. 通过更改排序数字修改商品图片的排列显示顺序。</li>
                                        <li>5. 图片质量要清晰，不能虚化，要保证亮度充足。</li>
                                        <li>6. 操作完成后请点下一步，否则无法在网站生效。</li>
                                    </ul><h4>建议:</h4><ul><li>1. 主图为白色背景正面图。</li><li>2. 排序依次为正面图-&gt;背面图-&gt;侧面图-&gt;细节图。</li></ul>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>

            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <input type="hidden" value="@Model.Id" name="Id" />
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    }
</div>
<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
<script src="/static/plugins/js/fileupload/jquery.ui.widget.js"></script>
<script src="/static/plugins/js/fileupload/jquery.iframe-transport.js"></script>
<script src="~/static/plugins/js/layui/layui.js"></script>
<script src="/static/plugins/mlselection.js"></script>
<script src="/static/plugins/jquery.mousewheel.js"></script>
<script src="/static/plugins/jquery.charCount.js"></script>
<script src="/static/plugins/ajaxfileupload.js"></script>
<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script src="/static/home/<USER>/sellergoods_add_step1.js"></script>
<script src="/static/home/<USER>/sellergoods_add_step2.js"></script>
<script src="/static/home/<USER>/sellergoods_add_step3.js"></script>
<script src="/static/plugins/perfect-scrollbar.min.js"></script>

@*<script src="~/static/admin/js/xm-select.js"></script>*@
<script type="text/javascript" asp-location="Footer">
    var ADMINSITEURL = "/@adminarea";
    var HOMESITEROOT = "/@adminarea";
    var BASESITEURL = "/@adminarea";
    var ADMINSITEROOT = "/static/admin";
    var Querytheson = "@Url.Action("Querytheson", "ProductCategory")";
    var createData = "@Url.Action("CreateGoods")";
    var CreateImg = "@Url.Action("UploadImg")";
    $(function () {
        showCheckClass();

        //电脑端手机端tab切换
        $(".tabs").tabs();

        $("#default_user_portrait").change(function () {
            $("#textfield4").val($("#default_user_portrait").val());
        });

         // 图片上传
        $('#fileupload').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: "@Url.Action("UploadImg", new { Id = Model.Id })",
                done: function (e, data) {
                    if (data != 'error') {
                        add_uploadedfile(data.result);
                    }
                }
            });
        });


        $('#article_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                article_title: {
                    required: true
                },
                article_url: {
                    url: true
                },
                article_sort: {
                    number: true,
                    range: [0, 255]
                }
            },
            messages: {
                article_title: {
                    required: '@T("标题名称不能为空")'
                },
                article_url: {
                    url: '@T("必须输入正确格式的网址")'
                },
                article_sort: {
                    number: '@T("排序只能为数字")',
                    range: '@T("数字范围为0~255，数字越小越靠前")'
                }
            }
        });

    });
     function add_uploadedfile(file_data) {
        var newImg = '<tr id="' + file_data.file_id + '" class="tatr2"><input type="hidden" name="file_id[]" value="' + file_data.file_id + '" /><td><img width="40px" height="40px" src="' + file_data.file_path + '" /></td><td>' + file_data.file_name + '</td><td><a href="javascript:insert_editor(\'' + file_data.file_path + '\');">@T("插入编辑器")</a> | <a href="javascript:del_file_upload(' + file_data.file_id + ');">@T("删除")</a></td></tr>';
        $('#thumbnails').prepend(newImg);
        }

    function insert_editor(file_path) {
        ue.execCommand('insertimage', { src: file_path });
    }
    function del_file_upload(file_id) {
        layer.confirm('@T("您确定要删除吗?")', {
            btn: ['@T("确定")', '@T("取消")'],
            title: false,
        }, function () {
            $.getJSON("@Url.Action("DeleteImg")", { id: + file_id }, function (result) {
                if (result) {
                    $('#' + file_id).remove();
                    layer.close(layer.index);
                } else {
                    layer.alert('@T("删除失败")');
                }
            });
        });
    }

    @*var data1 = $.parseJSON('@Html.Raw(ViewBag.List)')

    var demo1 = xmSelect.render({
        el: '#demo1',
        radio: true,
        autoRow: true,
        toolbar: { show: true },
        filterable: true,
        remoteSearch: true,
        remoteMethod: function (val, cb, show) {
            //这里如果val为空, 则不触发搜索
            if (!val) {
                return cb(data1);
            }
            $.ajax({
                type: 'get',
                //url: '@Url.Action("GetlikeName")',
                url: '@Url.Action("GetlikeName", "ProductModel")',
                data: {
                    Key: val
                },
                success(data) {
                    cb(data);
                },
                error: function (arg1) {
                    cb([]);
                }

            })
        }
    });*@


    var data1 = $.parseJSON('@Html.Raw(ViewBag.List)');
    var demo1 = xmSelect.render({
        el: '#demo1',
        //radio: true,
        paging: true,
        pageSize: 10,
        filterable: true,
        filterMethod: function (val, item, index, prop) {
            if (item.name.toLowerCase().indexOf(val.toLowerCase()) != -1) {//名称中包含的大小写都搜索出来
                return true;
            }
            return false;//其他的就不要了
        },
        pageEmptyShow: false,
        clickClose: true,
        data: data1
    });
</script>
