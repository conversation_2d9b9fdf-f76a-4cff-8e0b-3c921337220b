﻿@{
    Layout = "~/Views/Shared/_Root.cshtml";

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/index.css");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/carousel.css");

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/index.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/dat.gui.min.js");
}
<div class="body1">
    <canvas id="c" height="874"></canvas>
    <p>@T("智能生活  物联未来")</p>
    <span>@T("物联网模组专业供应商")</span>
</div>
<div class="hezi"></div>
<main>
    <section class="section -mt-64">
        <div class="container container-lg">
            <div class="row">
                <div class="bthz">
                    <span class="zhubiaoti">@T("一站式解决方案")</span>
                </div>
                <div class="bthz1">
                    <span class="btnr">@T("提供从PCBA设计到后台SaaS包括APP小程序等快速落地的一站式解决方案")</span>
                </div>
                <div class="solve">
                    <div>
                        <i><img src="@(CDN.GetCDN())/images/mk.png"></i>
                        <span>@T("智能模块")</span>
                        <span class="iconbtwz1">@T("提供WIFI/蓝牙/NB-IOT/Lora/Cat1模组")</span>
                    </div>
                    <div>
                        <i><img src="@(CDN.GetCDN())/images/sj.png"></i>
                        <span>@T("智能APP")</span>
                        <span class="iconbtwz1">@T("APP开发和维护，管理智能硬件，享受方便快捷的智能生活")</span>
                    </div>
                    <div>
                        <i><img src="@(CDN.GetCDN())/images/q.png"></i>
                        <span>@T("智能产品")</span>
                        <span class="iconbtwz1">@T("海凌科提供一站式产品智能解决方案")</span>
                    </div>
                </div>
                <div class="solve">
                    <div>
                        <i><img src="@(CDN.GetCDN())/images/yun.png"></i>
                        <span>@T("智能云服务")</span>
                        <span class="iconbtwz1">@T("对接各大IOT云平台，包括天猫/飞燕/涂鸦/华为/小米等合作伙伴")</span>
                    </div>
                    <div>
                        <i><img src="@(CDN.GetCDN())/images/solution5.png"></i>
                        <span>@T("大数据开发")</span>
                        <span class="iconbtwz1">@T("接入平台的智能硬件进行数据分析和呈现，结合用户行为进行深度分析")</span>
                    </div>
                    <div>
                        <i><img src="@(CDN.GetCDN())/images/solution6.png"></i>
                        <span>@T("开放生态平台")</span>
                        <span class="iconbtwz1">@T("为合作伙伴提供信息系统，数据开放服务帮助合伙伴开发个性化增值服务")</span>
                    </div>
                </div>
            </div>

        </div>
    </section>
    @*产品*@
    <div class="cptj container container-lg">
        <div class="cptjgf row">
            <div class="cptjbt">
                <span class="zhubiaoti">@T("推荐产品")</span>
            </div>
            <div class="bthz2">
                <span class="btnr">@T("智慧赋能 打造高效快速的产品落地服务")</span>
            </div>
            @foreach (var item in Model.productRecomList)
            {
                <a href="@Url.DGAction("Details", "Product", new { Id = item.Id}, true)" target="_blank">
                    <div class="fangkuai">
                        <img src="@item.Image">
                        <div class="cpxqwzhf">
                            <p class="cpxqbt" title="@item.Name">@item.Name</p>
                            <p class="cpxqwz">@item.AdvWord</p>
                        </div>
                    </div>
                </a>
            }
        </div>
    </div>

    @*应用场景*@
    <div class="yycj">
        <div class="yycjgf">
            <div class="cptjbt">
                <span class="zhubiaoti">@T("应用场景")</span>
            </div>
            <div class="bthz3">
                <span class="btnr">@T("海凌科针对物联网设备连接需求，广泛地应用于智能家居市场，物联网市场，工业控制等应用场景")</span>
            </div>
            <div class="desktop-wrapper">
                <div class="container">
                    <div class="row">
                        <div class="options">
                            <div class="option" data-index="0">
                                <div>
                                    <div class="info-wrapper">
                                    </div>
                                    <div class="icon-wrapper sb">
                                        <div class="category-text">@T("智能制造")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="option active" data-index="1">
                                <div>
                                    <div class="info-wrapper">
                                    </div>
                                    <div class="icon-wrapper ce">
                                        <div class="category-text">@T("智慧物流")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="option" data-index="2">
                                <div>
                                    <div class="info-wrapper">
                                    </div>
                                    <div class="icon-wrapper sb">
                                        <div class="category-text">@T("智能产品")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="option" data-index="3">
                                <div>
                                    <div class="info-wrapper">
                                    </div>
                                    <div class="icon-wrapper ce">
                                        <div class="category-text">@T("电源模块")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="option" data-index="4">
                                <div>
                                    <div class="info-wrapper">
                                    </div>
                                    <div class="icon-wrapper sb">
                                        <div class="category-text">@T("智能安防")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="option" data-index="5">
                                <div>
                                    <div class="info-wrapper">
                                    </div>
                                    <div class="icon-wrapper ce">
                                        <div class="category-text">@T("智慧农业")</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    @*相关案例*@
    <div class="xgal">
        <div class="cptjbt">
            <span class="zhubiaoti">@T("相关案例")</span>
        </div>
        <div class="bthz4">
            <span class="btnr">@T("一站式智能控制解决方案提供商")</span>
        </div>
        <div class="xgalgf">
            @foreach (var item in Model.caseRecomList)
            {
                <a href="@Url.DGAction("details","case",new { Id=item.Id})">
                    <div>
                        <img src="@(CDN.GetCDN())@item.Pic">
                        <div class="cpxqwzhf">
                            <p class="cpxqbt">@item.Name</p>
                            <p class="cpxqwz">@item.Summary</p>
                        </div>
                    </div>
                </a>
            }
        </div>
    </div>
    @*新闻*@
    <div class="xwzx">
        <div class="xwzxgf">
            <div class="xwzxtpgf">
                <img src="@(CDN.GetCDN())/images/xwtp.png" class="xwtp">
            </div>
            <div class="xwzxfk">
                <p><span id="0" class="xwbt">@T("公司新闻")</span><span id="1" class="xwbt">@T("行业新闻")</span><span id="2" class="xwbt">@T("解决方案")</span></p>
                <ul class="xgalwz">
                    @foreach (var item in Model.ArticleList)
                    {

                        var ModelLan = ArticleLan.FindByAIdAndLIds((int)item.Id, (int)language.Id, true);
                        <li class="xwwzxl">
                            <span class="xwwz" title="@ModelLan.Name"><a href="@Url.DGAction("Details","Journalism",new { Id=item.Id})">@ModelLan.Name</a></span><span class="xwwz1">@item.CreateTime.ToString("yyyy-MM-dd")</span>
                        </li>
                    }
                </ul>
                <ul class="xgalwz">
                    @foreach (var item in Model.IndustryList)
                    {
                        var ModelLan = ArticleLan.FindByAIdAndLIds((int)item.Id, (int)language.Id, true);
                        <li class="xwwzxl">
                            <span class="xwwz" title="@ModelLan.Name"><a href="@Url.DGAction("Details","Journalism",new { Id=item.Id})">@ModelLan.Name</a></span><span class="xwwz1">@item.CreateTime.ToString("yyyy-MM-dd")</span>
                        </li>
                    }
                </ul>
                <ul class="xgalwz">
                    @foreach (var item in Model.Solutionlist as IEnumerable<Solution>)
                    {
                        var ModelLan = SolutionLan.FindByAIdAndLIds(item.Id, (int)language.Id, true);
                        <li class="xwwzxl">
                            <span class="xwwz" title="@ModelLan.Name "><a href="@(item.Url==""||item.Url==null?Url.DGAction("Details","Solution",new { Id=item.Id}):item.Url)">@ModelLan.Name</a></span><span class="xwwz1">@item.CreateTime.ToString("yyyy-MM-dd")</span>
                        </li>
                    }
                </ul>
            </div>
        </div>
    </div>

</main>

<script type="text/javascript" asp-location="Footer">
    $(function () {
        var canh = 2;

        $(".desktop-wrapper .option").click(function () {
            $(".option").removeClass("active");
            $(this).addClass("active");
            clearInterval(carousel);
            canh = $(this).data("index");
            carousel = setInterval(() => {
                $(".option").removeClass("active");
                $(".desktop-wrapper .option:eq(" + canh + ")").addClass("active");
                canh++;
                canh = canh == 6 ? 0 : canh;
            }, 3000)
        });

        carousel = setInterval(() => {
            $(".option").removeClass("active");
            $(".desktop-wrapper .option:eq(" + canh + ")").addClass("active");
            canh++;
            canh = canh == 6 ? 0 : canh;
        }, 3000)
        $(".xwbt").click(function () {
            $(".xgalwz").hide();
            $(".xgalwz:eq(" + $(this).attr("id") + ")").show();
        });
    });
</script>