﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH;
using DH.Core;
using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;
using DH.Entity;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Exceptions;
using Pek.Helpers;
using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>会员管理</summary>
[DisplayName("会员管理")]
[Description("用于会员管理")]
[AdminArea]
[DHMenu(100,ParentMenuName = "Members", ParentMenuDisplayName = "会员", ParentMenuUrl = "~/{area}/Member", ParentMenuOrder = 80, CurrentMenuUrl = "~/{area}/Member", CurrentMenuName = "MemberList", CurrentIcon = "&#xe667;", LastUpdate = "20240125")]
public class MemberController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 会员列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("会员列表")]
    public IActionResult Index(String search_field_name, String search_field_value, String search_sort, String search_state, Int32 search_grade, Int32 page = 1)
    {
        dynamic viewModel = new ExpandoObject();

        viewModel.search_field_name = search_field_name;

        search_field_value = search_field_value.SafeString().Trim();
        viewModel.search_field_value = search_field_value;

        viewModel.search_sort = search_sort;
        viewModel.search_state = search_state;
        viewModel.search_grade = search_grade;

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        var UserList = UserE.Searchs(pages, search_field_name, search_field_value, search_sort, search_state, search_grade).Select(x => new { x.ID, x.Name, x.LastLoginIP, Sex = GetResource(x.Sex.ToString()), RegisterTime = x.RegisterTime.ToFullString(), LastLogin = x.LastLogin.ToFullString(), x.Avatar, x.Logins, x.Enable, x.DisplayName, x.Mail, x.Mobile, SsoId = UserConnect.FindByProviderAndUserID("ChuangChu", x.ID)?.LinkID }).ToDynamicList();

        viewModel.UserList = UserList;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "search_field_value", search_field_value }, { "search_field_name", search_field_name }, { "search_sort", search_sort }, { "search_state", search_state }, { "search_grade", search_grade.ToString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 新增会员
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增会员")]
    public IActionResult CreateMember()
    {
        return View();
    }

    /// <summary>
    /// 新增会员
    /// </summary>
    /// <param name="Name"></param>
    /// <param name="PassWord"></param>
    /// <param name="Remark"></param>
    /// <param name="Code"></param>
    /// <param name="DisplayName"></param>
    /// <param name="Sex"></param>
    /// <param name="Mobile"></param>
    /// <param name="Mail"></param>
    /// <param name="WangWang"></param>
    /// <param name="TrueName"></param>
    /// <param name="QQ"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增会员")]
    public IActionResult CreateMember(String Name, String PassWord, String Remark, String Code, String DisplayName, Int32 Sex, String Mobile, String Mail, String WangWang, String TrueName, String QQ)
    {
        try
        {
            if (Name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("会员名不能为空") });
            }
            if (PassWord.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("密码不能为空") });
            }
            if (PassWord.Length < 6 || PassWord.Length > 20)
            {
                return Prompt(new PromptModel { Message = GetResource("密码长度不能小于6位且不能大于20位") });
            }

            if (!ValidateHelper.IsMobile(Mobile))
            {
                return Prompt(new PromptModel { Message = GetResource("请输入正确的手机号码") });
            }

            if (!ValidateHelper.IsEmail(Mail))
            {
                return Prompt(new PromptModel { Message = GetResource("请输入正确的邮箱") });
            }

            Name = Name.SafeString().Trim();
            Mobile = Mobile.SafeString().Trim();
            Mail = Mail.SafeString().Trim();
            DisplayName = DisplayName.SafeString().Trim();

            var Model = UserE.FindByName(Name);
            if (Model != null)
            {
                return Prompt(new PromptModel { Message = GetResource("用户名已存在 请重新输入！") });
            }

            var MobileModel = UserE.FindByMobile(Mobile);
            if (MobileModel != null)
            {
                return Prompt(new PromptModel { Message = GetResource("手机号已存在 请重新输入！") });
            }

            var MailModel = UserE.FindByMail(Mail);
            if (MailModel != null)
            {
                return Prompt(new PromptModel { Message = GetResource("邮箱已存在 请重新输入！") });
            }

            if (!UserE.FindByDisplayName(0, DisplayName))
            {
                return Prompt(new PromptModel { Message = GetResource("昵称已存在 请重新输入！") });
            }

            using (var tran1 = UserE.Meta.CreateTrans())
            {
                var UserModel = new UserE
                {
                    Name = Name,
                    Password = PassWord.MD5(),
                    Sex = Sex == 1 ? SexKinds.男 : Sex == 2 ? SexKinds.女 : SexKinds.未知,
                    Enable = true,
                    RegisterTime = DateTime.Now,
                    RegisterIP = Pek.Helpers.DHWeb.IP,
                    Remark = Remark,
                    Code = Code.SafeString().Trim(),
                    DisplayName = DisplayName,
                    Mobile = Mobile,
                    Mail = Mail,
                    RoleID = Role.GetOrAdd(DHSetting.Current.DefaultRole).ID
                };
                UserModel.Insert();

                var userDetail = new UserDetail
                {
                    Id = UserModel.ID,
                    TrueName = TrueName,
                    WangWang = WangWang,
                    QQ = QQ,
                    Points = SiteSettingInfo.SiteSettings.PointsReg
                };
                userDetail.Insert();

                PointsLog.AddPointsLog(UserModel.ID, Name, SiteSettingInfo.SiteSettings.PointsReg, "注册会员", "regist");

                var modelSnsAlbumClass = new SnsAlbumClass();
                modelSnsAlbumClass.UId = UserModel.ID;
                modelSnsAlbumClass.Name = Name;
                modelSnsAlbumClass.Des = "买家秀";
                modelSnsAlbumClass.Sort = 0;
                modelSnsAlbumClass.IsDefault = true;
                modelSnsAlbumClass.Insert();

                //UserLog.AddLog($"添加用户【{Name}】:{UserModel.ID}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");
                Loger.UserLog($"添加用户", $"【{Name}】:{UserModel.ID} {ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");

                tran1.Commit();
            }
        }
        catch (DHException ex)
        {
            return Prompt(new PromptModel { Message = ex.Message });
        }

        return MessageTip(GetResource("创建成功"));
    }

    /// <summary>
    /// 修改会员页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改会员页面")]
    public IActionResult EditMember(Int32 Id)
    {
        var Model = UserE.FindByID(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除！"));
        }

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var _workContext = EngineContext.Current.Resolve<IWorkContext>();
            var userDetail = UserDetail.FindById(Id);

            ViewBag.CountryList = Country.FindAllWithCache().OrderBy(e => e.Id).Select(e => new SelectListItem { Value = e.Id.ToString(), Text = CountryLan.FindNameByCIdAndLId(e.Id, _workContext.WorkingLanguage.Id) });
        }
        ViewBag.RoleList = Role.FindAllWithCache().Where(x => x.ID != 1 && x.IsSystem == true).OrderBy(e => e.ID).Select(x => new Role { ID = x.ID, Name = RoleLan.FindByRIdAndLId(x.ID, WorkingLanguage.Id).Name });


        return View(Model);
    }

    /// <summary>
    /// 修改会员
    /// </summary>
    /// <param name="PassWord">密码</param>
    /// <param name="Id">用户Id</param>
    /// <param name="Remark"></param>
    /// <param name="Code"></param>
    /// <param name="DisplayName"></param>
    /// <param name="Sex"></param>
    /// <param name="Enable"></param>
    /// <param name="Mobile"></param>
    /// <param name="Mail"></param>
    /// <param name="TrueName"></param>
    /// <param name="PayPwd"></param>
    /// <param name="EmailBind"></param>
    /// <param name="MobileBind">MobileBind</param>
    /// <param name="IsAllowTalk"></param>
    /// <param name="IsBuy"></param>
    /// <param name="InformAllow"></param>
    /// <param name="AuthState"></param>
    /// <param name="WangWang"></param>
    /// <param name="QQ"></param>
    /// <param name="AreaId"></param>
    /// <param name="CityId"></param>
    /// <param name="CountryId"></param>
    /// <param name="ProvinceId"></param>
    /// <param name="Region"></param>
    /// <param name="RoleID"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改会员")]
    public IActionResult EditMember(String PassWord, Int32 Id, String Remark, String Code, String DisplayName, Int32 Sex, Int32 Enable, String Mobile, String Mail, String TrueName, String PayPwd, Int32 EmailBind, Int32 MobileBind, Int32 IsAllowTalk, Int32 IsBuy, Int32 InformAllow, Int16 AuthState, String WangWang, String QQ, Int32 ProvinceId, Int32 CityId, Int32 AreaId, String Region, Int32 RoleID, Int32 CountryId = 1)
    {
        try
        {
            if (!ValidateHelper.IsMobile(Mobile))
            {
                return Prompt(new PromptModel { Message = GetResource("请输入正确的手机号码") });
            }

            if (!ValidateHelper.IsEmail(Mail))
            {
                return Prompt(new PromptModel { Message = GetResource("请输入正确的邮箱") });
            }

            var UserModel = UserE.FindByID(Id);
            if (UserModel == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
            }

            Mobile = Mobile.SafeString().Trim();
            Mail = Mail.SafeString().Trim();
            DisplayName = DisplayName.SafeString().Trim();

            if (!UserE.FindByMobile(Id, Mobile))
            {
                return Prompt(new PromptModel { Message = GetResource("手机号已存在 请重新输入！") });
            }

            if (!UserE.FindByMail(Id, Mail))
            {
                return Prompt(new PromptModel { Message = GetResource("邮箱已存在 请重新输入！") });
            }

            if (PassWord.IsNotNullAndWhiteSpace())
            {
                if (PassWord.Length < 6 || PassWord.Length > 20)
                {
                    return Prompt(new PromptModel { Message = GetResource("密码长度不可少于6位且不能大于32位") });
                }
                UserModel.Password = PassWord;
            }

            if (!UserE.FindByDisplayName(Id, DisplayName))
            {
                return Prompt(new PromptModel { Message = GetResource("昵称已存在 请重新输入！") });
            }

            if (AuthState == 3 && TrueName.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("实名认证为已认证时，真实姓名不能为空") });
            }

            using (var tran1 = UserE.Meta.CreateTrans())
            {
                UserModel.Remark = Remark.SafeString().Trim();
                UserModel.Code = Code.SafeString().Trim();
                UserModel.DisplayName = DisplayName;
                UserModel.Sex = Sex == 1 ? SexKinds.男 : Sex == 2 ? SexKinds.女 : SexKinds.未知;
                UserModel.Enable = Enable != 0;
                UserModel.Mobile = Mobile;
                UserModel.Mail = Mail;
                UserModel.RoleID = RoleID;
                UserModel.Ex1 = 1;
                UserModel.Save();

                var userDetail = UserDetail.FindById(Id);
                if (userDetail == null)
                {
                    userDetail = new UserDetail();
                    userDetail.Id = Id;
                }
                userDetail.TrueName = TrueName;

                if (PayPwd.IsNotNullAndWhiteSpace())
                {
                    if (PayPwd.Length < 6 || PayPwd.Length > 32)
                    {
                        return Prompt(new PromptModel { Message = GetResource("支付密码长度不可少于6位且不能大于32位") });
                    }
                    userDetail.PayPwd = PayPwd.MD5();
                }
                userDetail.EmailBind = EmailBind != 0;
                userDetail.MobileBind = MobileBind != 0;
                userDetail.IsAllowTalk = IsAllowTalk != 1;
                userDetail.IsBuy = IsBuy != 1;
                userDetail.InformAllow = InformAllow != 1;
                userDetail.AuthState = AuthState;

                if (userDetail.ProvinceId != ProvinceId || userDetail.CityId != CityId || userDetail.AreaId != AreaId)
                {
                    userDetail.CountryId = CountryId;
                    userDetail.ProvinceId = ProvinceId;
                    userDetail.CityId = CityId;
                    userDetail.AreaId = AreaId;
                    userDetail.AreaInfo = Region;
                }

                userDetail.WangWang = WangWang;
                userDetail.QQ = QQ;

                userDetail.Save();

                tran1.Commit();
            }

            UserE.Meta.Cache.Clear("");
            UserDetail.Meta.Cache.Clear("");
            Loger.UserLog("修改", $"修改会员信息，会员ID：{Id}");
        }
        catch (DHException ex)
        {
            return Prompt(new PromptModel { Message = ex.Message });
        }

        return MessageTip(GetResource("修改成功"));
    }

    /// <summary>
    /// 验证邮箱
    /// </summary>
    /// <param name="Mail"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("验证邮箱")]
    public IActionResult VerifyEmail(String Mail, Int32 Id)
    {
        return Json(UserE.FindByMail(Id, Mail.SafeString().Trim()));
    }

    /// <summary>
    /// 验证手机号
    /// </summary>
    /// <param name="Mobile"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("验证手机号")]
    public IActionResult VerifyMobile(String Mobile, Int32 Id)
    {
        return Json(UserE.FindByMobile(Id, Mobile.SafeString().Trim()));
    }

    /// <summary>
    /// 验证用户名
    /// </summary>
    /// <param name="Name"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("验证用户名")]
    public IActionResult VerifyName(String Name, Int32 Id)
    {
        return Json(UserE.FindByUserName(Id, Name.SafeString().Trim()));
    }

    /// <summary>
    /// 验证昵称
    /// </summary>
    /// <param name="DisplayName"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("验证昵称")]
    public IActionResult VerifyDisplayName(String DisplayName, Int32 Id)
    {
        return Json(UserE.FindByDisplayName(Id, DisplayName.SafeString().Trim()));
    }

    /// <summary>禁用/启用用户</summary>
    /// <returns></returns>
    [DisplayName("禁用/启用用户")]
    [HttpGet]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult Disable(String Ids, Int32 State = 0)
    {
        var result = new DResult();

        var list = UserE.FindByIds(Ids);
        if (State == 0)
        {
            list.ForEach(e => e.Enable = false);
        }
        else
        {
            list.ForEach(e => e.Enable = true);
        }

        list.Save();
        UserE.Meta.Cache.Clear("");
        Loger.UserLog($"{(State==0?"禁用":"启用")}用户", $"操作目标用户ID：{Ids}");

        return Json(new DResult() { code = 10000, msg = State == 0 ? GetResource("禁用成功") : GetResource("启用成功") });
    }

    /// <summary>
    /// 修改权限
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("修改权限")]
    public IActionResult EditRole(Int32 Id)
    {
        var Model = UserE.FindByID(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("会员不存在或已被删除！"));
        }

        return View(Model);
    }

    /// <summary>
    /// 修改用户权限
    /// </summary>
    /// <param name="Id">用户Id</param>
    /// <param name="AutoGuJian">自动生成固件</param>
    /// <param name="GuJianLogo">Logo</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改用户权限")]
    public IActionResult EditRole(Int32 Id, Int32 AutoGuJian, Int32 GuJianLogo)
    {
        try
        {
            var UserModel = UserE.FindByID(Id);
            if (UserModel == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
            }

            var userDetail = UserDetail.FindById(Id);
            if (userDetail == null)
            {
                userDetail = new UserDetail();
                userDetail.Id = Id;
            }

            userDetail.RoleExIds = String.Empty;
            if (AutoGuJian == 1)
            {
                userDetail.RoleExIds += ",AutoGuJian";
            }
            if (GuJianLogo == 1)
            {
                userDetail.RoleExIds += ",GuJianLogo";
            }
            userDetail.RoleExIds = userDetail.RoleExIds.Trim(',');

            userDetail.Save();
            Loger.UserLog("修改用户权限", $"修改用户权限，用户ID：{Id}");
        }
        catch (DHException ex)
        {
            return Prompt(new PromptModel { Message = ex.Message });
        }

        return MessageTip(GetResource("修改成功"));
    }
}