﻿
.<PERSON>y-<PERSON> {

}

.dg {
    display: none;
}



.<PERSON>y-Way p, .<PERSON>y-Way span {
    position: absolute;
    left: 0px;
    right: 0px;
    top: 92px;
    font-size: 23px;
    color: #FFFFFF;
    text-align: center;
}

.Milky-Way span {
    top: 148px;
    font-size: 14px;
}

.solve {
    padding-top: 47px;
    text-align: center;
}

    .solve > p {
        text-align: center;
        font-size: 16px;
        color: #35354B;
        margin-bottom: 12px;
        font-weight: bold;
    }

    .solve > span {
        display: inline-block;
        width: 80%;
        margin:0 auto;
        text-align: center;
        font-size: 11px;
        color: #656565;
    }

    .solve div {
        display: flex;
    }

.solve-one {
    padding-top: 30px;
}

.solve div p {
    flex: 1;
    text-align: center;
    padding-bottom: 34px;
    margin: 0px;
}

    .solve div p span {
        display: inline-block;
        width: 63px;
        height: 63px;
        border-radius: 50%;
        line-height: 63px;
        box-shadow: 0px 2px 15px 1px rgba(177, 173, 173, 0.5);
        margin: 0 auto;
    }

        .solve div p span img {
            width: 51.85%;
        }

    .solve div p i, .solve div p b {
        font-style: normal;
        font-weight: normal;
        display: inline-block;
        width: 100%;
        text-align: center;
    }

    .solve div p i {
        padding-top: 12px;
        padding-bottom: 6px;
        font-size: 13px;
        font-weight: bold;
        color: #090909;
    }

    .solve div p b {
        width: 82.42%;
        font-size: 9px;
        color: #999999;
    }

.recommended,.case {
    background-color: #FBFAFF;
    padding-top: 47px;
}

    .recommended p, .case p {
        font-weight: bold;
        color: #313131;
        text-align: center;
        font-size: 16px;
    }

    .recommended span, .case span {
        width: 100%;
        display: inline-block;
        color: #656565;
        text-align: center;
        font-size: 9px;
    }

    .recommended ul,.case ul {
        margin: 0px;
        padding: 0px;
        display: block;
        overflow: hidden;
        padding-bottom: 39px;
    }

        .recommended ul li, .case ul li {
            margin-top: 18px;
            list-style: none;
            width: 43.37%;
            height: auto;
            margin-top: 27px;
            margin-left: 4.8%;
            background-color:#fff;
            float: left;
            margin-bottom: 9px;
            border-radius: 5px;
            box-shadow: 0px 2px 15px 1px rgba(177, 173, 173, 0.38);
        }

            .recommended ul li a, .case ul li a {
                text-decoration: none !important;
            }

            .recommended ul li:nth-child(2n), .case ul li:nth-child(2n) {
                margin-left: 4.27%;
            }

            .recommended ul li img, .case ul li img {
                width: 100%;
            }

            .recommended ul li b, .case ul li b {
                padding-left: 5.5%;
                margin-top: 5px;
                margin-bottom: 0px;
                display: inline-block;
                font-size: 11px;
                color: #333333;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .recommended ul li i, .case ul li i {
                padding-left: 5.5%;
                padding-right: 5.9%;
                margin-bottom: 15px;
                display: inline-block;
                font-size: 11px;
                color: #858585;
                width: 100%;
                min-height: 35px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                white-space: pre-wrap;
                font-style: normal;
            }

.scenario{
    padding-top:31px;
    padding-bottom:28px;
}

    .scenario p {
        font-size: 17px;
        font-weight: 400;
        color: #474747;
        margin-bottom:7px;
        text-align:center;
    }

    .scenario span {
        display: block;
        width: 80%;
        margin: 0 auto;
        font-size: 9px;
        font-weight: 400;
        color: #434A50;
        margin-bottom: 7px;
        text-align: center;
    }

    .scenario li {
        margin-top: 18px;
        list-style: none;
        width: 43.37%;
        height: auto;
        margin-top: 17px;
        margin-left: 4.8%;
        float: left;
        margin-bottom: 9px;
        position:relative;
    }

.scenario ul li:nth-child(2n) {
    margin-left: 4.27%;
}
    .scenario ul{
        overflow:hidden;
    }

    .scenario li img {
        width: 100%;
    }
    .scenario li p {
        margin:0px;
        text-align:left;
        position: absolute;
        padding-top:4px;
        padding-bottom:4px;
        background: rgba(0, 0, 0, 0.5);
        left: 0px;
        right: 0px;
        bottom: 0px;
        padding-left:6.19%;
        color:#Fff;
        font-size:7px;
    }

.case p {
    font-size: 17px;
    font-weight:normal;
}
.news {
    padding-top: 10px;
    background-color: #FBFAFF;
}

    .news>p {
        text-align: center;
        font-size: 17px;
        font-weight: 400;
        color: #474747;
    }

    .news > span {
        display: block;
        text-align: center;
        font-size: 9px;
        font-weight: 400;
        color: #434A50;
        padding-bottom: 12px;
        max-width: 90%;
        margin: 0 auto;
    }
    .news div {
        width: 86.8%;
        margin: 0 auto;
        background-color: #FFFFFF;
        box-shadow: 0px 3px 17px 1px rgba(177, 174, 174, 0.38);
        border-radius: 5px;
        padding:15px 18px 15px 20px;
    }

    .news p span {
        display: inline-block;
        cursor: pointer;
        height: 16px;
        line-height: 16px;
        font-size: 8px;
        color: #333;
        text-align: center;
        padding-left: 1.5%;
        padding-right: 1.5%;
        position: relative;
        transition: all .5s;
    }

        .news p span:first-child:after {
            content: "";
            position: absolute;
            right: -3px;
            top: 3px;
            bottom: 3px;
            background-color: #333;
            width: 1px;
        }

        .news p span:last-child:before {
            content: "";
            position: absolute;
            left: -3px;
            top: 3px;
            bottom: 3px;
            background-color: #333;
            width: 1px;
        }

        .news div p {
            position: relative;
        }


    .news p .selected{
        font-size:11px;
    }

    .news ul li {
        display: flex;
        font-size: 8px;
        line-height:20px;
    }

        .news ul li a {
            display: flex;
            color: inherit;
            text-decoration: none !important;
            width: 100%;
        }

        .news ul li i:first-child {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 5%;
        }

        .news ul li i:last-child {
            width:auto
        }