﻿@{
    Html.AppendCssFileParts("~/css/Case.css");

    if (Model.type == 0)
    {
        Html.AppendTitleParts(T("客户案例").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        var modelCaseCategory = Model.Model as CaseCategory;
        Html.AppendTitleParts(modelCaseCategory.Name + DG.Setting.Current.PageTitleSeparator + T("客户案例").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
}

<div class="top">
    <img src="@(CDN.GetCDN())/images/CaseBanner.png" />
    <div>
        <h2>@T("客户案例")</h2>
        <P>@T("打造全新物联网行业解决方案，满足客户个性化需求")</P>
    </div>
</div>

<div class="navigation" style="border-bottom: 1px solid #eeeeee;">
    <div class="navigation-con">
        <div class="navigation-con-left">
            <div>
                <i>@T("案例分类")：</i>
                <a class='@(Model.type==0?"selected":"")' href="@Url.DGAction("Index","Case",IsHtml:true)">@T("全部")</a>
                @foreach (var item in Model.CaseTypelist as IEnumerable<CaseCategory>)
                {
                    <a class="@(Model.type==item.Id?"selected":"")" href="@Url.DGAction("List" , "Case", new { type = item.Id})" id="@item.Id">@item.Name</a>
                }
            </div>
        </div>
        <div class="navigation-con-right">
            <div class="input-group">
                <input type="text" class="form-control" id="acseKeyVal" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2" value="@Model.key">
                <span class="input-group-addon" id="basic-addon2"><img src="@(CDN.GetCDN())/images/fdj.png" /></span>
            </div>
        </div>
    </div>
    <div>
         
    </div>
</div>

<div style="background-color:#FAFAFA">
    <div class="Case">
        @foreach (var item in Model.SolutionList)
        {
            <a href="@Url.DGAction("Details",new { Id=item.Id})">
                <div>
                    <img src="@(CDN.GetCDN())@item.Pic" />
                    <p>@item.Name</p>
                    <span>@item.Summary</span>
                </div>
            </a>
        }
    </div>

    <div class="paging" style="text-align: center;padding-bottom:70px">
        <ul class="pagination">
            @*<li><a class="paging_a opt_for">1</a></li>
                <li><a class="paging_a" href="/search?page=2&amp;Key_Word=&amp;SearchType=0">2</a></li>
                <li><a class="paging_a" href="/search?page=2" aria-label="Next">»</a></li>*@
            @Html.Raw(Model.Str)
        </ul>
    </div>
</div>

<script asp-location="Footer">
    $("#basic-addon2").click(function () {
        window.location.href = '@Url.DGAction("Index")?key=' + $("#acseKeyVal").val();
    })


    $("#acseKeyVal").keyup((e) => {
        if (e.keyCode == 13) {
            $("#basic-addon2").click();
        }
    })


</script>