﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>知识问答</summary>
public partial class KnowledgeQuizModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>标题</summary>
    public String? Name { get; set; }

    /// <summary>内容</summary>
    public String? Content { get; set; }

    /// <summary>标签</summary>
    public String? Tags { get; set; }

    /// <summary>产品型号Id</summary>
    public Int32 MId { get; set; }

    /// <summary>产品型号内容</summary>
    public String? MIdName { get; set; }

    /// <summary>点击数</summary>
    public Int32 Clicks { get; set; }

    /// <summary>点赞数</summary>
    public Int32 HelpFuls { get; set; }

    /// <summary>数据状态 0为不可用，1为已发表</summary>
    public Int32 Status { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IKnowledgeQuiz model)
    {
        Id = model.Id;
        Name = model.Name;
        Content = model.Content;
        Tags = model.Tags;
        MId = model.MId;
        MIdName = model.MIdName;
        Clicks = model.Clicks;
        HelpFuls = model.HelpFuls;
        Status = model.Status;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
