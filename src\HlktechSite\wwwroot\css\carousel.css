h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: .5rem;
    font-family: inherit;
    font-weight: 500;
}

.desktop-wrapper {
    display: flex;
    justify-content: center;
    overflow: hidden;
}

.desktop-wrapper .options {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    overflow: hidden;
    min-width: 600px;
    width: 100%;
    height: 400px;
}

.desktop-wrapper .options .option {
    position: relative;
    overflow: hidden;
    min-width: 80px;
    background-size: 750px auto;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);
    color: #fff;
}

.desktop-wrapper .options .option.active {
    flex-grow: 10000;
    -webkit-transition-property: all;
    -moz-transition-property: all;
    transition-property: all;
    -webkit-transition-duration: 0.4s;
    -moz-transition-duration: 0.4s;
    transition-duration: 0.4s;
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
}

.desktop-wrapper .options .option.active>div {
    z-index: 1;
}

.desktop-wrapper .options .option.active>div .info-wrapper {
    z-index: 999;
}

.desktop-wrapper .options .option.active>div .info-wrapper .title {
    font-size: 1.953rem;
    text-transform: uppercase;
    line-height: 1;
    opacity: 1;
    position: relative;
    margin: 0 0 .5rem;
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    transition-property: opacity;
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    transition-duration: 0.2s;
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    -webkit-transition-delay: 0.2s;
    -moz-transition-delay: 0.2s;
    transition-delay: 0.2s;
}

.desktop-wrapper .options .option.active>div .info-wrapper .subtitle {
    color: white;
    font-size: .9rem;
    letter-spacing: .035rem;
    line-height: 1.125;
    margin: 0;
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    transition-property: opacity;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    -webkit-transition-delay: 0.5s;
    -moz-transition-delay: 0.5s;
    transition-delay: 0.5s;
}

.desktop-wrapper .options .option.active>div .info-wrapper .description {
    left: 0px;
    opacity: 1;
    margin-top: 1.5rem;
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    transition-property: opacity;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    -webkit-transition-delay: 0.6s;
    -moz-transition-delay: 0.6s;
    transition-delay: 0.6s;
}

.desktop-wrapper .options .option.active>div .info-wrapper .description p {
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.desktop-wrapper .options .option.active>div .info-wrapper .ext-link {
    text-decoration: none;
    width: 6rem;
    color: #e9ecef;
    text-shadow: 1px 2px 4px rgba(33, 37, 41, 0.5);
    box-shadow: 2px 2px 4px rgba(33, 37, 41, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: .35rem .75rem;
    position: relative;
    overflow: hidden;
    transition: all 0.2s linear 0s;
}

.desktop-wrapper .options .option.active>div .info-wrapper .ext-link:after {
    position: absolute;
    transition: .3s;
    content: '';
    width: 0;
    left: 0;
    bottom: 0;
    height: .15rem;
    background: rgba(255, 255, 255, 0.2);
}

.desktop-wrapper .options .option.active>div .info-wrapper .ext-link:hover {
    text-shadow: none;
    box-shadow: 3px 3px 8px rgba(33, 37, 41, 0.6);
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
}

.desktop-wrapper .options .option.active>div .info-wrapper .ext-link:hover:after {
    left: 0;
    width: 100%;
}

.desktop-wrapper .options .option.active>div:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 101%;
    top: 0;
    left: 0;
    z-index: 1;
    /*background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.65) 30%, rgba(0, 0, 0, 0) 100%);*/
    /* FF3.6-15 */
    /*background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.65) 30%, rgba(0, 0, 0, 0) 100%);*/
    /* Chrome10-25,Safari5.1-6 */
    /*background: linear-gradient(to bottom, rgba(0, 0, 0, 0.65) 30%, rgba(0, 0, 0, 0) 100%);*/
    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    -webkit-transition-property: all;
    -moz-transition-property: all;
    transition-property: all;
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    transition-duration: 0.2s;
}

    .desktop-wrapper .options .option.active > div .icon-wrapper {
        display: flex;
        align-items: flex-start;
        align-content: baseline;
        background: rgba(0, 0, 0, 0.5);
    }

.desktop-wrapper .options .option.active>div .icon-wrapper .category-text {
    opacity: 1;
    display: initial;
    -webkit-transition-property: all;
    -moz-transition-property: all;
    transition-property: all;
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    transition-duration: 0.2s;
}

.desktop-wrapper .options .option.active>div .icon-wrapper .category-icon:before {
    opacity: 1;
}

.desktop-wrapper .options .option:not(.active) {
    flex-grow: 1;
    filter: grayscale(70%);
}

.desktop-wrapper .options .option:not(.active):hover {
    filter: grayscale(0%);
}

.desktop-wrapper .options .option:not(.active):hover>div:before {
    background: rgba(2, 24, 31, 0.5);
}

.desktop-wrapper .options .option:not(.active)>div .title,
.desktop-wrapper .options .option:not(.active)>div .subtitle,
.desktop-wrapper .options .option:not(.active)>div .description {
    opacity: 0;
}

    .desktop-wrapper .options .option:not(.active) > div .icon-wrapper {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        align-items: center;
        /* padding-bottom: 1rem; */
        background: rgba(0, 0, 0, 0.5);
        overflow: hidden;
    }

.desktop-wrapper .options .option:not(.active)>div .icon-wrapper .category-icon:before {
    opacity: .5;
}

    .desktop-wrapper .options .option:not(.active) > div:before {
        /*background: rgba(52, 58, 64, 0.6);*/
        content: "";
        height: 100%;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        -webkit-transition-property: all;
        -moz-transition-property: all;
        transition-property: all;
        -webkit-transition-duration: 0.2s;
        -moz-transition-duration: 0.2s;
        transition-duration: 0.2s;
    }

    .desktop-wrapper .options .option > div {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: absolute;
        top: 0;
        left: 0;
        transition: 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);
        background-repeat:no-repeat;
        background-size:100% 100%;

    }

        .desktop-wrapper .options .option > div .icon-wrapper .category-text {
            line-height: 40px;
            padding-left: .75rem;
            /*opacity: 0;*/
            /* display: none; */
            position: relative;
            z-index: 100;
        }

.desktop-wrapper .options .option>div .icon-wrapper .category-icon {
    display: flex;
    align-items: flex-start;
    width: 40px;
    height: 40px;
    border-radius: 100%;
    position: relative;
    box-shadow: 0 2px 9px rgba(33, 37, 41, 0.4);
}

.desktop-wrapper .options .option>div .icon-wrapper .category-icon:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-repeat: no-repeat;
    -webkit-mask-image: url(../images/sports_biotech_icon.svg);
    mask-image: url(../images/sports_biotech_icon.svg);
    background-color: #fff;
    height: 20px;
    width: 21px;
}

.desktop-wrapper .options .option>div .icon-wrapper.sb .category-icon {
    background-color: #0DAFE0;
}

.desktop-wrapper .options .option>div .icon-wrapper.ce .category-icon {
    background-color: #f2501f;
}

.options> :nth-child(1) {
    background-color: #515272;
    background-image: url("../images/t1.jpg");
    background-position: center center;
    background-size: cover !important;
}

.options > :nth-child(2) {
    background-color: #6c3f31;
    background-image: url("../images/t2.jpg");
    background-position: center center;
    background-size: cover !important;
}

.options > :nth-child(3) {
    background-color: #323119;
    background-image: url("../images/t3.jpg");
    background-position: center center;
    background-size: cover !important;
}

.options > :nth-child(4) {
    background-color: #5d92cb;
    background-image: url("../images/t4.jpg");
    background-position: center center;
    background-size: cover !important;
}

.options > :nth-child(5) {
    background: #e9ecef;
    background-image: url("../images/t5.jpg");
    background-position: center center;
    background-size: cover !important;
}

.options > :nth-child(6) {
    background-color: #112a3b;
    background-image: url("../images/t6.jpg");
    background-position: center center;
    background-size: cover !important;
}

.mobile-wrapper {
    height: initial;
    max-width: 320px;
    margin: 0 auto;
}

.mobile-wrapper .options {
    position: relative;
    height: auto;
    max-width: none !important;
    flex-basis: 100% !important;
}

.mobile-wrapper .options .option {
    position: relative;
    overflow: hidden;
    background-size: auto 120%;
    background-position: center;
    transition: 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);
    color: #fff;
}

.mobile-wrapper .options .option>div {
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    width: 100%;
    padding: 2rem 1rem 1rem;
    top: 0;
    left: 0;
    transition: 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);
}

.mobile-wrapper .options .option>div .info-wrapper {
    z-index: 999;
}

.mobile-wrapper .options .option>div .info-wrapper .title {
    font-size: 1.5rem;
    text-shadow: 1px 2px 4px rgba(33, 37, 41, 0.5);
    text-transform: uppercase;
    line-height: 1;
    opacity: 1;
    position: relative;
    margin: 0 0 .5rem;
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    transition-property: opacity;
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    transition-duration: 0.2s;
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    -webkit-transition-delay: 0.2s;
    -moz-transition-delay: 0.2s;
    transition-delay: 0.2s;
}

.mobile-wrapper .options .option>div .info-wrapper .subtitle {
    color: white;
    font-size: .8rem;
    letter-spacing: .06rem;
    line-height: 1.125;
    margin: 0;
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    transition-property: opacity;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    -webkit-transition-delay: 0.5s;
    -moz-transition-delay: 0.5s;
    transition-delay: 0.5s;
}

.mobile-wrapper .options .option>div .info-wrapper .description {
    left: 0px;
    opacity: 1;
    margin-top: 1.5rem;
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    transition-property: opacity;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    -webkit-transition-delay: 0.6s;
    -moz-transition-delay: 0.6s;
    transition-delay: 0.6s;
}

.mobile-wrapper .options .option>div .info-wrapper .description p {
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.mobile-wrapper .options .option>div .info-wrapper .ext-link {
    text-decoration: none;
    width: 6rem;
    color: #e9ecef;
    text-shadow: 1px 2px 4px rgba(33, 37, 41, 0.5);
    box-shadow: 2px 2px 4px rgba(33, 37, 41, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: .35rem .75rem;
    position: relative;
    overflow: hidden;
    transition: all 0.2s linear 0s;
}

.mobile-wrapper .options .option>div .info-wrapper .ext-link:after {
    position: absolute;
    transition: .3s;
    content: '';
    width: 0;
    left: 0;
    bottom: 0;
    height: .15rem;
    background: rgba(255, 255, 255, 0.2);
}

.mobile-wrapper .options .option>div .info-wrapper .ext-link:hover {
    text-shadow: none;
    box-shadow: 3px 3px 8px rgba(33, 37, 41, 0.6);
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
}

.mobile-wrapper .options .option>div .info-wrapper .ext-link:hover:after {
    left: 0;
    width: 100%;
}

.mobile-wrapper .options .option>div .icon-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 2rem 0 0;
    z-index: 999;
    opacity: 0.8;
}

.mobile-wrapper .options .option>div .icon-wrapper .category-text {
    line-height: 40px;
    padding-left: .75rem;
}

.mobile-wrapper .options .option>div .icon-wrapper .category-icon {
    display: flex;
    align-items: flex-start;
    width: 32px;
    height: 32px;
    border-radius: 100%;
    position: relative;
    box-shadow: 0 2px 9px rgba(33, 37, 41, 0.4);
}

.mobile-wrapper .options .option>div .icon-wrapper .category-icon:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-repeat: no-repeat;
    -webkit-mask-image: url(../images/sports_biotech_icon.svg);
    mask-image: url(../images/sports_biotech_icon.svg);
    background-color: #fff;
    height: 16px;
    width: 16px;
}

.mobile-wrapper .options .option>div .icon-wrapper.sb .category-icon {
    background-color: #0DAFE0;
}

.mobile-wrapper .options .option>div .icon-wrapper.ce .category-icon {
    background-color: #f2501f;
}

.mobile-wrapper .options .option>div:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 101%;
    top: 0;
    left: 0;
    z-index: 1;
    background: rgba(52, 58, 64, 0.7);
    -webkit-transition-property: all;
    -moz-transition-property: all;
    transition-property: all;
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    transition-duration: 0.2s;
}

.container .row h4:first-of-type {
    text-align: center;
    margin: 0 auto;
    padding-bottom: 1rem;
}