﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";
}

<div class="top">
    <img src="@(CDN.GetCDN())/images/downBanner.png" />
    <h2>@T("下载中心")</h2>
    <P>
        @T("深耕行业十数载，累计服务超过10W+客户2")
    </P>
</div>


<div class="input-group seach-div">
    <input class="form-control" type="text" id="key" name="key" value="@Model.Key" />
    <a href="javascript:;" onclick="seach()">@T("搜索")</a>
</div>

<ul class="type-menu">
    <li id="0" class="@(Model.CId==0?"selected":"")">@T("全部")</li>
        @foreach (var item in Model.ProTypelist)
        {
            <li id="@item.Id" class="@(Model.CId==item.Id?"selected":"")">@item.Name</li>
        }
</ul>


<ul class="rows-list down-list">
    @foreach (var item in Model.list)
    {
        <li><a href="@Url.DGAction("Details",new { Id=item.Id})"><img src="@(CDN.GetCDN())@item.Image" /><b>@item.Name</b><i>@T("资料下载")</i></a></li>
    }
</ul>

<div class="paging" style="text-align: center;">
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>


<script asp-location="Footer">
    $(".type-menu li").click(function () {
        location.href = "@Url.DGAction("Index")?CId=" + $(this).attr("id") + "&key=" + $("#key").val();
    });

    $("#key").keydown(function (e) {
        if (e.which == 13) {
            location.href = "@Url.DGAction("Index")?CId=" + $(".type-menu .selected").attr("id") + "&key=" + $("#key").val();
        }
    });

    function seach(){
        location.href = "@Url.DGAction("Index")?CId=" + $(".type-menu .selected").attr("id") + "&key=" + $("#key").val();
    }

</script>