﻿using DH.Core.Infrastructure;
using DH.SearchEngine.Interfaces;

using HlktechSite.Entity;

using NewLife;
using NewLife.Collections;

namespace HlktechSite.Common;

public class LuceneHelper
{
    /// <summary>
    /// 重建Lucene索引库
    /// </summary>
    public static void CreateLuceneIndex()
    {
        var _searchEngine = EngineContext.Current.Resolve<ISearchEngine>();

        _searchEngine.LuceneIndexer.DeleteAll();
        _searchEngine.CreateIndex(new List<string>()
        {
            nameof(Knowledge)
        });

        var list = Knowledge.FindAllByStatus();

        _searchEngine.LuceneIndexer.Delete(list);
    }

    /// <summary>
    /// 重建Lucene索引库
    /// </summary>
    public static void CreateLuceneIndexs()
    {
        var _searchEngine = EngineContext.Current.Resolve<ISearchEngine>();

        _searchEngine.LuceneIndexer.DeleteAll();

        var list = new List<ILuceneIndexable>();

        var listKnowledge = Knowledge.GetAllByPublish();
        foreach(var item in listKnowledge)
        {
            var sb = Pool.StringBuilder.Get();

            if (!item.MIdName.IsNullOrEmpty())
                sb.Append(item.MIdName + " ");

            item.Content += sb.Put(true);

            list.Add(item);
        }

        var listKnowledgeQuiz = KnowledgeQuiz.GetAllByPublish();
        foreach (var item in listKnowledgeQuiz)
        {
            var sb = Pool.StringBuilder.Get();

            if (!item.MIdName.IsNullOrEmpty())
                sb.Append(item.MIdName + " ");

            item.Content += sb.Put(true);

            list.Add(item);
        }

        var listSolution = Solution.GetAll();
        foreach (var item in listSolution)
        {
            var sb = Pool.StringBuilder.Get();

            if (!item.Name.IsNullOrEmpty())
                sb.Append(item.Name + " ");

            item.Content += sb.Put(true);

            list.Add(item);
        }

        _searchEngine.CreateIndex(list);
    }
}
