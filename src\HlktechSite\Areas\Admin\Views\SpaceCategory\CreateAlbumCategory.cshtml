﻿@{
}
<div class="page">
    <form method="post" id="album_class_form">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><label class="validation" for="aclass_name">相册名称：</label></td>
                    <td class="vatop rowform"><input class="w300 text" type="text" name="aclass_name" id="aclass_name" value="" /></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="validation" for="aclass_des">描述：</label></td>
                    <td class="vatop rowform"><textarea class="w300 textarea" rows="3" name="aclass_des" id="aclass_des"></textarea></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="validation" for="aclass_sort">排序：</label></td>
                    <td class="vatop rowform"><input class="w50 text" type="text" name="aclass_sort" id="aclass_sort" value="@ViewBag.Sort" /></td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="提交" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript" asp-location="Footer">
    $(function () {
        $('#album_class_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                aclass_name: {
                    required: true,
                    maxlength: 20,
                    remote: {
                        url: "@Url.Action("GetByNames")",
                        type: 'get',
                        data: {
                            gc_name: function () {
                                return $('#aclass_name').val();
                            }
                        }
                    }
                },
                aclass_des: {
                    maxlength: 100
                },
                aclass_sort: {
                    digits: true
                }
            },
            messages: {
                aclass_name: {
                    required: '<i class="iconfont">&#xe64c;</i>相册名称不能为空',
                    maxlength: '<i class="iconfont">&#xe64c;</i>相册名称不能超过20个字符',
                    remote: '<i class="iconfont">&#xe64c;</i>相册名称已存在'
                },
                aclass_des: {
                    maxlength: '<i class="iconfont">&#xe64c;</i>描述不能超过100个字符'
                },
                aclass_sort: {
                    digits: '<i class="iconfont">&#xe64c;</i>排序只能填写数字'
                }
            }
        });
    });
</script>