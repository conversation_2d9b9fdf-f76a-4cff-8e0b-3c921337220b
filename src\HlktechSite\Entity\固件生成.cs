using NewLife;

using System.ComponentModel;

using XCode;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>固件生成</summary>
[Serializable]
[DataObject]
[Description("固件生成")]
[BindIndex("IX_DG_GuJians_UId", false, "UId")]
[BindIndex("IX_DG_GuJians_UId_GtId", false, "UId,GtId")]
[BindTable("DG_GuJians", Description = "固件生成", ConnName = "DG", DbType = DatabaseType.None)]
public partial class GuJians
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _GtId;
    /// <summary>固件模块Id</summary>
    [DisplayName("固件模块Id")]
    [Description("固件模块Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("GtId", "固件模块Id", "")]
    public Int32 GtId { get => _GtId; set { if (OnPropertyChanging("GtId", value)) { _GtId = value; OnPropertyChanged("GtId"); } } }

    private Int32 _UId;
    /// <summary>用户Id</summary>
    [DisplayName("用户Id")]
    [Description("用户Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UId", "用户Id", "")]
    public Int32 UId { get => _UId; set { if (OnPropertyChanging("UId", value)) { _UId = value; OnPropertyChanged("UId"); } } }

    private Int16 _State;
    /// <summary>状态。0为未生成，1为生成中，2为已生成</summary>
    [DisplayName("状态")]
    [Description("状态。0为未生成，1为生成中，2为已生成")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("State", "状态。0为未生成，1为生成中，2为已生成", "")]
    public Int16 State { get => _State; set { if (OnPropertyChanging("State", value)) { _State = value; OnPropertyChanged("State"); } } }

    private DateTime _SendTime;
    /// <summary>数据被自动化固件服务器请求时间</summary>
    [DisplayName("数据被自动化固件服务器请求时间")]
    [Description("数据被自动化固件服务器请求时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("SendTime", "数据被自动化固件服务器请求时间", "")]
    public DateTime SendTime { get => _SendTime; set { if (OnPropertyChanging("SendTime", value)) { _SendTime = value; OnPropertyChanged("SendTime"); } } }

    private DateTime _ReceivedTime;
    /// <summary>接收任务完成的时间</summary>
    [DisplayName("接收任务完成的时间")]
    [Description("接收任务完成的时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("ReceivedTime", "接收任务完成的时间", "")]
    public DateTime ReceivedTime { get => _ReceivedTime; set { if (OnPropertyChanging("ReceivedTime", value)) { _ReceivedTime = value; OnPropertyChanged("ReceivedTime"); } } }

    private String _ReceivedInfo;
    /// <summary>接收数据内容</summary>
    [DisplayName("接收数据内容")]
    [Description("接收数据内容")]
    [DataObjectField(false, false, true, 1000)]
    [BindColumn("ReceivedInfo", "接收数据内容", "")]
    public String ReceivedInfo { get => _ReceivedInfo; set { if (OnPropertyChanging("ReceivedInfo", value)) { _ReceivedInfo = value; OnPropertyChanged("ReceivedInfo"); } } }

    private String _FilePath;
    /// <summary>固件下载地址</summary>
    [DisplayName("固件下载地址")]
    [Description("固件下载地址")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("FilePath", "固件下载地址", "")]
    public String FilePath { get => _FilePath; set { if (OnPropertyChanging("FilePath", value)) { _FilePath = value; OnPropertyChanged("FilePath"); } } }

    private String _Content;
    /// <summary>参数内容</summary>
    [DisplayName("参数内容")]
    [Description("参数内容")]
    [DataObjectField(false, false, true, 1000)]
    [BindColumn("Content", "参数内容", "")]
    public String Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String _Logo;
    /// <summary>固件Logo上传地址</summary>
    [DisplayName("固件Logo上传地址")]
    [Description("固件Logo上传地址")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Logo", "固件Logo上传地址", "")]
    public String Logo { get => _Logo; set { if (OnPropertyChanging("Logo", value)) { _Logo = value; OnPropertyChanged("Logo"); } } }

    private Int32 _LId;
    /// <summary>语言Id</summary>
    [DisplayName("语言Id")]
    [Description("语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object this[String name]
    {
        get
        {
            switch (name)
            {
                case "Id": return _Id;
                case "GtId": return _GtId;
                case "UId": return _UId;
                case "State": return _State;
                case "SendTime": return _SendTime;
                case "ReceivedTime": return _ReceivedTime;
                case "ReceivedInfo": return _ReceivedInfo;
                case "FilePath": return _FilePath;
                case "Content": return _Content;
                case "Logo": return _Logo;
                case "LId": return _LId;
                case "CreateUser": return _CreateUser;
                case "CreateUserID": return _CreateUserID;
                case "CreateTime": return _CreateTime;
                case "CreateIP": return _CreateIP;
                case "UpdateUser": return _UpdateUser;
                case "UpdateUserID": return _UpdateUserID;
                case "UpdateTime": return _UpdateTime;
                case "UpdateIP": return _UpdateIP;
                default: return base[name];
            }
        }
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "GtId": _GtId = value.ToInt(); break;
                case "UId": _UId = value.ToInt(); break;
                case "State": _State = Convert.ToInt16(value); break;
                case "SendTime": _SendTime = value.ToDateTime(); break;
                case "ReceivedTime": _ReceivedTime = value.ToDateTime(); break;
                case "ReceivedInfo": _ReceivedInfo = Convert.ToString(value); break;
                case "FilePath": _FilePath = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "Logo": _Logo = Convert.ToString(value); break;
                case "LId": _LId = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 字段名
    /// <summary>取得固件生成字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>固件模块Id</summary>
        public static readonly Field GtId = FindByName("GtId");

        /// <summary>用户Id</summary>
        public static readonly Field UId = FindByName("UId");

        /// <summary>状态。0为未生成，1为生成中，2为已生成</summary>
        public static readonly Field State = FindByName("State");

        /// <summary>数据被自动化固件服务器请求时间</summary>
        public static readonly Field SendTime = FindByName("SendTime");

        /// <summary>接收任务完成的时间</summary>
        public static readonly Field ReceivedTime = FindByName("ReceivedTime");

        /// <summary>接收数据内容</summary>
        public static readonly Field ReceivedInfo = FindByName("ReceivedInfo");

        /// <summary>固件下载地址</summary>
        public static readonly Field FilePath = FindByName("FilePath");

        /// <summary>参数内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>固件Logo上传地址</summary>
        public static readonly Field Logo = FindByName("Logo");

        /// <summary>语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得固件生成字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>固件模块Id</summary>
        public const String GtId = "GtId";

        /// <summary>用户Id</summary>
        public const String UId = "UId";

        /// <summary>状态。0为未生成，1为生成中，2为已生成</summary>
        public const String State = "State";

        /// <summary>数据被自动化固件服务器请求时间</summary>
        public const String SendTime = "SendTime";

        /// <summary>接收任务完成的时间</summary>
        public const String ReceivedTime = "ReceivedTime";

        /// <summary>接收数据内容</summary>
        public const String ReceivedInfo = "ReceivedInfo";

        /// <summary>固件下载地址</summary>
        public const String FilePath = "FilePath";

        /// <summary>参数内容</summary>
        public const String Content = "Content";

        /// <summary>固件Logo上传地址</summary>
        public const String Logo = "Logo";

        /// <summary>语言Id</summary>
        public const String LId = "LId";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}