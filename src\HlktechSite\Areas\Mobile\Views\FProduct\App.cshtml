﻿@model JumpProduct
@{
    var id = (Int32)ViewBag.Id;
    var modelJumpProductLan = JumpProductLan.FindByJIdAndLId(id, language.Id);
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover" />
    <title>@(modelJumpProductLan.Name) @T("APP下载") - @T("海凌科电子")</title>
    <meta name="description" content="海凌科下载中心为您提供WIFI模块,电源模块,蓝牙模块,人脸识别模块,语音识别模块,3G模块,4G模块,串口服务器,GPRS模块,工业级路由器等产品的资料下载" />
    <meta name="keywords" content="@(modelJumpProductLan.Name) @T("APP下载")" />
    <link href="~/css/weui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>
    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/lay/modules/jquery.js"></script>
    <script src="~/static/usercenter/pc/js/jquery-1.12.2.min.js"></script>
    <link href="~/iconfont/iconfont.css" rel="stylesheet" />
</head>
<body>
    <div style="text-align: center; margin-top: 7rem;">
        @if (!Model.AppLogo.IsNullOrWhiteSpace())
        {
            <img src="@CDN.GetCDN()/@Model?.AppLogo" style="max-width: 10rem;" />
        }
    </div>
    @if (language.UniqueSeoCode == "cn")
    {
        <div style="text-align: center; margin-top: 3rem; font-weight: bold; font-size: 1rem; letter-spacing: 0.5rem;">
            @modelJumpProductLan.AdWord
        </div>
    }
    else
    {
        <div style="text-align: center; margin-top: 3rem; font-weight: bold; font-size: 1rem;">
            @modelJumpProductLan.AdWord
        </div>
    }

    @if (@Model?.IosPaths.IsNullOrWhiteSpace() == false)
    {
        <div style="text-align: center; margin-top: 3rem;">
            <img src="~/images/苹果图标@2x.png" style="max-width: 3rem;" />
            <p style="margin-top: 1.2rem;">
                <a href="@Model?.IosPaths" style="background:url('/images/矩形********') no-repeat center center; background-size : contain; display: block; padding: 0.5rem;">@T("下载IOS版本")</a>
            </p>
        </div>
    }
    @if (@Model?.AndroidPaths.IsNullOrWhiteSpace() == false || @Model?.AndroidPaths1.IsNullOrWhiteSpace() == false)
    {
        <div style="text-align: center; margin-top: 3rem;">
            <img src="~/images/安卓图标@2x.png" style="max-width: 3rem;" />
            @if (language.UniqueSeoCode == "cn")
            {
                @if (@Model?.AndroidPaths.IsNullOrWhiteSpace() == false)
                {
                    <p style="margin-top: 1.2rem;">
                        <a href="@Model?.AndroidPaths" style="background:url('/images/矩形********') no-repeat center center; background-size : contain; display: block; padding: 0.5rem;">@T("下载Android国内版")</a>
                    </p>
                }
                @if (@Model?.AndroidPaths1.IsNullOrWhiteSpace() == false)
                {
                    <p style="margin-top: 1.2rem;">
                        <a href="@Model?.AndroidPaths1" style="background:url('/images/矩形********') no-repeat center center; background-size : contain; display: block; padding: 0.5rem;">@T("下载Android国外版")</a>
                    </p>
                }
            }
            else
            {
                @if (@Model?.AndroidPaths1.IsNullOrWhiteSpace() == false)
                {
                    <p style="margin-top: 1.2rem;">
                        <a href="@Model?.AndroidPaths1" style="background:url('/images/矩形********') no-repeat center center; background-size : contain; display: block; padding: 0.5rem;">@T("下载Android国外版")</a>
                    </p>
                }
                @if (@Model?.AndroidPaths.IsNullOrWhiteSpace() == false)
                {
                    <p style="margin-top: 1.2rem;">
                        <a href="@Model?.AndroidPaths" style="background:url('/images/矩形********') no-repeat center center; background-size : contain; display: block; padding: 0.5rem;">@T("下载Android国内版")</a>
                    </p>
                }
            }
        </div>
    }

</body>
</html>