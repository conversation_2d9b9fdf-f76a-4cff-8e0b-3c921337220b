﻿.btn-default, .btn-primary, .btn-success, .btn-info, .btn-warning, .btn-danger, .btn-strong {
    text-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

    .btn-default:active, .btn-primary:active, .btn-success:active, .btn-info:active, .btn-warning:active, .btn-danger:active, .btn-default.active, .btn-primary.active, .btn-success.active, .btn-info.active, .btn-warning.active, .btn-danger.active {
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    .btn-default.disabled, .btn-primary.disabled, .btn-success.disabled, .btn-info.disabled, .btn-warning.disabled, .btn-danger.disabled, .btn-default[disabled], .btn-primary[disabled], .btn-success[disabled], .btn-info[disabled], .btn-warning[disabled], .btn-danger[disabled], fieldset[disabled] .btn-default, fieldset[disabled] .btn-primary, fieldset[disabled] .btn-success, fieldset[disabled] .btn-info, fieldset[disabled] .btn-warning, fieldset[disabled] .btn-danger {
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    .btn-default .badge, .btn-primary .badge, .btn-success .badge, .btn-info .badge, .btn-warning .badge, .btn-danger .badge {
        text-shadow: none;
    }

.btn:active, .btn.active {
    background-image: none;
}

.btn-default {
    text-shadow: 0 1px 0 #fff;
    background-image: none;
    background-color: #fff;
    border-color: #d9d9d9;
    color: #262829;
}

    .btn-default:hover, .btn-default:focus {
        background-color: #fff;
        border-color: #00aaff;
        color: #00aaff;
    }

    .btn-default:active, .btn-default.active {
        background-color: #d6f1ff;
        border-color: #00aaff;
    }

    .btn-default.disabled, .btn-default[disabled], fieldset[disabled] .btn-default, .btn-default.disabled:hover, .btn-default[disabled]:hover, fieldset[disabled] .btn-default:hover, .btn-default.disabled:focus, .btn-default[disabled]:focus, fieldset[disabled] .btn-default:focus, .btn-default.disabled.focus, .btn-default[disabled].focus, fieldset[disabled] .btn-default.focus, .btn-default.disabled:active, .btn-default[disabled]:active, fieldset[disabled] .btn-default:active, .btn-default.disabled.active, .btn-default[disabled].active, fieldset[disabled] .btn-default.active {
        background-color: #f7f9fa;
        border-color: #e5e5e5;
        color: #ccc;
        cursor: not-allowed;
        background-image: none;
    }

.btn-success {
    background-image: -webkit-linear-gradient(top,#5cb85c 0%,#419641 100%);
    background-image: -o-linear-gradient(top,#5cb85c 0%,#419641 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#5cb85c),to(#419641));
    background-image: linear-gradient(to bottom,#5cb85c 0%,#419641 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5cb85c',endColorstr='#ff419641',GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    background-repeat: repeat-x;
    border-color: #3e8f3e;
}

    .btn-success:hover, .btn-success:focus {
        background-color: #419641;
        background-position: 0 -15px;
    }

    .btn-success:active, .btn-success.active {
        background-color: #419641;
        border-color: #3e8f3e;
    }

    .btn-success.disabled, .btn-success[disabled], fieldset[disabled] .btn-success, .btn-success.disabled:hover, .btn-success[disabled]:hover, fieldset[disabled] .btn-success:hover, .btn-success.disabled:focus, .btn-success[disabled]:focus, fieldset[disabled] .btn-success:focus, .btn-success.disabled.focus, .btn-success[disabled].focus, fieldset[disabled] .btn-success.focus, .btn-success.disabled:active, .btn-success[disabled]:active, fieldset[disabled] .btn-success:active, .btn-success.disabled.active, .btn-success[disabled].active, fieldset[disabled] .btn-success.active {
        background-color: #419641;
        background-image: none;
    }

.btn-info {
    background-image: -webkit-linear-gradient(top,#5bc0de 0%,#2aabd2 100%);
    background-image: -o-linear-gradient(top,#5bc0de 0%,#2aabd2 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#5bc0de),to(#2aabd2));
    background-image: linear-gradient(to bottom,#5bc0de 0%,#2aabd2 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5bc0de',endColorstr='#ff2aabd2',GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    background-repeat: repeat-x;
    border-color: #28a4c9;
}

    .btn-info:hover, .btn-info:focus {
        background-color: #2aabd2;
        background-position: 0 -15px;
    }

    .btn-info:active, .btn-info.active {
        background-color: #2aabd2;
        border-color: #28a4c9;
    }

    .btn-info.disabled, .btn-info[disabled], fieldset[disabled] .btn-info, .btn-info.disabled:hover, .btn-info[disabled]:hover, fieldset[disabled] .btn-info:hover, .btn-info.disabled:focus, .btn-info[disabled]:focus, fieldset[disabled] .btn-info:focus, .btn-info.disabled.focus, .btn-info[disabled].focus, fieldset[disabled] .btn-info.focus, .btn-info.disabled:active, .btn-info[disabled]:active, fieldset[disabled] .btn-info:active, .btn-info.disabled.active, .btn-info[disabled].active, fieldset[disabled] .btn-info.active {
        background-color: #2aabd2;
        background-image: none;
    }

.btn-warning {
    background-image: -webkit-linear-gradient(top,#f0ad4e 0%,#eb9316 100%);
    background-image: -o-linear-gradient(top,#f0ad4e 0%,#eb9316 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#f0ad4e),to(#eb9316));
    background-image: linear-gradient(to bottom,#f0ad4e 0%,#eb9316 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff0ad4e',endColorstr='#ffeb9316',GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    background-repeat: repeat-x;
    border-color: #e38d13;
}

    .btn-warning:hover, .btn-warning:focus {
        background-color: #eb9316;
        background-position: 0 -15px;
    }

    .btn-warning:active, .btn-warning.active {
        background-color: #eb9316;
        border-color: #e38d13;
    }

    .btn-warning.disabled, .btn-warning[disabled], fieldset[disabled] .btn-warning, .btn-warning.disabled:hover, .btn-warning[disabled]:hover, fieldset[disabled] .btn-warning:hover, .btn-warning.disabled:focus, .btn-warning[disabled]:focus, fieldset[disabled] .btn-warning:focus, .btn-warning.disabled.focus, .btn-warning[disabled].focus, fieldset[disabled] .btn-warning.focus, .btn-warning.disabled:active, .btn-warning[disabled]:active, fieldset[disabled] .btn-warning:active, .btn-warning.disabled.active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning.active {
        background-color: #eb9316;
        background-image: none;
    }

.btn-danger {
    background-image: -webkit-linear-gradient(top,#d9534f 0%,#c12e2a 100%);
    background-image: -o-linear-gradient(top,#d9534f 0%,#c12e2a 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#d9534f),to(#c12e2a));
    background-image: linear-gradient(to bottom,#d9534f 0%,#c12e2a 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9534f',endColorstr='#ffc12e2a',GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    background-repeat: repeat-x;
    border-color: #b92c28;
}

    .btn-danger:hover, .btn-danger:focus {
        background-color: #c12e2a;
        background-position: 0 -15px;
    }

    .btn-danger:active, .btn-danger.active {
        background-color: #c12e2a;
        border-color: #b92c28;
    }

    .btn-danger.disabled, .btn-danger[disabled], fieldset[disabled] .btn-danger, .btn-danger.disabled:hover, .btn-danger[disabled]:hover, fieldset[disabled] .btn-danger:hover, .btn-danger.disabled:focus, .btn-danger[disabled]:focus, fieldset[disabled] .btn-danger:focus, .btn-danger.disabled.focus, .btn-danger[disabled].focus, fieldset[disabled] .btn-danger.focus, .btn-danger.disabled:active, .btn-danger[disabled]:active, fieldset[disabled] .btn-danger:active, .btn-danger.disabled.active, .btn-danger[disabled].active, fieldset[disabled] .btn-danger.active {
        background-color: #c12e2a;
        background-image: none;
    }

.thumbnail, .img-thumbnail {
    -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.075);
    box-shadow: 0 1px 2px rgba(0,0,0,.075);
}

.dropdown-menu {
    min-width: 160px;
    padding: 0;
    margin-top: 0;
    border-radius: 0 0 2px 2px;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -o-box-shadow: none;
    -ms-box-shadow: none;
    box-shadow: none;
}

.btn-group {
    overflow: hidden;
}

    .btn-group.open {
        overflow: visible;
    }

        .btn-group.open .btn-select {
            color: #00aaff;
            border-color: #00aaff;
        }

.server-location-select.open .btn-select {
    border-color: #fff;
}

.btn-group.open .dropdown-menu {
    margin-top: 4px;
    max-height: 500px;
    padding: 8px 0;
    border: 1px solid #d9d9d9;
    box-shadow: 0 2px 6px rgba(0,0,0,.12);
    overflow-x: hidden;
    overflow-y: auto;
}

.btn-group.open .dropdown-toggle {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -o-box-shadow: none;
    -ms-box-shadow: none;
    box-shadow: none;
}

.dropdown-menu > li > a {
    padding: 0 8px 0 16px;
    font-size: 12px;
    line-height: 32px;
}

.dropdown-hover-tips {
    position: relative;
    top: 0;
    right: 0;
}

.dropdown-menu > li > a:hover {
    background-color: transparent;
    color: #00aaff;
}

.dropdown-menu > li > a:focus, .dropdown-menu > li > a.ny-drop-active {
    color: #00aaff;
}

.dropdown-menu > li > a.disabled {
    color: #ccc;
}

    .dropdown-menu > li > a.disabled:hover {
        cursor: not-allowed;
        background: #fff;
        color: #ccc;
    }

    .dropdown-menu > li > a.disabled + .op-btn-cover, .op-btn-wrap > a.disabled + .op-btn-cover {
        z-index: 99;
    }

.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
    background-color: #2e6da4;
    background-image: -webkit-linear-gradient(top,#337ab7 0%,#2e6da4 100%);
    background-image: -o-linear-gradient(top,#337ab7 0%,#2e6da4 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#337ab7),to(#2e6da4));
    background-image: linear-gradient(to bottom,#337ab7 0%,#2e6da4 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7',endColorstr='#ff2e6da4',GradientType=0);
    background-repeat: repeat-x;
}

.navbar-default {
    background-image: -webkit-linear-gradient(top,#fff 0%,#f8f8f8 100%);
    background-image: -o-linear-gradient(top,#fff 0%,#f8f8f8 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#fff),to(#f8f8f8));
    background-image: linear-gradient(to bottom,#fff 0%,#f8f8f8 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff',endColorstr='#fff8f8f8',GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    background-repeat: repeat-x;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.15),0 1px 5px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 0 rgba(255,255,255,.15),0 1px 5px rgba(0,0,0,.075);
}

    .navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .active > a {
        background-image: -webkit-linear-gradient(top,#dbdbdb 0%,#e2e2e2 100%);
        background-image: -o-linear-gradient(top,#dbdbdb 0%,#e2e2e2 100%);
        background-image: -webkit-gradient(linear,left top,left bottom,from(#dbdbdb),to(#e2e2e2));
        background-image: linear-gradient(to bottom,#dbdbdb 0%,#e2e2e2 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdbdbdb',endColorstr='#ffe2e2e2',GradientType=0);
        background-repeat: repeat-x;
        -webkit-box-shadow: inset 0 3px 9px rgba(0,0,0,.075);
        box-shadow: inset 0 3px 9px rgba(0,0,0,.075);
    }

.navbar-brand, .navbar-nav > li > a {
    text-shadow: 0 1px 0 rgba(255,255,255,.25);
}

.navbar-inverse {
    background-image: -webkit-linear-gradient(top,#3c3c3c 0%,#222 100%);
    background-image: -o-linear-gradient(top,#3c3c3c 0%,#222 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#3c3c3c),to(#222));
    background-image: linear-gradient(to bottom,#3c3c3c 0%,#222 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff3c3c3c',endColorstr='#ff222222',GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    background-repeat: repeat-x;
    border-radius: 4px;
}

    .navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .active > a {
        background-image: -webkit-linear-gradient(top,#080808 0%,#0f0f0f 100%);
        background-image: -o-linear-gradient(top,#080808 0%,#0f0f0f 100%);
        background-image: -webkit-gradient(linear,left top,left bottom,from(#080808),to(#0f0f0f));
        background-image: linear-gradient(to bottom,#080808 0%,#0f0f0f 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff080808',endColorstr='#ff0f0f0f',GradientType=0);
        background-repeat: repeat-x;
        -webkit-box-shadow: inset 0 3px 9px rgba(0,0,0,.25);
        box-shadow: inset 0 3px 9px rgba(0,0,0,.25);
    }

    .navbar-inverse .navbar-brand, .navbar-inverse .navbar-nav > li > a {
        text-shadow: 0 -1px 0 rgba(0,0,0,.25);
    }

.navbar-static-top, .navbar-fixed-top, .navbar-fixed-bottom {
    border-radius: 0;
}

@media (max-width:767px) {
    .navbar .navbar-nav .open .dropdown-menu > .active > a, .navbar .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #fff;
        background-image: -webkit-linear-gradient(top,#337ab7 0%,#2e6da4 100%);
        background-image: -o-linear-gradient(top,#337ab7 0%,#2e6da4 100%);
        background-image: -webkit-gradient(linear,left top,left bottom,from(#337ab7),to(#2e6da4));
        background-image: linear-gradient(to bottom,#337ab7 0%,#2e6da4 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7',endColorstr='#ff2e6da4',GradientType=0);
        background-repeat: repeat-x;
    }
}

.alert {
    text-shadow: 0 1px 0 rgba(255,255,255,.2);
    -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.25),0 1px 2px rgba(0,0,0,.05);
    box-shadow: inset 0 1px 0 rgba(255,255,255,.25),0 1px 2px rgba(0,0,0,.05);
}

.alert-info {
    background-image: -webkit-linear-gradient(top,#d9edf7 0%,#b9def0 100%);
    background-image: -o-linear-gradient(top,#d9edf7 0%,#b9def0 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#d9edf7),to(#b9def0));
    background-image: linear-gradient(to bottom,#d9edf7 0%,#b9def0 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9edf7',endColorstr='#ffb9def0',GradientType=0);
    background-repeat: repeat-x;
    border-color: #9acfea;
}

.alert-warning {
    background-image: -webkit-linear-gradient(top,#fcf8e3 0%,#f8efc0 100%);
    background-image: -o-linear-gradient(top,#fcf8e3 0%,#f8efc0 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#fcf8e3),to(#f8efc0));
    background-image: linear-gradient(to bottom,#fcf8e3 0%,#f8efc0 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffcf8e3',endColorstr='#fff8efc0',GradientType=0);
    background-repeat: repeat-x;
    border-color: #f5e79e;
}

.alert-danger {
    background-image: -webkit-linear-gradient(top,#f2dede 0%,#e7c3c3 100%);
    background-image: -o-linear-gradient(top,#f2dede 0%,#e7c3c3 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#f2dede),to(#e7c3c3));
    background-image: linear-gradient(to bottom,#f2dede 0%,#e7c3c3 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff2dede',endColorstr='#ffe7c3c3',GradientType=0);
    background-repeat: repeat-x;
    border-color: #dca7a7;
}

.progress {
    background-image: -webkit-linear-gradient(top,#ebebeb 0%,#f5f5f5 100%);
    background-image: -o-linear-gradient(top,#ebebeb 0%,#f5f5f5 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#ebebeb),to(#f5f5f5));
    background-image: linear-gradient(to bottom,#ebebeb 0%,#f5f5f5 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffebebeb',endColorstr='#fff5f5f5',GradientType=0);
    background-repeat: repeat-x;
}

.progress-bar {
    background-image: -webkit-linear-gradient(top,#337ab7 0%,#286090 100%);
    background-image: -o-linear-gradient(top,#337ab7 0%,#286090 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#337ab7),to(#286090));
    background-image: linear-gradient(to bottom,#337ab7 0%,#286090 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7',endColorstr='#ff286090',GradientType=0);
    background-repeat: repeat-x;
}

.progress-bar-success {
    background-image: -webkit-linear-gradient(top,#5cb85c 0%,#449d44 100%);
    background-image: -o-linear-gradient(top,#5cb85c 0%,#449d44 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#5cb85c),to(#449d44));
    background-image: linear-gradient(to bottom,#5cb85c 0%,#449d44 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5cb85c',endColorstr='#ff449d44',GradientType=0);
    background-repeat: repeat-x;
}

.progress-bar-info {
    background-image: -webkit-linear-gradient(top,#5bc0de 0%,#31b0d5 100%);
    background-image: -o-linear-gradient(top,#5bc0de 0%,#31b0d5 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#5bc0de),to(#31b0d5));
    background-image: linear-gradient(to bottom,#5bc0de 0%,#31b0d5 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5bc0de',endColorstr='#ff31b0d5',GradientType=0);
    background-repeat: repeat-x;
}

.progress-bar-warning {
    background-image: -webkit-linear-gradient(top,#f0ad4e 0%,#ec971f 100%);
    background-image: -o-linear-gradient(top,#f0ad4e 0%,#ec971f 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#f0ad4e),to(#ec971f));
    background-image: linear-gradient(to bottom,#f0ad4e 0%,#ec971f 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff0ad4e',endColorstr='#ffec971f',GradientType=0);
    background-repeat: repeat-x;
}

.progress-bar-danger {
    background-image: -webkit-linear-gradient(top,#d9534f 0%,#c9302c 100%);
    background-image: -o-linear-gradient(top,#d9534f 0%,#c9302c 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#d9534f),to(#c9302c));
    background-image: linear-gradient(to bottom,#d9534f 0%,#c9302c 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9534f',endColorstr='#ffc9302c',GradientType=0);
    background-repeat: repeat-x;
}

.progress-bar-striped {
    background-image: -webkit-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
    background-image: -o-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
    background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
}

.list-group {
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.075);
    box-shadow: 0 1px 2px rgba(0,0,0,.075);
}

.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
    text-shadow: 0 -1px 0 #286090;
    background-image: -webkit-linear-gradient(top,#337ab7 0%,#2b669a 100%);
    background-image: -o-linear-gradient(top,#337ab7 0%,#2b669a 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#337ab7),to(#2b669a));
    background-image: linear-gradient(to bottom,#337ab7 0%,#2b669a 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7',endColorstr='#ff2b669a',GradientType=0);
    background-repeat: repeat-x;
    border-color: #2b669a;
}

    .list-group-item.active .badge, .list-group-item.active:hover .badge, .list-group-item.active:focus .badge {
        text-shadow: none;
    }

.panel {
    margin-bottom: 31px;
    border: 1px solid #ddd;
    border-radius: 1px;
    background-color: #fafdff;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.panel-default > .panel-heading {
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    padding: 16px 29px;
    background: #fff;
}

.panel-primary > .panel-heading {
    background-image: -webkit-linear-gradient(top,#337ab7 0%,#2e6da4 100%);
    background-image: -o-linear-gradient(top,#337ab7 0%,#2e6da4 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#337ab7),to(#2e6da4));
    background-image: linear-gradient(to bottom,#337ab7 0%,#2e6da4 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7',endColorstr='#ff2e6da4',GradientType=0);
    background-repeat: repeat-x;
}

.panel-success > .panel-heading {
    background-image: -webkit-linear-gradient(top,#dff0d8 0%,#d0e9c6 100%);
    background-image: -o-linear-gradient(top,#dff0d8 0%,#d0e9c6 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#dff0d8),to(#d0e9c6));
    background-image: linear-gradient(to bottom,#dff0d8 0%,#d0e9c6 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdff0d8',endColorstr='#ffd0e9c6',GradientType=0);
    background-repeat: repeat-x;
}

.panel-info > .panel-heading {
    background-image: -webkit-linear-gradient(top,#d9edf7 0%,#c4e3f3 100%);
    background-image: -o-linear-gradient(top,#d9edf7 0%,#c4e3f3 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#d9edf7),to(#c4e3f3));
    background-image: linear-gradient(to bottom,#d9edf7 0%,#c4e3f3 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9edf7',endColorstr='#ffc4e3f3',GradientType=0);
    background-repeat: repeat-x;
}

.panel-warning > .panel-heading {
    background-image: -webkit-linear-gradient(top,#fcf8e3 0%,#faf2cc 100%);
    background-image: -o-linear-gradient(top,#fcf8e3 0%,#faf2cc 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#fcf8e3),to(#faf2cc));
    background-image: linear-gradient(to bottom,#fcf8e3 0%,#faf2cc 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffcf8e3',endColorstr='#fffaf2cc',GradientType=0);
    background-repeat: repeat-x;
}

.panel-danger > .panel-heading {
    background-image: -webkit-linear-gradient(top,#f2dede 0%,#ebcccc 100%);
    background-image: -o-linear-gradient(top,#f2dede 0%,#ebcccc 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#f2dede),to(#ebcccc));
    background-image: linear-gradient(to bottom,#f2dede 0%,#ebcccc 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff2dede',endColorstr='#ffebcccc',GradientType=0);
    background-repeat: repeat-x;
}

.well {
    background-image: -webkit-linear-gradient(top,#e8e8e8 0%,#f5f5f5 100%);
    background-image: -o-linear-gradient(top,#e8e8e8 0%,#f5f5f5 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#e8e8e8),to(#f5f5f5));
    background-image: linear-gradient(to bottom,#e8e8e8 0%,#f5f5f5 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe8e8e8',endColorstr='#fff5f5f5',GradientType=0);
    background-repeat: repeat-x;
    border-color: #dcdcdc;
    -webkit-box-shadow: inset 0 1px 3px rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.1);
    box-shadow: inset 0 1px 3px rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.1);
}

body {
    font-family: "microsoft yahei";
}

a {
    color: #00aaff;
}

    a:hover, a:focus {
        color: #006fbc;
        text-decoration: none;
    }

    a:focus {
        outline: none;
    }

h1, h2, h3, h4, h5, h6 {
    margin: 0;
}

h3 {
    color: #004a7d;
    font-size: 18px;
    font-weight: bold;
}

.link:hover {
    text-decoration: underline;
}

label {
    font-weight: normal;
}

.btn {
    border: none;
}

    .btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn.active.focus {
        outline: none;
    }

    .btn:hover, .btn:focus, .btn.focus {
        color: #fff;
    }

    .btn:active, .btn.active {
        background-image: none;
        outline: none;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

.badge {
    background-color: #004a7d;
    vertical-align: baseline;
}

.ny-form {
}

    .ny-form .form-group {
        margin-bottom: 24px;
        line-height: 30px;
    }

    .ny-form .ny-control-label {
        float: left;
    }

.ny-control-label {
    width: 135px;
    padding-right: 0;
    text-align: right;
    color: #666;
}

.ny-label-long {
    width: 200px;
}

.ny-label-darken {
    color: #5f5f5f;
}

.ny-form-control {
    display: inline-block;
    white-space: nowrap;
    color: #333;
    background-color: #fff;
    background-image: none;
    outline: none;
}

    .ny-form-control .ny-long-input {
        width: 440px;
    }

    .ny-form-control .ny-middle-input {
        width: 280px;
    }

    .ny-form-control input[text], .ny-form-control input[password] {
        height: 32px;
    }

    .ny-form-control textarea {
        height: auto;
        padding-top: 2px;
        line-height: 2em;
    }

select.ny-input-reset {
    min-width: 148px;
    height: 32px;
    padding: 4px 0;
}

.error-reminder, .input-reminder {
    display: block;
    line-height: 2em;
}

.error-reminder, .necessary-mark {
    color: red;
}

.input-reminder {
    color: #999;
}

input.error-input, input.error-input:focus {
    border-color: red;
    background-color: #fff5f5;
}

.ny-input-reset {
    display: inline-block;
    height: 32px;
    padding-top: 0;
    padding-left: 10px;
    padding-bottom: 0;
    border: 1px solid #d9dcde;
    line-height: inherit;
    outline: none;
}

    .ny-input-reset, .ny-input-reset:active, .ny-input-reset:focus, .ny-input-reset:active:focus {
        outline: none;
        -webkit-box-shadow: none;
        box-shadow: none;
        color: inherit;
    }

        .ny-input-reset[readonly], .ny-input-reset[readonly]:focus {
            background: #F7F7F7;
            border-color: #d9dcde;
        }

        .ny-input-reset:focus {
            border-color: #00aaff;
        }

.ny-input-group {
    position: relative;
}

    .ny-input-group .ny-input-reset {
        position: relative;
        z-index: 10;
    }

.ny-input-reset.input-calendar {
    width: 240px;
    height: 32px;
    line-height: 32px;
    background: url(img/uc/icon_calendar.png) no-repeat 218px center;
}

.ny-input-addon {
    display: inline-block;
    border: 1px solid #ddd;
    padding: 0 10px;
    background-color: #f7f7f7;
    font-weight: 400;
    text-align: center;
    color: #4d4d4d;
    vertical-align: top;
    line-height: inherit;
}

.ny-input-group .ny-input-addon {
    position: relative;
    z-index: 1;
    margin-left: -5px;
}

.ny-th-title, .ny-th-title-sm {
    display: inline-block;
    width: 105px;
    margin-right: 15px;
    text-align: right;
}

.ny-th-title-sm {
    width: 80px;
}

.ny-td-multi-lines {
    padding-top: 16px;
    padding-bottom: 16px;
    line-height: 2em;
}

    .ny-td-multi-lines > div {
        line-height: 1.7em;
        font-size: 12px;
    }

td .list-empty-tips {
    display: inline-block;
    padding: 40px 0 40px 52px;
    background-position: left center;
}

.protect-block .form-group .ny-btn {
    min-width: 96px;
}

.protect-block .form-group .send-captcha {
    height: 25px;
    line-height: 25px;
}

.text-primary, .text-primary:hover, .text-primary:active, .text-primary:focus {
    color: #00aaff;
}

.text-muted {
    color: #a0a2a3;
}

.text-operate, .text-stress, .text-orange {
    color: #ff6600;
}

.text-fail, .text-red, .text-unfinished, .text-danger {
    color: #ff3300;
}

.text-success, .text-green, .text-finished {
    color: #2bbe22;
}

.ny-btn {
    display: inline-block;
    height: 32px;
    line-height: 31px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    padding: 0 16px;
    font-size: 12px;
    color: #262829;
    cursor: pointer;
    text-align: center;
}

    .ny-btn.align-left {
        text-align: left;
    }

    .ny-btn.ny-btn-middle {
        width: 130px;
    }

    .ny-btn:hover, .ny-btn:focus {
        text-decoration: none;
        background-color: #fff;
        border-color: #00aaff;
        color: #00aaff;
    }

    .ny-btn:active, .ny-btn.active {
        background-color: #d6f1ff;
        border-color: #00aaff;
    }

.ny-form .ny-btn {
    font-size: 12px;
}

.btn-strong {
    height: 32px;
    line-height: 31px;
    border: 1px solid #eb5e00;
    padding: 0 16px;
    background-color: #ff6600;
    outline: none;
    color: #fff;
}

    .btn-strong.btn-strong-high {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
    }

    .btn-strong:hover, .btn-strong:focus {
        text-decoration: none;
        background-color: #ff7d26;
        border-color: #ff6f0f;
        color: #fff;
    }

    .btn-strong:active {
        background-color: #f26a00;
        border-color: #e05a00;
    }

    .btn-strong.disabled, .btn-strong[disabled], fieldset[disabled] .btn-strong, .btn-strong.disabled:hover, .btn-strong[disabled]:hover, fieldset[disabled] .btn-strong:hover, .btn-strong.disabled:focus, .btn-strong[disabled]:focus, fieldset[disabled] .btn-strong:focus, .btn-strong.disabled.focus, .btn-strong[disabled].focus, fieldset[disabled] .btn-strong.focus, .btn-strong.disabled:active, .btn-strong[disabled]:active, fieldset[disabled] .btn-strong:active, .btn-strong.disabled.active, .btn-strong[disabled].active, fieldset[disabled] .btn-strong.active {
        background-color: #f7f9fa;
        border-color: #e5e5e5;
        color: #ccc;
        cursor: not-allowed;
    }

.btn-muted, .btn-muted:focus {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #d9d9d9;
    background-color: #f7f7f7;
    line-height: 31px;
    color: #262829;
    outline: none;
}

    .btn-muted:hover {
        border-color: #00aaff;
        background-color: #fff;
        text-decoration: none;
        color: #00aaff;
    }

    .btn-muted:active {
        background-color: #d6f1ff;
        border-color: #00aaff;
        color: #262829;
    }

    .btn-muted.disabled, .btn-muted[disabled], fieldset[disabled] .btn-muted, .btn-muted.disabled:hover, .btn-muted[disabled]:hover, fieldset[disabled] .btn-muted:hover, .btn-muted.disabled:focus, .btn-muted[disabled]:focus, fieldset[disabled] .btn-muted:focus, .btn-muted.disabled.focus, .btn-muted[disabled].focus, fieldset[disabled] .btn-muted.focus, .btn-muted.disabled:active, .btn-muted[disabled]:active, fieldset[disabled] .btn-muted:active, .btn-muted.disabled.active, .btn-muted[disabled].active, fieldset[disabled] .btn-muted.active {
        border-color: #e5e5e5;
        background-color: #f7f9fa;
        color: #ccc;
        cursor: not-allowed;
    }

.btn-select {
    position: relative;
    padding-left: 10px;
    padding-right: 29px;
    min-width: 160px;
    height: 32px;
    background-color: #fff;
    border: 1px solid #d9dcde;
    color: #4d4d4d;
    font-size: 12px;
    line-height: 32px;
    text-align: left;
}

.btn-group, .btn-group-vertical {
    vertical-align: bottom;
}

.select-with-icon .btn-select, .select-with-icon .dropdown-menu > li > a {
    padding-left: 32px;
    background-position: 10px center;
    background-repeat: no-repeat;
}

.select-with-icon .btn-select {
    border-color: transparent;
}

.btn-group.open .btn-select, .btn-select:hover, .btn-select:active, .btn-select:active:focus {
    color: #00aaff;
    text-decoration: none;
    background-color: #fff;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}

.btn-select.disabled, .btn-select[disabled], fieldset[disabled] .btn-select, .btn-select.disabled:hover, .btn-select[disabled]:hover, fieldset[disabled] .btn-select:hover, .btn-select.disabled:focus, .btn-select[disabled]:focus, fieldset[disabled] .btn-select:focus, .btn-select.disabled.focus, .btn-select[disabled].focus, fieldset[disabled] .btn-select.focus, .btn-select.disabled:active, .btn-select[disabled]:active, fieldset[disabled] .btn-select:active, .btn-select.disabled.active, .btn-select[disabled].active, fieldset[disabled] .btn-select.active {
    color: #eee;
    cursor: not-allowed;
}

.btn-select .ny-caret {
    position: absolute;
    top: 50%;
    right: 5%;
    margin-top: -2px;
}

.btn-group.open .btn-select .ny-caret, .btn-group.open .btn-select-muted .ny-caret, .btn-select:active .ny-caret, .btn-select-muted:active .ny-caret, .btn-select:active:focus .ny-caret, .btn-select-muted:active:focus .ny-caret {
    content: "";
    border-top: 0;
    border-bottom: 5px dashed;
    border-bottom: 5px solid\9
}

.dropdown-with-search .drop-outer {
    display: none;
}

.dropdown-with-search.open .drop-outer {
    position: absolute;
    display: block;
    border: 1px solid #00aaff;
    border-top: none;
    border-bottom: none;
    background-color: #fff;
    width: 100%;
    z-index: 100;
}

.dropdown-with-search .btn-select {
    min-width: 280px;
}

.dropdown-with-search .ny-input-reset {
    display: block;
    width: 95%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
}

.dropdown-with-search .dropdown-menu {
    left: -1px;
}

.btn-group-short .btn-select, .btn-group-short .dropdown-menu {
    min-width: 120px;
}

.btn-group-mini .btn-select {
    height: 20px;
    line-height: 20px;
    min-width: 45px;
    padding-left: 0;
}

.btn-group-mini .btn-select {
    padding-right: 9px;
}

.btn-group-mini .dropdown-menu {
    min-width: 70px;
    padding-left: 0;
}

.btn-group-mini .btn-select, .btn-group-mini.open .btn-select, .btn-group-mini .btn-select:hover, .btn-group-mini .btn-select:active, .btn-group-mini .btn-select:active:focus {
    border-color: transparent;
    background-color: transparent;
}

.btn-group-mini .dropdown-toggle .selected-content {
    font-size: 12px;
    color: #00aaff;
}

.btn-success {
    height: 28px;
    padding: 0 16px;
    background: #10d12c;
    border: none;
    line-height: 28px;
    color: #fff;
}

    .btn-success:hover, .btn-success:focus {
        color: #fff;
        text-decoration: none;
        background-color: #08b922;
        outline: none;
    }

    .btn-success:active {
        color: #fff;
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#08a51f',endColorstr='#0acf27');
        background: -webkit-gradient(linear,0% 0%,0% 100%,from(#08a51f),to(#0acf27));
        background: -moz-linear-gradient(top,#08a51f,#0acf27);
        background: -ms-linear-gradient(top,#08a51f,#0acf27);
        background: linear-gradient(top,#08a51f,#0acf27);
    }

    .btn-success.disabled, .btn-success[disabled], fieldset[disabled] .btn-success, .btn-success.disabled:hover, .btn-success[disabled]:hover, fieldset[disabled] .btn-success:hover, .btn-success.disabled:focus, .btn-success[disabled]:focus, fieldset[disabled] .btn-success:focus, .btn-success.disabled.focus, .btn-success[disabled].focus, fieldset[disabled] .btn-success.focus, .btn-success.disabled:active, .btn-success[disabled]:active, fieldset[disabled] .btn-success:active, .btn-success.disabled.active, .btn-success[disabled].active, fieldset[disabled] .btn-success.active {
        background: #66ed7d;
        cursor: not-allowed;
    }

.btn-primary {
    position: relative;
    height: 32px;
    padding: 0 16px;
    background-color: #00aaff;
    border: 1px solid #009ced;
    line-height: 31px;
    color: #fff;
}

    .btn-primary:hover, .btn-primary:focus {
        text-decoration: none;
        border-color: #0fafff;
        background-color: #26b7ff;
        color: #fff;
        outline: none;
    }

        .btn-primary:focus:active, .btn-primary:active {
            background-color: #00a0f0;
            border-color: #0092db;
            color: #fff;
        }

    .btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled.focus, .btn-primary[disabled].focus, fieldset[disabled] .btn-primary.focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, button.btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary.active {
        background-color: #f7f9fa;
        border-color: #e5e5e5;
        color: #ccc;
        cursor: not-allowed;
    }

.btn-reverse {
    border: 1px solid #00aaff;
    background-color: #fff;
    color: #00aaff;
}

    .btn-reverse:focus, .btn-reverse:hover {
        background-color: #00aaff;
        border-color: #009ced;
        color: #fff;
    }

.ny-form-control .btn-reverse {
    height: 32px;
    line-height: 30px;
    font-size: 12px;
}

.btn-reverse:focus:active, .btn-reverse:active {
    background-color: #00a0f0;
    border-color: #0092db;
    color: #fff;
    filter: none;
}

.btn-reverse.disabled, .btn-reverse[disabled], .ny-btn-group .btn-reverse.disabled, .ny-btn-group .btn-reverse[disabled], .ny-btn-group .btn-reverse.disabled:hover, .ny-btn-group .btn-reverse[disabled]:hover, fieldset[disabled] .btn-reverse, .btn-reverse.disabled:hover, .btn-reverse[disabled]:hover, fieldset[disabled] .btn-reverse:hover, .btn-reverse.disabled:focus, .btn-reverse[disabled]:focus, fieldset[disabled] .btn-reverse:focus, .btn-reverse.disabled.focus, .btn-reverse[disabled].focus, fieldset[disabled] .btn-reverse.focus, .btn-reverse.disabled:active, .btn-reverse[disabled]:active, fieldset[disabled] .btn-reverse:active, .btn-reverse.disabled.active, .btn-reverse[disabled].active, fieldset[disabled] .btn-reverse.active {
    background-color: #f7f9fa;
    border-color: #e5e5e5;
    color: #ccc;
    cursor: not-allowed;
}

.ny-btn-group {
    display: inline-block;
    border-radius: 2px;
}

    .ny-btn-group .btn-reverse {
        position: relative;
        min-width: 106px;
        margin-left: -5px;
        border-radius: 0;
        border-color: #ddd;
        color: #4d4d4d;
        font-size: 12px;
        text-align: center;
    }

    .ny-btn-group .ny-btn {
        margin-right: 10px;
    }

    .ny-btn-group .btn-reverse:hover {
        color: #00aaff;
        background: #fff;
    }

    .ny-btn-group .btn-reverse:first-child {
        margin-left: 0;
        border-radius: 2px 0 0 2px;
    }

    .ny-btn-group .btn-reverse:last-child {
        border-radius: 0 2px 2px 0;
    }

    .ny-btn-group .ny-tab-selected.btn-reverse {
        z-index: 10;
        border-color: #00aaff;
        background-color: #e5f8ff;
        color: #00aaff;
    }

    .ny-btn-group .ny-tab-selected.ny-btn {
        position: relative;
        border-color: #00aaff;
        background: #fff;
        color: #00aaff;
    }

        .ny-btn-group .ny-tab-selected.ny-btn:hover {
            background: #fff;
        }

        .ny-btn-group .ny-tab-selected.ny-btn:after {
            content: "";
            position: absolute;
            right: 0;
            bottom: 0;
            z-index: 10;
            width: 20px;
            height: 20px;
            background: url(img/uc/tab_selected.png);
        }

    .ny-btn-group .ny-tab-selected:hover {
        background-color: #e5f8ff;
    }

.ny-tabs-container {
    border-bottom: 1px solid #eee;
}

.ny-tab-title {
    line-height: 39px;
    padding: 0 10px;
    font-size: 14px;
}

.ny-tab {
    float: left;
    width: 132px;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 2px 2px 0 0;
    margin-bottom: -1px;
    margin-right: 10px;
    background-color: #fafafa;
    font-size: 14px;
}

    .ny-tab a {
        display: block;
        height: 100%;
        line-height: 39px;
        text-decoration: none;
        color: #4d4d4d;
    }

        .ny-tab a:hover {
            background-color: #f3f3f3;
            text-decoration: none;
            color: inherit;
        }

.ny-tab--current {
    border-top: 3px solid #00aaff;
    border-bottom-color: #fff;
    line-height: 37px;
}

    .ny-tab--current a, .ny-tab--current a:hover {
        background-color: #fff;
        color: #00aaff;
    }

.ny-search-group {
    margin-left: 8px;
}

.btn-select-muted {
    position: relative;
    padding-left: 16px;
    padding-right: 25px;
    min-width: 90px;
    height: 32px;
    background-color: #fff;
    border: 1px solid #d6d6d6;
    border-right: none;
    color: #262829;
    font-size: 12px;
    line-height: 31px;
    text-align: left;
}

.ny-search-group .btn-select-muted:after {
    position: absolute;
    top: 8px;
    right: 0;
    content: '';
    width: 1px;
    height: 16px;
    background-color: #d4d7d9;
}

.select-with-icon .btn-select-muted, .select-with-icon .dropdown-menu > li > a {
    padding-left: 32px;
}

.select-with-icon .btn-select-muted {
    background: no-repeat 10px center;
}

.btn-group.open .btn-select-muted, .btn-select-muted:hover, .btn-select-muted:active, .btn-select-muted:focus, .btn-select-muted:active:focus {
    color: #00aaff;
    text-decoration: none;
    background-color: #fff;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
    border-color: #d6d6d6;
}

.ny-search-group .btn-select-muted:hover, .ny-search-group .btn-select-muted:active, .ny-search-group .btn-select-muted:active:focus {
    border: 1px solid #ccc;
    border-right: none;
}

.btn-select-muted.disabled, .btn-select-muted[disabled], fieldset[disabled] .btn-select-muted, .btn-select-muted.disabled:hover, .btn-select-muted[disabled]:hover, fieldset[disabled] .btn-select-muted:hover, .btn-select-muted.disabled:focus, .btn-select-muted[disabled]:focus, fieldset[disabled] .btn-select-muted:focus, .btn-select-muted.disabled.focus, .btn-select-muted[disabled].focus, fieldset[disabled] .btn-select-muted.focus, .btn-select-muted.disabled:active, .btn-select-muted[disabled]:active, fieldset[disabled] .btn-select-muted:active, .btn-select-muted.disabled.active, .btn-select-muted[disabled].active, fieldset[disabled] .btn-select-muted.active {
    color: #eee;
    cursor: not-allowed;
}

.btn-select-muted .ny-caret {
    position: absolute;
    top: 50%;
    right: 10%;
    margin-top: -2px;
}

.ny-search-dropdown {
    min-width: 90px;
}

.search-group-input-wrapper {
    position: relative;
}

    .search-group-input-wrapper:before {
        display: block;
        content: " ";
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -8px;
        height: 16px;
        width: 1px;
        background: #d6d6d6;
    }

.input-clear-icon {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    width: 32px;
    height: 32px;
    background: url(img/uc/search_clear.png?v=1) no-repeat center;
    cursor: pointer;
}

    .input-clear-icon.active {
        display: block;
    }

    .input-clear-icon:hover {
        background: url(img/uc/search_clear_active.png?v=1) no-repeat center;
    }

.search-group-input {
    padding-right: 32px;
    width: 190px;
    height: 32px;
    line-height: 30px;
    border-right: none;
    border-left: none;
    border-color: #d6d6d6;
}

    .search-group-input:focus {
        z-index: 10;
        border-color: #d6d6d6;
    }

.btn-primary-search {
    background: #fff url(img/uc/btn_icon_search.png?v=1) no-repeat center !important;
    border: 1px solid #ccc !important;
    border-left: none !important;
    filter: none;
}

    .btn-primary-search:hover {
        background: #fff url(img/uc/btn_icon_search_active.png?v=1) no-repeat center !important;
    }

.btn-primary-faker {
    height: 28px;
    border: none;
    padding: 0 16px;
    background-color: #00aaff;
    line-height: 27px;
    color: #fff;
}

.ny-table {
    width: 100%;
    line-height: 39px;
    table-layout: fixed;
}

    .ny-table.no-fix {
        table-layout: auto;
    }

    .ny-table thead {
        background-color: #f7f9fa;
        font-size: 12px;
        font-weight: normal;
    }

    .ny-table th {
        position: relative;
        color: #636566;
        font-weight: 700;
    }

        .ny-table th > div {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .ny-table th > span.sort-list {
            position: relative;
            font-weight: 400;
            cursor: pointer;
        }

.body-reset .ny-table {
    table-layout: auto;
}

span.sort-list:after {
    position: absolute;
    top: 6px;
    right: -18px;
    content: '';
    width: 10px;
    height: 5px;
    background: url(img/uc/sort-select-icon.png) no-repeat center center;
}

ul.sort-list-content {
    display: none;
    position: absolute;
    right: -18px;
    top: 24px;
    min-width: 88px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    padding: 8px 0;
    box-shadow: 0 2px 6px rgba(0,0,0,.12);
}

    ul.sort-list-content li {
        height: 35px;
        line-height: 35px;
        padding: 0 15px;
        word-break: keep-all;
        font-size: 12px;
        color: #262829;
    }

        ul.sort-list-content li.active, ul.sort-list-content li:hover {
            color: #00aaff;
        }

.ny-table tr {
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    border-left: none;
    border-right: none;
}

.ny-table thead tr {
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;
}

.ny-table th, .ny-table td {
    padding-left: 16px;
    padding-right: 6px;
}

.ny-table td {
    line-height: 24px;
    padding-top: 16px;
    padding-bottom: 16px;
    font-size: 12px;
    color: #262829;
}

.table-list .ny-table td {
    height: 50px;
    line-height: 50px;
}

.ny-table tbody tr:hover, .ny-table tbody tr.active {
    background-color: #EDF7FC;
}

.ny-table tbody tr td.ny-nonedata-td {
    background-color: #fff;
}

.ny-table tr.disabled td, .ny-table tr.disabled .ny-td-multi-lines > div, .ny-table tr.disabled td a {
    color: #a0a2a3;
}

@media screen and (max-width:1200px) {
    .ny-table th, .ny-table td {
        padding-left: 7px;
    }
}

.ny-nonedata-td, .ny-table .ny-nonedata-td {
    padding: 0;
    text-align: center;
    height: 64px;
}

.ny-nonedata-wrapper {
    display: inline-block;
    background-position: left center !important;
    text-align: left;
}

.ny-nonedata-text {
    font-size: 12px;
    color: #636566;
    padding-left: 22px;
    background: url(img/uc/tip_icon_info_16.png) no-repeat left center;
}

.th-sortable {
    cursor: pointer;
}

.ny-caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 5px dashed;
    border-top: 5px solid\9;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
}

.nytips-modal {
    z-index: 2000;
}
