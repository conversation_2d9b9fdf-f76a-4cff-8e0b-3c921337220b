﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>知识库</summary>
[Serializable]
[DataObject]
[Description("知识库")]
[BindIndex("IX_DG_Knowledge_MId", false, "MId")]
[BindTable("DG_Knowledge", Description = "知识库", ConnName = "OnlineKeFu", DbType = DatabaseType.None)]
public partial class Knowledge : IKnowledge, IEntity<IKnowledge>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>标题</summary>
    [DisplayName("标题")]
    [Description("标题")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Name", "标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _Tags;
    /// <summary>标签</summary>
    [DisplayName("标签")]
    [Description("标签")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Tags", "标签", "", Master = true)]
    public String? Tags { get => _Tags; set { if (OnPropertyChanging("Tags", value)) { _Tags = value; OnPropertyChanged("Tags"); } } }

    private Int32 _MId;
    /// <summary>产品型号Id</summary>
    [DisplayName("产品型号Id")]
    [Description("产品型号Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("MId", "产品型号Id", "")]
    public Int32 MId { get => _MId; set { if (OnPropertyChanging("MId", value)) { _MId = value; OnPropertyChanged("MId"); } } }

    private String? _MIdName;
    /// <summary>产品型号内容</summary>
    [DisplayName("产品型号内容")]
    [Description("产品型号内容")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("MIdName", "产品型号内容", "")]
    public String? MIdName { get => _MIdName; set { if (OnPropertyChanging("MIdName", value)) { _MIdName = value; OnPropertyChanged("MIdName"); } } }

    private Int32 _Clicks;
    /// <summary>点击数</summary>
    [DisplayName("点击数")]
    [Description("点击数")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Clicks", "点击数", "")]
    public Int32 Clicks { get => _Clicks; set { if (OnPropertyChanging("Clicks", value)) { _Clicks = value; OnPropertyChanged("Clicks"); } } }

    private Int32 _HelpFuls;
    /// <summary>点赞数</summary>
    [DisplayName("点赞数")]
    [Description("点赞数")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("HelpFuls", "点赞数", "")]
    public Int32 HelpFuls { get => _HelpFuls; set { if (OnPropertyChanging("HelpFuls", value)) { _HelpFuls = value; OnPropertyChanged("HelpFuls"); } } }

    private Int32 _Status;
    /// <summary>数据状态 0为不可用，1为已发表</summary>
    [DisplayName("数据状态0为不可用")]
    [Description("数据状态 0为不可用，1为已发表")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "数据状态 0为不可用，1为已发表", "")]
    public Int32 Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IKnowledge model)
    {
        Id = model.Id;
        Name = model.Name;
        Content = model.Content;
        Tags = model.Tags;
        MId = model.MId;
        MIdName = model.MIdName;
        Clicks = model.Clicks;
        HelpFuls = model.HelpFuls;
        Status = model.Status;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "Content" => _Content,
            "Tags" => _Tags,
            "MId" => _MId,
            "MIdName" => _MIdName,
            "Clicks" => _Clicks,
            "HelpFuls" => _HelpFuls,
            "Status" => _Status,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "Tags": _Tags = Convert.ToString(value); break;
                case "MId": _MId = value.ToInt(); break;
                case "MIdName": _MIdName = Convert.ToString(value); break;
                case "Clicks": _Clicks = value.ToInt(); break;
                case "HelpFuls": _HelpFuls = value.ToInt(); break;
                case "Status": _Status = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得知识库字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>标签</summary>
        public static readonly Field Tags = FindByName("Tags");

        /// <summary>产品型号Id</summary>
        public static readonly Field MId = FindByName("MId");

        /// <summary>产品型号内容</summary>
        public static readonly Field MIdName = FindByName("MIdName");

        /// <summary>点击数</summary>
        public static readonly Field Clicks = FindByName("Clicks");

        /// <summary>点赞数</summary>
        public static readonly Field HelpFuls = FindByName("HelpFuls");

        /// <summary>数据状态 0为不可用，1为已发表</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得知识库字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>标题</summary>
        public const String Name = "Name";

        /// <summary>内容</summary>
        public const String Content = "Content";

        /// <summary>标签</summary>
        public const String Tags = "Tags";

        /// <summary>产品型号Id</summary>
        public const String MId = "MId";

        /// <summary>产品型号内容</summary>
        public const String MIdName = "MIdName";

        /// <summary>点击数</summary>
        public const String Clicks = "Clicks";

        /// <summary>点赞数</summary>
        public const String HelpFuls = "HelpFuls";

        /// <summary>数据状态 0为不可用，1为已发表</summary>
        public const String Status = "Status";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
