$(function () {
	var verifyState = nyData.verifyData.step;
	var authType = nyData.verifyData['authType'];

	template.helper('userCard', function(string) {
		return cardFilter(string);
	});
	template.helper('bankCard', function(string) {
		return bankCardFilter(string);
	});
	// 手动渲染模板
	NY.proxyRenderer.renderContent();


	NY.component.initDropDown();

	template.helper('money', function(money) {
		return parseInt(money);
	});

	lightbox.option({
		alwaysShowNavOnTouchDevices: false,
		showImageNumberLabel: false
	});

	// IE placeholder 兼容
	if(authType == 2) {
		$("#bankCardInput").attr('placeholder', nyData.verifyData.accountNumb);
		$("#cardInput").attr('placeholder', nyData.verifyData.authNumb);
		$("#authName").attr('placeholder', nyData.verifyData.authName);
	}else {
		$("#authName").attr('placeholder', nyData.verifyData.authName);
		$("#cardInput").attr('placeholder', nyData.verifyData.authNumb);
	}

	if(verifyState == 1) {
		$('.choose-item').on('click', 'a', function() {
			var $_this = $(this);
			var type = $_this.data('type');
			var url;
			if(nyData.verifyData.is_success_edit == 1) {
				url = '/User/Verify/successEditChoseTypeDo'
			}else {
				url = '/User/Verify/choseTypeDo'
			}
			type && NY.post({
				url: url,
				data: { verify_type: type },
				isCoverSuccess: true,
				beforeSend: function() {
					NY.hideWaiting();
				},
				success: function(data) {
					if(data.result){
						if(data.url) {
							window.location.href = data.url;
						}
						if(data.reload) {
							window.location.reload();
						}
					}else {
						NY.warn(data.text || '网络错误', 2, function() {
							if(data.reload) {
								window.location.reload();
							}
							if(data.url) {
								window.location.href = data.url;
							}
						});
					}
				}
			});
		});
	}

	if(verifyState == 2) {
		// 身份证显示
		var $_cardInput = $("#cardInput");
		var $_cardShow = $("#cardShow");

		$_cardInput.on('focus', function() {
			var $_this = $(this);
			if($_this.val()) {
				$_cardShow.text(cardFilter($_this.val())).show();
			}
			$_this.on('keyup', function() {
				$_cardShow.text(cardFilter($_this.val())).show();
			});

		});
		$_cardInput.on('blur', function() {
			$_cardShow.hide();
		});
		//银行卡显示
		if(authType == 2) {
			var $_bankCardInput = $("#bankCardInput");
			var $_bankCardShow = $("#bankCardShow");

			$_bankCardInput.on('focus', function() {
				var $_this = $(this);
				if($_this.val()) {
					$_bankCardShow.text(bankCardFilter($_this.val())).show();
				}
				$_this.on('keyup', function() {
					$_bankCardShow.text(bankCardFilter($_this.val())).show();
				});

			});
			$_bankCardInput.on('blur', function() {
				$_bankCardShow.hide();
			});

			$("#bankSelect").on('click', 'a', function() {
				$("#bankInput").val($(this).data('drop'));
			});
		}

		$(".upload-reminder").on('click', function(e) {
			var $_this = $(this);
			var $_parent = $_this.parent();
			$_parent.addClass('no-img');
			$_parent.find('.img-src').val('').end().find('.user-img').attr('href', '').find('img').attr('src', '');
		});


		var $_uploadBtn = $(".upload-btn");
		//上传身份证
		uploadFile('/verify/upload?attachments_type=2', $_uploadBtn[0], function(a, b, data, el) {
			uploadFn(a, b, data, el);
		});
		uploadFile('/verify/upload?attachments_type=2', $_uploadBtn[1], function(a, b, data, el) {
			uploadFn(a, b, data, el);
		});

		$("#goNext").on('click', function() {
			var flag = false,
				is_success_edit = nyData.verifyData.is_success_edit,
				oldAuthType = nyData.verifyData.oldAuthType,
				authType = nyData.verifyData.authType,
				url = '';
			if(is_success_edit == 1) {
				if(oldAuthType == authType) {
					url = '/User/Verify/successEditNewVerifyDo';
					flag = true;
				}else {
					url = '/User/Verify/successEditNewVerifyDo';
					$("#authName, #cardInput").removeClass('validate-input');
					flag = formValidate('#userForm');
				}
			}else {
				url = '/User/Verify/newEditDo';
				flag = formValidate('#userForm');
			}
			if(flag) {
				// 如果是修改认证资料，则弹出认证扣款提示
				var verifyPrice, content;
				if(nyData.verifyData.authType == 1) {
					verifyPrice = nyData.verifyData.mobileNewVerifyPrice;
					content = "本次手机号认证需要扣除<span class='text-stress'>" + verifyPrice + "</span>元认证费用，认证费用不予退还。请确保手机号码、手机号码所属人、身份证号完全匹配，否则会导致认证失败。确认提交认证？"
				}else {
					verifyPrice = nyData.verifyData.newVerifyPrice;
					content = "本次银行卡认证需要扣除<span class='text-stress'>" + verifyPrice + "</span>元认证费用，认证费用不予退还。请确保手机号码、手机号码所属人、身份证号、银行卡预留手机号完全匹配，否则会导致认证失败。确认提交认证？"
				}
				if (is_success_edit == 1) {
					NY.tips.ask({
						title: "温馨提示",
						content: content,
						width: 700,
						ok: function () {
							NY.post({
								url: url,
								data: $("#userForm").serialize(),
								isCoverSuccess: true,
								complete: function() {
									NY.hideWaiting();
								},
								beforeSend: function() {
									NY.waiting('认证审核提交中，请耐心等待', true);
								},
								success: function(data) {
									if(data.result){
										if(data.url) {
											window.location.href = data.url;
										}
										if(data.reload) {
											window.location.reload();
										}
									}else {
										NY.warn(data.text || '网络错误', 2, function() {
											if(data.reload) {
												window.location.reload();
											}
											if(data.url) {
												window.location.href = data.url;
											}
										});
									}
								}
							});
						}
					});
				}
				else {
					NY.post({
						url: url,
						data: $("#userForm").serialize(),
						isCoverSuccess: true,
						beforeSend: function() {
							NY.hideWaiting();
						},
						success: function(data) {
							if(data.result){
								if(data.url) {
									window.location.href = data.url;
								}
								if(data.reload) {
									window.location.reload();
								}
							}else {
								NY.warn(data.text || '网络错误', 2, function() {
									if(data.reload) {
										window.location.reload();
									}
									if(data.url) {
										window.location.href = data.url;
									}
								});
							}
						}
					});
				}

			}
		});
	}

	if(verifyState == 3) {
		var $_submitBtn = $("#submitBtn");
		$(".info-checkbox").on('click', function() {
			var flag = $(this).find('input').is(":Checked");
			if(flag) {
				$_submitBtn.removeAttr('disabled');
				$_submitBtn.removeClass('disabled-btn');
			}else {
				$_submitBtn.attr('disabled', true);
				$_submitBtn.addClass('disabled-btn');
			}
		});

		var freeNum = nyData.verifyData.newVerifyMaxFreeNum;
		var firstFree = nyData.verifyData.firstFree;
		var verifyPrice, content;
		if(nyData.verifyData.authType == 1) {
			verifyPrice = nyData.verifyData.mobileNewVerifyPrice;
			content = "请确保手机号码、手机号码所属人、身份证号完全匹配，否则会导致认证失败。确认提交认证？";
			if(freeNum == 0 && !firstFree) {
				content = "本次手机号认证需要扣除<span class='text-stress'>" + verifyPrice + "</span>元认证费用，认证费用不予退还。" + content;
			}
			if( nyData.verifyData.firstFree && ((nyData.common.money - 0) > (nyData.verifyData.newVerifyFreeMoney - 0)) ) {
				content = "由于您当前余额大于 <span class='text-stress'>"+nyData.verifyData.newVerifyFreeMoney+"</span> 元，首次认证免费; " + content;
			}
		}else {
			verifyPrice = nyData.verifyData.newVerifyPrice;
			content = "请确保手机号码、手机号码所属人、身份证号、银行卡预留手机号完全匹配，否则会导致认证失败。确认提交认证？";
			if(freeNum == 0 && !firstFree) {
				content = "本次银行卡认证需要扣除<span class='text-stress'>" + verifyPrice + "</span>元认证费用，认证费用不予退还。" + content;
			}
			if( nyData.verifyData.firstFree && ((nyData.common.money - 0) > (nyData.verifyData.newVerifyFreeMoney - 0)) ) {
				content = "由于您当前余额大于 <span class='text-stress'>"+nyData.verifyData.newVerifyFreeMoney+"</span> 元，首次认证免费; " + content;
			}
		}
		$_submitBtn.on('click', function() {
			NY.tips.ask({
				title: '认证确认',
				content: content,
				width: 700,
				ok: function () {
					sureInfo();
				}
			});
		});

		function sureInfo() {
			$_submitBtn.attr('disabled', true);
			$_submitBtn.addClass('disabled-btn');
			NY.get({
				url: "/User/Verify/submitVerify",
				complete: function() {
					$_submitBtn.removeAttr('disabled');
					$_submitBtn.removeClass('disabled-btn');
					NY.hideWaiting();
				},
				beforeSend: function() {
					NY.waiting('认证审核提交中，请耐心等待', true);
				},
				isCoverSuccess: true,
				success: function(data) {
					if(data.result){
						if(data.url) {
							window.location.href = data.url;
						}
					}else {
						NY.warn(data.text || '网络错误', 2, function() {
							if(data.reload) {
								window.location.reload();
							}
							if(data.url) {
								window.location.href = data.url;
							}
						});
					}
				}
			});
		}
	}


	if(verifyState == 4) {
		$(".repeat-submit").on('click', function() {
			NY.get({
				url: "/User/Verify/reVerify",
				isCoverSuccess: true,
				beforeSend: function() {
					NY.hideWaiting();
				},
				success: function(data) {
					if(data.result){
						if(data.url) {
							window.location.href = data.url;
						}
						if(data.reload) {
							window.location.reload();
						}
					}else {
						NY.warn(data.text || '网络错误', 2, function() {
							if(data.reload) {
								window.location.reload();
							}
							if(data.url) {
								window.location.href = data.url;
							}
						});
					}
				}
			});
		});
	}

	$(".go-prev").on('click', function() {
		NY.get({
			url: "/user/Verify/stepReturn",
			isCoverSuccess: true,
			beforeSend: function() {
				NY.hideWaiting();
			},
			success: function(data) {
				if(data.result){
					if(data.url) {
						window.location.href = data.url;
					}
					if(data.reload) {
						window.location.reload();
					}
				}else {
					NY.warn(data.text || '网络错误', 2, function() {
						if(data.reload) {
							window.location.reload();
						}
						if(data.url) {
							window.location.href = data.url;
						}
					});
				}
			}
		});
	});

});


function formValidate(form) {
	var flag = true;
	var $_form = $(form);
	$_form.find('.validate-input').each(function(index, item) {
		var $_this = $(item);
		var $_errorDiv = $_this.closest('.validate-control').find('.error-reminder span');
		var $_reminderDiv = $_this.closest('.validate-control').find('.input-reminder');

		if($_this.val()) {
			$_errorDiv.hide();
			$_reminderDiv.show();
		}
		else {
			$_this.focus();
			$_errorDiv.show();
			$_reminderDiv.hide();
			$('html,body').animate({scrollTop: $_this.closest('.validate-control').offset().top - 70}, 300);
			flag = false;
			return false;
		}
	});
	return flag;
};

function uploadFn(a, b, data, el) {
	var result = JSON.parse(data.response);
	var $_this = $(el);
	if(result.result) {
		var url = result.url;
		$_this.removeClass('no-img');
		$_this.find('.img-src').val(url).end().find('.user-img').attr('href', url).find('img').attr('src', url);
	}
};


//身份证过滤
function cardFilter(str) {
	str = str.toString().toUpperCase();
	var a = str.slice(0, 6);
	var b = str.slice(6, 10);
	var c = str.slice(10, 14);
	var d = str.slice(14, 18);
	return a + ' ' + b + ' ' + c + ' ' + d;
};
//银行卡过滤
function bankCardFilter(str) {
	str = str.toString().toUpperCase();
	var a = str.slice(0, 4);
	var b = str.slice(4, 8);
	var c = str.slice(8, 12);
	var d = str.slice(12, 16);
	var e = str.slice(16, 20);
	return a + ' ' + b + ' ' + c + ' ' + d + ' ' + e;
};

//上传组件
function uploadFile(url,browse_button,callback){
	var size = 1;
	var uploader = new plupload.Uploader({
		runtimes : 'html5,flash,silverlight,html4',
		browse_button : browse_button,
		url : url,
		file_data_name:'file',
		multi_selection:false,
		flash_swf_url : "/template/User/skin01/PC/Static/lib/plugin/plupload/js/Moxie.swf",
		filters : {
			max_file_size: size + 'mb',
			mime_types: [
				{title : "文件", extensions : 'png,jpg,jpeg'},
			]
		},
		init: {
			FilesAdded: function(up, files) {
				$(browse_button).find('.loading-cover').show();
				uploader.start();
			},
			UploadProgress: function(up, file) {},
			FileUploaded:function(a,b,data){
				$(browse_button).find('.loading-cover').hide();
				var result = JSON.parse(data.response);
				if(result.result) {
					callback && callback(a,b,data, browse_button);
				}else {
					NY.warn(result.text, 3);
				}
			},
			Error: function(up, err) {
				NY.error('文件大小超出' + size +'M上限', 1.5);
			}
		}
	});
	uploader.init();
}