﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>SEO多语言信息存放表</summary>
public partial class SeoInfoLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>SeoId</summary>
    public Int32 SId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>SEO标题</summary>
    public String? SeoTitle { get; set; }

    /// <summary>SEO关键词</summary>
    public String? SeoKeywords { get; set; }

    /// <summary>SEO描述</summary>
    public String? SeoDescription { get; set; }

    /// <summary>类型</summary>
    public String? SeoType { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISeoInfoLan model)
    {
        Id = model.Id;
        SId = model.SId;
        LId = model.LId;
        SeoTitle = model.SeoTitle;
        SeoKeywords = model.SeoKeywords;
        SeoDescription = model.SeoDescription;
        SeoType = model.SeoType;
    }
    #endregion
}
