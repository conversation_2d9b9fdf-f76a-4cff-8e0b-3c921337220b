using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;

using DH.Extensions;
using DH.Helpers;

using NewLife;
using NewLife.Data;

using Pek;

using XCode;

namespace HlktechSite.Entity;

/// <summary>成品翻译表</summary>
public partial class EndProductLan : CubeEntityBase<EndProductLan>
{
    #region 对象操作
    static EndProductLan()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(Sort));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add<UserModule>();
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add<IPModule>();
    }

    /// <summary>验证并修补数据，通过抛出异常的方式提示验证失败。</summary>
    /// <param name="isNew">是否插入</param>
    public override void Valid(Boolean isNew)
    {
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return;

        // 建议先调用基类方法，基类方法会做一些统一处理
        base.Valid(isNew);

        // 在新插入数据或者修改了指定字段时进行修正
        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化EndProductLan[成品翻译表]数据……");

    //    var entity = new EndProductLan();
    //    entity.Id = 0;
    //    entity.Name = "abc";
    //    entity.AdvWord = "abc";
    //    entity.Image = "abc";
    //    entity.Content = "abc";
    //    entity.MobileContent = "abc";
    //    entity.Summary = "abc";
    //    entity.UsageScenarios = "abc";
    //    entity.Specifications = "abc";
    //    entity.Commend = true;
    //    entity.Shelf = true;
    //    entity.Sort = 0;
    //    entity.GId = 0;
    //    entity.LId = 0;
    //    entity.CreateUser = "abc";
    //    entity.CreateUserID = 0;
    //    entity.CreateTime = DateTime.Now;
    //    entity.CreateIP = "abc";
    //    entity.UpdateUser = "abc";
    //    entity.UpdateUserID = 0;
    //    entity.UpdateTime = DateTime.Now;
    //    entity.UpdateIP = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化EndProductLan[成品翻译表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>
    /// 获取商品表的数据
    /// </summary>
    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public EndProducts Goods => Extends.Get(nameof(Goods), k => EndProducts.FindById(GId));
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static EndProductLan FindById(Int32 id)
    {
        if (id <= 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据商品编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static IList<EndProductLan> FindByGId(Int32 id)
    {
        if (id <= 0) return new List<EndProductLan>();

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.GId == id);



        return FindAll(_.GId == id);
    }


    /// <summary>根据编号查找</summary>
    /// <param name="GId"></param>
    /// <param name="LId"></param>
    /// <returns>实体对象</returns>
    public static EndProductLan FindByGIdAndLId(Int32 GId, Int32 LId)
    {
        if (GId <= 0 || LId <= 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.GId == GId && e.LId == LId);


        return Find(_.GId == GId & _.LId == LId);
    }


    /// <summary>
    /// 根据ID集合删除数据
    /// </summary>
    /// <param name="Ids">ID集合</param>
    public static void DelByGIds(String Ids)
    {
        if (Delete(_.GId.In(Ids)) > 0)
            Meta.Cache.Clear("");
    }

    /// <summary>
    /// 通过商品Id和语言Id获取翻译数据
    /// </summary>
    /// <param name="GId">商品Id</param>
    /// <param name="lId">语言Id</param>
    /// <param name="IsGetDefault">是否获取默认数据</param>
    /// <returns></returns>
    public static (String Name, String AdvWord, String Content, String Image, String MobileContent, String Specifications, String Summary, String UsageScenarios, bool Shelf, bool Commend, int sort) FindByGIdAndLId(Int32 GId, Int32 lId, Boolean IsGetDefault = true)
    {
        if (GId <= 0 || lId <= 0) return ("", "", "", "", "", "", "", "", false, false, 0);

        if (Meta.Session.Count < 1000)
        {
            var model = Meta.Cache.Find(e => e.GId == GId && e.LId == lId);
            if (model == null)
            {
                if (!IsGetDefault)
                {
                    return ("", "", "", "", "", "", "", "", false, false, 0);
                }
                else
                {
                    return FindNameAndRemark(GId, model);
                }
            }
            return (model.Name, model.AdvWord, model.Content, model.Image, model.MobileContent, model.Specifications, model.Summary, model.UsageScenarios, model.Shelf, model.Commend, model.Sort);
        }
        var exp = new WhereExpression();
        exp = _.GId == GId & _.LId == lId;

        var m = Find(exp);
        if (m == null)
        {
            if (!IsGetDefault)
            {
                return ("", "", "", "", "", "", "", "", false, false, 0);
            }
            else
            {
                return FindNameAndRemark(GId, m);
            }
        }
        return (m.Name, m.AdvWord, m.Content, m.Image, m.MobileContent, m.Specifications, m.Summary, m.UsageScenarios, m.Shelf, m.Commend, m.Sort);
    }

    /// <summary>
    /// 获取翻译数据
    /// </summary>
    /// <param name="GId"></param>
    /// <param name="model"></param>
    /// <returns></returns>
    private static (String Name, String AdvWord, String Content, String Image, String MobileContent, String Specifications, String Summary, String UsageScenarios, bool Shelf, bool Commend, int sort) FindNameAndRemark(Int32 GId, EndProductLan model)
    {
        var r = EndProducts.FindById(GId);

        if (model == null)
        {
            return (r.Name, r.AdvWord, r.Content, UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), r.Image.IsNotNullAndWhiteSpace() ? r.Image : ""), r.MobileContent, r.Specifications, r.Specifications, r.UsageScenarios, r.Shelf, r.Commend, r.Sort);
        }
        else
        {
            var Name = model.Name.IsNullOrWhiteSpace() ? r.Name : model.Name;
            var Image = model.Image.IsNullOrWhiteSpace() ? r.Image : model.Image;
            var AdvWord = model.AdvWord.IsNullOrWhiteSpace() ? r.AdvWord : model.AdvWord;
            var Content = model.Content.IsNullOrWhiteSpace() ? r.Content : model.Content;
            var MobileContent = model.MobileContent.IsNullOrWhiteSpace() ? r.MobileContent : model.MobileContent;
            var Specifications = model.Specifications.IsNullOrWhiteSpace() ? r.Specifications : model.Specifications;
            var Summary = model.Summary.IsNullOrWhiteSpace() ? r.Summary : model.Summary;
            var UsageScenarios = model.UsageScenarios.IsNullOrWhiteSpace() ? r.UsageScenarios : model.UsageScenarios;
            var Shelf = r.Shelf;
            var Commend = r.Commend;
            var Sort = r.Sort;

            return (Name, AdvWord, Content, UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Image.IsNotNullAndWhiteSpace() ? Image : ""), MobileContent, Specifications, Summary, UsageScenarios, Shelf, Commend, Sort);
        }
    }

    /// <summary>
    /// 根据语言ID查询上架的产品(没有产品分类的Id)
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="page"></param>
    /// <param name="LId"></param>
    /// <returns></returns>
    public static IEnumerable<EndProductLan> SearchByShelf(string Key, PageParameter page, int LId)
    {
        if (Meta.Session.Count < 1000)
        {
            var list1 = FindAllWithCache();
            list1 = list1.Where(x => x.Shelf == true && (!Key.IsNotNullAndWhiteSpace() || x.Name.SafeString().ToLower().Contains(Key.ToLower()) || x.Goods?.Name.ToLower().Contains(Key.ToLower()) == true && x.Goods?.Shelf == true) && x.LId == LId).OrderByDescending(x => x.Sort).ThenByDescending(x => x.CreateTime).ToList();
            page.TotalCount = list1.Count();
            list1 = list1.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
            return list1;
        }
        else
        {
            var exp = new WhereExpression();
            if (Key.IsNotNullOrWhiteSpace())
                exp &= _.Name.Contains(Key);
            exp &= _.Shelf == true;
            exp &= _.LId == LId;
            return FindAll(exp, page).OrderByDescending(x => x.CreateTime);
        }
    }


    /// <summary>
    /// 根据语言ID查询上架的产品
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="CId"></param>
    /// <param name="page"></param>
    /// <param name="LId"></param>
    /// <returns></returns>
    public static IEnumerable<EndProductLan> SearchByShelf(string Key, int CId, PageParameter page, int LId)
    {
        if (Meta.Session.Count < 1000)
        {
            var list1 = Meta.Cache.FindAll(
                x => x.Shelf == true &&
                (!Key.IsNotNullAndWhiteSpace() || x.Name.SafeString().ToLower().Contains(Key.ToLower()) || x.Goods?.Name.ToLower().Contains(Key.ToLower()) == true && x.Goods?.Shelf == true) &&
                (CId == 0 || x.Goods.CId == CId) &&
                x.LId == LId).OrderByDescending(x => x.Sort).ThenByDescending(x => x.CreateTime).ToList();
            page.TotalCount = list1.Count();
            list1 = list1.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
            return list1;
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.Name.Contains(Key);
            exp &= _.Shelf == true;
            exp &= _.LId == LId;
            exp &= _.Id.In(EndProducts.FindSQLWithKey(EndProducts._.CId == CId));
            return FindAll(exp, page).OrderByDescending(x => x.Goods.CreateTime);
        }
    }

    /// <summary>
    /// 根据语言ID查询上架的产品
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="CId"></param>
    /// <param name="page"></param>
    /// <param name="LId"></param>
    /// <returns></returns>
    public static IEnumerable<EndProductLan> SearchByShelf(string Key, String CId, PageParameter page, int LId)
    {
        if (Meta.Session.Count < 1000)
        {
            var list1 = Meta.Cache.FindAll(
                x => x.Shelf == true &&
                (!Key.IsNotNullAndWhiteSpace() || x.Name.SafeString().ToLower().Contains(Key.ToLower()) || x.Goods?.Name.ToLower().Contains(Key.ToLower()) == true && x.Goods?.Shelf == true) &&
                (!CId.IsNotNullOrWhiteSpace() || CId.SplitAsInt().Contains(x.Goods.CId)) &&
                 x.LId == LId).OrderByDescending(x => x.Sort).ThenByDescending(x => x.CreateTime).ToList();
            page.TotalCount = list1.Count();
            list1 = list1.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
            return list1;
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.Name.Contains(Key);
            exp &= _.Shelf == true;
            exp &= _.LId == LId;
            exp &= _.Id.In(EndProducts.FindSQLWithKey(EndProducts._.CId.In(CId.SplitAsInt(CId))));
            return FindAll(exp, page).OrderByDescending(x => x.Goods.CreateTime);
        }
    }

    /// <summary>
    /// 根据语言获取最大的Sort
    /// </summary>
    /// <param name="LID">语言ID</param>
    public static EndProductLan GetMAxSort(int LID)
    {
        if (Meta.Session.Count < 1000)
        {
            return Meta.Cache.FindAll(x => x.LId == LID).OrderByDescending(x => x.Sort).Skip(0).Take(1).FirstOrDefault();
        }
        else
        {
            return FindAll(_.LId == LID, new PageParameter { Desc = true, Sort = _.Sort, PageSize = 1 }).FirstOrDefault();
        }
    }

    /// <summary>
    /// 查询推荐的产品
    /// </summary>
    /// <param name="p">分页参数</param>
    /// <param name="LId"></param>
    /// <returns></returns>
    public static IEnumerable<EndProductLan> FinByRecommend(PageParameter p, int LId)
    {
        if (Meta.Session.Count < 1000)
        {
            return Meta.Cache.FindAll(x => x.LId == LId && x.Commend).OrderByDescending(x => x.Sort).ThenByDescending(x => x.CreateTime).Skip(--p.PageIndex * p.PageSize).Take(p.PageSize);
        }
        else
        {
            return FindAll(_.LId == LId & _.Commend, p);
        }
    }
    #endregion

    #region 高级查询

    // Select Count(Id) as Id,Category From DG_ProductLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<EndProductLan> _CategoryCache = new FieldCache<EndProductLan>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    #endregion
}