﻿@model IEnumerable<OnlineKeFu>
@{
    var cdn = CDN.GetCDN();
}

<style>
    .kfpanel {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: stretch;
    }

        .kfpanel .header {
            text-align: center;
            font-weight: normal;
            flex-basis: 40px;
            line-height: 40px;
            text-align: center;
        }

        .kfpanel .body {
            overflow-x: hidden;
            overflow-y: scroll;
            flex-grow: 1;
        }

        .kfpanel .consult-list {
        }

            .kfpanel .consult-list li {
                width: 100%;
                text-align: left;
                margin-bottom: 10px;
                font-weight: normal;
                padding-left: 13px;
            }

                .kfpanel .consult-list li img {
                    width: 19px;
                }

                .kfpanel .consult-list li .wwimg {
                    width: 77px !important;
                }
                
</style>

<div class="kfpanel">
    <div class="header">
        <span>@T("在线客服")</span>
    </div>
    <div class="body">
        <ul class="consult-list">
            @foreach (var item in Model)
            {
                if (item.Location != 0 && item.Location != 2) continue; //非平台下客服不加载
                switch (item.OType)
                {
                    case 0:
                        <li class="clearfix">
                            <a target="_blank" href="http://wpa.qq.com/msgrd?v=3&amp;uin=@(item.ONumber)&amp;site=qq&amp;menu=yes">
                                <img border="0" src="@(cdn)/images/JS_qq.png" alt="QQ" title="点击开始QQ交谈/留言">
                                <span class="margin-small-left">@($"{item.OName}")</span>
                            </a>
                        </li>
                        break;

                    case 1:
                        <li class="clearfix">
                            <a target="_blank" href="https://www.taobao.com/go/market/webww/ww.php?ver=3&amp;touid=@(item.ONumber)&amp;siteid=cntaobao&amp;status=1&amp;charset=utf-8">
                                <img class="wwimg" border="0" src="@(cdn)/images/ww.gif" alt="旺旺" title="点击开始与旺旺交谈/留言">
                            </a>
                        </li>
                        break;

                    case 2:
                        <li class="clearfix">
                            <a rel="nofollow" href="skype:@(item.ONumber)?chat" target="_blank">
                                <img src="@(cdn)/images/skype.png" alt="Skype" title="点击开始Skype交谈/留言" />
                                <span class="margin-small-left">@($"{item.OName}")</span>
                            </a>
                        </li>
                        break;
                    case 3:
                        var number = item.ONumber.TrimStart('+').Replace(" ", "");

                        //Whatsapp
                        <li class="clearfix">
                            <a rel="nofollow" href="https://api.whatsapp.com/send?phone=@(number)&text=Hello" target="_blank">
                                <img style="width:20px;height:20px;" src="@(cdn)/images/whatsapp.png" alt="Whatsapp" title="点击开始Whatsapp交谈/留言" />
                                <span class="margin-small-left">@($"{item.OName}")</span>
                            </a>
                        </li>
                        break;
                }
            }
        </ul>
    </div>

</div>

<script asp-location="Footer">
    $(() => {
        $(".icon-times-circle-o").click(function () {
            $(this).parents().find(".fixed-bar").css("right", "-135px");
            $(".show-fixed-bar").addClass("comeOut");
        });
        $(".show-fixed-bar").click(() => {
            $(".icon-times-circle-o").parents().find(".fixed-bar").css("right", "0px");
            $(".show-fixed-bar").removeClass("comeOut");
        })
    })
</script>