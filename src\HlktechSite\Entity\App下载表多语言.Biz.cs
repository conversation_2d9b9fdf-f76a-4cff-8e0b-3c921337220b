﻿using DG.Entity;

using NewLife;
using NewLife.Data;

using XCode;

namespace HlktechSite.Entity;

public partial class AppManagersLan : CubeEntityBase<AppManagersLan>
{
    #region 对象操作
    static AppManagersLan()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(JId));

        // 过滤器 UserModule、TimeModule、IPModule

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(JId), nameof(LId));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化AppManagersLan[App下载表多语言]数据……");

    //    var entity = new AppManagersLan();
    //    entity.JId = 0;
    //    entity.LId = 0;
    //    entity.Name = "abc";
    //    entity.Content = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化AppManagersLan[App下载表多语言]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    #endregion

    #region 高级查询
    /// <summary>
    /// 根据App Id获取所属语言数据
    /// </summary>
    /// <param name="JId">App Id</param>
    /// <returns></returns>
    public static IList<AppManagersLan> FindAllByJId(Int32 JId)
    {
        if (JId <= 0) return new List<AppManagersLan>();

        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.JId == JId);

        return FindAll(_.JId == JId);
    }

    /// <summary>
    /// 通过App Id和语言Id获取翻译数据
    /// </summary>
    /// <param name="JId">App Id</param>
    /// <param name="lId">语言Id</param>
    /// <param name="IsGetDefault">是否获取默认数据</param>
    /// <returns></returns>
    public static (String? Name, String? Content) FindByJIdAndLId(Int32 JId, Int32 lId, Boolean IsGetDefault = true)
    {
        if (JId <= 0 || lId <= 0) return ("", "");

        if (Meta.Session.Count < 1000)
        {
            var model = Meta.Cache.Find(e => e.JId == JId && e.LId == lId);
            if (model == null)
            {
                if (!IsGetDefault)
                {
                    return ("", "");
                }
                else
                {
                    return FindNameAndContent(JId);
                }
            }
            else
            {
                if (IsGetDefault)
                {
                    var name = model.Name;
                    var content = model.Content;

                    if (name.IsNullOrWhiteSpace() || content.IsNullOrWhiteSpace())
                    {
                        var r = AppManagers.FindById(JId);

                        if (r != null)
                        {
                            if (name.IsNullOrWhiteSpace()) name = r.Name;
                            if (content.IsNullOrWhiteSpace()) content = r.Content;

                            return (name, content);
                        }
                    }
                }
            }
            return (model.Name, model.Content);
        }
        var exp = new WhereExpression();
        exp = _.JId == JId & _.LId == lId;

        var m = Find(exp);
        if (m == null)
        {
            if (!IsGetDefault)
            {
                return ("", "");
            }
            else
            {
                return FindNameAndContent(JId);
            }
        }
        //else
        //{
        //    var name = m.Name;
        //    var infoUrl = m.InfoUrl;
        //    var adWord = m.AdWord;

        //    if (name.IsNullOrWhiteSpace() || infoUrl.IsNullOrWhiteSpace() || adWord.IsNullOrWhiteSpace())
        //    {
        //        var r = JumpProduct.FindById(JId);

        //        if (r != null)
        //        {
        //            if (name.IsNullOrWhiteSpace()) name = r.Name;
        //            if (infoUrl.IsNullOrWhiteSpace()) infoUrl = r.InfoUrl;
        //            if (adWord.IsNullOrWhiteSpace()) adWord = r.AdWord;

        //            return (name, infoUrl, adWord);
        //        }
        //    }
        //}
        return ("", "");
    }

    /// <summary>
    /// 获取翻译数据
    /// </summary>
    /// <param name="JId"></param>
    /// <returns></returns>
    private static (String? Name, String? Content) FindNameAndContent(Int32 JId)
    {
        var r = AppManagers.FindById(JId);

        if (r == null)
        {
            return ("", "");
        }
        else
        {
            return (r.Name, r.Content);
        }
    }

    /// <summary>高级查询</summary>
    /// <param name="jId">App下载Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<AppManagersLan> Search(Int32 jId, Int32 lId, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (jId >= 0) exp &= _.JId == jId;
        if (lId >= 0) exp &= _.LId == lId;
        if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.Content.Contains(key);

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,Category From DG_AppManagersLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<AppManagersLan> _CategoryCache = new FieldCache<AppManagersLan>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IAppManagersLan ToModel()
    {
        var model = new AppManagersLan();
        model.Copy(this);

        return model;
    }

    /// <summary>
    /// 通过App Id和语言Id获取翻译数据
    /// </summary>
    /// <param name="JId">App Id</param>
    /// <param name="LId">语言Id</param>
    /// <param name="IsGetDefault">当翻译为空时是否获取默认数据</param>
    /// <returns></returns>
    public static (String? Name, String? Content) FindItemByJIdAndlId(Int32 JId, Int32 LId, Boolean IsGetDefault = true)
    {
        if (JId <= 0 || LId <= 0) return ("", "");

        if (Meta.Session.Count < 1000)
        {
            var model = Meta.Cache.Find(e => e.JId == JId && e.LId == LId);

            if (IsGetDefault)
            {
                return FindNameAndContent(JId, model);
            }
            else
            {
                if (model == null)
                    return ("", "");

                return (model.Name, model.Content);
            }
        }

        var exp = new WhereExpression();
        exp = _.JId == JId & _.LId == LId;

        var m = Find(exp);

        if (IsGetDefault)
        {
            return FindNameAndContent(LId, m);
        }
        else
        {
            if (m == null)
                return ("", "");

            return (m.Name, m.Content);
        }
    }

    /// <summary>
    /// 获取翻译数据
    /// </summary>
    /// <param name="JId"></param>
    /// <param name="model">翻译实体</param>
    /// <returns></returns>
    private static (String? Name, String? Content) FindNameAndContent(Int32 JId, AppManagersLan? model)
    {
        var r = AppManagers.FindById(JId);

        if (model == null)
        {
            return (r?.Name, r?.Content);
        }
        else
        {
            var Name = model.Name.IsNullOrWhiteSpace() ? r?.Name : model.Name;
            var Content = model.Content.IsNullOrWhiteSpace() ? r?.Content : model.Content;
            return (Name, Content);
        }
    }
    #endregion
}
