using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;

using DH.Extensions;
using DH.SearchEngine;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity;

/// <summary>单页文章</summary>
public partial class SingleArticle : CubeEntityBase<SingleArticle>
{
    #region 对象操作
    static SingleArticle()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(Sort));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add<UserModule>();
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add<IPModule>();
    }

    /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
    /// <param name="isNew">是否插入</param>
    public override void Valid(Boolean isNew)
    {
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return;

        // 在新插入数据或者修改了指定字段时进行修正
        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
    }

    /// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    [EditorBrowsable(EditorBrowsableState.Never)]
    protected override void InitData()
    {
        // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        if (Meta.Session.Count > 0) return;
        if (XTrace.Debug) XTrace.WriteLine("开始初始化SingleArticle[单页文章]数据……");

        var entity = new SingleArticle();
        entity.Url = "";
        entity.Show = true;
        entity.Name = "代理招商";
        entity.Sort = 1;
        entity.Content = "<div class=\"agent-content\"><div class=\"agent-content-top\"><h2>代理招商</h2><p>深圳海凌科电子自2009年成立以来一直致力于物联网智能家居WIFI模块产品的研发、生产、销售与服务，目前已为上万家企业单位提供了智能WIFI模组、微型电源模块以及从物联网智能产品从云到端的一站式解决方案。公司拥有强大的研发团队和技术力量，产品已获得国家多项专利技术，为中国的智能家居行业建立了新的里程碑。</p><img src=\"/images/agentcom.png\" /></div></div><div class=\"choose-us\"><h2>为什么选择我们</h2><p>经过数十年的发展，海凌科电子一方面潜心产品的技术研发，注重产品品质和诚信服务，更与全球顶级网络芯片厂商保持战略合作，积极整合利用其优质技术资源，打造企业产品核心技术竞争能力。另一方面不断加强市场销售渠道建设，建立完善的营销网络和服务体系，为客户提供更优质的本地化服务。我们诚挚邀请优质的企业加盟海凌科，一起和我们合作、分享、成长、共赢。</p><div><span><img src=\"/images/chooseUS1.png\" /><i>丰富的经验累积</i></span><span><img src=\"/images/chooseUS2.png\" /><i>优质的技术支持服务</i></span><span><img src=\"/images/chooseUS3.png\" /><i>用户口碑好</i></span><span><img src=\"/images/chooseUS4.png\" /><i>一站式的解决方案</i></span><span> <img src=\"/images/chooseUS5.png\" /><i>可视化的开发工具</i></span><span> <img src=\"/images/chooseUS6.png\" /><i>便捷的接入套件</i></span></div></div><div class=\"support\"><h2>代理商的支持</h2><p>海凌科电子在全国进行招商代理是为了充分整合代理商的资源优势，为其创造良好的市场环境和企业综合竞争力。海凌科电子专心于产品研发和服务支持，不断打造更具市场竞争力的产品，为代理商提供更优质的产品和服务。海凌科将在如下几方面为代理商提供全方位的支持：</p><div><span><img class=\"agent-logo\" src=\"/images/agentlogo1.png\" /><i>技术沟通</i><i class=\"agent-support-con\">为代理商提供全面的产品、服务培训和技术支持</i><img src=\"/images/one.png\" /></span><span><img class=\"agent-logo\" src=\"/images/agentlogo2.png\" /><i>提供资料</i><i class=\"agent-support-con\">为代理商提供产品手册、相关证书、调试工具等各类资料</i><img src=\"/images/two.png\" /></span><span><img class=\"agent-logo\" src=\"/images/agentlogo3.png\" /><i>合作协助</i><i class=\"agent-support-con\">协助代理商跟进重大事项、提供相应的人员和技术支持</i><img src=\"/images/three.png\" /></span><span><img class=\"agent-logo\" src=\"/images/agentlogo4.png\" /><i>方案输出</i><i class=\"agent-support-con\">配合共同制定区域产品营销方案、配合乙方做好产品推广、销售等活动</i><img src=\"/images/four.png\" /></span><span><img class=\"agent-logo\" src=\"/images/agentlogo5.png\" /><i>项目跟进</i><i class=\"agent-support-con\">设立专门的代理商技术服务团队</i><img src=\"/images/five.png\" /></span></div></div>";
        entity.Pic = "";
        entity.CreateUser = "管理员";
        entity.CreateUserID = 1;
        entity.CreateTime = DateTime.Now;
        entity.CreateIP = "abc";
        entity.UpdateUser = "abc";
        entity.UpdateUserID = 0;
        entity.UpdateTime = DateTime.Now;
        entity.UpdateIP = "abc";
        entity.Summary = "";
        entity.Insert();

        var entity1 = new SingleArticle();
        entity1.Url = "";
        entity1.Show = true;
        entity1.Name = "关于我们";
        entity1.Sort = 2;
        //entity1.Content = "<div class=\"AboutUs-con\"><h2>关于我们</h2><span style=\"margin-top:57.1px;\">深圳市海凌科电子有限公司创建于2009年9月，是一家专业提供物联网智能家居WIFI模块产品的研发、生产、销售为一体的技术型企业。</span><span>2016年10月20日在前海股权中心挂牌，股票代码：668537。</span><span>2016年11月21日获得国家高新技术企业认证。</span><img src=\"/images/ole2.png\" /><span>目前公司的产品主要产品有IOTWIFI模组，无线路由模组和电源模块。我们的产品被广泛的应用于各种智能控制行业和场合。</span><img src=\"/images/agentcom.png\" /><h2>企业优势</h2><i style=\"margin-top:55px\">公司凭借自身的背景优势，积极开展自主研发，</i><i>同时与国际众多知名厂商合作，努力拓展线上线下销售渠道，全面提升服务范围和质量。</i><i>相信通过我们的不懈努力，必将开拓出更加宽广的市场前景，智能家居领域的明天一定更加美好！</i><div class=\"advantage\"><div><img src=\"/images/advantage1.png\" /><h2>一站式云到端配套服务</h2><p>公司具备完善成熟的云到端一站式配套，从互联网端的云服务平台，APP应用软件开发，到物联网领域的解决方案，家庭终端硬件设备，涵盖终端硬件设备，涵盖全面。</p></div><div><img src=\"/images/advantage2.png\" /><h2>开放的商务模式</h2><p>公司采取开放的商务模式，所有合作厂商均拥有产品品牌、定价、销售渠道的自主权。</p></div><div><img src=\"/images/advantage3.png\" /><h2>自主研发技术</h2><p>公司拥有专利技术，以及产品独立知识产权。研发实力过硬，具有一支技术量强大的研发团队。</p></div></div><h2 style=\"padding-bottom:54px;\">企业资质</h2><i>在市场竞争日益激烈的今天，拥有创新生产力才是制胜的关键法宝。公司拥有强大的研发团队和技术力量，</i><i>产品已获得国家多项专利技术，为中国的智能家居行业建立了新的里程碑。</i><div class=\"certification\"><div><img src=\"/images/certification1.png\" /><span>实用新型专利证书</span></div><div><img src=\"/images/certification2.png\" /><span>产品执行标准备案证书</span></div><div><img src=\"/images/certification3.png\" /><span>软件著作权</span></div><div><img src=\"/images/certification4.png\" /><span>软件著作权</span></div></div><div class=\"milestone\"><h2>里程碑</h2><img src=\"/images/milestone.png\" /></div><div class=\"cooperation\"><h2>合作客户</h2><div><img src=\"/images/cooperation1.png\" /><img src=\"/images/cooperation2.png\" /><img src=\"/images/cooperation3.png\" /><img src=\"/images/cooperation4.png\" /><img src=\"/images/cooperation5.png\" /><img src=\"/images/cooperation6.png\" /><img src=\"/images/cooperation7.png\" /><img src=\"/images/cooperation8.png\" /><img src=\"/images/cooperation9.png\" /><img src=\"/images/cooperation10.png\" /><img src=\"/images/cooperation11.png\" /><img src=\"/images/cooperation12.png\" /></div></div></div>";
        entity1.Content = "<div class=\"AboutUs-con\"><h2>关于我们</h2><span style=\"margin-top:57.1px;\">深圳市海凌科电子有限公司创建于2009年9月，是一家专业提供物联网智能家居WIFI模块产品的研发、生产、销售为一体的技术型企业。</span><span>2016年10月20日在前海股权中心挂牌，股票代码：668537。</span><span>2016年11月21日获得国家高新技术企业认证。</span><img src=\"/images/ole2.png\"/><span>目前公司的产品主要产品有IOTWIFI模组，无线路由模组和电源模块。我们的产品被广泛的应用于各种智能控制行业和场合。</span><img src=\"/images/agentcom.png\"/><h2>企业优势</h2><i style=\"margin-top:55px\">公司凭借自身的背景优势，积极开展自主研发，</i><i>同时与国际众多知名厂商合作，努力拓展线上线下销售渠道，全面提升服务范围和质量。</i><i>相信通过我们的不懈努力，必将开拓出更加宽广的市场前景，智能家居领域的明天一定更加美好！</i><div class=\"advantage\"><div><img src=\"/images/advantage1.png\"/><h2>一站式云到端配套服务</h2><p>公司具备完善成熟的云到端一站式配套，从互联网端的云服务平台，APP应用软件开发，到物联网领域的解决方案，家庭终端硬件设备，涵盖终端硬件设备，涵盖全面。</p></div><div><img src=\"/images/advantage2.png\"/><h2>开放的商务模式</h2><p>公司采取开放的商务模式，所有合作厂商均拥有产品品牌、定价、销售渠道的自主权。</p></div><div><img src=\"/images/advantage3.png\"/><h2>自主研发技术</h2><p>公司拥有专利技术，以及产品独立知识产权。研发实力过硬，具有一支技术量强大的研发团队。</p></div></div><h2 style=\"padding-bottom:54px;\">企业资质</h2><i>在市场竞争日益激烈的今天，拥有创新生产力才是制胜的关键法宝。公司拥有强大的研发团队和技术力量，</i><i>产品已获得国家多项专利技术，为中国的智能家居行业建立了新的里程碑。</i><div class=\"certification\"><div><img src=\"/images/certification1.png\"/><span>实用新型专利证书</span></div><div><img src=\"/images/certification2.png\"/><span>产品执行标准备案证书</span></div><div><img src=\"/images/certification3.png\"/><span>软件著作权</span></div><div><img src=\"/images/certification4.png\"/><span>软件著作权</span></div></div><div class=\"milestone\"><h2>里程碑</h2><img src=\"/images/milestone.png\"/></div><div class=\"cooperation\"><h2>合作客户</h2><div><img src=\"/images/cooperation1.png\"/><img src=\"/images/cooperation2.png\"/><img src=\"/images/cooperation3.png\"/><img src=\"/images/cooperation4.png\"/><img src=\"/images/cooperation5.png\"/><img src=\"/images/cooperation6.png\"/><img src=\"/images/cooperation7.png\"/><img src=\"/images/cooperation8.png\"/><img src=\"/images/cooperation9.png\"/><img src=\"/images/cooperation10.png\"/><img src=\"/images/cooperation11.png\"/><img src=\"/images/cooperation12.png\"/></div></div></div>";
        entity1.Pic = "";
        entity1.CreateUser = "管理员";
        entity1.CreateUserID = 1;
        entity1.CreateTime = DateTime.Now;
        entity1.CreateIP = "abc";
        entity1.UpdateUser = "abc";
        entity1.UpdateUserID = 0;
        entity1.UpdateTime = DateTime.Now;
        entity1.UpdateIP = "abc";
        entity1.Summary = "";
        entity1.Insert();











        XTrace.WriteLine("进入到第一次生成单页文章数据成功 Id="+ entity.Id);
        if (XTrace.Debug) XTrace.WriteLine("完成初始化SingleArticle[单页文章]数据！");
    }

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    #endregion

    #region 扩展查询
    /// <summary>获取全部数据</summary>
    /// <returns>实体集合</returns>
    public static IEnumerable<SingleArticle> GetAll()
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Entities.OrderByDescending(e => e.Id);

        return FindAll();
    }

    /// <summary>
    /// 根据文章标题查询
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public static SingleArticle? FindByName(String name)
    {
        if (name.IsNullOrEmpty()) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

        // 单对象缓存
        //return Meta.SingleCache.GetItemWithSlaveKey(name) as App;

        return Find(_.Name == name);
    }
    #endregion

    #region 高级查询

    // Select Count(Id) as Id,Category From SingleArticle Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<SingleArticle> _CategoryCache = new FieldCache<SingleArticle>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="name"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    public static IEnumerable<SingleArticle> Searchs(string name,PageParameter page)
    {
      
        var exp = new WhereExpression();

        if (!name.IsNullOrEmpty()) exp &= _.Name.Contains(name) | _.Url.Contains(name);
        return FindAll(exp, page);
    }

    #endregion

    #region 业务操作
    /// <summary>
    /// 根据ID集合删除数据
    /// </summary>
    /// <param name="Ids">ID集合</param>
    public static void DelByIds(String Ids)
    {
        //var list = FindByIds(Ids);
        //if (list.Delete() > 0)
        if (Delete(_.Id.In(Ids.Trim(','))) > 0)
            Meta.Cache.Clear("");
    }
    #endregion
}