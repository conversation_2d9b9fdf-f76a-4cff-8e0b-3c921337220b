﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>解决方案</summary>
[Serializable]
[DataObject]
[Description("解决方案")]
[BindIndex("IX_DG_Solution_MId", false, "MId")]
[BindIndex("IX_DG_Solution_CId", false, "CId")]
[BindTable("DG_Solution", Description = "解决方案", ConnName = "DG", DbType = DatabaseType.None)]
public partial class Solution : ISolution, IEntity<ISolution>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _MId;
    /// <summary>产品型号Id</summary>
    [DisplayName("产品型号Id")]
    [Description("产品型号Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("MId", "产品型号Id", "")]
    public Int32 MId { get => _MId; set { if (OnPropertyChanging("MId", value)) { _MId = value; OnPropertyChanged("MId"); } } }

    private Int32 _CId;
    /// <summary>解决方案分类Id</summary>
    [DisplayName("解决方案分类Id")]
    [Description("解决方案分类Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CId", "解决方案分类Id", "")]
    public Int32 CId { get => _CId; set { if (OnPropertyChanging("CId", value)) { _CId = value; OnPropertyChanged("CId"); } } }

    private String? _Url;
    /// <summary>解决方案跳转链接</summary>
    [DisplayName("解决方案跳转链接")]
    [Description("解决方案跳转链接")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Url", "解决方案跳转链接", "")]
    public String? Url { get => _Url; set { if (OnPropertyChanging("Url", value)) { _Url = value; OnPropertyChanged("Url"); } } }

    private Boolean _Show;
    /// <summary>解决方案是否显示，0为否，1为是，默认为1</summary>
    [DisplayName("解决方案是否显示")]
    [Description("解决方案是否显示，0为否，1为是，默认为1")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Show", "解决方案是否显示，0为否，1为是，默认为1", "")]
    public Boolean Show { get => _Show; set { if (OnPropertyChanging("Show", value)) { _Show = value; OnPropertyChanged("Show"); } } }

    private Int32 _Sort;
    /// <summary>解决方案排序</summary>
    [DisplayName("解决方案排序")]
    [Description("解决方案排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Sort", "解决方案排序", "")]
    public Int32 Sort { get => _Sort; set { if (OnPropertyChanging("Sort", value)) { _Sort = value; OnPropertyChanged("Sort"); } } }

    private String? _Name;
    /// <summary>解决方案标题</summary>
    [DisplayName("解决方案标题")]
    [Description("解决方案标题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "解决方案标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _Summary;
    /// <summary>简介</summary>
    [DisplayName("简介")]
    [Description("简介")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Summary", "简介", "")]
    public String? Summary { get => _Summary; set { if (OnPropertyChanging("Summary", value)) { _Summary = value; OnPropertyChanged("Summary"); } } }

    private String? _Pic;
    /// <summary>解决方案主图</summary>
    [DisplayName("解决方案主图")]
    [Description("解决方案主图")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Pic", "解决方案主图", "")]
    public String? Pic { get => _Pic; set { if (OnPropertyChanging("Pic", value)) { _Pic = value; OnPropertyChanged("Pic"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>发布时间</summary>
    [DisplayName("发布时间")]
    [Description("发布时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "发布时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISolution model)
    {
        Id = model.Id;
        MId = model.MId;
        CId = model.CId;
        Url = model.Url;
        Show = model.Show;
        Sort = model.Sort;
        Name = model.Name;
        Content = model.Content;
        Summary = model.Summary;
        Pic = model.Pic;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "MId" => _MId,
            "CId" => _CId,
            "Url" => _Url,
            "Show" => _Show,
            "Sort" => _Sort,
            "Name" => _Name,
            "Content" => _Content,
            "Summary" => _Summary,
            "Pic" => _Pic,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "MId": _MId = value.ToInt(); break;
                case "CId": _CId = value.ToInt(); break;
                case "Url": _Url = Convert.ToString(value); break;
                case "Show": _Show = value.ToBoolean(); break;
                case "Sort": _Sort = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "Summary": _Summary = Convert.ToString(value); break;
                case "Pic": _Pic = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得解决方案字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>产品型号Id</summary>
        public static readonly Field MId = FindByName("MId");

        /// <summary>解决方案分类Id</summary>
        public static readonly Field CId = FindByName("CId");

        /// <summary>解决方案跳转链接</summary>
        public static readonly Field Url = FindByName("Url");

        /// <summary>解决方案是否显示，0为否，1为是，默认为1</summary>
        public static readonly Field Show = FindByName("Show");

        /// <summary>解决方案排序</summary>
        public static readonly Field Sort = FindByName("Sort");

        /// <summary>解决方案标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>简介</summary>
        public static readonly Field Summary = FindByName("Summary");

        /// <summary>解决方案主图</summary>
        public static readonly Field Pic = FindByName("Pic");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>发布时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得解决方案字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>产品型号Id</summary>
        public const String MId = "MId";

        /// <summary>解决方案分类Id</summary>
        public const String CId = "CId";

        /// <summary>解决方案跳转链接</summary>
        public const String Url = "Url";

        /// <summary>解决方案是否显示，0为否，1为是，默认为1</summary>
        public const String Show = "Show";

        /// <summary>解决方案排序</summary>
        public const String Sort = "Sort";

        /// <summary>解决方案标题</summary>
        public const String Name = "Name";

        /// <summary>内容</summary>
        public const String Content = "Content";

        /// <summary>简介</summary>
        public const String Summary = "Summary";

        /// <summary>解决方案主图</summary>
        public const String Pic = "Pic";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>发布时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
