﻿using DG.Cube;
using DG.Web.Framework;

using DH.SearchEngine;
using DH.SearchEngine.Interfaces;

using HlktechSite.DTO;
using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Reflection;

using Pek;

using System.Dynamic;

namespace HlktechSite.Controllers;

/// <summary>
/// 搜索控制器
/// </summary>
public class SearchController : DGBaseControllerX
{

    private readonly ISearchEngine _searchEngine;

    public SearchController(ISearchEngine searchEngine)
    {
        _searchEngine = searchEngine;
    }

    /// <summary>
    /// 搜索首页
    /// </summary>
    /// <returns></returns>
    public IActionResult Index(string Key_Word, int type, Int32 page = 1, Int32 size = 10)
    {
        ViewBag.KeyWord = Key_Word;
       
        Key_Word = Key_Word.SafeString().Trim();
        var SearchType = type;
        var TotalCount = 0;//总条数
        ViewBag.SearchType = SearchType;
        dynamic viewModel = new ExpandoObject();

        var list = new List<SearchModel>();
        if (Key_Word.IsNotNullAndWhiteSpace())
        {
            Key_Word = Key_Word.Trim();
            if (SearchType == 0) //全部
            {
                var options = new SearchOptions(Key_Word, page, size, "Name,Content");
                options.Score = 0f;
                var result = _searchEngine.ScoredSearch(options);

                if (result.Results.Count > 0) TotalCount = result.TotalHits;

                foreach (var item in result.Results)
                {
                    var modelSearchModel = new SearchModel();
                    if (item.Entity.GetType().Name == "Solution")
                    {
                        var model = item.Entity.ChangeType<Solution>();
                        modelSearchModel.Id = model.Id;
                        modelSearchModel.Name = model.Name;
                        var Models = Solution.FindById(model.Id);
                        if (Models != null)
                        {
                            modelSearchModel.UpdateTime = Models.UpdateTime.ToDateString();
                            modelSearchModel.CreateTime = Models.CreateTime.ToDateString();
                            modelSearchModel.CategoryName = "解决方案";
                            modelSearchModel.SearchType = 4;
                        }
                    }
                    else if (item.Entity.GetType().Name == "Case")  //案例
                    {
                        var model = item.Entity.ChangeType<Case>();
                        modelSearchModel.Id = model.Id;
                        modelSearchModel.Name = model.Name;

                        var Models = Case.FindById(model.Id);
                        if (Models != null)
                        {
                            modelSearchModel.UpdateTime = Models.UpdateTime.ToDateString();
                            modelSearchModel.CreateTime = Models.CreateTime.ToDateString();
                            modelSearchModel.CategoryName = "案例";
                            modelSearchModel.SearchType = 2;
                        }
                    }
                    else if (item.Entity.GetType().Name == "Knowledge")
                    {
                        var model = item.Entity.ChangeType<Knowledge>();
                        modelSearchModel.Id = model.Id;
                        modelSearchModel.Name = model.Name;

                        var Models = Knowledge.FindById(model.Id);
                        if (Models != null)
                        {
                            modelSearchModel.UpdateTime = Models.UpdateTime.ToDateString();
                            modelSearchModel.HelpFuls = Models.HelpFuls;
                            modelSearchModel.CreateTime = Models.CreateTime.ToDateString();
                            modelSearchModel.PModel = Models.MName;
                            modelSearchModel.CategoryName = "知识库";
                            modelSearchModel.SearchType = 1;
                        }
                    }
                    //else if (item.Entity.GetType().Name == "WorkOrder")
                    //{
                    //    var model = item.Entity.ChangeType<WorkOrder>();
                    //    modelSearchModel.Id = model.Id;
                    //    modelSearchModel.Name = model.Name;

                    //    var Models = WorkOrder.FindById(model.Id);
                    //    if (Models != null)
                    //    {
                    //        modelSearchModel.UpdateTime = Models.CreateTime.ToDateString();
                    //        modelSearchModel.PModel = model.MName;
                    //    }

                    //    modelSearchModel.CategoryName = "开放工单";
                    //    modelSearchModel.SearchType = 2;
                    //}
                    else if (item.Entity.GetType().Name == "KnowledgeQuiz")
                    {
                        var model = item.Entity.ChangeType<KnowledgeQuiz>();
                        modelSearchModel.Id = model.Id;
                        modelSearchModel.Name = model.Name;
                        modelSearchModel.SearchType = 3;
                        var Models = KnowledgeQuiz.FindById(model.Id);
                        if (Models != null)
                        {
                            modelSearchModel.UpdateTime = Models.UpdateTime.ToDateString();
                            modelSearchModel.HelpFuls = Models.HelpFuls;
                            modelSearchModel.CreateTime = Models.CreateTime.ToDateString();
                            modelSearchModel.PModel = Models.MName;
                        }
                        modelSearchModel.CategoryName = "知识问答";
                    }
                    list.Add(modelSearchModel);
                }
            }
            // 添加搜索记录
            var record = SearchRecords.FindByKeyWord(Key_Word);
            if (record == null)
            {
                record = new SearchRecords
                {
                    KeyWord = Key_Word.ToLower(),
                    Hits = 1,
                    Importance = 0,
                    SType = SearchType
                };
                record.Insert();
            }
            else
            {
                record.Hits += 1;
                record.Update();
            }

        }
        else
        {
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = size,
                RetrieveTotalCount = true
            };

            var result = SearchInfo.Search(SearchType, "", 0, pages).OrderByDescending(x => x.CreateTime); //创建时间倒序排序
            foreach (var item in result)
            {
                var Model = new SearchModel();
                Model.Id = item.OtherId;
                Model.Name = item.Name;
                Model.SearchType = item.SType;
                Model.UpdateTime = item.UpdateTime.ToDateString();
                Model.MId = item.MId;
                list.Add(Model);
                if (item.SType == 1)
                {
                    var Knowledgemodel = Knowledge.FindById(item.OtherId);
                    Model.PModel = Knowledgemodel.MName;
                    Model.HelpFuls = Knowledgemodel.HelpFuls;
                    Model.CreateTime = Knowledgemodel.CreateTime.ToDateString();
                    Model.SearchType = 1;
                    Model.CategoryName = "知识库";
                }
                else if (item.SType == 2)//工单暂时注释
                {

                    var Knowledgemodel = Case.FindById(item.OtherId);
                    Model.CategoryName = "案例";
                    Model.PModel = Knowledgemodel.MName;
                    Model.SearchType = 2;
                    //Model.HelpFuls = Knowledgemodel.HelpFuls;
                    Model.CreateTime = Knowledgemodel.CreateTime.ToDateTimeString();
                }
                else if (item.SType == 3)//知识问答
                {
                    var knowledgeQuizmodel = KnowledgeQuiz.FindById(item.OtherId);
                    Model.PModel = knowledgeQuizmodel.MName;
                    Model.HelpFuls = knowledgeQuizmodel.HelpFuls;
                    Model.CreateTime = knowledgeQuizmodel.CreateTime.ToDateString();
                    Model.SearchType = 3;
                    Model.CategoryName = "知识问答";
                }
                else if (item.SType == 4)//解决方案
                {
                    var knowledgeQuizmodel = Solution.FindById(item.OtherId);
                    Model.PModel = knowledgeQuizmodel.MName;
                    //Model.HelpFuls = knowledgeQuizmodel.HelpFuls;
                    Model.CreateTime = knowledgeQuizmodel.CreateTime.ToDateString();
                    Model.SearchType = 4;
                    Model.CategoryName = "解决方案";
                }

            }
            TotalCount = pages.TotalCount.ToInt();
        }

        var PageCount = Math.Ceiling((Double)TotalCount / 10);

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());

        viewModel.Str = PageHelper.CreatePage(page, TotalCount, PageCount, Url.Action("Index"), dic);
        viewModel.List = list;
        viewModel.TotalCount = TotalCount;

        var page1 = new PageParameter()
        {
            PageIndex = 1,
            PageSize = 3,
            OrderBy = "Importance desc, Hits desc",
            Desc = true
        };
        var List = SearchRecords.Searchs(page1);
        var HotTopic = "";
        foreach (var item in List)
        {
            HotTopic = HotTopic + " " + item.KeyWord;
        }

        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("搜索"), IsLast = true });
        ViewBag.Locations = navigations;
        viewModel.HotTopic = HotTopic;
        viewModel.Recordslist = List;

        return DGView(list, true);
    }

    /// <summary>
    /// 详情页
    /// </summary>
    /// <returns></returns>
    public IActionResult Details(int Id)
    {
        dynamic viewModel = new ExpandoObject();
        //var SolutionModel = Solution.FindById(Id);
        //viewModel.SolutionModel = SolutionModel;
        //viewModel.previous = Solution.Findprevious(SolutionModel.CreateTime);
        //viewModel.Nex = Solution.FindNext(SolutionModel.CreateTime);
        return DGView(viewModel, true);
    }
}
