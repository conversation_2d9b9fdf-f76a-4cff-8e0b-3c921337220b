//import { debuglog } from "util";

$(function(){
    // 商品图片ajax上传
    $('.dssc-upload-btn').find('input[type="file"]').on('change', function(){
        var id = $(this).attr('id');
        //var id = $(this).attr('data');
        ajaxFileUpload(id);
    });
    //浮动导航  waypoints.js
//    $("#uploadHelp").waypoint(function(event, direction) {
//        $(this).parent().toggleClass('sticky', direction === "down");
//        event.stopPropagation();
//    }); 
    // 关闭相册
    $('a[dstype="close_album"]').click(function(){
        $(this).hide();
        $(this).prev().show();
        $(this).parent().next().html('');
    });
    // 绑定点击事件
    $('div[dstype^="file"]').each(function(){
        if ($(this).find('input[type="hidden"]').val() != '') {
            selectDefaultImage($(this));
        }
    });
});

// 图片上传ajax
function ajaxFileUpload(id, o) {
    $('img[dstype="' + id + '"]').attr('src', "/images/loading.gif");

    $.ajaxFileUpload({
        //url : HOMESITEURL + '/Sellergoodsadd/image_upload',
        url: CreateImgs,
        //secureuri: true,
        fileElementId : id,
        dataType : 'json',
        data: {
            name: id
            //data: $('form').serializeArray()
        },
        success: function (data, status) {
          //if (typeof(data.error) != 'undefined') {
            if (!data.success) {
                alert(data.error);
                        $('img[dstype="' + id + '"]').attr('src',DEFAULT_GOODS_IMAGE);
                    } else {
                        //$('input[dstype="' + id + '"]').val(data.name);
                $('input[dstype="' + id + '"]').val(data.file_name);
                $('input[dsId="' + id + '"]').val(data.file_id);
                $('input[typeId="' + id + '"]').val("1");             
                        //$('img[dstype="' + id + '"]').attr('src', data.thumb_name);
                $('img[dstype="' + id + '"]').attr('src', data.file_path);
                        selectDefaultImage($('div[dstype="' + id + '"]'));      // 选择默认主图
                    }

                },
        error : function (data, status, e) {
                    alert(e);

                }
    });
    return false;

}

// 选择默认主图&&删除
function selectDefaultImage($this) {
    // 默认主题
    $this.click(function(){
        $(this).parents('ul:first').find('.show-default').removeClass('selected').find('input').val('0');
        $(this).addClass('selected').find('input').val('1');
    });
    // 删除
    $this.parents('li:first').find('a[dstype="del"]').click(function(){
        $this.unbind('click').removeClass('selected').find('input').val('0');
        $this.prev().find('input').val('').end().find('img').attr('src', DEFAULT_GOODS_IMAGE);
    });
}

// 从图片空间插入主图
function insert_img(name, src, color_id) {
    var $_thumb = $('ul[dstype="ul'+ color_id +'"]').find('.upload-thumb');
    $_thumb.each(function(){
        if ($(this).find('input').val() == '') {
            $(this).find('img').attr('src', src);
            $(this).find('input').val(name);
     
            selectDefaultImage($(this).next());      // 选择默认主图
            return false;
        }
    });
}

function insert_imgs(id,name, src, color_id) {
    var $_thumb = $('ul[dstype="ul' + color_id + '"]').find('.upload-thumb');
    $_thumb.each(function () {
        if ($(this).find('input:eq(0)').val() == '') {
            $(this).find('img').attr('src', src);
            $(this).find('input:eq(0)').val(name);
            $(this).find('input:eq(1)').val(id);
            $(this).find('input:eq(2)').val(name);

            selectDefaultImage($(this).next());      // 选择默认主图
            return false;
        }
    });
}