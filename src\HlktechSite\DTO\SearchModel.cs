﻿namespace HlktechSite
{
    /// <summary>
    /// 搜索实体
    /// </summary>
    public class SearchModel
    {
        /// <summary>
        /// 编号
        /// </summary>
        public Int64 Id { get; set; }

        /// <summary>
        /// 标题/名称
        /// </summary>
        public String Name { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public String UpdateTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public String CreateTime { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        public String PModel { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        public String CategoryName { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public Int32 SearchType { get; set; }

        /// <summary>
        /// 产品型号Id
        /// </summary>
        public Int32 MId { get; set; }

        /// <summary>
        /// 点赞数
        /// </summary>
        public Int32 HelpFuls { get; set; } = 0;
    }
}
