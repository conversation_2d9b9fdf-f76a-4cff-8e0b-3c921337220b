﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using Pek.Models;
using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

[DisplayName("样品申请")]
[Description("用于查看样品申请")]
[AdminArea]
[DHMenu(45,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/Sample", CurrentMenuName = "SampleList", CurrentIcon = "&#xe71f;", LastUpdate = "20240125")]
public class SampleController : DGBaseAdminControllerX
{
    /// <summary>
    /// 样品申请的首页
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("样品申请列表")]
    public IActionResult Index(string key, int page = 1)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            OrderBy = "CreateTime",
            Desc = true,
            RetrieveTotalCount = true
        };
        var list = SampleApplication.FindByName(key, pages);
        ViewBag.key = key.IsNullOrWhiteSpace() ? "" : key;
        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key } });
        return View(list);
    }

    /// <summary>
    /// 定制申请的详情页
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("样品申请的详情页")]
    public IActionResult Details(int id)
    {
        var customization = SampleApplication.FindById(id);
        if (customization == null)
        {
            return View404();
        }
        return View(customization);
    }
}
