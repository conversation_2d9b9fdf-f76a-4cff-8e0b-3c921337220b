﻿@using DG.Helpers;
@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    if (Model.AId == 0)
    {
        Html.AppendTitleParts(T("新闻中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        var modelArticleCategory = Model.Model as ArticleCategory;
        Html.AppendTitleParts(modelArticleCategory?.Name + DG.Setting.Current.PageTitleSeparator + T("新闻中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
}

<div class="top">
    <img src="@(CDN.GetCDN())/images/journalism.png" />
    <div>
        <h2>@T("新闻资讯")</h2>
        <P>@T("热点/痛点/观点 连点成线，物联大事件脉络尽在掌握")</P>
    </div>
</div>

<div class="input-group seach-div">
    <input type="text" class="form-control" id="KeyVal" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2" value="@Model.Key">
    <a href="javascript:;">@("搜索")</a>
</div>

<ul class="type-menu">
    <li class="@(Model.AId == 0 ? "selected" : "")"><a href="@Url.DGAction("Index")">@T("全部")</a></li>
    @foreach (var item in Model.ArticleTypelist)
    {
        <li class="@(Model.AId == item.Id ? "selected" : "")">
            <a id="@item.Id" href="@Url.DGAction("List", new { AId = item.Id})">@item.Name</a>
        </li>
    }
</ul>

<div class="jour-list">
    @foreach (var item in Model.JournalismList as IEnumerable<Article>)
    {
        <a href="@Url.DGAction("Details", "Journalism", new { Id = item.Id })">
            <div>
                <img src="@UrlHelper.Combine(CDN.GetCDN().IsNullOrWhiteSpace()?Pek.Helpers.DHWeb.GetSiteUrl():CDN.GetCDN(), item.Pic.IsNullOrWhiteSpace() ? "" : item.Pic)" />
                <p>
                    <span>@item.Name</span>
                    <i>@(item.Summary.IsNullOrWhiteSpace()?item.Name:item.Summary)</i>
                    <b>@item.CreateTime.ToString("yyyy-M-d")</b>
                </p>
            </div>
        </a>
    }

    <div class="paging" style=" display: block; box-shadow: inherit; text-align: center;">
        <ul class="pagination">
            @Html.Raw(Model.Str)
        </ul>
    </div>

</div>

<script asp-location="Footer">
    var cid = @Model.AId;

    $(".seach-div a").click(function () {
        if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#KeyVal").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { AId = Model.AId})?key=" + $("#KeyVal").val();
        }
    });

    $(".seach-div input").keyup(function (event) {
        if (event.keyCode == 13) {
            if (cid == 0){
           location.href = "@Url.DGAction("Index")?key=" + $("#KeyVal").val();
        }
        else{
           location.href = "@Url.DGAction("List", new { AId = Model.AId})?key=" + $("#KeyVal").val();
        }
        }
    });

</script>