﻿body {
  width: 100%;
  min-width: 1270px;
}

.title1 {
  color: #fff !important;
  background-color: #337ab7;
}

.title1:hover {
  background-color: #337ab7 !important;
}

.yemei {
  width: 100%;
  min-width: 1145px;
  margin: 0 auto;
  background-color: #ffffff;
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  z-index: 1;
  box-shadow: 0px 6px 19px 1px rgba(177, 174, 174, 0.2);
  z-index: 10;
}

.logo {
  float: left;
  margin-top: 22px;
  margin-left: 11.56%;
}

.nav {
  float: right;
}

.nav li a {
  color: #434343;
  margin-top: 22px;
}

#stg1 {
  cursor: pointer;
}

.tck {
  width: 640px;
  padding-bottom: 20px;
  background-color: #f2f2f2;
  position: fixed;
  right: 0;
  top: 84px;
  z-index: 1000;
}

.tck a {
  text-decoration: none !important;
}

.tckgf,
.tckgf2 {
  width: 600px;
  overflow: hidden;
  display: flex;
  padding-bottom: 15px;
}

.tckgf2 > .goumaiguifan:first-child a,
.tckgf3 > .goumaiguifan:first-child a {
  text-decoration: none !important;
  color: #666666 !important;
  cursor: auto !important;
}

.tckgf {
  margin-left: 30px;
  margin-top: 40px;
  overflow: inherit;
}

.qhyy {
  line-height: 40px;
  float: left;
  margin-left: 5px;
  font-size: 18px;
  font-family: 微软雅黑;
  color: #303133;
}

.ct {
  height: 1px;
  width: 560px;
  margin-top: 4px;
  margin-left: 30px;
  background-color: #dedede;
}

.yyhz {
  margin-left: 20px;
  float: left;
}

.yyhz:hover {
  cursor: pointer;
}

.yyhz {
  position: relative;
}

.yyhz dl {
  display: none;
  position: absolute;
  left: 0;
  top: 42px;
  background-color: #fff;
  padding-top: 5px;
  padding-bottom: 5px;
  margin: 0px;
}

.yyhz dl dd a {
  display: inline-block;
  text-align: center;
  width: 100px;
  height: 37px;
  line-height: 37px;
  color: #666;
  font-size: 1.6rem;
  text-decoration: none !important;
}

.yyhz dl dd a:hover {
  background-color: #f5f5f5;
}

.yyhzchange {
  display: inline-block;
  width: 51px;
  height: 42px;
  float: left;
  text-align: center;
  line-height: 42px;
}

.yyhzchange img {
  display: inline-block;
}

.IsGray {
  background-color: #ccc;
}

.tckwz {
  font-size: 18px;
  font-family: 微软雅黑;
  color: #666666;
  font-weight: 400;
  float: left;
}

.hezi {
  width: 100%;
  height: 600px;
}

.fdjk {
  background-color: #4e6ef2;
  width: 40px;
  height: 40px;
  float: left;
  border-radius: 0 10px 10px 0;
  text-align: center;
}

.fdj {
  margin-top: calc(50% - 8.5px);
}

.tckgf3 {
  width: calc(100% - 47px);
  overflow: hidden;
  padding-bottom: 15px;
  display: flex;
}

.tckgf3.product_center {
  display: flex;
  line-height: 1.628571;
}

.tckgf4 {
  height: 40px;
  width: 600px;
  margin-left: 40px;
}
.tckgf2 > .goumaiguifan:first-child,
.tckgf3 > .goumaiguifan:first-child {
  margin-left: 30px;
}
.tckgf2 > .goumaiguifan:first-child > .tckwz,
.tckgf3 > .goumaiguifan:first-child > .tckwz {
  font-weight: bold;
}
.goumaiguifan {
  margin-left: 38px;
  margin-top: 15px;
  display: inline-block;
  vertical-align: top;
}

.goumaiguifan .tckwz {
  float: none;
}

.goumaiguifan img {
  margin-bottom: 2px;
}

@media screen and (max-width: 1300px) {
  .logo {
    margin-left: 0px;
  }
}

.footer-box {
  background: #f1f1f1;
  overflow: hidden;
  width: 100%;
  color: #888;
  padding: 30px 10px 0 30px;
}

.footer-container {
  width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.footer-container > p {
  border-top: 1px solid #e5e5e5;
  text-align: center;
  height: 37px;
  line-height: 37px;
  margin-bottom: 0px;
}

.footer-service-item span {
  font-size: 16px;
  color: #3a3a3a;
  margin-top: 11px;
}

.footer-service-item {
  float: left;
}

.yeweilogo:hover,
.lianjie {
  cursor: pointer;
}

.footer-line {
  height: 0;
  display: block;
  clear: both;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 10px;
}

.footer-links {
  padding-top: 40px;
}

.footer-article-item {
  width: 165px;
  float: left;
  line-height: 2;
  font-size: 14px;
}

.footer-contact-item {
  float: left;
}

.footer-contact-item p {
  text-align: center;
  width: 100%;
  max-width: 113px;
  margin: 0;
}

.footer-article-item dt {
  color: #3a3a3a;
  font-size: 18px;
  margin-bottom: 10px;
  line-height: 2;
  font-weight: bold;
}

.henxian {
  height: 1px;
  width: 240px;
  background-color: #e5e5e5;
  margin: 10px 50px 10px 0;
}

.xian {
  height: 220px;
  width: 1px;
  background-color: #e5e5e5;
  float: left;
  margin-left: 50px;
}

.footer-copyright {
  line-height: 40px;
  font-size: 14px;
  text-align: center;
}

.footer-copyright p {
  /*        margin-bottom: 9px;*/
  font-size: 14px;
  color: #888;
}

.footer-article-item dd {
  margin-bottom: 10px;
}
/*
.footer-article-item:first-child dd {
    margin-bottom: 6px;
}
*/
.footer-article-item dd a {
  color: #888;
  transition: all 0.1s;
}

.footer-article-item dd a:hover {
  color: #666;
}

.footer-copyright > p {
  margin-bottom: 0px;
}

.footer-service-item a {
  color: #434343;
}

.footer-service-item a:hover {
  color: #434343;
  text-decoration: none;
}

/*右侧悬浮菜单*/
.fixed-bar {
  position: fixed;
  right: 0px;
  top: 32%;
  z-index: 50;
  _position: absolute;
  transition: all 0.4s cubic-bezier(0, 0.105, 0.035, 1.57);
  _bottom: auto;
  _top: expression(
    eval(
        document.documentElement.scrollTop +
          document.documentElement.clientHeight-this.offsetHeight-(
            parseInt(this.currentStyle.marginTop, 10) ||0
          )- (parseInt(this.currentStyle.marginBottom, 10) ||0)
      )-30
  );
}

.fixed-bar .wide-bar {
  width: 134px;
  background: #fff;
}

.fixed-bar .consult-box {
  border: 1px solid #e6e6e6;
}

.fixed-bar .consult-box .consult-header {
  position: relative;
  height: 36px;
  margin: -1px -1px 0;
  background: #0499e5;
}

.fixed-bar .consult-box .consult-title {
  color: #fff;
  font: normal 16px/36px "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1";
  text-align: center;
  margin: 0px;
  position: relative;
  padding-right: 12px;
}

.clearfix:after,
.layout:after,
.container:after,
.container-layout:after,
.line:after,
.line-small:after,
.line-middle:after,
.line-big:after,
.nav-main:after,
.nav-sub:after,
.navbar:after {
  content: " ";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  overflow: hidden;
}

.fixed-bar .consult-list {
  margin: 0 2px -1px;
  padding: 9px 0;
  border-bottom: 1px dashed #eaeaea;
}

.fixed-bar .gotop {
  position: relative;
  z-index: 1;
  display: block;
  width: 68px;
  height: 66px;
  overflow: hidden;
  margin: -1px 0 0;
  padding: 22px 0 0;
  border: 1px solid #e6e6e6;
  border-top: 1px dashed #eaeaea;
  background-color: #fff;
  color: #666;
  font: normal 14px/36px "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1";
  text-align: center;
}

.fixed-bar .wide-bar .gotop {
  width: 132px;
}

.fixed-bar .icon {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 23px;
  height: 12px;
  overflow: hidden;
  margin: -18px 0 0 -12px;
  background-position: right -220px;
  text-indent: -999em;
}

.icon-times-circle-o {
  right: 20px;
  position: absolute;
  top: 13px;
  width: 12px;
  height: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0, 0.105, 0.035, 1.57);
}

.icon-times-circle-o:hover {
  transform: rotate(180deg);
}

.icon-times-circle-o > img {
  vertical-align: top;
}

.fixed-bar .consult-list li {
  list-style: none;
  padding: 6px 0 6px 23px;
  margin: 0px;
  color: #666;
  font: normal 12px/24px "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1";
}
.fixed-bar .consult-list li:first-child {
  padding: 6px 0 6px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.fixed-bar .consult-list li:first-child span {
  color: red !important;
}

.clearfix a {
  text-decoration: none !important;
}

span.margin-small-left {
  margin-left: 5px;
  color: initial !important;
}

.show-fixed-bar {
  position: fixed;
  right: -48px;
  top: 43%;
  z-index: 40;
  _position: absolute;
  transition: all 0.4s;
  _bottom: auto;
  cursor: pointer;
}

.comeOut {
  right: 0px;
}
.navigation-con-left div {
  font-size: 16px;
}
.navigation-con-left div i {
  font-style: normal;
}
.navigation-con-left > div a {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  position: relative;
  color: #90949a;
  text-decoration: none;
  min-width: 84px;
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
}
.navigation-con-left > div .selected {
  color: #343434 !important;
}

.navigation-con-left > div .selected:after {
  content: "";
  position: absolute;
  left: 0px;
  right: 0px;
  bottom: 0px;
  height: 2px;
  background-color: #414141;
}
