﻿using DG.Web.Framework;

using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek.Models;

using System.ComponentModel;

using ToolGood.Words;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>操作日志</summary>
[DisplayName("操作日志")]
[Description("系统操作日志的管理")]
[AdminArea]
[DHMenu(50,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/Logs", CurrentMenuName = "Logs", CurrentIcon = "&#xe667;", LastUpdate = "20240125")]
public class LogsController : BaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 50;

    /// <summary>
    /// 操作日志列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("操作日志")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 操作日志列表接口
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("操作日志")]
    public IActionResult GetList(String date, String category, String actions, String userName, String success, String key, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true
        };
        DateTime startTime = default;
        DateTime endTime = default;

        if (!date.IsNullOrWhiteSpace())
        {
            var searchDateSplite = date.Trim().Split(GetResource("到"));
            if (searchDateSplite.Length == 2)
            {
                startTime = searchDateSplite[0].ToDateTime();
                endTime = searchDateSplite[1].ToDateTime();
            }
        }

        var userId = -1;

        if (!userName.IsNullOrWhiteSpace())
        {
            var modelUser = UserE.FindByName(userName);
            if (modelUser != null) userId = modelUser.ID;
            else
            {
                return Json(new { code = 0, msg = "success", count = 0, data = new List<Log>() });
            }
        }

        var list = Log.Search(category, actions, success?.ToBoolean(), userId, startTime, endTime, key, pages).Select(item =>
        {
            var PhysicalAddress = item.CreateIP.IPToAddress();
            if (CurrentLanguage != "cn")
            {
                PhysicalAddress = WordsHelper.GetPinyin(PhysicalAddress);
            }
            return new { item.ID, Category = GetResource(item.Category), Action = GetResource(item.Action), item.Success, Remark = LogE.GetRoutName(item.Remark), UserName = UserE.FindByID(item.CreateUserID)?.Name, item.CreateTime, item.CreateIP, PhysicalAddress };
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = list });
    }


    /// <summary>
    /// 模糊查询用户名
    /// </summary>
    /// <param name="Key"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("模糊查询用户名")]
    public IActionResult FindBylikeName(string Key)
    {
        var list = new List<Xmsekect>();
        if (Key.IsNullOrWhiteSpace())
        {
            return Json(list);
        }
        var List1 = UserE.FindByLikeNames(Key);

        foreach (var item in List1)
        {
            var model = new Xmsekect();
            model.name = item.Name;
            model.value = item.ID;
            list.Add(model);
        }
        return Json(list);
    }

    /*
    /// <summary>
    /// 导出操作日志
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("导出操作日志")]
    public IActionResult ExportLog(String date, String category, String actions, String userName, String success, String key)
    {
        var pages = new PageParameter()
        {
            PageIndex = 1,
            PageSize = 10000,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime desc"
        };

        DateTime startTime = default;
        DateTime endTime = default;

        if (!date.IsNullOrWhiteSpace())
        {
            var searchDateSplite = date.Trim().Split(GetResource("到"));
            if (searchDateSplite.Length == 2)
            {
                startTime = searchDateSplite[0].ToDateTime();
                endTime = searchDateSplite[1].ToDateTime();
            }
        }

        var userId = -1;

        if (!userName.IsNullOrWhiteSpace())
        {
            var modelUser = UserE.FindByName(userName);
            if (modelUser != null) userId = modelUser.ID;
            else
            {
                return Json(new { code = 0, msg = "success", count = 0, data = new List<Log>() });
            }
        }

        var list = Log.Search(category, actions, success?.ToBoolean(), userId, startTime, endTime, key, pages).Select(item =>
        {
            var PhysicalAddress = item.CreateIP.IPToAddress();
            if (CurrentLanguage != "cn")
            {
                PhysicalAddress = WordsHelper.GetPinyin(PhysicalAddress);
            }
            return new LogExport { Id = item.ID.ToString(), Category = GetResource(item.Category), Action = GetResource(item.Action), Success = item.Success, Remark = LogE.GetRoutName(item.Remark), UserName = UserE.FindByID(item.CreateUserID)?.Name, CreateTime = item.CreateTime.ToFullString(), CreateIP = item.CreateIP, PhysicalAddress = PhysicalAddress };
        });

        return File(list.ToExcelBytes(ExcelFormat.Xlsx), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{"OperationLog"} {DateTime.Now:yyyyMMddhhmm}.xlsx");
    }
    */
}
/// <summary>
/// 下拉搜素类
/// </summary>

public class Xmsekect
{
    public string name { get; set; }
    public int value { get; set; }
    public bool disabled { get; set; } = false;
}
