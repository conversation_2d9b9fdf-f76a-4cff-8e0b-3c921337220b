﻿using DH.Extensions;
using DH.Services.Components;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;
using Pek;

namespace HlktechSite.Components;

public class KFViewComponent : DHViewComponent
{
    public IViewComponentResult Invoke(Int32 LId)
    {
        var kf = OnlineKeFu.FindByLId(LId).OrderBy(x => x.OType).ThenByDescending(x => x.Sort).ThenBy(x => x.CreateTime);

        if (HttpContext.Request.IsMobileBrowser())
        {
            return View("Default.Mobile", kf);
        }
        else
        {
            return View(kf);
        }            
    }
}
