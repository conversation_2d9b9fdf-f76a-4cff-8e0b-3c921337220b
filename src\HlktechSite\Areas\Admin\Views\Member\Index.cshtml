﻿@using System.Text.Json
@{
    var model = SiteSettingInfo.SiteSettings;
    var MemberGrade = JsonSerializer.Deserialize<List<GradeModel>>(model.MemberGrade);
    MemberGrade.Reverse();
}
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("会员管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("CreateMember")','@T("添加会员")')"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dd>
                    <select name="search_field_name">
                        <!option value="member_id" @(Model.search_field_name == "member_id" ? "selected" : "")>@T("会员Id")</!option>
                        <!option value="member_name" @(Model.search_field_name == "member_name" ? "selected" : "")>@T("会员名")</!option>
                        <!option value="member_email" @(Model.search_field_name == "member_email" ? "selected" : "")>@T("电子邮箱")</!option>
                        <!option value="member_mobile" @(Model.search_field_name == "member_mobile" ? "selected" : "")>@T("手机号码")</!option>
                        <!option value="member_truename" @(Model.search_field_name == "member_truename" ? "selected" : "")>@T("真实姓名")</!option>
                    </select>
                </dd>
                <dd>
                    <input type="text" value="@Model.search_field_value" name="search_field_value" class="txt">
                </dd>
                <dd>
                    <select name="search_sort">
                        <!option value="">@T("排序")</!option>
                        <!option value="lastlogin desc" @(Model.search_sort == "lastlogin desc" ? "selected" : "")>@T("最后登录")</!option>
                        <!option value="logins desc" @(Model.search_sort == "logins desc" ? "selected" : "")>@T("登录次数")</!option>
                        <!option value="logins desc" @(Model.search_sort == "registertime desc" ? "selected" : "")>@T("注册时间")</!option>
                    </select>
                </dd>
                <dd>
                    <select name="search_state">
                        <!option value="">@T("会员状态")</!option>
                        <!option value="no_informallow" @(Model.search_state == "no_informallow" ? "selected" : "")>@T("禁止举报")</!option>
                        <!option value="no_isbuy" @(Model.search_state == "no_isbuy" ? "selected" : "")>@T("禁止购买")</!option>
                        <!option value="no_isallowtalk" @(Model.search_state == "no_isallowtalk" ? "selected" : "")>@T("禁止发表言论")</!option>
                        <!option value="no_memberstate" @(Model.search_state == "no_memberstate" ? "selected" : "")>@T("禁止登录")</!option>
                    </select>
                </dd>
                <dd>
                    <select name="search_grade">
                        <!option value='-1'>@T("会员级别")</!option>
                        <!option value="1" @(Model.search_grade == 1 ? "selected" : "")>V1</!option>
                        <!option value="2" @(Model.search_grade == 2 ? "selected" : "")>V2</!option>
                        <!option value="3" @(Model.search_grade == 3 ? "selected" : "")>V3</!option>
                        <!option value="4" @(Model.search_grade == 4 ? "selected" : "")>V4</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("会员")</th>
                <th></th>
                <th>@T("SSOID")</th>
                <th>@T("登陆次数")</th>
                <th>@T("最后登录")</th>
                <th>@T("积分")</th>
                <th>@T("预存款")</th>
                <th>@T("经验值")</th>
                <th>@T("会员级别")</th>
                <th>@T("登录")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.UserList)
            {
                var userDatail = UserDetail.FindById(item.ID) as UserDetail;
                if (userDatail == null) {
                    userDatail = new UserDetail();
                    userDatail.Id = item.ID;
                    userDatail.Save();
                }

                var Grade = MemberGrade.Find(e => e.exppoints <= userDatail.Points);

            <tr id="<EMAIL>" style="background: rgb(255, 255, 255);">
                <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.ID"></td>
                <td class="w48 picture">
                    <div class="size-44x44">
                        <span class="thumb">
                            <i></i>
                            @{
                                    var Avatar = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), model.DefaultUserPortrait.IsNotNullAndWhiteSpace() ? model.DefaultUserPortrait : $"{DH.DHSetting.Current.UploadPath}/common/default_user_portrait.gif");
                            }
                            <img src="@Avatar" width="44" height="44">
                        </span>
                    </div>
                </td>
                <td>
                    <p class="name"><strong>@item.Name</strong>(真实姓名: @userDatail.TrueName)</p>
                    <p class="smallfont">昵称:&nbsp;@item.DisplayName</p>
                    <p class="smallfont">注册时间:&nbsp;@item.RegisterTime</p>

                    <div class="im">
                        @if (!string.IsNullOrWhiteSpace(item.Mail))
                        {
                            <span class="email">
                                <a href="mailto:@item.Mail" class=" yes" title="电子邮箱:@item.Mail">@item.Mail</a>
                            </span>
                        }

                        @if (!string.IsNullOrWhiteSpace(userDatail.WangWang))
                        {
                            <a target="_blank" href="http://www.taobao.com/webww/ww.php?ver=3&touid=@userDatail.WangWang&siteid=cntaobao&status=1&charset=utf-8" title="WangWang: @userDatail.WangWang"><img border="0" src="http://amos.alicdn.com/realonline.aw?v=2&uid=berin141503&site=cntaobao&s=1&charset=utf-8" /></a>
                        }
                        @if (!string.IsNullOrWhiteSpace(userDatail.QQ))
                        {
                            <a target="_blank" href="http://wpa.qq.com/msgrd?v=3&uin=@userDatail.QQ&site=qq&menu=yes" class="" title="QQ: @userDatail.QQ"><img border="0" src="http://wpa.qq.com/pa?p=2:@userDatail.QQ:52" /></a>
                        }
                        <div style="font-size:13px; padding-left:10px">&nbsp;&nbsp;@item.Mobile</div>
                    </div>
                </td>
                <td class="align-center">@item.SsoId</td>
                <td class="align-center">@item.Logins</td>
                <td class="w150 align-center">
                    <p>@item.LastLogin</p>
                    <p>@item.LastLoginIP</p>
                </td>
                <td>@userDatail.Points</td>
                <td class="align-center">
                    <p>可用:&nbsp;<strong class="red">@userDatail.AvailablePredeposit.ToString("#0.00")</strong>&nbsp;元</p>
                    <p>冻结:&nbsp;<strong class="red">@userDatail.FreezePredeposit.ToString("#0.00")</strong>&nbsp;元</p>
                </td>
                <td>@userDatail.ExpPoints</td>
                <td>@Grade?.level_name</td>
                <td>@(item.Enable ? "允许" : "禁止")</td>
                <td>
                    <a href="javascript:dsLayerOpen('@Url.Action("EditMember", new { Id = item.ID })','@T("编辑")-@item.Name')" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                    <a href="javascript:dsLayerOpen('@Url.Action("EditRole", new { Id = item.ID })','@T("编辑权限")-@item.Name')" class="dsui-btn-edit"><i class="iconfont"></i>@T("权限")</a>
                    <a href="javascript:dsLayerOpen('@Url.Action("SendNotice", "UserNotice", new { Name = item.Name })','@T("编辑")-@item.Name')')" class="dsui-btn-add"><i class="iconfont"></i>通知</a>
                    @if (!item.Enable)
                    {
                        <a href="javascript:dsLayerConfirm('@Url.Action("Disable", new { Ids = item.ID, State = 1 })','您确定要启用吗?')" class="dsui-btn-add"><i class="iconfont"></i>允许登录</a>
                    }
                    else
                    {
                        <a href="javascript:dsLayerConfirm('@Url.Action("Disable", new { Ids = item.ID, State = 0 })','您确定要禁用吗?')" class="dsui-btn-del"><i class="iconfont"></i>禁止登录</a>
                    }
                    <a href="javascript:dsLayerOpen('@Url.Action("PdAdd", "Predeposit", new { Id = item.ID })','调节预存款<EMAIL>')" class="dsui-btn-edit"><i class="iconfont"></i>调节预存款</a>
                </td>
            </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("禁止登录")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script asp-location="Footer">
    function submit_delete(ids_str) {
        _uri = "@Url.Action("Disable")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要禁用吗?');
    }
</script>