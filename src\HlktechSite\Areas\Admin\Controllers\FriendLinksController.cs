﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH;
using DH.Entity;
using DH.Helpers;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Helpers;
using Pek.IO;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>友情链接管理</summary>
[DisplayName("友情链接管理")]
[Description("用于友情链接管理的管理")]
[AdminArea]
[DHMenu(50,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/FriendLinks", CurrentMenuName = "FriendLinksList", CurrentIcon = "&#xe67d;", LastUpdate = "20240125")]
public class FriendLinksController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 50;

    /// <summary>
    /// 友情链接列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("友情链接列表")]
    public IActionResult Index(string name, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        var list = FriendLinks.Search(name.SafeString().Trim(), pages).Select(x=>new FriendLinks { Name=x.Name, Id=x.Id, Pic = x.Pic.IsNotNullAndWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Pic) : "", Url =x.Url, Sort=x.Sort});
        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });
        return View(viewModel);
    }

    /// <summary>
    /// 添加友情链接管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加友情链接")]
    public IActionResult AddFriendLinks()
    {
        ViewBag.LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        var Sort = FriendLinks.FindMax("Sort");
        ViewBag.Sort = Sort + 1;
        return View();
    }

    /// <summary>
    /// 新增提交
    /// </summary>
    /// <param name="default_user_portrait"></param>
    /// <param name="Title"></param>
    /// <param name="Urls"></param>
    /// <param name="Sort"></param>
    /// <param name="FType"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("新增提交")]
    [HttpPost]
    public IActionResult AddFriendLinks(IFormFile default_user_portrait, string Title, string Urls, int Sort, short FType)
    {

        if (Title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = FriendLinks.FindByName(Title.SafeString().Trim());
        if (ex != null)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }

        var Model = new FriendLinks();
        Model.Name = Title.SafeString().Trim();
        Model.Url = Urls.SafeString().Trim();
        Model.Sort = Sort;
        Model.FType = FType;
        Model.Insert();
        if (default_user_portrait != null)
        {
            var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
            if (!bytes.IsImageFile())
            {
                return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
            }

            var filename = $"{"FriendLinks" + Model.Id}{Path.GetExtension(default_user_portrait.FileName)}";
            var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"FriendLinks/{filename}");
            var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
            saveFileName.EnsureDirectory();
            default_user_portrait.SaveAs(saveFileName);
            Model.Pic = filepath.Replace("\\", "/");
            Model.Update();
        }
        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        using (var tran1 = FriendLinksLan.Meta.CreateTrans())
        {
            var filea = Request.Form.Files;
            var list = filea.Count();
            foreach (var item in Languagelist)
            {

                var Name = $"[{item.Id}].default_user_portrait";                                  

                var aaaa = new FriendLinksLan();
                aaaa.Name = GetRequest($"[{item.Id}].Title").SafeString().Trim();
                aaaa.Url = GetRequest($"[{item.Id}].Url").SafeString().Trim();
                aaaa.Sort = (GetRequest($"[{item.Id}].Sort")).ToInt();
                aaaa.FType = (GetRequest($"[{item.Id}].FType")).ToDGShort();
                aaaa.FId = Model.Id;
                aaaa.LId = item.Id;
                aaaa.Insert();
                var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                if (file != null)
                {
                    var bytes = file.OpenReadStream().ReadBytes(file.Length);
                    if (!bytes.IsImageFile())
                    {
                        return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                    }

                    var filename = $"{"FriendLinkslan" + aaaa.Id}{Path.GetExtension(file.FileName)}";
                    var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"FriendLinkslan/{filename}");
                    var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                    saveFileName.EnsureDirectory();
                    file.SaveAs(saveFileName);
                    aaaa.Pic = filepath.Replace("\\", "/");
                    aaaa.Update();
                }

            }
            tran1.Commit();
        }
        FriendLinksLan.Meta.Cache.Clear("");//清除缓存
        //return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
        return MessageTip(GetResource("保存成功！"));
    }
    /// <summary>
    /// 单页文章管理修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("单页文章管理修改页面")]
    public IActionResult EditFriendLinks(Int32 Id)
    {
        ViewBag.LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        var Model = FriendLinks.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        //dynamic viewModel = new ExpandoObject();
        //viewModel.Model = Model;
        ViewBag.Images = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Model.Pic.IsNotNullAndWhiteSpace() ? Model.Pic : "");
        return View(Model);
    }


    /// <summary>
    /// 友情链接修改接口
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="default_user_portrait"></param>
    /// <param name="Title"></param>
    /// <param name="Urls"></param>
    /// <param name="Sort"></param>
    /// <param name="FType"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("友情链接修改接口")]
    public IActionResult EditFriendLinks(Int32 Id, IFormFile default_user_portrait, string Title, string Urls, int Sort, short FType)
    {
        if (Title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = FriendLinks.FindByName(Title);
        if (ex != null && ex.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        var Model = FriendLinks.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
        }
        Model.Name = Title.SafeString().Trim();
        Model.Url = Urls.SafeString().Trim();
        Model.Sort = Sort;
        Model.FType = FType;
        if (default_user_portrait != null)
        {
            var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
            if (!bytes.IsImageFile())
            {
                return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
            }

            var filename = $"{"FriendLinks" + Model.Id}{Path.GetExtension(default_user_portrait.FileName)}";
            var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"FriendLinks/{filename}");
            var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
            saveFileName.EnsureDirectory();
            default_user_portrait.SaveAs(saveFileName);
            Model.Pic = filepath.Replace("\\", "/");
        }
        Model.Update();

        var list = FriendLinksLan.FindAllByFId(Model.Id);  //获取到的当前权限的语言

        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        using (var tran1 = FriendLinksLan.Meta.CreateTrans())
        {
            var filea = Request.Form.Files;
            foreach (var item in Languagelist)
            {
              
                var aaaa = list.Find(x => x.LId == item.Id);

                if (aaaa == null)
                {
                    aaaa = new FriendLinksLan();
                    aaaa.Name = (GetRequest($"[{item.Id}].Title")).SafeString().Trim();
                    aaaa.Url = (GetRequest($"[{item.Id}].Url")).SafeString().Trim();
                    aaaa.Sort = (GetRequest($"[{item.Id}].Sort")).ToInt();
                    aaaa.FType = (GetRequest($"[{item.Id}].FType")).ToDGShort();
                    aaaa.FId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Insert();
                }
                else {

                    aaaa.Name = (GetRequest($"[{item.Id}].Title")).SafeString().Trim();
                    aaaa.Url = (GetRequest($"[{item.Id}].Url")).SafeString().Trim();
                    aaaa.Sort = (GetRequest($"[{item.Id}].Sort")).ToInt();
                    aaaa.FType = (GetRequest($"[{item.Id}].FType")).ToDGShort();
                    aaaa.FId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Update();
                }

                var Name = $"[{item.Id}].default_user_portrait";
         
                var file = filea.Where(x => x.Name == Name).FirstOrDefault();
                if (file != null)
                {
                    var bytes = file.OpenReadStream().ReadBytes(file.Length);
                    if (!bytes.IsImageFile())
                    {
                        return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                    }

                    var filename = $"{"FriendLinkslan" + aaaa.Id}{Path.GetExtension(file.FileName)}";
                    var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"FriendLinkslan/{filename}");
                    var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                    saveFileName.EnsureDirectory();
                    file.SaveAs(saveFileName);
                    aaaa.Pic = filepath.Replace("\\", "/");
                    aaaa.Update();
                }
            }
            tran1.Commit();
        }




        SingleArticle.Meta.Cache.Clear("");
        //return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
        return MessageTip(GetResource("保存成功！"));
    }

    /// <summary>
    /// 友情链接管理删除
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("友情链接管理删除")]
    public IActionResult Delete(string ids)
    {
        var res = new DResult();
        using (var tran1 = FriendLinksLan.Meta.CreateTrans()) {
            var List = FriendLinks.FindByIds(ids.Trim(','));
            FriendLinks.DelByIds(ids.Trim(','));
            foreach (var item in List)
            {
                if (item.Pic.IsNotNullAndWhiteSpace())
                {
                    DHSetting.Current.WebRootPath.GetFullPath().CombinePath(item.Pic).AsFile().Delete(); //删除主表中的图片
                }
                var LANlist = FriendLinksLan.FindAllByFId(item.Id); //查询出翻译表的删除数据
                FriendLinksLan.DelByFIds(ids.Trim(',')); //根据ids删除翻译表数据
                foreach (var row in LANlist)
                {
                    if (row.Pic.IsNotNullAndWhiteSpace())
                    {
                        DHSetting.Current.WebRootPath.GetFullPath().CombinePath(row.Pic).AsFile().Delete(); //删除翻译的图片
                    }
                }
            }
            FriendLinksLan.DelByFIds(ids.Trim(','));
            tran1.Commit();
        }
           
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }


    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload.Length;
        fileModel.FileType = 1;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"SingleArticle/{filename}");
        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath.Replace("\\", "/");
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = Pek.Helpers.DHWeb.GetSiteUrl() + "/" + filepath.Replace("\\", "/") });
    }

    /// <summary>
    /// 图片删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片删除")]
    public IActionResult DeleteImg(Int32 Id)
    {
        var model = UploadInfo.FindById(Id);

        if (model != null)
        {
            DHSetting.Current.WebRootPath.GetFullPath().CombinePath(model.FileUrl).AsFile().Delete();
            model.Delete();
        }

        return Ok("true");
    }

    /// <summary>
    /// 根据名称查询
    /// </summary>
    /// <param name="title"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("根据名称查询")]
    public IActionResult FinByName(string title, int Id)
    {
        var Model = SingleArticle.FindByName(title.SafeString().Trim());
        if (Id != 0)
        {
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }
        }
        else
        {
            if (Model != null)
            {
                return Json(false);
            }
        }

        return Json(true);
    }


    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = FriendLinks.FindById(Id);

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = FriendLinks.FindByName(value);
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.Sort = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }




    ///// <summary>查询产品型号名称</summary>
    ///// <returns></returns>
    //[DisplayName("查询产品型号名称")]
    //[EntityAuthorize(PermissionFlags.Detail)]
    //public IActionResult GetbyProductName(string keyword)
    //{
    //    var List = ProductModel.FindByLikeNames(keyword.SafeString().Trim()).Select(x => new { name = x.Name, value = x.Id });
    //    return Json(List);
    //}
}
