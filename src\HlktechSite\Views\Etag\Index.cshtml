﻿@{
    Html.AppendTitleParts(T("电子标签").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    var cdn = CDN.GetCDN();
}
<style>
    li {
        list-style: none;
    }

    .title1 {
        color: #333 !important;
        background-color: #fff;
    }

        .title1:hover {
            background-color: #eee !important;
        }

    .title11 {
        color: #fff !important;
        background-color: #337ab7;
    }

        .title11:hover {
            background-color: #337ab7 !important;
        }

    .etag {
        background: url("@(cdn)/images/电子价签.png") no-repeat;
        /*background-size:100% 100%;*/
        /*height: 4709px;*/
    }

        .etag .banner {
            height: 55vh;
            background:url("@(cdn)/images/etag_banner.png") no-repeat;
            background-size:100% 100%;
            background-color: cornflowerblue;
            padding-top: 0.5px;
            /*position:absolute;
            left:0;
            top:0;*/
        }

            .etag .banner > a {
                color: white;
            }

            .etag .banner h1 {
                margin-top: 149px;
                text-align: center;
                font-size: 56px;
                color: white;
            }

            .etag .banner p {
                width: 844px;
                margin: 0 auto;
                margin-top: 35px;
                text-align: center;
                font-size: 20px;
                color: white;
            }

        .etag .content-block1 {
            /*background-color: transparent;*/
            background-color:#FAFAFA;
            height: 980px;
            box-sizing: border-box;
            padding-top: 30px;
        }

            .etag .content-block1 > div {
                display: flex;
                justify-content: space-around;
                flex-direction: column;
                align-items: center;
            }

                .etag .content-block1 > div p {
                    text-align: center;
                }

                .etag .content-block1 > div h3 {
                    font-size: 2em;
                    margin-bottom: 50px;
                }

            .etag .content-block1 .more {
                display: block;
                width: 150px;
                height: 35px;
                line-height: 35px;
                text-align: center;
                border: 1px solid #48A9F3;
                margin-top: 38px;
            }

            .etag .content-block1 .div2 {
                margin-top: 50px;
            }

                .etag .content-block1 .div2 h3 {
                    margin-bottom: 20px;
                }

        .etag .content-block2 {
            background-color: #F4F4F4;
            /*height: 980px;*/
            box-sizing: border-box;
            padding-top: 45px;
        }



            .etag .content-block2 .tabpg {
                margin-top: 40px;
            }

                .etag .content-block2 .tabpg .tabs-box {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .etag .content-block2 .tabpg .tabs {
                    width: 600px;
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                }

                    .etag .content-block2 .tabpg .tabs li {
                        border-radius: 30px;
                        border: 1px solid #ccc;
                        color: #aaa;
                        width: 120px;
                        height: 40px;
                        line-height: 40px;
                        text-align: center;
                        list-style: none;
                        font-size: 1.1em;
                        cursor: pointer;
                    }

                    .etag .content-block2 .tabpg .tabs .this {
                        background-color: #1C9EFF;
                        border-style: none;
                        color: white;
                    }

                .etag .content-block2 .tabpg .pages-box {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-top: 20px;
                }

                .etag .content-block2 .tabpg .pages {
                    overflow: hidden;
                    height: 750px;
                    width: 1300px;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                }

                    .etag .content-block2 .tabpg .pages .page {
                        display: flex;
                        justify-content: space-around;
                        flex-wrap: wrap;
                        align-items: flex-start;
                        height: 100%;
                        width: 100%;
                        flex-shrink: 0;
                        transition: all 0.5s ease-out;
                    }

                        .etag .content-block2 .tabpg .pages .page .item {
                            background-color: white;
                            border-radius: 5px;
                            /*box-shadow: 2px 2px 8px rgb(114 114 114 / 30%);*/
                            width: 280px;
                            height: 350px;
                            margin-bottom: 20px;
                            box-sizing: border-box;
                            overflow: hidden;
                            margin: 10px 15px;
                        }

                            .etag .content-block2 .tabpg .pages .page .item .imgbox {
                                height: 83%;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                            }

                                .etag .content-block2 .tabpg .pages .page .item .imgbox img {
                                    width: 90%;
                                    height: 80%;
                                    background-color: greenyellow;
                                }

                            .etag .content-block2 .tabpg .pages .page .item .about {
                                height: 17%;
                                display: flex;
                                justify-content: space-between;
                                padding: 0px 5px;
                                align-items: center;
                                padding: 0 8px;
                                box-sizing: border-box;
                            }

                                .etag .content-block2 .tabpg .pages .page .item .about .name {
                                    font-family: 微软雅黑,sans-serif;
                                    font-size: 1.3em;
                                    font-weight: 300;
                                    max-width: 60%;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                    overflow: hidden;
                                    cursor: pointer;
                                    font-weight: bold;
                                    color: #555;
                                }

                                .etag .content-block2 .tabpg .pages .page .item .about .price {
                                    font-family: 仿宋,sans-serif;
                                    color: red;
                                    font-size: 1.3em;
                                }

                                    .etag .content-block2 .tabpg .pages .page .item .about .price b {
                                        font-size: 1.5em;
                                    }

        .etag .content-block3 {
            background-color: #FAFAFA;
            height: 1030px;
            box-sizing: border-box;
            padding-top: 100px;
        }

            .etag .content-block3 .blocks {
                display: flex;
                justify-content: center;
                align-items: stretch;
                height: 750px;
                margin-top: 54px;
            }

                .etag .content-block3 .blocks .center {
                    width: 1300px;
                    display: grid;
                    grid-template-columns: repeat(3,1fr);
                    grid-template-rows: repeat(2, 1fr);
                    grid-gap: 30px 15px;
                }

                    .etag .content-block3 .blocks .center .item {
                        background-color: rgba(255,255,255,1);
                        /*border: 1px solid black;*/
                        box-shadow: 2px 2px 8px #ccc;
                        overflow: hidden;
                        box-sizing: border-box;
                    }

                        .etag .content-block3 .blocks .center .item .imgbox {
                            height: 78%;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }

                            .etag .content-block3 .blocks .center .item .imgbox img {
                                /*background-color: #29c5c5;*/
                                width: 100%;
                                height: 100%;
                            }

                        .etag .content-block3 .blocks .center .item .about {
                            height: 22%;
                            padding-left: 25px;
                        }

                            .etag .content-block3 .blocks .center .item .about h4 {
                                font-weight: 500;
                                font-size: 1.6em;
                                color: #444;
                            }

                            .etag .content-block3 .blocks .center .item .about p {
                                font-weight: 500;
                                font-size: 1.1em;
                            }

        .etag .content-block4 {
            height: 780px;
            box-sizing: border-box;
            background-color: rgba(255, 255, 255, 1);
            padding-top: 100px;
        }

            .etag .content-block4 .div1 {
            }

            .etag .content-block4 .hzjg {
                display: flex;
                justify-content: center;
            }

                .etag .content-block4 .hzjg img {
                    width: 1400px;
                }
</style>
<div class="etag">
    <div class="banner">
        <h1>@T("电子价签")</h1>
        <p>@T("构建各领域互联网化的商品新零售服务模式")</p>
    </div>
    <div class="content-block1">
        <div class="div1">
            <h3>@T("灵创电子价格价签")</h3>
            <div>
                <p>@T("灵创电子价签是一种带有信息收发功能的电子显示装置。他是基于服务器系统+无线")</p>
                <p>@T("接入点+电子货架标签+智能手机终端组合而成")</p>
            </div>
            <a class="more" href="/">@T("了解更多")></a>
        </div>
        <div class="div2">
            <h3>@T("云价签系统架构图")</h3>
            <p>@T("完美解决客户所有相关需求")</p>
            <img src="@(cdn)/images/dasr.png" style="  background-color: #29c5c5;width: 700px;height: 500px;" alt="" />
        </div>
    </div>
    <div class="content-block2">
        <div class="div1">
            <h3 style=" text-align: center;font-size:2em;margin-bottom:18px;">@T("产品一览")</h3>
            <p style="text-align:center">@T("我们致力于为每位客户提供定制化服务，完美解决客户所有相关需求")</p>
        </div>
        <div class="tabpg">
            <div class="tabs-box">
                <ul class="tabs">
                    <li class="this">@T("标准版")</li>
                    <li>@T("仓储版")</li>
                    <li>@T("配件")</li>
                    <li>@T("基站")</li>
                </ul>
            </div>
            <div class="pages-box">
                <div class="pages">
                    <div id="page1" class="page">
                        @for (int i = 0; i < 8; i++)
                        {
                            <div class="item">
                                <a href="#" class="imgbox">
                                    <img src="" style="background-color: aquamarine;" alt="" />
                                </a>
                                <div class="about">
                                    <a href="#" class="name">@T("测试产品测试产品测试产品测试产品")</a>
                                    <span class="price">@T($"￥<b>199</b>起")</span>
                                </div>
                            </div>
                        }
                    </div>
                    <div id="page2" class="page">
                        @for (int i = 0; i < 8; i++)
                        {
                            <div class="item">
                                <a href="#" class="imgbox">
                                    <img src="" style="background-color: aquamarine;" alt="" />
                                </a>
                                <div class="about">
                                    <a href="#" class="name">@T("测试产品测试产品测试产品测试产品")</a>
                                    <span class="price">@T($"￥<b>199</b>起")</span>
                                </div>
                            </div>
                        }
                    </div>
                    <div id="page3" class="page">
                        @for (int i = 0; i < 8; i++)
                        {
                            <div class="item">
                                <a href="#" class="imgbox">
                                    <img src="" style="background-color: aquamarine;" alt="" />
                                </a>
                                <div class="about">
                                    <a href="#" class="name">@T("测试产品测试产品测试产品测试产品")</a>
                                    <span class="price">@T($"￥<b>199</b>起")</span>
                                </div>
                            </div>
                        }
                    </div>
                    <div id="page4" class="page">
                        @for (int i = 0; i < 8; i++)
                        {
                            <div class="item">
                                <a href="#" class="imgbox">
                                    <img src="" style="background-color: aquamarine;" alt="" />
                                </a>
                                <div class="about">
                                    <a href="#" class="name">@T("测试产品测试产品测试产品测试产品")</a>
                                    <span class="price">@T($"￥<b>199</b>起")</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
        <script>
            //var pw = $(".etag .content-block2 .tabpg .pages").width();
            var $page = $(".etag .content-block2 .tabpg .pages").children().first();           
            $(".tabpg .tabs li").on("click",
                function(e) {
                    $(this).parent().children().removeClass("this");
                    var idx = $(this).addClass("this").index();                    
                    var left = idx * 100;
                    $page.css("marginLeft",`-${left}%`);
                });
        </script>
    </div>

    <div class="content-block3">
        <div class="div1">
            <h3 style=" text-align: center;font-size:2em;margin-bottom:18px;">应用场景</h3>
            <p style="text-align:center">电子价签在各个领域都有广泛应用，不限于如下行业</p>
        </div>
        <div class="blocks">
            <div class="center">
                <div class="item">
                    <div class="imgbox">
                        <img style="" src="@(cdn)/images/yt1.png" alt="" />
                    </div>
                    <div class="about">
                        <h4>零售连锁</h4>
                        <p>节省频繁更换价签的人力成本</p>
                    </div>
                </div>
                <div class="item">
                    <div class="imgbox">
                        <img style="" src="@(cdn)/images/yt2.png" alt="" />
                    </div>
                    <div class="about">
                        <h4>海鲜行业</h4>
                        <p>传统纸质价签遇水便会受损</p>
                    </div>
                </div>
                <div class="item">
                    <div class="imgbox">
                        <img style="" src="@(cdn)/images/yt3.png" alt="" />
                    </div>
                    <div class="about">
                        <h4>生鲜</h4>
                        <p>实现线上线下价格同步</p>
                    </div>
                </div>
                <div class="item">
                    <div class="imgbox">
                        <img style="" src="@(cdn)/images/yt4.png" alt="" />
                    </div>
                    <div class="about">
                        <h4>果蔬</h4>
                        <p>促销活动急速生成</p>
                    </div>
                </div>
                <div class="item">
                    <div class="imgbox">
                        <img style="" src="@(cdn)/images/yt5.png" alt="" />
                    </div>
                    <div class="about">
                        <h4>数码3C</h4>
                        <p>确保全渠道的数字化战略部署</p>
                    </div>
                </div>
                <div class="item">
                    <div class="imgbox">
                        <img style="" src="@(cdn)/images/yt6.png" alt="" />
                    </div>
                    <div class="about">
                        <h4>仓库</h4>
                        <p>提高空间，人员和设备的使用率</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="content-block4">
        <div class="div1">
            <h3 style=" text-align: center;font-size:2em;margin-bottom:18px;">@T("合作机构")</h3>
            <p style="text-align:center">@T("极灵思创与多家机构建立合作关系")</p>
        </div>
        <div class="hzjg">
            <img src="~/images/hezuojigou.png" alt="" />
        </div>
    </div>
</div>


