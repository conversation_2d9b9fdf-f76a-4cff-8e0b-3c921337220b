﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>成品跳转表</summary>
public partial interface IJumpProduct
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>产品名称</summary>
    String? Name { get; set; }

    /// <summary>推文链接</summary>
    String? InfoUrl { get; set; }

    /// <summary>内容</summary>
    String? Content { get; set; }

    /// <summary>国内Android下载地址</summary>
    String? AndroidPaths { get; set; }

    /// <summary>国外Android下载地址</summary>
    String? AndroidPaths1 { get; set; }

    /// <summary>IOS下载地址</summary>
    String? IosPaths { get; set; }

    /// <summary>App Logo</summary>
    String? AppLogo { get; set; }

    /// <summary>京东链接</summary>
    String? JdUrl { get; set; }

    /// <summary>淘宝链接</summary>
    String? TbUrl { get; set; }

    /// <summary>拼多多链接</summary>
    String? PddUrl { get; set; }

    /// <summary>广告词</summary>
    String? AdWord { get; set; }

    /// <summary>点击数</summary>
    Int32 Clicks { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
