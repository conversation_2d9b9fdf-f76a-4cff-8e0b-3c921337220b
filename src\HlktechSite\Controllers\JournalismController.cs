﻿using DG.Cube;
using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Seo;

using System.Dynamic;
using XCode;

namespace HlktechSite.Controllers;

//新闻资讯
[DHSitemap(IsUse = true)]
public class JournalismController : DGBaseControllerX
{
    public override IEnumerable<DHSitemap> CreateSiteMap()
    {
        var list = new List<DHSitemap>();

        list.Add(new DHSitemap
        {
            SType = SiteMap.栏目首页,
            ActionName = "Index",
            ControllerName = "Journalism"
        });

        var ProductCategoryList = ArticleCategory.GetAll();
        foreach (var row in ProductCategoryList)
        {
            list.Add(new DHSitemap
            {
                SType = SiteMap.栏目页,
                ActionName = "List",
                ControllerName = "Journalism",
                Data = new Dictionary<String, Object> { { "AId", row.Id } }
            });
        }

        var GoodList = Article.GetAll();
        foreach (var row in GoodList)
        {
            list.Add(new DHSitemap
            {
                SType = SiteMap.内容页,
                ActionName = "Details",
                ControllerName = "Journalism",
                Data = new Dictionary<String, Object> { { "Id", row.Id } }
            });
        }

        var langlist = Language.FindByStatus();
        var defaultlang = Language.FindByDefault();

        foreach (var item in langlist)
        {
            if (item.Id == defaultlang.Id) continue;
            list.Add(new DHSitemap
            {
                SType = SiteMap.栏目首页,
                ActionName = "Index",
                ControllerName = "Journalism",
                UniqueSeoCode = item.UniqueSeoCode
            });

            foreach (var row in ProductCategoryList)
            {
                list.Add(new DHSitemap
                {
                    SType = SiteMap.栏目页,
                    ActionName = "List",
                    ControllerName = "Journalism",
                    UniqueSeoCode = item.UniqueSeoCode,
                    Data = new Dictionary<String, Object> { { "AId", row.Id } }
                });
            }

            foreach (var row in GoodList)
            {
                list.Add(new DHSitemap
                {
                    SType = SiteMap.内容页,
                    ActionName = "Details",
                    ControllerName = "Journalism",
                    UniqueSeoCode = item.UniqueSeoCode,
                    Data = new Dictionary<String, Object> { { "Id", row.Id } }
                });
            }

        }

        return list;
    }

    /// <summary>
    /// 新闻资讯首页
    /// </summary>
    /// <param name="Key">关键词</param>
    /// <param name="p"></param>
    /// <param name="AId">新闻资讯的类型</param>
    /// <returns></returns>
    public IActionResult Index(string Key, int AId = 0, int p = 1)
    {
        Key = Key.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        IEnumerable<Article> articleslIst;
        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 9,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime desc, Sort desc",
        };
        viewModel.AId = AId;
        viewModel.Key = Key;
        IEnumerable<ArticleCategory> ArticleTypelist;

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
            ArticleTypelist = ArticleCategoryLan.FindAllByLevel(WorkingLanguage.Id).Select(x => new ArticleCategory
            {
                Id = x.AId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.articleCategory.Name : x.Name
            });
        else
            ArticleTypelist = ArticleCategory.FindAllByLevel(0);
        viewModel.ArticleTypelist = ArticleTypelist;

        if (localizationSettings.IsEnable)
        {
            articleslIst = ArticleLan.Searchs(Key, AId, pages, WorkingLanguage.Id).Select(x => new Article
            {
                Id = x.AId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.article?.Name : x.Name,
                Url = x.article?.Url.SafeString() ?? "",
                Pic = x.Pic.IsNullOrWhiteSpace() ? x.article?.Pic : x.Pic,
                Summary = x.Summary.IsNullOrWhiteSpace() ? x.article?.Summary : x.Summary,
                CreateTime = x.article.CreateTime
            });
        }
        else
        {
            articleslIst = Article.Searchs(Key, AId, pages);
        }
        viewModel.JournalismList = articleslIst;
        viewModel.Model = ArticleTypelist.FirstOrDefault(e => e.Id == AId);

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());

        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "Index", "Journalism", "", dic, "p");
        return DGView(viewModel, true);
    }

    /// <summary>
    /// 新闻资讯首页
    /// </summary>
    /// <param name="Key">关键词</param>
    /// <param name="p"></param>
    /// <param name="AId">新闻资讯的类型</param>
    /// <returns></returns>
    public IActionResult List(string Key, int AId = 0, int p = 1)
    {
        var model = ArticleCategory.FindById(AId);
        if (model == null)
        {
            return View404();
        }

        Key = Key.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        IEnumerable<Article> articleslIst;
        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 9,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime desc, Sort desc",
        };
        viewModel.AId = AId;
        viewModel.Key = Key;
        IEnumerable<ArticleCategory> ArticleTypelist;

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
            ArticleTypelist = ArticleCategoryLan.FindAllByLevel(WorkingLanguage.Id).Select(x => new ArticleCategory
            {
                Id = x.AId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.articleCategory.Name : x.Name
            });
        else
            ArticleTypelist = ArticleCategory.FindAllByLevel(0);
        viewModel.ArticleTypelist = ArticleTypelist;

        if (localizationSettings.IsEnable)
        {
            articleslIst = ArticleLan.Searchs(Key, AId, pages, WorkingLanguage.Id).Select(x => new Article
            {
                Id = x.AId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.article?.Name : x.Name,
                Url = x.article.Url.SafeString(),
                Pic = x.Pic.IsNullOrWhiteSpace() ? x.article?.Pic : x.Pic,
                Summary = x.Summary.IsNullOrWhiteSpace() ? x.article?.Summary : x.Summary,
                CreateTime = x.article.CreateTime
            });
        }
        else
        {
            articleslIst = Article.Searchs(Key, AId, pages);
        }
        viewModel.JournalismList = articleslIst;
        viewModel.Model = ArticleTypelist.FirstOrDefault(e => e.Id == AId);

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
        dic.TryAdd("AId", AId.ToString());

        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "List", "Journalism", "", dic, "p");
        return DGView(viewModel, "Index", true);
    }

    /// <summary>
    /// 新闻资讯详情页
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    public IActionResult Details(int Id)
    {
        dynamic viewModel = new ExpandoObject();

        //随机获取5条产品
        var goodslist = Goods.FindAll();
        List<Goods> list = new();
        var exp = new WhereExpression();
        exp &= GoodsLan._.LId == WorkingLanguage.Id;
        var goodslanlist = GoodsLan.FindAll(exp);
        if (goodslist.Count > 0)
        {
            for (int i = 0; i < 5; i++)
            {
                Random r = new();
                int num = r.Next(1, goodslist.Count + 1);
                var data = goodslist[num - 1];
                var goodslan = goodslanlist.FirstOrDefault(e => e.GId == data.Id);
                if(goodslan != null)
                {
                    if(!goodslan.Name.IsNullOrWhiteSpace()) data.Name = goodslan.Name;
                    if(!goodslan.AdvWord.IsNullOrWhiteSpace()) data.AdvWord = goodslan.AdvWord;
                    if (!goodslan.Image.IsNullOrWhiteSpace()) data.Image = goodslan.Image;
                }
                list.Add(data);
            }
        }

        viewModel.GoodsList = list;

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Model = ArticleLan.FindByAIdAndLId(Id, WorkingLanguage.Id);
            if (Model == null)
            {
                return View404();
            }

            var modelArticle = Article.FindById(Model.AId);

            var article = new Article
            {
                Name = Model.Name.IsNullOrWhiteSpace() ? modelArticle?.Name : Model.Name,
                Summary = Model.Summary.IsNullOrWhiteSpace() ? modelArticle?.Summary : Model.Summary,
                Content = (Model.Content.SafeString().IsNullOrWhiteSpace() ? modelArticle?.Content : Model.Content),
                CreateTime = Model.article.CreateTime
            };
            viewModel.Model = article;
            var pre = Article.Findprevious(Model.article.CreateTime, Model.article.AId) ?? new Article();
            var Nex = Article.FindNext(Model.article.CreateTime, Model.article.AId) ?? new Article();
            var preLan = ArticleLan.FindByAIdAndLId(pre.Id, WorkingLanguage.Id) ?? new ArticleLan();
            if (preLan.Name.IsNotNullAndWhiteSpace())
                pre.Name = preLan.Name;
            var NexLan = ArticleLan.FindByAIdAndLId(Nex.Id, WorkingLanguage.Id) ?? new ArticleLan();
            if (NexLan.Name.IsNotNullAndWhiteSpace())
                Nex.Name = NexLan.Name;
            viewModel.previous = pre;
            viewModel.Nex = Nex;

            ViewBag.Titles = ArticleCategoryLan.FindByAIdAndLId(Model.AId, WorkingLanguage.Id)?.Name;

            ViewBag.MId = modelArticle?.MId ?? String.Empty;

            return DGView(viewModel, true);
        }
        else
        {
            var Model = Article.FindById(Id);
            if (Model == null)
            {
                return View404();
            }
            Model.Content = Model.Content.SafeString();
            viewModel.Model = Model;
            viewModel.previous = Article.Findprevious(Model.CreateTime, Model.AId);
            viewModel.Nex = Article.FindNext(Model.CreateTime, Model.AId);
            ViewBag.Titles = ArticleCategory.FindById(Model.AId)?.Name;
            ViewBag.MId = Model.MId ?? String.Empty;

            return DGView(viewModel, true);
        }
    }
}
