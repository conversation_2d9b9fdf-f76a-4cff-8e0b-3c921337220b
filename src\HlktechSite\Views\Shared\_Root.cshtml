﻿@{
    Layout = "_Root.Head";

    Html.AppendHeadCustomParts("<meta name=\"applicable-device\" content=\"pc\">");

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "/static/plugins/bootstrap/css/bootstrap.min.css");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/common.css");

    // Script
    Html.AppendScriptParts(ResourceLocation.Head, "/static/plugins/bootstrap/js/bootstrap.min.js");
    Html.AppendScriptParts(ResourceLocation.Head, "/static/js/jquery-1.12.2.min.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/md5.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/static/plugins/layer/layer.js");
}

@await Html.PartialAsync("_top")

@RenderBody()

@await Html.PartialAsync("_Footer")