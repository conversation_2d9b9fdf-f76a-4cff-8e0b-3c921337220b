﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>成品表</summary>
public partial interface IEndProducts
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>成品名称+规格名称</summary>
    String? Name { get; set; }

    /// <summary>成品广告词</summary>
    String? AdvWord { get; set; }

    /// <summary>产品型号Id集合，以逗号区分</summary>
    String? MId { get; set; }

    /// <summary>成品分类ID</summary>
    Int32 CId { get; set; }

    /// <summary>一级分类ID</summary>
    Int32 Cid1 { get; set; }

    /// <summary>二级分类ID</summary>
    Int32 Cid2 { get; set; }

    /// <summary>三级分类ID</summary>
    Int32 Cid3 { get; set; }

    /// <summary>成品主图</summary>
    String? Image { get; set; }

    /// <summary>成品内容</summary>
    String? Content { get; set; }

    /// <summary>手机端成品描述</summary>
    String? MobileContent { get; set; }

    /// <summary>简介</summary>
    String? Summary { get; set; }

    /// <summary>使用场景</summary>
    String? UsageScenarios { get; set; }

    /// <summary>参数规格</summary>
    String? Specifications { get; set; }

    /// <summary>成品点击数量</summary>
    Int32 Clicks { get; set; }

    /// <summary>成品推荐</summary>
    Boolean Commend { get; set; }

    /// <summary>成品是否上架</summary>
    Boolean Shelf { get; set; }

    /// <summary>排序</summary>
    Int32 Sort { get; set; }

    /// <summary>PC购买Url</summary>
    String? PcGouUrl { get; set; }

    /// <summary>移动购买Url</summary>
    String? MobileGouUrl { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
