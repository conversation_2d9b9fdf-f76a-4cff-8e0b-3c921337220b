﻿@model IEnumerable<Language>
@{
    var returnUrl = DH.Helpers.DHWeb.GetRawUrl(Context.Request);
}
@if (Model.Count() > 0)
{
    <ul style="margin-left: 100px;list-style-type:none;display:inline;">
        <li><span style="font-size: 16px;">@T("海凌科通行证")</span></li>
        <li class="dropdown" style="float: right;margin-right: 100px; float: right;">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" style="color: #FFFFFF;text-decoration:none;">Language <span class="caret"></span></a>
            <ul class="dropdown-menu">
                @foreach (var lang in Model)
                {
                    <li><a href='@Url.RouteUrl("ChangeLanguage", new { langid = lang.Id, returnUrl }, WebHelper2.CurrentRequestProtocol)' title="@lang.Name">@lang.DisplayName</a></li>
                }
            </ul>
        </li>
    </ul>
}