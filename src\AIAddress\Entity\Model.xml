﻿<?xml version="1.0" encoding="utf-8"?>
<EntityModel xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:schemaLocation="https://newlifex.com https://newlifex.com/Model202309.xsd" Version="11.7.2023.0327" Document="https://newlifex.com/xcode/model" xmlns="https://newlifex.com/Model202309.xsd">
  <Option>
    <!--类名模板。其中{name}替换为Table.Name，如{name}Model/I{name}Dto等-->
    <ClassNameTemplate />
    <!--显示名模板。其中{displayName}替换为Table.DisplayName-->
    <DisplayNameTemplate />
    <!--基类。可能包含基类和接口，其中{name}替换为Table.Name-->
    <BaseClass>CubeEntityBase</BaseClass>
    <!--命名空间-->
    <Namespace>AIAddress.Entity</Namespace>
    <!--输出目录-->
    <Output>.\</Output>
    <!--是否使用中文文件名。默认false-->
    <ChineseFileName>False</ChineseFileName>
    <!--用于生成Copy函数的参数类型。例如{name}或I{name}-->
    <ModelNameForCopy />
    <!--带有索引器。实现IModel接口-->
    <HasIModel>False</HasIModel>
    <!--可为null上下文。生成String?等-->
    <Nullable>True</Nullable>
    <!--数据库连接名-->
    <ConnName>DH</ConnName>
    <!--模型类模版。设置后生成模型类，用于接口数据传输，例如{name}Model-->
    <ModelClass>{name}Model</ModelClass>
    <!--模型类输出目录。默认当前目录的Models子目录-->
    <ModelsOutput>.\Models\</ModelsOutput>
    <!--模型接口模版。设置后生成模型接口，用于约束模型类和实体类，例如I{name}-->
    <ModelInterface>I{name}</ModelInterface>
    <!--模型接口输出目录。默认当前目录的Interfaces子目录-->
    <InterfacesOutput>.\Interfaces\</InterfacesOutput>
    <!--用户实体转为模型类的模型类。例如{name}或{name}DTO-->
    <ModelNameForToModel />
    <!--命名格式。Default/Upper/Lower/Underline-->
    <NameFormat>Default</NameFormat>
    <!--魔方区域显示名-->
    <DisplayName />
    <!--魔方控制器输出目录-->
    <CubeOutput />
  </Option>
  <Tables>
    <Table Name="Address" TableName="DH_Address" Description="地址处理">
      <Columns>
        <Column Name="Id" DataType="Int32" PrimaryKey="True" Description="编号" />
        <Column Name="Salesman" DataType="String" Description="业务员" />
        <Column Name="Department" DataType="String" Description="部门名称" />
        <Column Name="Receiving" DataType="String" Length="100" Description="收货联系人" />
        <Column Name="Delivery" DataType="String" Length="300" Description="收货地址" />
        <Column Name="Lat" DataType="Double" Description="Lat" />
        <Column Name="Lng" DataType="Double" Description="Lng" />
        <Column Name="Detail" DataType="String" Length="300" Description="Detail" />
        <Column Name="Town" DataType="String" Description="Town" />
        <Column Name="PhoneNum" DataType="String" Description="PhoneNum" />
        <Column Name="CityCode" DataType="String" Description="CityCode" />
        <Column Name="Province" DataType="String" Description="Province" />
        <Column Name="Person" DataType="String" Description="Person" />
        <Column Name="ProvinceCode" DataType="String" Description="ProvinceCode" />
        <Column Name="Text" DataType="String" Length="300" Description="Text" />
        <Column Name="County" DataType="String" Description="County" />
        <Column Name="City" DataType="String" Description="City" />
        <Column Name="CountyCode" DataType="String" Description="CountyCode" />
        <Column Name="TownCode" DataType="String" Description="TownCode" />
        <Column Name="Process" DataType="Boolean" Description="是否请求" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Id,Salesman,Department,Receiving" Unique="True" />
      </Indexes>
    </Table>
  </Tables>
</EntityModel>