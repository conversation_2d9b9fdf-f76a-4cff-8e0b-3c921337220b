﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>数据字典翻译</summary>
public partial class DataDictionaryLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>字典Id</summary>
    public Int32 DId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>字典值——名称</summary>
    public String? Name { get; set; }

    /// <summary>字典值——描述</summary>
    public String? Content { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDataDictionaryLan model)
    {
        Id = model.Id;
        DId = model.DId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
    }
    #endregion
}
