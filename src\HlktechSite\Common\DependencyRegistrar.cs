﻿using Autofac;

using DH.Infrastructure.DependencyManagement;

using Pek.Infrastructure;

namespace HlktechSite.Common;

/// <summary>
/// 依赖注册商
/// </summary>
public class DependencyRegistrar : IDependencyRegistrar
{
    /// <summary>
    /// 注册服务和接口
    /// </summary>
    /// <param name="builder">容器制造商</param>
    /// <param name="typeFinder">类型查找器</param>
    public virtual void Register(ContainerBuilder builder, ITypeFinder typeFinder)
    {
        //slug路由转换
        builder.RegisterType<SlugRouteTransformer>().AsSelf().InstancePerLifetimeScope();

    }

    /// <summary>
    /// 获取此依赖关系注册器实现的顺序
    /// </summary>
    public int Order => 1;
}
