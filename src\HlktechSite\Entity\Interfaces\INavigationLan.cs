﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>页面导航翻译</summary>
public partial interface INavigationLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>页面导航Id</summary>
    Int32 NId { get; set; }

    /// <summary>关联所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>页面导航标题</summary>
    String? Name { get; set; }

    /// <summary>页面导航链接</summary>
    String? Url { get; set; }
    #endregion
}
