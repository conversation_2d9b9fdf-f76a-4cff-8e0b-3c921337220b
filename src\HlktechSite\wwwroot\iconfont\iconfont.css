@font-face {
    font-family: "iconfont"; /* Project id 171514 */
    src: url('iconfont.woff2?t=1651135471841') format('woff2'), url('iconfont.woff?t=1651135471841') format('woff'), url('iconfont.ttf?t=1651135471841') format('truetype');
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: inline-block;
    margin-right: 3px;
}

.icon-xiazai:before {
    content: "\e601";
}

.icon-rexian:before {
    content: "\e65f";
}

.icon-baoxiu:before {
    content: "\e680";
}

.icon-aixin:before {
    content: "\eca1";
}

.icon-jingdongicon-:before {
    content: "\e63e";
}

.icon-taobao:before {
    content: "\e615";
}

.icon-check-line:before {
    content: "\e64c";
}

.icon-daishenhe:before {
    content: "\e600";
}

.icon-xitongguanli1:before {
    content: "\e62f";
}
