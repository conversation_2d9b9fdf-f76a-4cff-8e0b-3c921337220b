﻿@using DG.Helpers
@using DH.MVC;
@using DH.AspNetCore.Extensions
@{
    var page = Pek.Helpers.DHWeb.HttpContext.Items["DGPage"] as Pager;
    Html.AddCssFileParts("~/static/plugins/js/layui/css/layui.css");
    Html.AddScriptParts("~/static/plugins/js/layui/layui.js");
    //<script src="~/static/plugins/js/layui/layui.js" asp-location="Head"></script>   
}
<style>
        .layui-card-header {
            height: 62px;
        }

        .layui-card .layui-table tr:nth-last-child(2) td i {
            background-color: white !important;
        }

        .layui-card .layui-table tr:last-child td i {
            background-color: white;
        }

        .table-body {
            background-color: #fff;
            margin-top: 10px;
            padding: 0 10px;
            border: 1px solid #e6e6e6;
        }

        xm-select {
            min-height: 30px!important;
            max-height: 30px!important;
            line-height: 30px!important;
        }

            xm-select .xm-label .xm-label-block {
                height: 20px !important;
                line-height: 20px !important;
            }

        .seller-inline-3 {
            width: 130px !important;
        }
        @if (language.UniqueSeoCode == "en")
        { 
        <text>
        .layui-form-item.layui-inline .layui-form-label {
            width: auto
        }
        .dg-form .layui-form-label {
        width:auto
        }
      
        </text>
        }
</style>

<script src="~/static/admin/js/xm-select.js"></script>
<div class="layui-fluid">
    <form class="layui-form dg-form">
        <div class="layui-form-item" id="search" style="margin-bottom: 10px;">
            <div class="layui-inline">
                <label class="layui-form-label">@T("类别")：</label>
                <div class="layui-input-inline seller-inline-3">
                      @HtmlExtensions.ForDropDownList(Html, "category", Log.FindAllCategoryName(), page["category"], T("类别").ToString(), false, new Dictionary<String, String> { { "lay-filter", "category" } }) 
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">@T("操作")：</label>
                <div class="layui-input-inline seller-inline-3">
                     @HtmlExtensions.ForDropDownList(Html, "actions", Log.FindAllActionName(), page["actions"], T("全部").ToString(), false, new Dictionary<String, String> { { "lay-filter", "actions" } }) 
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">@T("选择用户")</label>
                <div class="layui-input-inline width700">
                    <div id="demo1"></div>
                </div>
                @*<div class="layui-input-inline seller-inline-4">
                <input type="text" name="username" id="username" autocomplete="off" class="layui-input" placeholder="@T("用户名"),@T("不支持模糊查询")" />
            </div>*@
            </div>

            @*<div class="layui-form-item">
            <label class="layui-form-label">选择用户</label>
            <div class="layui-input-inline width700">
                <div id="demo1"></div>
            </div>
            <div class="layui-form-mid layui-word-aux">请先选择项目后搜索</div>
        </div>*@


            <div class="layui-inline">
                <label class="layui-form-label">@T("操作时间")：</label>
                <div class="layui-input-inline seller-inline-4">
                    <input type="text" name="date" id="date" placeholder="@T("开始时间") @T("到") @T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <div class="">
                    <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="*"><i class="iconfont icon-chaxun"></i>@T("筛选")</button>
                </div>
            </div>
            <div class="layui-inline" style="float:right">
                <a style="display:none" id="InverterExport" class="layui-btn layui-btn-primary layui-btn-sm" onclick="ExportSn()"><i class="layui-icon">&#xe601;</i>@T("导出日志")</a>
            </div>
        </div>
    </form>

    <div class="table-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>
<script type="text/html" id="success">
        {{# if(d.Success) { }}
        <i class="iconfont icon-gou" style="color: green;"></i>
        {{# } else { }}
        <i class="iconfont icon-cha" style="color: red;"></i>
        {{# } }}
</script> 
<script asp-location="Footer">
    $(function(){
        
        $("link[href='/static/admin/css/admin.css']").remove();

        var Names = "";
        var active, layuiIndex;
        console.log(layui);
        layui.config({
            base: '/layuiadmin/modules/'
        })
            .use(['table', 'jquery', 'laydate'], function () {
                 
            var 
                form = layui.form,
                table = layui.table,
                laydate = layui.laydate;

                console.log("active");

                table.render({
                elem: '#tablist'
                , url: '@Url.Action("GetList")'
                , page: true //开启分页
                , cellMinWidth: 80
                , smartReloadModel: true
                , cols: [[
                    { field: 'ID', title: '@T("ID")', width: 70, align: "center" }
                    , { field: 'Category', title: '@T("类别")', width: 89 }
                    , { field: 'Action', title: '@T("操作")', width: 90  }
                    , { field: 'Success', title: '@T("成功")', templet: '#success', width: 60 }
                    , { field: 'Remark', title: '@T("详细信息")' }
                    , { field: 'UserName', title: '@T("用户名")', width: 100 }
                    , { field: 'CreateIP', title: 'IP @T("地址")', width: 100 }
                    , { field: 'PhysicalAddress', title: '@T("物理地址")', width: 130 }
                    , { field: 'CreateTime', title: '@T("时间")', width: 160 }
                ]]
                , limit: 16
                , limits: [10, 16, 20, 30, 50, 100]
                , height: 'full-108'
                , id: 'tables'
                });
            
           
            window.active = {
                reload: function () {
                    table.reload('tables',
                        {
                            page: {
                                curr: 1
                            },
                            where: {
                                category: $("#category").val(),
                                date: $("#date").val(),
                                actions: $("#actions").val(),
                                //username: $("#username").val()
                                username: Names
                            }
                        });
                     }
             };
                

            //时间插件
            laydate.render({
                elem: '#date',
                range: '@T("到")',
                format: 'yyyy-MM-dd',
                lang: '@language.UniqueSeoCode',
                trigger: 'click', //自动弹出控件的事件，采用click弹出
                done: function (value, date, endDate) {
                    //console.log(value);
                    $("#date").val(value);
                    window.active.reload();
                }
            });

           

            $("#username").on("input", function (e) {
                window.active.reload();
            });

            form.on("select(category)", function () {
                window.active.reload();
            });

            form.on("select(actions)", function () {
                window.active.reload();
            });


            var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true,
            autoRow: true,
            toolbar: { show: true },
            filterable: true,
            remoteSearch: true,
            remoteMethod: function (val, cb, show) {
                //console.log("响应打印值==" + val);
                //这里如果val为空, 则不触发搜索
                if (!val) {
                    return cb([]);
                }
                Names = "";
                //这里引入了一个第三方插件axios, 相当于$.ajax
                $.ajax({
                    method: 'get',
                    url: '@Url.Action("FindBylikeName")?Key=' + val,
                    params: {
                        Key: val,
                    }
                }).then(response => {
                    var res = response;
                    cb(res)
                }).catch(err => {
                    cb([]);
                });
            },

            on: function (data) {
                Names = "";
                var arr = data.arr;
                if (arr.length > 0) {
                    Names= arr[0].name;
                }
                window.active.reload();
            },
        })
            
        });

        // 导出日志
        function ExportSn() {
            var href = "@Url.Action("ExportLog")?category=" + $("#category").val() + "&date=" + $("#date").val() + "&actions=" + $("#actions").val() + "&username=" + Names;
            $("#InverterExport").attr("href", href);
        }

        
        
    })        
</script>
