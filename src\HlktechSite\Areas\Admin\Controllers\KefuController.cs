﻿using System.ComponentModel;
using System.Dynamic;
using System.Linq.Dynamic.Core;

using DG.Cube;
using DG.Cube.BaseControllers;

using DH;
using DH.Entity;
using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Models;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>客服管理</summary>
[DisplayName("客服管理")]
[Description("用于客服的管理")]
[AdminArea]
[DHMenu(23,ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/Kefu", CurrentMenuName = "KefuList", CurrentIcon = "&#xe606;", LastUpdate = "20240125")]

public class KefuController : DGBaseAdminControllerX
{
    protected static Int32 MenuOrder { get; set; } = 23;

    /// <summary>
    /// 客服列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("客服列表")]
    public IActionResult Index(string Key, string type, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            OrderBy = "CreateTime",
            Desc = true
        };

        viewModel.list = OnlineKeFu.FindAllByTypeKey(Key, type, pages);

        viewModel.Key = Key;
        viewModel.type = type;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "Key", Key }, { "type", type } }); ;

        return View(viewModel);
    }


    /// <summary>
    /// 添加客服
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加客服")]
    public IActionResult CreateModel()
    {
        ViewBag.list = Language.FindByStatus().OrderBy(e => e.DisplayOrder).Select(x => new { name = x.Name, value = x.Id }).ToDynamicList().ToJson();
        return View();
    }


    /// <summary>
    /// 创建客服
    /// </summary>
    /// <param name="OName">名字</param>
    /// <param name="OType">类型</param>
    /// <param name="LIds">语言集合</param>
    /// <param name="ONumber">号码</param>
    /// <param name="Sort">排序</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("创建客服")]
    public IActionResult CreateModel(string OName, int OType, string LIds, int Location, string ONumber, int Sort)
    {
        OName = OName.SafeString().Trim();
        LIds = LIds.SafeString().Trim();
        ONumber = ONumber.SafeString().Trim();

        if (OName.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("客服姓名不能为空！"));
        }

        if (LIds.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("请选择所关联的语言集合！"));
        }

        if (ONumber.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("请输入客服号码！"));
        }

        if (OType == 0 && !ONumber.IsInt())
        {
            return MessageTip(GetResource("请输入正确的QQ号！"));
        }

        var kf = new OnlineKeFu
        {
            OName = OName,
            LIds = $",{LIds.Trim(',')},",
            OType = (short)OType,
            ONumber = ONumber,
            Sort = Sort,
            Location = Location,
        };
        Loger.UserLog("创建客服", $"创建客服:{kf.OName},号码:{kf.ONumber},排序{kf.Sort}");

        kf.Insert();

        return MessageTip(GetResource("添加成功！"));
    }



    /// <summary>
    /// 修改客服
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改客服")]
    public IActionResult EditModel(int Id)
    {
        var kf = OnlineKeFu.FindById(Id);
        if (kf == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除！"));
        }
        ViewBag.list = Language.FindByStatus().OrderBy(e => e.DisplayOrder).Select(x => new { name = x.Name, value = x.Id, selected = kf.LIds.Contains("," + x.Id.ToString() + ",") }).ToDynamicList().ToJson();
        return View(kf);
    }


    /// <summary>
    /// 修改客服
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改客服")]
    public IActionResult EditModel(int Id, string OName, int OType, int Location, string LIds, string ONumber, int Sort)
    {

        var kf = OnlineKeFu.FindById(Id);

        if (kf == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除！"));
        }

        OName = OName.SafeString().Trim();
        LIds = LIds.SafeString().Trim();
        ONumber = ONumber.SafeString().Trim();

        if (OName.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("客服姓名不能为空！"));
        }

        if (LIds.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("请选择所关联的语言集合！"));
        }

        if (ONumber.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource("请输入客服号码！"));
        }

        if (OType == 0 && !ONumber.IsInt())
        {
            return MessageTip(GetResource("请输入正确的QQ号！"));
        }

        var oldName = kf.OName;
        var oldNumber = kf.ONumber;
        var oldType = kf.OType;
        var oldSort = kf.Sort;


        kf.OName = OName;
        kf.LIds = $",{LIds.Trim(',')},";
        kf.OType = (short)OType;
        kf.ONumber = ONumber;
        kf.Sort = Sort;
        kf.Location = Location;
        kf.Update();

        Loger.UserLog("修改客服", $"名称:{oldName}=>{kf.OName},号码:{oldNumber}=>{kf.ONumber},类型:{oldType}=>{kf.OType},排序:{oldSort}=>{kf.Sort}");

        return MessageTip(GetResource("修改成功！"));
    }


    /// <summary>
    /// 删除客服
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除客服")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();

        var kfs = OnlineKeFu.FindByIds(Ids);

        OnlineKeFu.DelByIds(Ids.Trim(','));

        Loger.UserLog("删除客服", $"删除客服:{kfs.Select(e => e.OName).Join(",")}");

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

}
