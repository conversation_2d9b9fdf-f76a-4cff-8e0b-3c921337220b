﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.IO;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;
using System.Linq.Dynamic.Core;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>成品管理</summary>
[DisplayName("成品管理")]
[Description("用于成品的管理")]
[AdminArea]
[DHMenu(89,ParentMenuName = "EndProductMenu", ParentMenuUrl = "~/{area}/EndProductModels", ParentMenuOrder = 71, CurrentMenuUrl = "~/{area}/EndProducts", CurrentMenuName = "EndProductList", CurrentIcon = "&#xe71f;", LastUpdate = "20240125")]
public class EndProductController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 89;

    /// <summary>
    /// 成品列表
    /// </summary>
    /// <param name="name"></param>
    /// <param name="commend">是否上架</param>
    /// <param name="CId">分类Id</param>
    /// <param name="CId1">一级分类</param>
    /// <param name="CId2">二级分类</param>
    /// <param name="CId3">三级分类</param>
    /// <param name="page"></param>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("成品列表")]
    public IActionResult Index(string name, int commend, int CId = -1, int CId1 = -1, int CId2 = -1, int CId3 = -1, int page = 1)
    {
        name = name.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = EndProducts.Searchs(name, commend, CId, CId1, CId2, CId3, pages).Select(x => new EndProducts { AdvWord = x.AdvWord, Name = x.Name, Id = x.Id, Image = x.Image.IsNotNullAndWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Image) : "" });
        ViewBag.list = list;
        viewModel.commend = commend;
        viewModel.page = page;
        viewModel.name = name;

        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });

        var vlist = DictionariesCategory.FindAllByLevel(0).Select(x => new { gc_id = x.Id, gc_name = x.Name, depth = x.Level });
        viewModel.jsonlist = vlist.ToJson();

        return View(viewModel);
    }


    /// <summary>
    /// 批量删除数据
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("成品删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();



        using (var tran1 = EndProducts.Meta.CreateTrans())
        {
            EndProducts.DelByIds(Ids.Trim(','));
            EndProductImages.DelByGIds(Ids.Trim(','));
            EndProductLan.DelByGIds(Ids.Trim(','));
            EndProductImagesLan.DelByGIds(Ids.Trim(','));
            tran1.Commit();
        }
        res.code = 10000;
        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg()
    {
        var filea = Request.Form.Files;
        var list = filea.Count();
        var img = filea.FirstOrDefault();
        var bytes = img.OpenReadStream().ReadBytes(img.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(img.FileName)}";
        var filepath = FileUtil.JoinPath(DH.DHSetting.Current.UploadPath, $"endProduct/{filename}");
        var saveFileName = DH.DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        img.SaveAs(saveFileName);

        var fileModel = new EndProductImages();
        fileModel.Sort = 0;
        fileModel.Url = filepath.Replace("\\", "/");
        fileModel.IsDefault = true;
        fileModel.Insert();
        return Json(new { success = true, file_id = fileModel.Id, file_name = filename, file_path = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), filepath) });
    }

    /// <summary>
    /// 商品新增页面one
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品新增页面one")]
    public IActionResult CreateGoodsOne()
    {
        ViewBag.CategoryList = EndProductClass.FindAllByLevel(0);
        ViewBag.CommonlyUsedOptions = EndProductClassStaple.GetAll().OrderByDescending(X => X.Counter).ToList();

        return View();
    }


    /// <summary>
    /// 商品新增页面one
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品新增页面one")]
    public IActionResult addStepone(string class_id)
    {
        Cache.Default.Set("pclass_id", class_id);

        if (class_id.IsNotNullAndWhiteSpace())
        {
            var Mdoel = new EndProductClassStaple();
            var ProModel = EndProductClass.FindById(class_id.ToInt());
            if (ProModel == null)
            {
                return Prompt(new PromptModel { Message = GetResource("错误产品型号") });
            }

            if (ProModel.Level == 0)
            {
                Mdoel.Cid1 = ProModel.Id;
            }
            if (ProModel.Level == 1)
            {
                Mdoel.Cid1 = ProModel.ParentId;
                Mdoel.Cid2 = ProModel.Id;
            }
            if (ProModel.Level == 2)
            {
                Mdoel.Cid2 = ProModel.ParentId;
                Mdoel.Cid3 = ProModel.Id;

                var Pmodel = EndProductClass.FindById(ProModel.ParentId);
                if (Pmodel == null)
                {
                    return Prompt(new PromptModel { Message = GetResource("产品分类数据错误请联系管理员") });
                }
                Mdoel.Cid1 = Pmodel.ParentId;
            }

            var EXIT = EndProductClassStaple.FindByCId123(Mdoel.Cid1, Mdoel.Cid2, Mdoel.Cid3);
            if (EXIT != null)
            {
                EXIT.Counter += 1;
                EXIT.Update();
            }
            else
            {
                var Models = new EndProductClassStaple();
                Models.Cid1 = Mdoel.Cid1;
                Models.Cid2 = Mdoel.Cid2;
                Models.Cid3 = Mdoel.Cid3;
                Models.Counter = 1;
                Models.Insert();
            }

        }

        return Redirect(Url.Action("CreateGoodstwo"));
    }


    /// <summary>
    /// 商品新增页面two
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品新增页面two")]
    public IActionResult CreateGoodstwo()
    {
        var csid = Cache.Default.Get<string>("pclass_id"); //获取选中Id
        if (csid.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("请重新选择分类"), IsOk = false });
        }
        var Name = "";

        var ProModel = EndProductClass.FindById(csid.ToInt());
        if (ProModel == null)
        {
            return Prompt(new PromptModel { Message = GetResource("错误产品型号") });
        }

        if (ProModel.Level == 0)
        {
            Name = ProModel.Name;
        }
        if (ProModel.Level == 1)
        {
            var ppModel = EndProductClass.FindById(ProModel.ParentId);
            Name = ppModel.Name;
            Name = Name + " / " + ProModel.Name;
        }
        if (ProModel.Level == 2)
        {

            var Pmodel = EndProductClass.FindById(ProModel.ParentId);
            if (Pmodel == null)
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类数据错误请联系管理员") });
            }

            var ppModel = EndProductClass.FindById(Pmodel.ParentId);
            if (ppModel == null)
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类数据错误请联系管理员") });
            }
            Name = ppModel.Name;

            Name = Name + " / " + Pmodel.Name;
            Name = Name + " / " + ProModel.Name;
        }

        ViewBag.className = Name;

        //TODO:型号临时测试          

        var prs = new List<EndProductModels>()
        {
            new EndProductModels()
            {
                Name ="测试型号1",
                Id = 1
            },
            new EndProductModels()
            {
                Name ="测试型号2",
                Id = 2
            }
        };
        //var ProducList = EndProductModels.GetAll().Select(x => new { name = x.Name, value = x.Id });
        var ProducList = prs.Select(x => new { name = x.Name, value = x.Id });

        ViewBag.List = ProducList.ToJson();
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(0, 1);
        ViewBag.Sort = (EndProducts.GetMaxSort() ?? new EndProducts()).Sort;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View();
    }



    /// <summary>
    /// 提交商品详情页面
    /// </summary>
    /// <param name="cate_name"></param>
    /// <param name="g_name"></param>
    /// <param name="g_jingle"></param>
    /// <param name="image_path"></param>
    /// <param name="goods_body"></param>
    /// <param name="goods_Mobilebody"></param>
    /// <param name="select"></param>
    /// <param name="summary"></param>
    /// <param name="spec"></param>
    /// <param name="nav_new_open"></param>
    /// <param name="shelf">是否上架</param>
    /// <param name="sort">排序</param>
    /// <param name="UsageScenarios"></param>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品新增页面three")]
    public IActionResult CreateGoodstwos(string cate_name, string g_name, string g_jingle, string image_path, string goods_body, string goods_Mobilebody, string select, string summary, string spec, int nav_new_open, int shelf, int sort, string UsageScenarios)
    {
        if (g_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("商品名称不能为空"), IsOk = false });
        }
        if (g_jingle.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("卖点不能为空"), IsOk = false });
        }
        if (select.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("商品型号不能为空"), IsOk = false });
        }
        if (image_path.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("请选择产品主图"), IsOk = false });
        }
        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        g_name = g_name.SafeString().Trim();
        var EXIT = EndProducts.FindByName(g_name);
        if (EXIT != null)
        {
            return MessageTip(GetResource("成品名称已存在"));
        }
        var Model = new EndProducts();
        Model.Name = g_name;
        Model.AdvWord = g_jingle;
        Model.Image = image_path;
        Model.MId = select;
        Model.MobileContent = goods_Mobilebody;
        Model.Content = goods_body;
        Model.Specifications = spec;
        Model.Summary = summary;
        Model.Commend = nav_new_open == 1;
        Model.UsageScenarios = UsageScenarios;
        Model.Shelf = shelf == 1;
        Model.Sort = sort;
        Cache.Default.Set("createEndProduct" + ManageProvider.User.ID, Model);

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {

            var List = new List<EndProductLan>();

            foreach (var item in Languagelist)
            {
                var LanModel = new EndProductLan();
                LanModel.Name = (GetRequest($"[{item.Id}].g_name")).SafeString().Trim();
                LanModel.AdvWord = (GetRequest($"[{item.Id}].g_jingle")).SafeString().Trim();
                LanModel.Summary = (GetRequest($"[{item.Id}].summary")).SafeString().Trim();
                LanModel.Image = (GetRequest($"[{item.Id}].image_path")).SafeString().Trim();
                LanModel.UsageScenarios = (GetRequest($"[{item.Id}].UsageScenarios")).SafeString().Trim();
                LanModel.MobileContent = (GetRequest($"goods_Mobilebody_{item.Id}")).SafeString().Trim();
                LanModel.Content = (GetRequest($"goods_body_{item.Id}")).SafeString().Trim();
                LanModel.Specifications = (GetRequest($"[{item.Id}].spec")).SafeString().Trim();
                LanModel.Shelf = (GetRequest($"[{item.Id}].shelf")).SafeString() == "1";
                LanModel.Commend = (GetRequest($"[{item.Id}].nav_new_open")).SafeString() == "1";
                LanModel.Sort = GetRequest($"[{item.Id}].sort").ToInt();
                LanModel.LId = item.Id;
                List.Add(LanModel);
            }
            //XTrace.WriteLine("获取到即将写入缓存的list的数量===" + List.Count());
            Cache.Default.Set("createEndProductLan" + ManageProvider.User.ID, List);
        }
        return Redirect(Url.Action("CreateGoodsthree", new { imgId = image_path }));
    }

    /// <summary>
    /// 进入上传图片页面
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品新增页面three")]
    public IActionResult CreateGoodsthree(int imgId)
    {
        var Model = AlbumPic.FindById(imgId);
        if (Model == null)
        {
            Model = new AlbumPic();
        }
        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言


        var List = Cache.Default.Get<List<EndProductLan>>("createEndProductLan" + ManageProvider.User.ID);

        ViewBag.lanCover = List.Select(x => new AlbumPic { Id = x.Image.IsNotNullAndWhiteSpace() ? x.Image.ToInt() : 0, Cover = (x.Image.IsNotNullAndWhiteSpace() ? UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), AlbumPic.FindById(x.Image.ToInt())?.Cover) : ""), LId = x.LId }).ToList();
        ViewBag.Languagelist = Languagelist;
        return View(Model);
    }


    /// <summary>
    /// 进入上传图片页面
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    //[EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品新增页面threes")]
    public IActionResult CreateGoodsthrees()
    {

        var csid = Cache.Default.Get<string>("pclass_id"); //获取选中Id
        var Goodsmodel = Cache.Default.Get<EndProducts>("createEndProduct" + ManageProvider.User.ID); //获取当前用户储存的产品缓存
        if (csid.IsNullOrWhiteSpace())
        {

            return Prompt(new PromptModel { Message = GetResource("数据已失效,请重新添加"), IsOk = false });
        }
        if (Goodsmodel == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据已失效,请重新添加"), IsOk = false });
        }
        var Model = new EndProducts();
        Model.Name = Goodsmodel.Name;
        Model.MId = Goodsmodel.MId;
        Model.Content = Goodsmodel.Content;
        Model.AdvWord = Goodsmodel.AdvWord;
        Model.MobileContent = Goodsmodel.MobileContent;
        Model.CId = csid.ToInt();
        Model.Specifications = Goodsmodel.Specifications;
        Model.Summary = Goodsmodel.Summary;
        Model.Commend = Goodsmodel.Commend;
        Model.Shelf = Goodsmodel.Shelf;
        Model.Sort = Goodsmodel.Sort;
        Model.UsageScenarios = Goodsmodel.UsageScenarios;
        using (var tran1 = EndProducts.Meta.CreateTrans())
        {

            var ProModel = EndProductClass.FindById(Model.CId);
            if (ProModel == null)
            {
                return Prompt(new PromptModel { Message = GetResource("错误产品型号") });
            }

            if (ProModel.Level == 0)
            {
                Model.Cid1 = ProModel.Id;
            }
            if (ProModel.Level == 1)
            {
                Model.Cid1 = ProModel.ParentId;
                Model.Cid2 = ProModel.Id;
            }
            if (ProModel.Level == 2)
            {

                Model.Cid2 = ProModel.ParentId;
                Model.Cid3 = ProModel.Id;

                var Pmodel = EndProductClass.FindById(ProModel.ParentId);
                if (Pmodel == null)
                {
                    return Prompt(new PromptModel { Message = GetResource("产品分类数据错误请联系管理员") });
                }
                Model.Cid1 = Pmodel.ParentId;
            }
            Model.Insert();

            var listimg = new List<EndProductImages>();
            for (int i = 0; i <= 4; i++)
            {
                var fileId = GetRequest("img[0][" + i + "][Id]");
                var fileName = GetRequest("img[0][" + i + "][name]");
                var filesort = GetRequest("img[0][" + i + "][sort]");
                var fileModel = AlbumPic.FindById(fileId.ToInt());
                if (fileModel != null)
                {
                    var Models = new EndProductImages();
                    Models.GId = Model.Id;
                    Models.Url = fileModel.Cover;
                    Models.Sort = filesort.ToInt();
                    if (filesort.ToInt() == 0)
                    {
                        Models.IsDefault = true;
                        Model.Image = Models.Url;
                    }
                    listimg.Add(Models);
                }
            }
            if (listimg.Count > 0)
            {
                listimg.Save();
            }

            //开启多语言
            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable)
            {

                var List = Cache.Default.Get<List<EndProductLan>>("createEndProductLan" + ManageProvider.User.ID);
                foreach (var item in List)
                {
                    item.GId = Model.Id;
                }

                List.Save();
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

                var listLanimg = new List<EndProductImagesLan>();
                var goodlanlists = new List<EndProductLan>();
                foreach (var item in Languagelist)
                {
                    var GoodsLanModel = EndProductLan.FindByGIdAndLId(Model.Id, item.Id);
                    for (int i = 0; i <= 4; i++)
                    {
                        var fileId = GetRequest($"[{item.Id}].img[0][" + i + "][Id]");
                        var fileName = GetRequest($"[{item.Id}].img[0][" + i + "][name]");
                        var filesort = GetRequest($"[{item.Id}].img[0][" + i + "][sort]");
                        var fileModel = AlbumPic.FindById(fileId.ToInt());
                        if (fileModel != null)
                        {
                            var Models = new EndProductImagesLan();
                            Models.GId = Model.Id;
                            Models.LId = item.Id;
                            Models.Url = fileModel.Cover;
                            Models.Sort = filesort.ToInt();
                            if (filesort.ToInt() == 0)
                            {
                                Models.IsDefault = true;
                                if (GoodsLanModel != null)
                                {
                                    GoodsLanModel.Image = Models.Url;
                                    goodlanlists.Add(GoodsLanModel);
                                }
                            }
                            listLanimg.Add(Models);
                        }
                    }
                }
                listLanimg.Save();
                goodlanlists.Update();
            }

            tran1.Commit();
        }
        Model.Update();


        EndProductImages.Meta.Cache.Clear("");//清除缓存
        EndProducts.Meta.Cache.Clear("");//清除缓存
        EndProductLan.Meta.Cache.Clear("");//清除缓存
        AlbumPic.Meta.Cache.Clear("");//清除缓存
        return Redirect(Url.Action("CreateGoodsfour", new { Id = Model.Id }));
    }


    /// <summary>
    /// 商品新增完成页
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("商品新增完成页")]
    public IActionResult CreateGoodsfour(int Id)
    {
        //暂时写死页面,不知关联网站
        ViewBag.Id = Id;
        return View();
    }


    /// <summary>
    /// 商品编辑页面
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="class_id"></param>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("商品编辑页面")]
    public IActionResult UpdateGoods(int Id, int class_id)
    {
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(Id, 11);
        var Model = EndProducts.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        //Model.Image = DHUrl.Combine(Model.Image);
        Model.Images = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Model.Image);
        if (class_id > 0)
        {

            Model.CId = 0;
            Model.Cid1 = 0;
            Model.Cid2 = 0;
            Model.Cid3 = 0;
            Model.CId = class_id;
            var ProModel = EndProductClass.FindById(Model.CId);
            if (ProModel == null)
            {
                return Prompt(new PromptModel { Message = GetResource("错误产品型号") });
            }

            if (ProModel.Level == 0)
            {
                Model.Cid1 = ProModel.Id;
            }
            if (ProModel.Level == 1)
            {
                Model.Cid1 = ProModel.ParentId;
                Model.Cid2 = ProModel.Id;
            }
            if (ProModel.Level == 2)
            {

                Model.Cid2 = ProModel.ParentId;
                Model.Cid3 = ProModel.Id;

                var Pmodel = EndProductClass.FindById(ProModel.ParentId);
                if (Pmodel == null)
                {
                    return Prompt(new PromptModel { Message = GetResource("成品分类数据错误请联系管理员") });
                }
                Model.Cid1 = Pmodel.ParentId;
            }
        }
        var Name = "";
        var class1 = EndProductClass.FindById(Model.Cid1);
        var class2 = EndProductClass.FindById(Model.Cid2);
        var class3 = EndProductClass.FindById(Model.Cid3);
        if (class1 != null)
        {
            Name = class1.Name;
        }
        if (class2 != null)
        {
            Name = Name + " / " + class2.Name;
        }
        if (class3 != null)
        {
            Name = Name + " / " + class3.Name;
        }

        ViewBag.Name = Name;

        var pages = new PageParameter();
        pages.PageIndex = 1;
        pages.PageSize = 10;
        pages.OrderBy = "CreateTime";
        //var ProducList = EndProductModels.GetAll().Select(x => new { name = x.Name, value = x.Id, selected = x.Id == Model.MId });
        var ProducList = EndProductModels.GetAll().Select(x => new { name = x.Name, value = x.Id, selected = Model.MId.IsNotNullOrWhiteSpace() && Model.MId.Trim(',').Split(',').Any(e => e == x.Id.ToString()) });
        ViewBag.List = ProducList.ToJson();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(Model);
    }



    /// <summary>
    /// 选择分类页
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("选择分类页")]
    public IActionResult SearchCategories(int Id)
    {
        var Model = EndProducts.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        ViewBag.list1 = EndProductClass.FindAllByLevel(0);
        ViewBag.list2 = EndProductClass.FindAllByParentId(Model.Cid1);
        ViewBag.list3 = EndProductClass.FindAllByParentId(Model.Cid2);
        ViewBag.CommonlyUsedOptions = EndProductClassStaple.FindAllWithCache().OrderByDescending(X => X.Counter).ToList();
        return View(Model);
    }

    //TODO:

    /// <summary>
    /// 选择分类提交
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="class_id"></param>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("选择分类提交")]
    public IActionResult SearchCategoriess(int Id, int class_id)
    {
        return Redirect(Url.Action("UpdateGoods", new { Id = Id, class_id = class_id }));
    }

    /// <summary>
    /// 修改图片页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改图片")]
    public IActionResult ModifyAPicture(int Id)
    {

        var Model = EndProducts.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        Model.Images = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Model.Image);

        var Imglist = EndProductImages.FindAllByGId(Id).Select(x => new EndProductImages { Id = x.Id, GId = x.GId, Sort = x.Sort, Urls = x.Url, Url = x.Url.IsNotNullAndWhiteSpace() ? x.Url : "", IsDefault = x.IsDefault });//获取到图片列表

        var image0 = Imglist.Where(x => x.Sort == 0).FirstOrDefault();

        var image1 = Imglist.Where(x => x.Sort == 1).FirstOrDefault();

        var image2 = Imglist.Where(x => x.Sort == 2).FirstOrDefault();

        var image3 = Imglist.Where(x => x.Sort == 3).FirstOrDefault();

        var image4 = Imglist.Where(x => x.Sort == 4).FirstOrDefault();
        if (image0 == null) { image0 = new EndProductImages(); }; ViewBag.Img0 = image0;
        if (image1 == null) { image1 = new EndProductImages(); }; ViewBag.Img1 = image1;
        if (image2 == null) { image2 = new EndProductImages(); }; ViewBag.Img2 = image2;
        if (image3 == null) { image3 = new EndProductImages(); }; ViewBag.Img3 = image3;
        if (image4 == null) { image4 = new EndProductImages(); }; ViewBag.Img4 = image4;

        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(Model);
    }

    /// <summary>
    /// 修改图片提交
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改图片提交")]
    public IActionResult ModifyAPictures(int Id)
    {
        var res = new DResult();
        var Lists = new List<EndProductImages>();
        var ListsLan = new List<EndProductImagesLan>();
        //var ImageList = EndProductImages.FindAllByGId(Id);
        var GoodsModel = EndProducts.FindById(Id);
        for (int i = 0; i <= 4; i++)
        {
            var filesort = GetRequest("img[0][" + i + "][sort]");
            var fileurl = GetRequest("img[0][" + i + "][name]");
            var fileId = GetRequest("img[0][" + i + "][Id]");
            var filetype = GetRequest("img[0][" + i + "][type]");
            if (filetype == "0")
            {
                var gimg = EndProductImages.FindById(fileId.ToInt());
                if (gimg != null)
                {
                    if (gimg.Url != fileurl || gimg.Sort != filesort.ToInt())
                    {
                        gimg.Url = fileurl;
                        gimg.Sort = filesort.ToInt();
                        if (filesort.ToInt() == 0)
                        {
                            gimg.IsDefault = true;
                            GoodsModel.Image = gimg.Url;

                        }
                        Lists.Add(gimg);
                    }
                }
                else
                {
                    var delModel = EndProductImages.FindAllByGIdAndSort(Id, filesort.ToInt());
                    if (delModel.Count != 0)
                    {
                        delModel.Delete();
                    }
                }
            }
            else
            {
                var picgimg = AlbumPic.FindById(fileId.ToInt());
                if (picgimg != null)
                {
                    var delModel = EndProductImages.FindAllByGIdAndSort(Id, filesort.ToInt()).FirstOrDefault();
                    //if (delModel.Count != 0)
                    //{
                    //    delModel.Delete();
                    //}
                    if (delModel == null)
                    {
                        delModel = new EndProductImages();
                    }
                    delModel.GId = Id;
                    delModel.Sort = filesort.ToInt();
                    delModel.Url = picgimg.Cover;
                    if (filesort.ToInt() == 0)
                    {
                        delModel.IsDefault = true;
                        GoodsModel.Image = delModel.Url;
                    }
                    Lists.Add(delModel);
                }
                else
                {
                    var delModel = EndProductImages.FindAllByGIdAndSort(Id, filesort.ToInt());
                    if (delModel.Count != 0)
                    {
                        delModel.Delete();
                    }
                }

            }
        }

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable) //开启多语言
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

            foreach (var item in Languagelist)
            {
                for (int i = 0; i <= 4; i++)
                {
                    var filesort = GetRequest("[" + item.Id + "].img[0][" + i + "][sort]");
                    var fileurl = GetRequest("[" + item.Id + "].img[0][" + i + "][name]");
                    var fileId = GetRequest("[" + item.Id + "].img[0][" + i + "][Id]");
                    var filetype = GetRequest("[" + item.Id + "].img[0][" + i + "][type]");
                    if (filetype == "0")
                    {
                        //var gimg = EndProductImages.FindById(fileId.ToInt());
                        var gimg = EndProductImagesLan.FindById(fileId.ToInt());
                        if (gimg != null)
                        {
                            if (gimg.Url != fileurl || gimg.Sort != filesort.ToInt())
                            {
                                gimg.Url = fileurl;
                                gimg.Sort = filesort.ToInt();
                                if (filesort.ToInt() == 0)
                                {
                                    gimg.IsDefault = true;
                                    GoodsModel.Image = gimg.Url;

                                }
                                ListsLan.Add(gimg);
                            }
                        }
                        else
                        {
                            //var delModel = EndProductImages.FindAllByGIdAndSort(Id, filesort.ToInt());
                            var delModel = EndProductImagesLan.FindAllByGIdAndSortAndLId(Id, filesort.ToInt(), item.Id);
                            if (delModel.Count != 0)
                            {
                                delModel.Delete();
                            }
                        }
                    }
                    else
                    {
                        var picgimg = AlbumPic.FindById(fileId.ToInt());
                        if (picgimg != null)
                        {
                            var delModel = EndProductImagesLan.FindAllByGIdAndSortAndLId(Id, filesort.ToInt(), item.Id).FirstOrDefault();
                            if (delModel == null)
                            {
                                delModel = new EndProductImagesLan();
                                delModel.LId = item.Id;
                                delModel.GId = GoodsModel.Id;
                            }
                            delModel.GId = Id;
                            delModel.Sort = filesort.ToInt();
                            delModel.Url = picgimg.Cover;
                            if (filesort.ToInt() == 0)
                            {
                                delModel.IsDefault = true;

                                var GoodLan = EndProductLan.FindByGIdAndLId(GoodsModel.Id, item.Id);
                                GoodLan.Image = delModel.Url;
                                GoodLan.Update();
                            }
                            ListsLan.Add(delModel);
                        }
                        else
                        {
                            var delModel = EndProductImagesLan.FindAllByGIdAndSortAndLId(Id, filesort.ToInt(), item.Id);
                            if (delModel.Count != 0)
                            {
                                delModel.Delete();
                            }
                        }

                    }
                }
            }
        }
        ListsLan.Save();
        Lists.Save();
        GoodsModel.Update();
        EndProductImages.Meta.Cache.Clear("");//清除缓存
        AlbumPic.Meta.Cache.Clear("");//清除缓存
        EndProducts.Meta.Cache.Clear("");//清除缓存
        EndProductLan.Meta.Cache.Clear("");//清除缓存
        EndProductImagesLan.Meta.Cache.Clear("");//清除缓存
        //return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
        res.success = true;
        res.data = Url.Action("Index");
        return Json(res);
    }

    /// <summary>
    /// 商品编辑
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="goods_body"></param>
    /// <param name="g_jingle"></param>
    /// <param name="g_name"></param>
    /// <param name="type_id"></param>
    /// <param name="image_path"></param>
    /// <param name="goods_Mobilebody"></param>
    /// <param name="select"></param>
    /// <param name="spec"></param>
    /// <param name="summary"></param>
    /// <param name="nav_new_open"></param>
    /// <param name="shelf">是否上架</param>
    /// <param name="Sort">排序</param>
    /// <param name="UsageScenarios"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("商品编辑")]
    public IActionResult UpdateGoods1(int Id, string goods_body, string g_jingle, string g_name, int type_id, int image_path, string goods_Mobilebody, string select, string summary, string spec, int nav_new_open, int shelf, int Sort, string UsageScenarios)
    {
        var Model = EndProducts.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除"), IsOk = false });
        }
        if (select.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("产品型号不能为空"), IsOk = false });
        }
        g_name = g_name.SafeString().Trim();
        var EXIT = EndProducts.FindByName(g_name);
        if (EXIT != null && EXIT.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("商品名称已存在"), IsOk = false });
        }
        if (Sort < 0)
        {
            return Prompt(new PromptModel { Message = GetResource("商品排序不能为负数"), IsOk = false });
        }
        using (var tran1 = EndProducts.Meta.CreateTrans())
        {
            Model.Name = g_name;
            Model.UsageScenarios = UsageScenarios;
            Model.Content = goods_body;
            Model.AdvWord = g_jingle;
            Model.CId = type_id;
            Model.MobileContent = goods_Mobilebody;
            Model.MId = select;
            Model.Specifications = spec;
            Model.Summary = summary;
            Model.Commend = nav_new_open == 1;
            Model.Shelf = shelf == 1;
            Model.Sort = Sort;
            if (image_path > 0)
            {
                var XC = AlbumPic.FindById(image_path);

                if (XC != null)
                {
                    var exut = EndProductImages.FindAllByGIdAndSort(Id, 0);
                    if (exut.Count != 0)
                    {
                        exut.Delete();//先删除
                    }
                    var Modelimage = new EndProductImages();
                    Modelimage.GId = Id;
                    Modelimage.Url = XC.Cover;
                    Modelimage.Sort = 0;
                    Modelimage.Insert();
                    Model.Image = XC.Cover;
                }
            }
            Model.Update();

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable) //开启多语言
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

                var List = EndProductLan.FindByGId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var models = List.Find(x => x.LId == item.Id);
                    if (models == null)
                    {
                        models = new EndProductLan();
                        models.Name = (GetRequest($"[{item.Id}].g_name")).SafeString().Trim();
                        models.AdvWord = (GetRequest($"[{item.Id}].g_jingle")).SafeString().Trim();
                        models.Summary = (GetRequest($"[{item.Id}].summary")).SafeString().Trim();
                        models.UsageScenarios = (GetRequest($"[{item.Id}].UsageScenarios")).SafeString().Trim();
                        models.Content = (GetRequest($"goods_body{item.Id}")).SafeString().Trim();
                        models.MobileContent = (GetRequest($"goods_Mobilebody_{item.Id}")).SafeString().Trim();
                        models.Specifications = (GetRequest($"[{item.Id}].spec")).SafeString().Trim();
                        models.Name = (GetRequest($"[{item.Id}].g_name")).SafeString().Trim();
                        models.Shelf = (GetRequest($"[{item.Id}].shelf")) == "1";
                        models.Commend = (GetRequest($"[{item.Id}].nav_new_open")) == "1";
                        models.Sort = GetRequest($"[{item.Id}].sort").ToInt();
                        models.LId = item.Id;
                        models.GId = Model.Id;
                        var image = (GetRequest($"[{item.Id}].image_path")).SafeString().Trim();
                        if (image.IsNotNullAndWhiteSpace())
                        {
                            var XC = AlbumPic.FindById(image.ToInt());

                            if (XC != null)
                            {
                                var exut = EndProductImagesLan.FindAllByGIdAndSortAndLId(Model.Id, 0, item.Id);
                                if (exut.Count != 0)
                                {
                                    exut.Delete();//先删除
                                }
                                var Modelimage = new EndProductImagesLan();
                                Modelimage.GId = Id;
                                Modelimage.LId = item.Id;
                                Modelimage.Url = XC.Cover;
                                Modelimage.Sort = 0;
                                Modelimage.Insert();
                                models.Image = XC.Cover;
                            }
                        }
                        models.Insert();
                    }
                    else
                    {
                        models.Name = (GetRequest($"[{item.Id}].g_name")).SafeString().Trim();
                        models.AdvWord = (GetRequest($"[{item.Id}].g_jingle")).SafeString().Trim();
                        models.Summary = (GetRequest($"[{item.Id}].summary")).SafeString().Trim();
                        models.UsageScenarios = (GetRequest($"[{item.Id}].UsageScenarios")).SafeString().Trim();
                        models.Content = (GetRequest($"goods_body{item.Id}")).SafeString().Trim();
                        models.MobileContent = (GetRequest($"goods_Mobilebody_{item.Id}")).SafeString().Trim();
                        models.Specifications = (GetRequest($"[{item.Id}].spec")).SafeString().Trim();
                        models.Name = (GetRequest($"[{item.Id}].g_name")).SafeString().Trim();
                        models.Shelf = (GetRequest($"[{item.Id}].shelf")) == "1";
                        models.Commend = (GetRequest($"[{item.Id}].nav_new_open")) == "1";
                        models.Sort = GetRequest($"[{item.Id}].sort").ToInt();
                        var image = (GetRequest($"[{item.Id}].image_path")).SafeString().Trim();
                        if (image.IsNotNullAndWhiteSpace())
                        {
                            var XC = AlbumPic.FindById(image.ToInt());

                            if (XC != null)
                            {
                                var exut = EndProductImagesLan.FindAllByGIdAndSortAndLId(Model.Id, 0, item.Id);
                                if (exut.Count != 0)
                                {
                                    exut.Delete();//先删除
                                }
                                var Modelimage = new EndProductImagesLan();
                                Modelimage.GId = Id;
                                Modelimage.LId = item.Id;
                                Modelimage.Url = XC.Cover;
                                Modelimage.Sort = 0;
                                Modelimage.Insert();
                                models.Image = XC.Cover;
                            }
                        }

                        models.Update();
                    }

                }
            }

            tran1.Commit();
        }

        EndProductImages.Meta.Cache.Clear("");//清除缓存
        EndProducts.Meta.Cache.Clear("");//清除缓存
        AlbumPic.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }


    /// <summary>
    /// 插入主图列表
    /// </summary>
    /// <param name="id"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("相册图片列表")]
    public IActionResult Piclist(int id, int page = 1)
    {

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        ViewBag.list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover) });
        ViewBag.ListXC = AlbumCategory.FindAllWithCache();

        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Piclist"), new Dictionary<String, String> { { "id", id.ToString() } });
        return View();
    }

    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("插入详情相册图片列表")]
    public IActionResult PiclistConten(int id, string type, int page = 1)
    {

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        var list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover) });
        ViewBag.list = list;
        ViewBag.ListXC = AlbumCategory.FindAllWithCache();
        ViewBag.type = type;
        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("PiclistConten"), new Dictionary<String, String> { { "id", id.ToString() }, { "type", type } });
        return View();
    }

    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("插入详情相册图片列表")]
    public IActionResult PiclistContens(int id, string type, int page = 1)
    {

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        ViewBag.list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover) });
        ViewBag.ListXC = AlbumCategory.FindAllWithCache();
        ViewBag.type = type;
        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("PiclistContens"), new Dictionary<String, String> { { "id", id.ToString() }, { "type", type } });
        return View();
    }

    /// <summary>
    /// 插入多图列表
    /// </summary>
    /// <param name="id"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("相册图片列表")]
    public IActionResult Piclistmultiple(int id, int page = 1)
    {

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        ViewBag.list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover), Spec = x.Cover });
        ViewBag.ListXC = AlbumCategory.FindAllWithCache();

        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Piclistmultiple"), new Dictionary<String, String> { { "id", id.ToString() } });
        return View();
    }

    /// <summary>
    /// 获取下拉标签
    /// </summary>
    /// <param name="stapleid"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取下拉标签")]
    public IActionResult PitchOn(int stapleid)
    {
        var common = EndProductClassStaple.FindById(stapleid);
        var one = EndProductClass.FindAllByLevel(0);
        var tow = EndProductClass.FindAllByLevel(1);
        var three = EndProductClass.FindAllByLevel(2);
        var ones = "";
        var twos = "";
        var threes = "";
        foreach (var item in one)
        {
            if (item.Id == common.Cid1)
            {
                ones += "<li class=\"\" onclick=\"selClass($(this));\" data-param=\"{gcid:" + item.Id + ", deep:" + item.Level + 1 + ", tid:1}\" dstype=\"selClass\"> <a  class=\"classDivClick\" href=\"javascript:void(0)\"><span class=\"has_leaf\"><i class=\"iconfont\"></i> " + item.Name + "</span></a> </li>";
            }
            else
            {
                ones += "<li class=\"\" onclick=\"selClass($(this));\" data-param=\"{gcid:" + item.Id + ", deep:" + item.Level + 1 + ", tid:1}\" dstype=\"selClass\"> <a class=\"\" href=\"javascript:void(0)\"><span class=\"has_leaf\"><i class=\"iconfont\"></i> " + item.Name + "</span></a> </li>";
            }
        }
        foreach (var item in tow)
        {
            if (item.Id == common.Cid2)
            {
                twos += "<li class=\"\" onclick=\"selClass($(this));\" data-param=\"{gcid:" + item.Id + ", deep:" + item.Level + 1 + ", tid:1}\" dstype=\"selClass\"> <a  class=\"classDivClick\" href=\"javascript:void(0)\"><span class=\"has_leaf\"><i class=\"iconfont\"></i> " + item.Name + "</span></a> </li>";
            }
            else
            {
                twos += "<li class=\"\" onclick=\"selClass($(this));\" data-param=\"{gcid:" + item.Id + ", deep:" + item.Level + 1 + ", tid:1}\" dstype=\"selClass\"> <a class=\"\" href=\"javascript:void(0)\"><span class=\"has_leaf\"><i class=\"iconfont\"></i> " + item.Name + "</span></a> </li>";
            }
        }
        foreach (var item in three)
        {
            if (item.Id == common.Cid3)
            {
                threes += "<li class=\"\" onclick=\"selClass($(this));\" data-param=\"{gcid:" + item.Id + ", deep:" + item.Level + 1 + ", tid:1}\" dstype=\"selClass\"> <a  class=\"classDivClick\" href=\"javascript:void(0)\"><span class=\"has_leaf\"><i class=\"iconfont\"></i> " + item.Name + "</span></a> </li>";
            }
            else
            {
                threes += "<li class=\"\" onclick=\"selClass($(this));\" data-param=\"{gcid:" + item.Id + ", deep:" + item.Level + 1 + ", tid:1}\" dstype=\"selClass\"> <a class=\"\" href=\"javascript:void(0)\"><span class=\"has_leaf\"><i class=\"iconfont\"></i> " + item.Name + "</span></a> </li>";
            }
        }

        return Json(new { done = true, one = ones, two = twos, three = threes, gc_id = common.Cid3 == 0 ? common.Cid2 == 0 ? common.Cid1 : common.Cid2 : common.Cid3, type_id = 0 });
    }

    /// <summary>
    /// 模糊查询产品型号数据
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("模糊查询产品型号数据")]
    public IActionResult GetlikeName(string Key, int page)
    {
        return Json(new
        {
            Data = new[]
            {
                new
                {
                    name = "测试型号1",
                    value = 1
                },
                new
                {
                    name = "测试型号2",
                    value = 2
                }
            }
        });

        if (Key.IsNullOrWhiteSpace())
        {
            return Json(new List<EndProductModels>());
        }
        var List1 = EndProductModels.FindAllByLikeName(Key.SafeString().Trim());
        return Json(new { Data = List1.Select(x => new { value = x.Id, name = x.Name }) });
    }


}
