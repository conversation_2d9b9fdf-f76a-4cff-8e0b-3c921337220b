﻿@{
    Html.AppendCssFileParts("~/css/product.css");

    if (Model.CId == 0)
    {
        Html.AppendTitleParts(T("产品中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }
    else
    {
        var modelProductCategory = Model.Model as ProductCategory;
        Html.AppendTitleParts(modelProductCategory.LngName + DG.Setting.Current.PageTitleSeparator + T("产品中心").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
    }

    var cdn = CDN.GetCDN();

    var localizationSettings = LocalizationSettings.Current;
}

<div class="top">
    <img src="@(cdn)/images/CaseBanner.png" />
    <div>
        <h2>@T("产品中心")</h2>
        <P>@T("深耕行业十数载，累计服务超过10W+客户")</P>
    </div>
</div>

<input type="hidden" id="CId" name="CId" value="@Model.CId" />
<div class="detail-nav">
    <div class="detail-nav-top">
        <div>
            <div>
                <i>@T("产品分类")：</i>
                <a class="@((Model.CId == 0) ? "selected productType" : "productType")" href="@Url.RouteUrl("Product")">@T("全部")</a>
                @foreach (var item in (Model.ProTypelist as IEnumerable<ProductCategory>))
                {
                    <a class="@(Model.CId == item.Id ? "selected productType" : "productType")" href="@Url.DGAction("List", "Product", new { CId = item.Id })" id="@item.Id">@item.Name</a>
                    var ProSecondlist = ProductCategory.FindAllByParentId(item.Id).Select(x => new { Id = x.Id, Name = localizationSettings.IsEnable ? ProductCategoryLan.FindByCIdAndLId(x.Id, language.Id)?.Name : x.Name });

                    @if (ProSecondlist != null)
                    {
                        <div style="display:@(Model.CId == item.Id?"block":"none")">
                            <ul>
                                @foreach (var item2 in ProSecondlist)
                                {
                                    <li><a class="@(Model.ChildId == item2.Id ? "selected":"")" href="@Url.DGAction("List","Product",new { CId = item2.Id})">@item2.Name</a></li>
                                }
                            </ul>
                        </div>
                    }
                }
            </div>
            <div class="input-group">
                <input type="text" id="keyVal" class="form-control" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2" value="@Model.Key">
                <span class="input-group-addon" id="basic-addon2"><img src="@(cdn)/images/fdj.png" /></span>
            </div>
        </div>
    </div>
</div>


<div class="detail-list">
    <div>
        @foreach (var item in Model.list as IEnumerable<Goods>)
        {
            <a href="@Url.DGAction("Details","Product",new {Id =item.Id})" target="_blank">
                <div title="@item.Name">
                    <img src="@item.Image" />
                    <p>@item.Name</p>
                    <i title="@item.AdvWord">@Html.Raw(item.AdvWord)</i>
                </div>
            </a>
        }
    </div>
    <div class="paging" style="text-align: center;">
        <ul class="pagination">
            @Html.Raw(Model.Str)
        </ul>
    </div>
</div>


<script type="text/javascript" asp-location="Footer">
    $(function () {
        //$(".detail-nav-top div div div").hide()


        $(".detail-nav div a").click(function () {
            $(".detail-nav div a").removeClass("selected");
            $(this).addClass("selected");
        });
        $(".productType").mouseover(function () {
            $(".detail-nav-top div div div").hide();
            if ($(this).next().find("li").length > 0) {
                $(this).next().show();
            }
        });

        //$(".detail-nav-top div div div").each(function () {
        //    this.onmouseout = function () {
        //        console.log(this.contains(event.toElement));
        //        if (!this.contains(event.toElement)) {
        //            $(this).hide();
        //        }
        //    };
        //});

        $("#basic-addon2").click(function () {
            var url;
            @if (Model.CId == 0)
            {
                @:url = "@Url.DGAction("Index", "Product")" + "?key=" + $(this).parent().find("input").val();
            }
            else
            {
                @:url = "@Url.DGAction("List", "Product", new { CId = Model.CId })" + "?key=" + $(this).parent().find("input").val();
            }
            location.href = url;
        });
        $("#keyVal").keyup(function (e) {
            if (e.keyCode == 13) {
                $("#basic-addon2").click();
            }
        })
    })
</script>