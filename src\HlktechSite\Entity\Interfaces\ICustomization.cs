﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>定制申请</summary>
public partial interface ICustomization
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>公司名称</summary>
    String? ComName { get; set; }

    /// <summary>主营业务</summary>
    String? Business { get; set; }

    /// <summary>申请人职务</summary>
    String? Job { get; set; }

    /// <summary>邮箱</summary>
    String? Email { get; set; }

    /// <summary>公司网址</summary>
    String? ComUrl { get; set; }

    /// <summary>公司联系人</summary>
    String? Linkman { get; set; }

    /// <summary>手机号</summary>
    String? Phone { get; set; }

    /// <summary>定制类型 0:解决方案 1:产品</summary>
    Int32 Type { get; set; }

    /// <summary>定制基于产品型号</summary>
    String? Model { get; set; }

    /// <summary>定制产品首批采购量</summary>
    String? Purchase { get; set; }

    /// <summary>定制产品预计年均需求量</summary>
    String? Predict { get; set; }

    /// <summary>定制需求</summary>
    String? Demand { get; set; }

    /// <summary>应用的项目背景</summary>
    String? Setting { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
