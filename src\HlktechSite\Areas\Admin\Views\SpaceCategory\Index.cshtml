﻿@{
}
<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>空间管理</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>相册列表</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("CreateAlbumCategory")','新增相册')"><span>新增相册分类</span></a></li>
                <li><a href="@Url.Action("Imagelist")"><span>图片列表</span></a></li>
                @*<li><a href="/index.php/admin/Goodsalbum/watermark.html"><span>水印设置</span></a></li>*@
            </ul>
        </div>
    </div>

    <div class="upload-con" id="uploader" style="display: none;">
        <form method="post" action="" id="fileupload" enctype="multipart/form-data">
            <div class="upload-con-div">
                选择相册：               
                <select name="category_id" id="category_id" class="select w80">
                   @* <option value='1' class="w80">默认相册</option>*@
                    @foreach (var item in Model.Lists)
                    {
                        <option value='@item.Id' class="w80">@item.Name</option>
                    }
                </select>
            </div>
            <div class="upload-con-div">
                选择文件：
                <div class="dssc-upload-btn">
                    <a href="javascript:void(0);">
                        <span><input type="file" hidefocus="true" size="1" class="input-file" name="file" multiple="multiple" /></span>
                        <p><i class="iconfont">&#xe733;</i>上传图片</p>
                    </a>
                </div>
            </div>
            <div dstype="file_msg"></div>
            <div class="upload-pmgressbar" dstype="file_loading"></div>
            <div class="upload-txt"><span>支持Jpg、Gif、Png格式，大小不超过1024KB的图片上传；浏览文件时可以按住ctrl或shift键多选。</span> </div>
        </form>
    </div>
    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>请输入ID或完整名称</dt>
                <dd><input class="txt" name="key" id="key" value="@Model.key" type="text"></dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn " title="查询">查询</a>
                <a id="open_uploader" href="JavaScript:void(0);" class="btn fr"><i class="iconfont">&#xe733;</i>上传图片</a>

            </div>
        </div>
    </form>

    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="提示相关设置操作时应注意的要点">操作提示</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>相册删除后，相册内全部图片都会删除，不能恢复，所以请谨慎操作</li>
        </ul>
    </div>


    <form method='post' id="picForm" name="picForm">
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th class="w24"></th>
                    <th class="w270">相册</th>
                    <th class="w270">图片数</th>
                    <th class="align-center">操作</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.list)
                {
                    <tr class="hover edit" id="<EMAIL>">
                        <td><input value="@item.Id" class="checkitem" type="checkbox" name="aclass_id[]"></td>
                        <td class="name">@item.Name</td>
                        <td>@item.Count</td>
                        <td class="align-center">
                            <a href="@Url.Action("Imagelist",new {AId=item.Id })" class="dsui-btn-view"><i class="iconfont"></i>图片</a>
                            <a href="javascript:dsLayerOpen('@Url.Action("UpdateAlbumCategory",new { Id=item.Id})','修改<EMAIL>')" class="dsui-btn-edit"><i class="iconfont"></i>修改</a>
                            @if (item.Id != 1)
                            {
                                @*<a href="javascript:;" onclick="submit_delete('@item.Id')" class="dsui-btn-del"><i class="iconfont"></i>删除</a>*@
                                <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                            }


                        </td>
                    </tr>
                }
            </tbody>
            <tfoot>
                <tr colspan="15" class="tfoot">
                    <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                    <td colspan="16">
                        <label for="checkallBottom">全选</label>
                        &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>删除</span></a>
                    </td>
                </tr>
            </tfoot>
        </table>
        <ul class="pagination">
            @Html.Raw(Model.Str)
        </ul>
    </form>
</div>

@*<script src="/static/plugins/js/fileupload/jquery.iframe-transport.js"></script>*@
<script src="/static/admin/js/jquery.iframe-transportNew.js"></script>
<script src="/static/plugins/js/fileupload/jquery.ui.widget.js"></script>
<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>

<script type="text/javascript" asp-location="Footer">
     var CreateImg = "@Url.Action("UploadImg")";
    // ajax 上传图片
    var upload_num = 0; // 上传图片成功数量
    $('#fileupload').fileupload({
        dataType: 'json',
        url: CreateImg,
        add: function (e, data) {
            $.each(data.files, function (index, file) {
                $('<div dstype=' + file.name.replace(/\./g, '_') + '><p>' + file.name + '</p><p class="loading"></p></div>').appendTo('div[dstype="file_loading"]');
            });
            data.submit();
        },
        done: function (e, data) {
            var param = data.result;
            $this = $('div[dstype="' + param.origin_file_name.replace(/\./g, '_') + '"]');
            $this.fadeOut(3000, function () {
                $(this).remove();
                if ($('div[dstype="file_loading"]').html() == '') {
                    setTimeout("window.location.reload()", 1000);
                }
            });
            if (param.state == 'true') {
                upload_num++;
                $('div[dstype="file_msg"]').html('<i class="iconfont">&#xe64d;' + '</i>' + '成功上传' + upload_num + '张图片');

            } else {
                $this.find('.loading').html(param.message).removeClass('loading');
            }
        }
    });
    @*function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }*@

    @*function submit_delete(IDS)
    {
        $.post("@Url.Action("Delete")", { Ids: IDS }, function (res) {
            if (!res.success) {
                alert(res.msg);
            } else {
                window.location.reload(); //刷新页面
            }

        })
    }*@
      function submit_delete(ids_str) {
          _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }
</script>
