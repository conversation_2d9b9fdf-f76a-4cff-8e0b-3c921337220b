﻿@using DG.Entity
@{
    Layout = null;

    String? Name = ViewBag.Name as String;
    String? Content = ViewBag.Content as String;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>@Name</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
</head>
<body>
    @Html.Raw(Content)
</body>
</html>