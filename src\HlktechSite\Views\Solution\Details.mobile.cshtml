﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    var Titles = ViewBag.Titles as String;
    var SolutionModel = Model.SolutionModel as Solution;

    Html.AppendTitleParts(SolutionModel.Name + DG.Setting.Current.PageTitleSeparator + Titles + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}

<h2 class="journalism-h2">@Model.SolutionModel.Name</h2>

<p class="journalism-msg">@T("时间")：@Model.SolutionModel.CreateTime    @("作者")：@Model.SolutionModel.CreateUser</p>

<div class="journalism-detail-con">
    @Html.Raw(Model.SolutionModel.Content)
</div>

<p class="flip-over">
    @if (Model.previous != null)
    {
        <span>@T("上一篇")：<a href="@Url.DGAction("Details",new { Id=Model.previous.Id})">@Model.previous.Name</a></span>
    }
    else
    {
        <span>@T("上一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
    }
    @if (Model.Nex != null)
    {
        <span>@T("下一篇")：<a href="@Url.DGAction("Details",new { Id=Model.Nex.Id})">@Model.Nex.Name</a></span>
    }
    else
    {
        <span>@T("下一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
    }
</p>