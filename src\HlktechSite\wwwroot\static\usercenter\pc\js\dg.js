﻿
//Unicode编码转换方法
var GB2312UnicodeConverter = {
    ToUnicode: function (str) {
        return escape(str).toLocaleLowerCase().replace(/%u/gi, '\\u');
    }
    , ToGB2312: function (str) {
        return unescape(str.replace(/\\u/gi, '%u'));
    }
};


function refresh() {
    $.post(Url, function (res) {
        //$(".header-message-count").html(res.message_count);
        $(".header-list-messages .cut li:eq(0) i").html(res.message_count);
        $(".header-message-count").html(res.message_count);
        if (res.message_count == "0") {
            //$(".header-list-messages #0 div").html('<p><a style="text-align:center" href="javascript:;" >您没有未读的通知</a ></p>');
            //$(".header-list-messages #0 span").hide();
            $(".header-list-messages #0 .message-empty").show();
            $(".header-list-messages #0 #headerMessageList,.header-list-messages #0 .header-message-all").hide();
        }
        else {
            //$(".header-list-messages #0 span").show();
            $(".header-list-messages #0 .message-empty").hide();
            $(".header-list-messages #0 #headerMessageList,.header-list-messages #0 .header-message-all").show();
            var joint = "";
            if (res.list != null) {
                for (var i = 0; i < res.list.length; i++) {
                    //joint += '<p><a href="' + GB2312UnicodeConverter.ToGB2312(res.list[i].Url) + '">' + GB2312UnicodeConverter.ToGB2312(res.list[i].messageTitle) + '</a><i>' + res.list[i].messageTime + '' + GB2312UnicodeConverter.ToGB2312(res.list[i].className) + '</i></p>';
                    joint += '<li><div class="header-message-type">' + GB2312UnicodeConverter.ToGB2312(res.list[i].className) + '<span class="header-message-time text-muted pull-right">' + res.list[i].messageTime + '</span><a href="javascript:;" class="header-message-read pull-right" style="display: none;" data-id="' + res.list[i].messageID + '">标记为已读</a></div><div class="text-overflow"><a class="black-link" href="' + GB2312UnicodeConverter.ToGB2312(res.list[i].Url) + '" title="">' + GB2312UnicodeConverter.ToGB2312(res.list[i].messageTitle) + '</a></div></li>';
                }
            }
            $(".header-list-messages #0 #headerMessageList").html(joint);
        }
    });

}


$(function () {



    setInterval(refresh(), 30000);

    $(".header-list-messages #0,.header-list-messages #1").mouseout(function () {
        $(".header-list-messages").find("." + $(this).attr("id")).removeClass("selected");
    })

    $(".header-list-messages .cut li").mouseover(function () {
        $(".header-list-messages .cut li").removeClass("selected");
        $(this).addClass("selected");
        $(".header-list-messages #0,.header-list-messages #1").hide();
        $(".header-list-messages #" + $(this).attr("data-type")).show();
    })

    $("#0 #headerMessageList,#1 #headerMessageList").on("mouseover", "li", function () {
        $(this).find("span").hide()
        $(this).find(".header-message-read").show()
    });

    $("#0 #headerMessageList,#1 #headerMessageList").on("click", ".header-message-read", function () {
        $.post(UrlSetRead, { Id: $(this).attr("data-id") }, function (res) {
            alert(res.msg);
            refresh();
        })
    })

    $("#0 #headerMessageList,#1 #headerMessageList").on("mouseout", "li", function () {
        $(this).find("span").show()
        $(this).find(".header-message-read").hide()
    });

    $(".IAllRead").click(function () {
        $.post(UrlSetRead, function (res) {
            alert(res.msg);
            refresh();
        })
    });

    $("#gainVerify").click(function () {
        $.post(GetVerifyUrl, { Phone: $("[name=Phone]").val() }, function (res) {
            if (res.success) {

            }
        });
    });

    $("#determine").click(function () {
        $("", $("#bandPhone").serialize(), function () {

        });
    });

})


