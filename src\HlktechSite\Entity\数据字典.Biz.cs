using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;
using DH.SearchEngine;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>数据字典</summary>
    public partial class DataDictionary : CubeEntityBase<DataDictionary>
    {
        #region 对象操作
        static DataDictionary()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(DId));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        /// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        [EditorBrowsable(EditorBrowsableState.Never)]
        protected override void InitData()
        {
            // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
            if (Meta.Session.Count > 0) return;

            if (XTrace.Debug) XTrace.WriteLine("开始初始化DataDictionary[数据字典]数据……");

            var list = new List<DataDictionary>();

            list.Add(new DataDictionary
            {
                CodeType = "CommunicationModule",
                Name = "通信模块",
                Status = true,
                DId=1,
                Sort= 1,
                Content = "首页工单通信模块数据"
            });

            list.Add(new DataDictionary
            {
                CodeType = "SensingModule",
                Name = "传感模块",
                Status = true,
                DId = 1,
                Sort = 2,
                Content = "首页工单传感模块数据"
            });

            list.Add(new DataDictionary
            {
                CodeType = "PowerModule",
                Name = "电源模块",
                DId = 1,
                Status = true,
                Sort = 3,
                Content = "首页工单电源模块数据"
            });

            list.Add(new DataDictionary
            {
                CodeType = "OtherProducts",
                Name = "其他产品",
                DId = 1,
                Status = true,
                Sort = 4,
                Content = "首页工单其他产品数据"
            });

            list.Save();

            if (XTrace.Debug) XTrace.WriteLine("完成初始化DataDictionary[数据字典]数据！");
        }

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        /// 数据字典分类
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public DictionariesCategory DictionariesCategory => Extends.Get(nameof(DictionariesCategory), k => DictionariesCategory.FindById(DId));

        /// <summary>
        /// 数据字典分类名称
        /// </summary>
        [XmlIgnore, ScriptIgnore]
        public string DName ="";
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static DataDictionary FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据字典类型Id查找</summary>
        /// <param name="dId">字典类型Id</param>
        /// <returns>实体列表</returns>
        public static IList<DataDictionary> FindAllByDId(Int32 dId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.DId == dId);

            return FindAll(_.DId == dId);
        }

        /// <summary>
        /// 根据字典值类型 及字典类型ID查询
        /// </summary>
        /// <param name="Codetype"></param>
        /// <param name="DId"></param>
        /// <returns></returns>
        public static DataDictionary FindByCodetypeAndDId(String Codetype, int DId)
        {
            if (Codetype.IsNullOrEmpty()) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.CodeType == Codetype&&e.DId== DId);


            return Find(_.CodeType == Codetype & _.DId==DId);
        }

        /// <summary>
        /// 根据ID集合删除数据
        /// </summary>
        /// <param name="Ids">ID集合</param>
        public static void DelByIds(String Ids)
        {
            if (Delete(_.Id.In(Ids.Trim(','))) > 0)
                Meta.Cache.Clear("");
        }

        /// <summary>模糊查询分类名称</summary>
        /// <param name="Key">关键字</param>
        /// <returns>实体列表</returns>
        public static IList<DataDictionary> FindAllByLikeCodeType(string Key)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.CodeType.ToLower().Contains(Key.ToLower()));

            return FindAll(_.CodeType.Contains(Key));
        }


       
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="dId">字典类型Id</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<DataDictionary> Search(Int32 dId, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (dId >= 0) exp &= _.DId == dId;
            if (!key.IsNullOrEmpty()) exp &= _.CodeType.Contains(key) | _.Name.Contains(key) | _.Content.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        // Select Count(Id) as Id,Category From DataDictionary Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<DataDictionary> _CategoryCache = new FieldCache<DataDictionary>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}