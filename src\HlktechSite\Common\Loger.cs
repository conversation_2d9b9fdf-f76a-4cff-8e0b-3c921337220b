﻿using XCode.Membership;

namespace HlktechSite.Common
{
    /// <summary>
    /// 日志操作类
    /// </summary>
    public static class Loger
    {
        /// <summary>
        /// 用户日志
        /// </summary>
        /// <param name="action"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        public static bool UserLog(String action, String? remark = null)
        {
            var log = new Log
            {
                Category = "用户",
                Action = action,
                Remark = remark,
            };

            return log.Insert() > 0;
        }

        /// <summary>
        /// 系统日志
        /// </summary>
        /// <param name="action"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        public static bool SystemLog(String action, String? remark = null)
        {
            var log = new Log
            {
                Category = "系统",
                Action = action,
                Remark = remark,
            };

            return log.Insert() > 0;
        }
    }
}
