﻿.pro-detail-msg > h2 {
    font-size: 11px;
    color: #333;
    font-weight: bold;
    text-align: center;
    margin-bottom: 13px;
    max-width: 95%;
    margin-left: auto;
    margin-right: auto;
}

.pro-detail-msg > i {
    display: block;
    text-align: center;
    font-size: 8px;
    margin-bottom: 10px;
}

    .pro-detail-msg > i > b {
        color: #FF0938;
    }

.pro-detail-msg > p {
    width: 93.2%;
    margin: 0 auto;
    font-size: 8px;
    text-align: center;
    margin-bottom: 24px;
}

.pro-detail-msg div {
    text-align: center;
    background-color: #fff;
    margin-bottom: 28px;
}

    .pro-detail-msg div a {
        display: inline-block;
        width: 21.43%;
        text-align: center;
        height: 22px;
        border-radius: 2px;
        line-height: 22px;
        margin-right: 2.8%;
        font-size: 9px;
        color: #fff;
        background-color: #409EFF;
        text-decoration: none !important;
        vertical-align: bottom;
    }

        .pro-detail-msg div a:last-child {
            background-color: #F56C6C;
            margin-right: 0px;
        }


.pro-type-menu {
    background-color: #EEEEEE;
    margin-bottom: 0px;
    text-align: center;
}

    .pro-type-menu a {
        margin-right: 2%;
        font-size: 8px;
        color: #333;
        font-weight: bold;
        height: 27px;
        line-height: 27px;
        text-decoration: none !important;
    }

    .pro-type-menu .selected {
        color: #1962AE;
    }

    .pro-type-menu a:last-child {
        margin-right: 0%;
    }


.pro-content>div {
    display: none;
}

.pro-content #c-id3 {
    padding-top: 12px;
    background-color: #FBFAFF;
    padding-bottom:31px;
}

    .pro-content #c-id3 ul {
    }

        .pro-content #c-id3 ul li {
            border-bottom: 1px solid #D7D7D7;
            display: flex;
            font-size: 9px;
            padding-top: 9px;
            padding-bottom: 9px;
        }

        .pro-content #c-id3 ul span {
            width: 31.87%;
            font-weight: bold;
            padding-left: 15.34%;
        }

        .pro-content #c-id3 ul p {
            margin:0px;
           flex:1;
        }

        .pro-content #c-id3 ul p i{
            display:block;
            margin-bottom:10px;
        }
            .pro-content #c-id3 ul p i a{
               color:#444;
               text-decoration:none !important;
            }

            .pro-content #c-id3 ul p i img {
                margin-left: 7px;
                max-width: 8px;
            }

.pro-content #c-id4,.pro-content #c-id5 {
    background-color: #FBFAFF;
    padding-top: 22px;
    padding-bottom: 18px;
}

    .pro-content #c-id4 ul, .pro-content #c-id5 ul {
        width: 88.53%;
        margin: 0 auto;
    }

    .pro-content #c-id4 li,.pro-content #c-id5 li {
        font-size: 7px;
        display: flex;
        height: 30px;
        line-height: 30px;
        padding-left: 1.81%;
        border-top:1px solid #EEEEEE;
    }

    .pro-content #c-id4 li a,.pro-content #c-id5 li a {
        display: flex;
        flex: 1;
        text-decoration: none;
        color: #333333;
    }

        .pro-content #c-id4 li:hover, .pro-content #c-id5 li:hover {
            background-color: #DDDDDD;
        }

    .pro-content #c-id4 li p,.pro-content #c-id5 li p {
        flex: 1;
        margin:0px;
    }

        .pro-content #c-id4 li img,.pro-content #c-id5 li img {
            max-width: 7px;
            margin-right: 3px;
            vertical-align: text-bottom;
        }

        .pro-content #c-id4 li span, .pro-content #c-id5 li span {
            display: inline-block;
            width: 23.46%;
            min-width: 90px;
        }

        .pro-content #c-id4 li i,.pro-content #c-id5 li i {
            display:inline-block;
            width:14.91%;
        }


.pro-content #c-id6{
    background-color:#FBFAFF;
    font-size:7px;
}

    .pro-content #c-id6 ul {
        width: 75.53%;
        margin: 0 auto;
    }

        .pro-content #c-id6 ul li {
            display:flex;
            border-top: 1px solid #EEEEEE;
        }
            .pro-content #c-id6 ul li{
                padding-top:21px;
            }

        .pro-content #c-id6 ul li:last-child {
            padding-bottom: 24px;
        }

                .pro-content #c-id6 ul li p {
                    width: 33.6%;
                    padding-left: 2.4%;
                    display: inline-block;
                    color: #444444;
                    font-weight: bold;
                }
        .pro-content #c-id6 ul li span{
           flex:1;
        }

            .pro-content #c-id6 ul li span img{
               max-width:9px;
               margin-right:3px;
               
            }

            .pro-content #c-id6 ul li span>a {
                margin-left: 8px;
                font-size:7px;
                color:#666666;
            }

            .pro-content #c-id6 ul li span>a:first-child {
                margin-left: 0px;
            }

            .pro-content #c-id6 ul li span i {
                display: inline-block;
                width:100%;
                margin-bottom:10px;
            }

                .pro-content #c-id6 ul li span i small{
                    margin-left:3px;
                }

                .pro-content #c-id6 ul li span i a {
                    margin-left: 7px;
                }


.down-details-title {
    display: block;
    padding-left: 15.47%;
    color: #333333;
    font-size: 8px;
    height: 27px;
    line-height: 27px;
    background-color: #EEEEEE;
    margin: 0px;
    font-weight: bold;
}
#c-id2{
    padding-top:10px;
}
#c-id2 table {
    width: 95%;
    margin-top: 81px;
    margin: auto;
    border-collapse: collapse;
    text-align: center;
    border: none;
    border-bottom: 0;
    border-left: 0;
    margin-bottom: 80px;
}

    #c-id2 table td p {
        margin: 15px 0 15px 0;
    }
    #c-id2 table td {
        border: 1px solid #CCC;
        word-break: break-all;
        overflow-wrap: break-word;
        vertical-align: middle;
        white-space: pre-wrap;
    }
.keepleft {
    text-align: left;
    padding-left: 20px;
}