﻿
.title1 {
    color: #333 !important;
    background-color: #fff;
}

    .title1:hover {
        background-color: #eee !important;
    }

.download-top > img {
    width: 100%;
}

.download-top {
    position: relative;
}
.navigation-con-left .btn-group {
    margin-left:20px;
}
.download-top > div {
    position: absolute;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    color: #FFFFFF;
}

        .download-top > div > h2 {
            margin-top: 149px;
            text-align: center;
            font-size: 56px;
        }

        .download-top > div > p {
            width: 844px;
            margin: 0 auto;
            margin-top: 35px;
            text-align: center;
            font-size: 20px;
        }


.navigation {
    position: relative;
    width: 100%;
    border-bottom: 1px solid #EEEEEE;
    height: 65px;
}

    .navigation::after {
        content: '';
        position: absolute;
        left: 0px;
        right: 0px;
        bottom: 0px;
        height: 1px;
        width: 100%;
        background-color: #EEEEEE;
    }

.navigation-con {
    position: relative;
    width: 1200px;
    margin: 0 auto;
}

.navigation-con-left {
    margin-top: 21px;
    float: left;
}

    .navigation-con-left a {
        color: #333333;
    }

        .navigation-con-left a:hover {
            text-decoration: none;
        }


.navigation-con-right {
    position: absolute;
    margin-top: 14px;
    right: 0px;
    line-height: 34.44px;
}

.input-group {
    margin-left: 21px;
    width: 279px;
}

.navigation-con-right .input-group {
    float: right;
}

.navigation-con-right .input-group-addon {
    background-color: #4E6EF2;
    border-radius: 0px 10px 10px 0px;
}


.download-list {
    width: 1220px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 70px;
    margin: 0 auto;
}

    .download-list > div {
        position: relative;
        width: 273.83px;
        border: 1px solid #ffffff;
        display: inline-block;
        margin-right: 31px;
        box-shadow: 0px 6px 19px 1px rgba(177,174,174,0.2);
        border-radius: 4px;
        margin-bottom: 60px;
        padding-bottom: 18px;
    }

        .download-list > div:hover {
            border: 1px solid #4D6DF1;
            box-shadow: 0px 6px 19px 1px rgba(177,174,174,0.4);
        }



        .download-list > div:nth-child(4n) {
            margin-right: 0px;
        }

        .download-list > div > img {
            width: 100%;
        }

        .download-list > div > h2 {
            font-size: 16px;
            font-weight: 400;
            color: rgba(51,51,51,1);
            text-align: center;
            padding-top: 4px;
            padding-bottom: 26px;
            margin: 0px;
        }

        .download-list > div > a {
            width: 151px;
            height: 27px;
            border-radius: 4px;
            border: 1px solid #A9ABA9;
            text-align: center;
            line-height: 27px;
            display: inline-block;
            margin-left: calc(50% - 75.5px);
            color: #A9ABA9;
        }

            .download-list > div > a:hover {
                background-color: #409EFF;
                text-decoration: none;
                color: #fff;
                border: 1px solid #409EFF;
            }

.download-details {
    width: 1200px;
    margin: 0 auto;
    padding-bottom: 84px;
}

    .download-details > p {
        margin-top: 56px;
        display: flex;
        height: 44px;
        line-height: 44px;
        padding-left: 21px;
        padding-right: 32px;
        background-color: #DDDDDD;
        color: #333840;
        font-size:20px;
        font-weight:bold;
    }

        .download-details > p > span {
            flex: 1;
        }

            .download-details > p > span:last-child {
                font-weight: 400;
                text-align: right;
                font-size: 18px;
            }

            .download-details > p > span > a:last-child {
                margin-left:37px;
            }


    .download-details ul {
        padding-top: 23px;
    }

    .download-details li {
        display: block;
        width: 100%;
        height: auto;
        line-height: 64px;
        padding-left: 9px;
        position: relative;
        padding-top: 20px;
        padding-bottom: 20px;
    }

        .download-details li:before {
            content: "";
            display: block;
            height: 2px;
            line-height: 64px;
            padding-left: 9px;
            position: absolute;
            left: 0px;
            right: 0px;
            bottom: 0px;
            background-color: #d9dada;
        }

    .download-details li {
        display: flex;
    }

        .download-details li span:first-child {
            flex: 1;
            text-align: left;
            font-size: 18px;
            font-weight: bold;
            color: #444;
        }

        .download-details li span:last-child {
            text-align: left;
            flex: 3
        }

            .download-details li span:last-child > i {
                font-style: normal;
                font-size: 18px;
                display: inline-block;
                padding-left: 30px;
                font-weight: 400;
            }


                .download-details li span:last-child > i img {
                    margin-left: 14px;
                    vertical-align: middle;
                    margin-bottom: 3px;
                }
