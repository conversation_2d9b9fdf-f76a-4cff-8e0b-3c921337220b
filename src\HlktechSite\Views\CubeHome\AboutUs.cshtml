﻿@model SingleArticle
@{
    Html.AppendCssFileParts("~/css/AboutUs.css");

    Html.AppendTitleParts(T("关于我们1").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}
<div class="top">
    <img src="@(CDN.GetCDN())/images/cpzx.jpg" />
    <div>
        <h2>@Model.Name</h2>
        <P>
            @T("关于我们2")
        </P>
    </div>
</div>

<div class="AboutUs-nav">
    <p>
        @await Component.InvokeAsync("Location", new { model = ViewBag.Locations })
    </p>
</div>
@Html.Raw(Model.Content)