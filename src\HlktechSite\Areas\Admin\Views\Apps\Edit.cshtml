﻿@model AppManagers
@{
    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .layui-tab-title {
        width: 95%;
        margin: 0 auto;
    }

    .layui-tab.layui-tab-brief.Lan {
        box-shadow: 0 0 5px #AAA inset;
        box-shadow: 0 0 5px #AAA;
        /* padding-bottom: 15px; */
        /* height: 300px; */
    }

    textarea {
        resize: vertical;
        overflow: hidden;
        min-width: 400px;
        padding: 10px 10px 0px 10px !important;
        min-height: 38px;
    }

</style>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("App管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:;" class="current"><span>@T("编辑")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("Edit", "Apps", FormMethod.Post, new { id = "form1", enctype = "multipart/form-data" }))
    {
        <div class="layui-tab layui-tab-brief Lan" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content" style="padding: 10px 10px 5px 10px !important;">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td colspan="2" class="required"><label class="gc_name validation" for="Name">@T("App名称"):</label></td>
                            </tr>
                            <tr class="noborder">
                                <td class="vatop rowform"><input type="text" maxlength="20" value="@Model.Name" name="Name" id="Name" class="txt"></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2"><label class="gc_name" for="Content">@T("内容"):</label></td>
                            </tr>
                            <tr class="noborder">
                                <td class="vatop rowform"><textarea cols="30" rows="10" value="" name="Content" id="Content" class="txt textarea">@Model?.Content</textarea></td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var modelLan = AppManagersLan.FindByJIdAndLId(Model.Id, item.Id, false);
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td colspan="2" class="required"><label class="gc_name validation" for="[@item.Id].Name">@T("App名称"):</label></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform"><input type="text" value="@modelLan.Name" name="[@item.Id].Name" id="[@item.Id].Name" class="txt"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td colspan="2"><label class="gc_name" for="[@item.Id].Content">@T("内容"):</label></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform">
                                            <!-- <input type="text" value="@modelLan.Content" name="[@item.Id].Content" id="[@item.Id].Content" class="txt textarea"> -->
                                            <textarea cols="30" rows="10" value="@modelLan.Content" name="[@item.Id].Content" id="[@item.Id].Content" class="txt textarea">@modelLan.Content</textarea>
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>


        <table class="ds-default-table">
            <tbody>
                <tr>
                    <td colspan="2"><label for="pic">@T("APP Logo:")</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform" style="width: 400px;">
                        <span class="type-file-show">
                            @if (Model.AppLogo != null)
                            {
                                <img class="show_image" src="/static/admin/images/preview.png">
                                <div class="type-file-preview"><img src="@CDN.GetCDN()/@Model.AppLogo"></div>
                            }
                        </span>
                        <span class="type-file-box" style="display: flex;">
                            <input type='text' name='textfield' id='textfield1' class='type-file-text' value="@Model.AppLogo"/>
                            <input type='button' name='button' id='button1' value='上传' class='type-file-button' />
                            <input name="pic" type="file" class="type-file-file" id="pic" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                        </span>
                    </td>
                    <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="AndroidPaths">@T("国内Android下载地址"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="@Model?.AndroidPaths" name="AndroidPaths" id="AndroidPaths" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="AndroidPaths1">@T("国外Android下载地址"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="@Model?.AndroidPaths1" name="AndroidPaths1" id="AndroidPaths1" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="AndroidVersion">@T("Android版本"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="@Model?.AndroidVersion" name="AndroidVersion" id="AndroidVersion" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="IosPaths">@T("IOS下载地址"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="@Model?.IosPaths" name="IosPaths" id="IosPaths" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2"><label class="gc_name" for="IosVersion">@T("IOS版本"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <input type="text" value="@Model?.IosVersion" name="IosVersion" id="IosVersion" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr>
                    <td colspan="2"><label for="apk">Apk 文件:</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <span class="type-file-box">
                            @if (Model?.AndroidPaths != null && Model?.ApkFilePath==null)
                            {
                                <input type='text' name='textfield2' id='textfield2' class='type-file-text' value="@Model.AndroidPaths"/>
                            }else{
                                <input type='text' name='textfield2' id='textfield2' class='type-file-text' value="@Model.ApkFilePath"/>
                            }
                            <input type='button' name='button2' id='button2' value='上传' class='type-file-button' />
                            <input name="apk" type="file" class="type-file-file" id="apk" size="30" hidefocus="true" ds_type="change_apk">
                        </span>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    }
</div>

<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script type="text/javascript" asp-location="Footer">
    console.log('APK文件地址','@Model.ApkFilePath')
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;

            const textarea = $('textarea');
            for (let i = 0; i < textarea.length; i++) {
                textarea[i].style.height = textarea[i].scrollHeight + 'px';
                
            }
            
        $('#form1').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                Name: {
                    required: true
                }
            },
            messages: {
                Name: {
                    required: '@T("App名称不能为空")'
                }
            }
        });
    })
</script>