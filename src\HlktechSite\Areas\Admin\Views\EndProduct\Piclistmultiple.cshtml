﻿@{
}
<div class="goods-gallery" dstype="gallery-0">
    <a class="sample_demo" href="@Url.Action("Piclistmultiple")" style="display:none;">提交</a>
    <div class="nav">
        <span class="l">
            用户相册 >
            全部图片
        </span>
        <span class="r">
            <select name="jumpMenu" style="width:100px;">
                <option value="0" style="width:80px;">请选择</option>
                @foreach (var item in ViewBag.ListXC)
                {
                    <option style="width:80px;" value="@item.Id">@item.Name</option>
                }
            </select>
        </span>
    </div>
    <ul class="list">

        @foreach (var item in ViewBag.list)
        {

            <li onclick="insert_imgs('@item.Id','@item.Spec', '@item.Cover',0);">
                <a href="JavaScript:void(0);"><img src="@item.Cover" /></a>
            </li>
        }
    </ul>
    <div class="pagination"><ul class="pagination">@Html.Raw(ViewBag.Str)</ul></div>
</div>
<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script asp-location="Footer">
    $(document).ready(function () {
        $('div[dstype="gallery-0"] .pagination a').ajaxContent({
            event: 'click', //mouseover
            loaderType: 'img',
            loadingMsg: '/static/home/<USER>/loading.gif',
            target: 'div[dstype="album-0"]'
        });
        $('div[dstype="gallery-0"]').find('select[name="jumpMenu"]').unbind().change(function () {
            var $_select_submit = $('div[dstype="gallery-0"]').find('.sample_demo');
            var $_href = $_select_submit.attr('href') + "?id=" + $(this).val();
            $_select_submit.attr('href', $_href);
            $_select_submit.unbind().ajaxContent({
                event: 'click', //mouseover
                loaderType: 'img',
                loadingMsg: '/static/home/<USER>/loading.gif',
                target: 'div[dstype="album-0"]'
            });
            $_select_submit.click();
        });
    });
</script>