﻿body, html {
    height: 100%;
    width: 100%;
    position: relative;
    -webkit-tap-highlight-color: transparent;
    max-width: 750px;
    margin: 0 auto;
}

ul {
    margin: 0px;
    padding: 0px;
}

li {
    list-style: none;
}

i {
    font-style: normal;
}

p {
    margin: 0px;
}

.nav-pills > li {
    display: block;
    width: 100%;
}

    .nav-pills > li > a {
        color: inherit;
    }

.navbar-toggle .icon-bar {
    background-color: #000;
}
.changelanguage > li > a {
    border-radius: 4px;
}

.changelanguage > li > a {
    position: relative;
    display: block;
    padding: 10px 15px;
}

.changelanguage > li > a {
    color: inherit;
}
.search-btn {
    display: inline-block;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    width: 37px;
    height: 34px;
    margin-top: 8px;
    padding: 8px;
    outline: none;
    margin-bottom: 8px;
}

    .search-btn img {
        width: 100%;
        margin-top: -7px;
    }

.com-footer {
    padding-top: 37px;
}

.com-footer-top {
    width: 90.78%;
    margin: 0 auto;
    padding-bottom: 20px;
    height: 53px;
    line-height: 56px;
    font-size: 1.3rem;
    color: #75777F;
    font-weight: bold;
    border-bottom: 2px solid #E5E5E5;
}

    .com-footer-top img {
        max-width: 38%;
        vertical-align: bottom;
        float: left;
    }


    .com-footer-top span {
        float: right;
    }

.com-footer-menu {
    padding-top: 4px;
}

.com-footer-menu {
    padding-top: 4px;
    width: 90.66%;
    margin: 0 auto;
}

    .com-footer-menu dd {
        padding-top: 16px;
        width: 49.2%;
        display: inline-block;
        padding-left: 9.41%;
        vertical-align: top;
    }

        .com-footer-menu dd:first-child, .com-footer-menu dd:nth-child(2) {
            padding-top: 20px;
        }


        .com-footer-menu dd ul li {
            width: 100%;
            font-size: 12px;
            color: #777777;
            height: 30px;
        }

            .com-footer-menu dd ul li a {
                color: inherit;
                text-decoration: none !important;
            }

        .com-footer-menu dd ul .com-footer-menu-title {
            font-size: 18px;
            height: 43px;
            font-weight: bold;
            color: #393939;
        }

.com-footer-contact {
    width: 74.76%;
    margin: 0 auto;
    padding-left: 0.2%;
}

    .com-footer-contact h2 {
        font-size: 18px;
        color: #393939;
        font-weight: bold;
        padding-bottom: 20px;
        margin: 0px;
    }

    .com-footer-contact p {
        font-weight: bold;
        font-size: 12px;
        color: #939393;
        margin: 0px;
        line-height: 27px;
    }

    .com-footer-contact hr {
        color: #E5E5E5;
        height: 1.5px;
        margin: 0px;
        margin-top: 12px;
        margin-bottom: 10px;
    }

.top {
    position: relative;
}

    .top img {
        width: 100%;
        height: 161px;
    }

    .top h2 {
        display: block;
        position: absolute;
        font-size: 24px;
        width: 100%;
        color: #fff;
        top: 44px;
        margin: 0px;
        text-align: center;
    }

    .top p {
        position: absolute;
        display: block;
        top: 105px;
        width: 100%;
        color: #fff;
        text-align: center;
    }

.seach-div {
    background-color: #FAFAFA;
    padding-left: 3.33%;
    padding-top: 5px;
    padding-bottom: 5px;
    display: flex !important;
    border-bottom: 1px solid #E5E5E5;
}

    .seach-div input {
        flex: 1;
        border: 1px solid #E5E5E5;
        border-radius: 2px;
        padding-left: 2%;
    }

    .seach-div a {
        display: inline-block;
        width: 11.87%;
        text-align: center;
        height: 32px;
        line-height: 32px;
        color: #1C1C1C !important;
        font-weight: bold;
        font-size: 11px;
        text-decoration: none !important;
    }

.type-menu {
    background-color: #FAFAFA;
    padding-left: 4.4%;
    padding-top: 13px;
    margin: 0px;
    overflow: hidden;
}

    .type-menu li {
        float: left;
        color: #90949A;
        font-size: 8px;
        padding-left: 1.8%;
        padding-right: 2.8%;
        padding-bottom: 5px;
        cursor: pointer;
    }

        .type-menu li a {
            color: inherit !important;
            text-decoration: none;
        }


    .type-menu .selected {
        color: #343434;
        position: relative;
    }

        .type-menu .selected:after {
            content: "";
            position: absolute;
            left: 0px;
            height: 2px;
            right: 0px;
            bottom: 0px;
            background-color: #343434;
        }


.rows-list {
    margin: 0px;
    padding: 0px;
    display: block;
    overflow: hidden;
    padding-bottom: 10px;
}

    .rows-list li {
        margin-top: 18px;
        list-style: none;
        width: 43.37%;
        height: 250px;
        margin-top: 27px;
        margin-left: 4.8%;
        background-color: #fff;
        float: left;
        margin-bottom: 9px;
        border-radius: 5px;
        box-shadow: 0px 2px 15px 1px rgba(177, 173, 173, 0.38);
    }

        .rows-list li:nth-child(2n) {
            margin-left: 4.27%;
        }


        .rows-list li a {
            text-decoration: none !important;
        }

        .rows-list li img {
            width: 100%;
        }

        .rows-list li b {
            padding-left: 5.5%;
            margin-top: 5px;
            margin-bottom: 0px;
            display: inline-block;
            font-size: 11px;
            color: #333333;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .rows-list li i {
            padding-left: 5.5%;
            padding-right: 5.9%;
            margin-bottom: 15px;
            font-size: 11px;
            color: #858585;
            width: 100%;
            height: 33px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            white-space: pre-wrap;
            font-style: normal;
        }




.pagination > li > a, .pagination > li > span {
    padding: 2px 8px;
    font-size: 11px;
}

.pro-nav {
    padding-left: 1.4%;
    position: relative;
    height: 77px;
    background-color: #FAFAFA;
}

    .pro-nav > a:first-child {
        width: 34px;
    }

    .pro-nav > a {
        font-size: 8px;
        display: inline-block;
        height: 27px;
        line-height: 27px;
        position: relative;
        color: #90949A !important;
        text-decoration: none;
        min-width: 54px;
        padding-left: 1%;
        padding-right: 1%;
        text-align: center;
        text-decoration: none !important;
    }

    .second {
        display: block;
        background-color: #fff;
    }

        .second > ul {
            padding-left: 3%;
            margin: 0 auto;
            overflow: hidden;
            font-size: 8px;
            background-color: #fafafa;
        }

            .second > ul > li {
                line-height: 30px;
                list-style: none;
                float: left;
                text-align: center;
                color: #90949A;
                height: 30px;
                margin-right: 4.9px;
            }

                .second > ul > li > a {
                    display: inline-block;
                    width: max-content;
                    padding-left: 5%;
                    padding-right: 5%;
                    position: relative;
                    color: #90949A !important;
                    text-decoration: none !important;
                }

                    .second > ul > li > a.selected:after {
                        content: "";
                        position: absolute;
                        left: 0px;
                        right: 0px;
                        background-color: #777;
                        bottom: 0px;
                        height: 2px;
                    }


    .pro-nav .selected {
        position: relative;
    }

        .pro-nav .selected:after {
            content: "";
            left: 0px;
            right: 0px;
            height: 2px;
            background-color: #333333;
            bottom: 0px;
            position: absolute;
        }


.down-list li a {
    text-align: center;
}

.down-list li i {
    padding: 0px;
    display: inline-block;
    min-height: initial;
    width: 54.51%;
    height: 13px;
    line-height: 13px;
    color: #fff;
    border-radius: 2px;
    background-color: #409EFF;
    text-align: center;
    cursor: pointer;
    margin-left: 22.745%;
}

.journalism-h2 {
    padding-left: 10%;
    padding-right: 10%;
    font-size: 16px;
    color: #35354B;
    padding-top: 30px;
    padding-bottom: 12px;
    background-color: #FBFAFF;
    text-align: center;
    margin: 0px;
}

.journalism-msg {
    text-align: center;
    font-size: 8px;
    margin-bottom: 0px;
    padding-bottom: 17px;
    background-color: #FBFAFF;
}

.journalism-detail-con {
    background-color: #FBFAFF;
    padding-left: 5%;
    padding-right: 5%;
}

    .journalism-detail-con > p {
        margin-bottom: 0px;
    }

    .journalism-detail-con img {
        width: 100%;
    }

.flip-over {
    background-color: #FBFAFF;
    padding-top: 32px;
    font-size: 8px;
    padding-left: 10%;
    padding-right: 10%;
    color: #333333;
    padding-bottom: 48px;
    display: flex;
}

    .flip-over span {
        flex: 1;
        overflow: hidden;
    }

        .flip-over span:last-child {
            text-align: right;
        }

    .flip-over a {
        display: inline-block;
        max-width: 40%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-decoration: none !important;
        vertical-align: middle;
    }

.jour-list {
    padding-top: 25px;
}

    .jour-list div {
        width: 89.73%;
        margin: 0 auto;
        height: 113px;
        display: flex;
        margin-bottom: 25px;
        box-shadow: 0px 2px 15px 1px rgba(177, 173, 173, 0.5);
    }

        .jour-list div > img {
            width: 43.6%;
            height: 100%;
        }

    .jour-list a {
        text-decoration: none !important;
    }

    .jour-list div p {
        padding-left: 3.06%;
        width: calc(100% - 43.6%);
        padding-top: 11.5px;
        position: relative;
        margin: 0px;
        font-size: 11px;
        color: #333333;
    }

        .jour-list div p span {
            width: 84%;
            height: 37px;
            /* text-overflow: ellipsis; */
            -o-text-overflow: ellipsis;
            padding-bottom: 9px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            white-space: pre-wrap;
        }

        .jour-list div p a {
            margin-top: 10px;
            margin-bottom: 8px;
            display: block;
            width: 67.5%;
            font-size: 11px;
            color: #333333 !important;
            text-decoration: none !important;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }


        .jour-list div p i {
            display: inline-block;
            color: #858585;
            font-size: 9px;
            width: 80.07%;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            white-space: pre-wrap;
        }


        .jour-list div p b {
            font-size: 8px;
            color: #858585;
            position: absolute;
            right: 1.87%;
            bottom: 4px;
        }

    .jour-list .paging {
        height: auto;
    }


.fixbtn {
    position: fixed;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    box-shadow: 1px 3px 10px 1px rgba(177, 174, 174, 0.26);
    font-size: 9px;
    color: #4A4A4A;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
    background-color: #fff;
    z-index: 100;
    right: 1.47%;
}

    .fixbtn > .icon {
        display: inline-block;
        margin: 0 auto;
        width: 33px;
        margin-top: calc(50% - 16.5px);
    }

.backtop {
    bottom: 20%;
    opacity: 0;
}

.kefubtn {
    bottom: 28%;
}

    .kefubtn .panel {
        background-color: white;
        width: 130px;
        height: 246px;
        border: 1px solid transparent;
        border-radius: 3px;
        box-shadow: 1px 3px 8px rgba(210,210,210,0.7);
        position: absolute;
        left: -143px;
        top: -136px;
        overflow-x: hidden;
        overflow-y: scroll;
    }

    .sale span {
        margin-top: 2px;
    }





.pagination > li > a, .pagination > li > span {
    padding: 6px 8px !important;
}
