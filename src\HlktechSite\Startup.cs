using Autofac;

using DG.Web.Framework;

using DH.SearchEngine;
using DH.SearchEngine.Extensions;
using DH.Web.Framework.WebMiddleware;

using Jering.Javascript.NodeJS;

using JiebaNet.Segmenter;

using NewLife;
using NewLife.Caching;
using NewLife.Caching.Services;
using NewLife.Log;
using NewLife.Redis.Extensions;
using Pek.Configs;
using Pek.Timing;

using Stardust;

namespace HlktechSite;

/// <summary>
/// 启动配置
/// </summary>
public class Startup
{
    /// <summary>
    /// 配置
    /// </summary>
    private readonly IConfiguration Configuration;
    /// <summary>
    /// 环境变量
    /// </summary>
    private readonly IWebHostEnvironment _webEnv;

    /// <summary>
    /// 初始化启动配置
    /// </summary>
    /// <param name="configuration">配置</param>
    /// <param name="env">环境变量</param>
    public Startup(IConfiguration configuration, IWebHostEnvironment env)
    {
        ConfigFileHelper.Set(env: env);
        Configuration = configuration;
        _webEnv = env;
    }

    /// <summary>
    /// 配置服务
    /// </summary>
    public void ConfigureServices(IServiceCollection services)
    {
        // 引入星尘，设置监控中间件
        var star = services.AddStardust(null, null, null);
        TracerMiddleware.Tracer = star?.Tracer;
        star?.SetWatchdog(120);

        // 分布式服务，使用配置中心RedisCache配置
        services.AddSingleton<ICacheProvider, RedisCacheProvider>();

        var config = star?.GetConfig();
        var cacheConn = config?["RedisCache"];
        Redis? redis = null;
        if (!cacheConn.IsNullOrEmpty())
        {
            redis = new FullRedis { Log = XTrace.Log, Tracer = star?.Tracer };
            redis.Init(cacheConn);
            services.AddSingleton(redis);
        }

        services.AddCube(Configuration, _webEnv);

        //var sslPath = "LettuceEncrypt".AsDirectory().FullName;
        //XTrace.WriteLine($"证书路径：{sslPath}");

        //services.AddLettuceEncrypt()
        //    .PersistDataToDirectory("LettuceEncrypt".AsDirectory(), "Password123");

        services.AddNodeJS(); // NodeJs调用
        // services.Configure<NodeJSProcessOptions>(options => options.ProjectPath = "Scripts/");
        services.Configure<NodeJSProcessOptions>(options => options.NodeAndV8Options = "--inspect-brk");
        services.Configure<OutOfProcessNodeJSServiceOptions>(options => options.InvocationTimeoutMS = -1);

        services.AddSearchEngine(new LuceneIndexerOptions()  // Lucene搜索引擎
        {
            Path = "lucene".GetFullPath()
        });

        if (redis != null)
        {
            services.AddDataProtection().PersistKeysToRedis(redis);
        }
    }

    /// <summary>
    /// 配置请求管道
    /// </summary>
    /// <param name="app">用于配置应用程序的请求管道的生成器</param>
    /// <param name="env">环境变量</param>
    /// <param name="luceneIndexerOptions">Lucene参数</param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, LuceneIndexerOptions luceneIndexerOptions)
    {
        app.UseCube(Configuration, env);

        UseLuceneSearch(env, luceneIndexerOptions);
    }

    /// <summary>
    /// 配置容器
    /// </summary>
    /// <param name="builder"></param>
    public void ConfigureContainer(ContainerBuilder builder)
    {
        builder.ConfigureContainer();
    }

    private static void UseLuceneSearch(IHostEnvironment env, LuceneIndexerOptions luceneIndexerOptions)
    {
        Task.Run(() =>
        {
            XTrace.WriteLine($"正在导入自定义词库...");
            var time = HiPerfTimer.Execute(() =>
            {
                var lines = File.ReadAllLines(Path.Combine(env.ContentRootPath, "App_Data", "CustomKeywords.txt"));
                var segmenter = new JiebaSegmenter();

                foreach(var word in lines)
                {
                    segmenter.AddWord(word);
                }
            });
            XTrace.WriteLine($"导入自定义词库完成，耗时{time}s");
        });

        string lucenePath = Path.Combine(env.ContentRootPath, luceneIndexerOptions.Path);
        if (!Directory.Exists(lucenePath) || Directory.GetFiles(lucenePath).Length < 1)
        {
            XTrace.WriteLine("索引库不存在，开始自动创建Lucene索引库...");
            LuceneHelper.CreateLuceneIndexs();
            XTrace.WriteLine("索引库创建完成！");
        }
    }

}
