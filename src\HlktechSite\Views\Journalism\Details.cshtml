﻿@{
    Html.AppendCssFileParts("~/css/solutionDetails.css");
    Html.AppendCssFileParts("~/css/iconfont/iconfont.css");

    var Article = Model.Model as Article;
    var Titles = ViewBag.Titles as String;

    Html.AppendTitleParts(Article?.Name + DG.Setting.Current.PageTitleSeparator + Titles + DG.Setting.Current.PageTitleSeparator + "Hi_Link");

    var MIds = ViewBag.MId as String;

    var list = new List<Goods>();
}
<style asp-location="true">
    .solution-image-text img {
    max-width: 100%;
    display: block;
    margin: 0 auto;
    }

    .title1 {
    color: #333 !important;
    background-color: #fff;
    }

    .title1:hover {
    background-color: #eee !important;
    }


    .title3 {
    color: #333 !important;
    background-color: #fff;
    }

    .title3:hover {
    background-color: #eee !important;
    }

    .title9 {
    color: #fff !important;
    background-color: #337ab7;
    }

    .title9:hover {
    background-color: #337ab7 !important;
    }

    .solution-con > h2, .solution-con > p {
    font-weight: normal;
    }

    .solution-con > h2 > span:last-child {
    color: #727272;
    text-align: right;
    font-weight: 400;
    flex: none;
    line-height: 26px;
    font-size: 18px;
    margin-left: 20px;
    }

    .bdshare-button-style0-24 a:hover {
    text-decoration: none !important;
    }

    .bdshare-button-style0-24 a, .bdshare-button-style0-24 .bds_more {
    transform: scaleX(0.8) scaleY(0.8) !important;
    margin: 6px 1px 6px 0 !important;
    }

    /* 产品推荐 --常用公共模块 */
    .productRecomment {
        padding: .2vw 0px 1.5vw 0px;
        /* border: 2px solid red; */
    }

    ._title {
        border-top: 1px solid #ccc;
        padding: 30px 0px 15px 0px;
        font-size: 20px !important;
    }

    .mainBox2_container {
        padding: .5vw 0px;
        display: flex;
        justify-content: left;
    }

    .mainBox2_content:not(:first-child) {
        margin-left: 20px;
    }

    .mainBox2_content {
        position: relative;
        display: flex;
        justify-content: space-evenly;
        max-width: 20%;
        min-height: 250px;
        flex-direction: column;
        background-color: white;
        padding: 0.4vw;
        border-radius: 5px;
        border: 1px solid rgb(235, 234, 234);
        cursor: pointer;
        box-shadow: 1px 6px 19px 2px rgba(177,174,174,0.2);
    }

    ._desc1 {
        margin-top: 5px;
        word-break: break-all;
        color: #666666;
        font-size: 14px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        /* border: 1px solid ; */
    }

    ._desc2 {
        margin-top: 5px;
        color: #F56C6C !important;
        font-size: 12px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    /* 产品推荐 --常用公共模块 -结束 */
</style>

<div class="solution-top">
    <img src="@(CDN.GetCDN())/images/journalism.png" />
    <div>
        <h2>@T("新闻资讯")</h2>
        <P>@T("热点/痛点/观点 连点成线，物联大事件脉络尽在掌握")</P>
    </div>
</div>

<div class="solution-middle">
    <div class="solution-con">
        <h2>
            <span>@Model.Model.Name</span>
            <span>@Model.Model.CreateTime</span>
        </h2>
        <div class="bdsharebuttonbox bdshare-button-style0-24" style=" margin-top: 20px;" data-bd-bind="1597627930250">
            <a style="background:none;color:#333;padding-left:0px;">@T("分享到"):</a>
            <a href="#" class="bds_more" data-cmd="more"></a>
            <a href="#" class="bds_qzone" data-cmd="qzone" title="@T("分享到QQ空间")"></a>
            <a href="#" class="bds_tsina" data-cmd="tsina" title="@T("分享到新浪微博")"></a>
            <a href="#" class="bds_tqq" data-cmd="tqq" title="@T("分享到腾讯微博")"></a>
            <a href="#" class="bds_renren" data-cmd="renren" title="@T("分享到人人网")"></a>
            <a href="#" class="bds_weixin" data-cmd="weixin" title="@T("分享到微信")"></a>
            <a href="#" class="bds_douban" data-cmd="douban" title="@T("分享到豆瓣网")"></a>
        </div>
        <div class="solution-image-text">
            @*<p>asdasdasdalskhd ahjkshd kajhs </p>*@
            @Html.Raw(Model.Model.Content)
        </div>

        <div class="productRecomment">
            <div class="_title">
                @T("产品推荐")
            </div>
            <div class="mainBox2_container">
                @{
                    @foreach (var item in Model.GoodsList as IEnumerable<Goods>)
                    {
                        <div class="mainBox2_content">
                            <a href="@Url.DGAction("Details","Product",new {Id =item.Id})" target="_blank">
                                <div style="max-height: 170px;">
                                    <img src="@(CDN.GetCDN())@item.Image" style="width: 100%;height: 100%;object-fit: fill;">
                                </div>
                                <div class="_desc1">@T(@item.Name)</div>
                                <div class="_desc2">
                                    @T(item.AdvWord)
                                </div>
                            </a>
                        </div>
                    }                                                                         
                 // for(int i=0;i<5;i++){
                // <div class="mainBox2_content">
                //     <div style="max-height: 170px;">
                //         <img src="https://www.hlktech.com/Uploads/AlbumPic/xmy53gc8zzaeq.png" style="width: 100%;height: 100%;object-fit: fill;">
                //     </div>
                //     <div class="_desc1">@T("海凌科人体存在传感器LD2410B毫米波")</div>
                //     <div class="_desc2">
                //         @T("雷达感应模块支持光敏带蓝牙")
                //     </div>
                // </div>
                //}
            }
        </div>
    </div>
        <p>
            @if (Model.previous != null)
            {
                <span>@T("上一篇")：<a href="@Url.DGAction("Details","Journalism",new {Id =Model.previous.Id})">@Model.previous.Name</a></span>
            }
            else
            {
                <span>@T("上一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
            }
            @if (Model.Nex != null)
            {
                <span>@T("下一篇")：<a href="@Url.DGAction("Details","Journalism",new {Id =Model.Nex.Id})" title="@Model.Nex.Name">@Model.Nex.Name</a></span>
            }
            else
            {
                <span>@T("下一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
            }
        </p>
        @if (!MIds.IsNullOrWhiteSpace())
        {
            <p>
                <span>
                    @T("关联产品")：
                    @foreach (var item in MIds.SplitAsInt(","))
                    {
                        var m = ProductModel.FindById(item);
                        var g = Goods.FindByMId(item);

                        foreach (var row in g)
                        {
                            if (list.Contains(row)) continue;
                            list.Add(row);

                            @:&nbsp;&nbsp;&nbsp;&nbsp;<a href="@Url.DGAction("Details", "Product", new { Id = row.Id })">@m?.Name</a>
                        }
                    }
                </span>
            </p>
        }
    </div>
</div>

<script type="text/javascript" asp-location="Footer">
    window._bd_share_config = {
        "common": {
            "bdSnsKey": {},
            "bdText": "",
            "bdMini": "2",
            "bdPic": "",
            "bdStyle": "0",
            "bdSize": "24"
        },
        "share": {}
    };
    with (document)
    0[(getElementsByTagName('head')[0] || body)
        .appendChild(createElement('script')).src = '/static/api/js/share.js?v=89860593.js?cdnversion='
        + ~(-new Date() / 36e5)];
</script>