using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DG.Entity;
using DH.SearchEngine;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;

namespace HlktechSite.Entity
{
    /// <summary>文章分类</summary>
    public partial class ArticleCategory : CubeEntityBase<ArticleCategory>
    {
        #region 对象操作
        static ArticleCategory()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            //var df = Meta.Factory.AdditionalFields;
            //df.Add(nameof(ParentId));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        [EditorBrowsable(EditorBrowsableState.Never)]
        protected override void InitData()
        {
            // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
            if (Meta.Session.Count > 0) return;

            if (XTrace.Debug) XTrace.WriteLine("开始初始化ArticleCategory[文章分类]数据……");

            var entity = new ArticleCategory();
            entity.Name = "公司新闻";
            entity.ParentId = 0;
            entity.ParentIdList = "";
            entity.Level = 0;
            entity.DisplayOrder = 1;
            //entity.CreateUser = "abc";
            //entity.CreateUserID = 0;
            //entity.CreateTime = DateTime.Now;
            //entity.CreateIP = "abc";
            //entity.UpdateUser = "abc";
            //entity.UpdateUserID = 0;
            //entity.UpdateTime = DateTime.Now;
            //entity.UpdateIP = "abc";
            entity.Insert();

            var entity1 = new ArticleCategory();
            entity1.Name = "行业新闻";
            entity1.ParentId = 0;
            entity1.ParentIdList = "";
            entity1.Level = 0;
            entity1.DisplayOrder = 2;
            //entity.CreateUser = "abc";
            //entity.CreateUserID = 0;
            //entity.CreateTime = DateTime.Now;
            //entity.CreateIP = "abc";
            //entity.UpdateUser = "abc";
            //entity.UpdateUserID = 0;
            //entity.UpdateTime = DateTime.Now;
            //entity.UpdateIP = "abc";
            entity1.Insert();

            if (XTrace.Debug) XTrace.WriteLine("完成初始化ArticleCategory[文章分类]数据！");
        }

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性

        /// <summary>
        ///是否存在子集
        /// </summary>
        [XmlIgnore, ScriptIgnore]
        public bool subset { get; set; } = false;

        /// <summary>
        /// 获取文章所属所有分类
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public IList<ArticleCategory> ParentList => Extends.Get(nameof(ParentList), k => ArticleCategory.GetParentList(Id));
        #endregion

        #region 扩展查询
        /// <summary>根据编号查找</summary>
        /// <param name="id">编号</param>
        /// <returns>实体对象</returns>
        public static ArticleCategory FindById(Int32 id)
        {
            if (id <= 0) return null;

            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

            // 单对象缓存
            return Meta.SingleCache[id];

            //return Find(_.Id == id);
        }

        /// <summary>根据所属父级Id查找</summary>
        /// <param name="parentId">所属父级Id</param>
        /// <returns>实体列表</returns>
        public static IList<ArticleCategory> FindAllByParentId(Int32 parentId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ParentId == parentId);

            return FindAll(_.ParentId == parentId);
        }

        /// <summary>根据编号、当前层级查找</summary>
        /// <param name="id">编号</param>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IList<ArticleCategory> FindAllByIdAndLevel(Int32 id, Int32 level)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Id == id && e.Level == level);

            return FindAll(_.Id == id & _.Level == level);
        }

        /// <summary>查找多级数据</summary>
        /// <param name="Id">分类Id</param>
        /// <returns>实体对象</returns>
        public static IList<ArticleCategory> GetParentList(Int32 Id)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                var model = Meta.Cache.Find(e => e.Id == Id);
                if (model == null) return new List<ArticleCategory>();

                var list = new List<ArticleCategory>();
                foreach (var item in model.ParentIdList.SplitAsInt(","))
                {
                    list.Add(Meta.Cache.Find(e => e.Id == item));
                }

                return list;
            }
            else
            {
                var model = FindById(Id);
                if (model == null) return new List<ArticleCategory>();

                var list = new List<ArticleCategory>();
                foreach (var item in model.ParentIdList.SplitAsInt(","))
                {
                    list.Add(Find(_.Id == item));
                }

                return list;
            }
        }

        /// <summary>
        /// 查询所有
        /// </summary>
        /// <returns></returns>
        public static IList<ArticleCategory> GetAll()
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return FindAllWithCache();

            // 单对象缓存
            return FindAll();
        }

        /// <summary>
        /// 根据ID集合删除数据
        /// </summary>
        /// <param name="Ids">ID集合</param>
        public static void DelByIds(String Ids)
        {
            //var list = FindByIds(Ids);
            //if (list.Delete() > 0)
            if (Delete(_.Id.In(Ids.Trim(','))) > 0)
                Meta.Cache.Clear("");
        }

        /// <summary>根据当前层级查找</summary>
        /// <param name="level">当前层级</param>
        /// <returns>实体列表</returns>
        public static IList<ArticleCategory> FindAllByLevel(Int32 level)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Level == level);

            return FindAll(_.Level == level);
        }

        /// <summary>
        /// 根据列表Ids获取列表
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static IList<ArticleCategory> FindByIds(String ids)
        {
            if (ids.IsNullOrWhiteSpace()) return new List<ArticleCategory>();

            ids = ids.Trim(',');

            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => ids.SplitAsInt(",").Contains(x.Id));
            }

            return FindAll(_.Id.In(ids.Split(',')));
        }

        /// <summary>根据名称查找</summary>
        /// <param name="name">设备DeviceName</param>
        /// <returns>实体对象</returns>
        public static ArticleCategory FindByName(String name)
        {
            if (name.IsNullOrWhiteSpace()) return null;

            if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

            return Find(_.Name == name);
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="parentId">所属父级Id</param>
        /// <param name="level">当前层级</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<ArticleCategory> Search(Int32 parentId, Int32 level, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (parentId >= 0) exp &= _.ParentId == parentId;
            if (level >= 0) exp &= _.Level == level;
            if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.ParentIdList.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        // Select Count(Id) as Id,Category From ArticleCategory Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
        //static readonly FieldCache<ArticleCategory> _CategoryCache = new FieldCache<ArticleCategory>(nameof(Category))
        //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        //};

        ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        ///// <returns></returns>
        //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
        #endregion

        #region 业务操作
        #endregion
    }
}