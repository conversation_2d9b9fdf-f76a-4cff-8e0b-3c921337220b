﻿@model User
@{
    var userDetail = UserDetail.FindById(Model.ID);
    if (userDetail == null)
    {
        userDetail = new UserDetail();
        userDetail.Id = Model.ID;
        userDetail.CountryId = 1;
        userDetail.Save();
    }
    else
    {
        if (userDetail.CountryId == 0)
        {
            userDetail.CountryId = 1;
            userDetail.Save();
        }
    }
    var RolesInfo = userDetail.RoleExIds.SafeString().Split(',');
}
<style asp-location="true">
    .page {
        min-height: 415px
    }
</style>
<div class="page">
    <form id="user_form" enctype="multipart/form-data" method="post">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required"><label>@T("自动生成固件"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="AutoGuJian_1" class="cb-enable @(RolesInfo.Contains("AutoGuJian") ? "selected" : "")"><span>@T("是")</span></label>
                        <label for="AutoGuJian_2" class="cb-disable @(!RolesInfo.Contains("AutoGuJian") ? "selected" : "")"><span>@T("否")</span></label>
                        <input id="AutoGuJian_1" name="AutoGuJian" value="1" type="radio" @(RolesInfo.Contains("AutoGuJian") ? "checked=\"checked\"" : "")>
                        <input id="AutoGuJian_2" name="AutoGuJian" @(!RolesInfo.Contains("AutoGuJian") ? "checked=\"checked\"" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips">该权限用于前台会员是否可以申请自动生成固件</td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("固件Logo"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="GuJianLogo_1" class="cb-enable @(RolesInfo.Contains("GuJianLogo") ? "selected" : "")"><span>@T("是")</span></label>
                        <label for="GuJianLogo_2" class="cb-disable @(!RolesInfo.Contains("GuJianLogo") ? "selected" : "")"><span>@T("否")</span></label>
                        <input id="GuJianLogo_1" name="GuJianLogo" value="1" type="radio" @(RolesInfo.Contains("GuJianLogo") ? "checked=\"checked\"" : "")>
                        <input id="GuJianLogo_2" name="GuJianLogo" @(!RolesInfo.Contains("GuJianLogo") ? "checked=\"checked\"" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips">该权限用于前台会员自动生成固件时是否允许定制上传Logo</td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>