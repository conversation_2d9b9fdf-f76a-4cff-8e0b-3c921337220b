﻿@{
}
@model HlktechSite.Entity.Agent

<style asp-location="true">
    .page {
        min-height: 415px
    }
    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #419DFD !important;
    }

        .layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #419DFD !important;
        }

    .layui-tab-content {
        padding: 0px !important;
    }
</style>
<div class="page">
    <form id="navigation_form" method="post">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><label class="validation" for="nav_title">公司名称</label></td>
                    <td><input type="text" name="Name" id="Name" value="@Model.CompanyName" class="w200" /></td>
                </tr>
                <tr class="noborder">
                    <td>联系人</td>
                    <td><input type="text" name="ContactPerson" id="ContactPerson" value="@Model.ContactPerson" class="w200" /></td>
                </tr>
                <tr class="noborder">
                    <td>联系电话人</td>
                    <td><input type="text" name="Phone" id="Phone" value="@Model.Phone" class="w200" /></td>
                </tr>
                <tr class="noborder">
                    <td>邮箱</td>
                    <td><input type="text" name="Email" id="Email" value="@Model.Email" class="w200" /></td>
                </tr>
                <tr class="noborder">
                    <td>联系地址</td>
                    <td><textarea  name="ContactAddress">@Model.ContactAddress</textarea></td>
                </tr>
                <tr class="noborder">
                    <td>申请说明</td>
                    <td><textarea  name="Summary">@Model.Summary</textarea></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">处理</td>
                    <td>
                        <div class="onoff">
                            <label for="sms_login_show1" class="cb-enable   @(Model.IsThrough?"selected":"")">代理商</label>
                            <label for="sms_login_show0" class="cb-disable  @(!Model.IsThrough?"selected":"")">未处理</label>
                            <input id="sms_login_show1" name="Status" value="1" type="radio" @(Model.IsThrough ? "checked" : "")>
                            <input id="sms_login_show0" name="Status" value="0" type="radio" @(!Model.IsThrough ? "checked" : "")>
                        </div>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <input type="hidden" name="Id" value="@Model.Id" />
                    <td colspan="15"><input class="btn" type="submit" value="提交" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript" asp-location="Footer">
    $(function () {
        $('#navigation_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                nav_title: {
                    required: true
                },
                nav_url: {
                    url: true
                },
                nav_sort: {
                    number: true,
                    range: [0, 255]
                }
            },
            messages: {
                nav_title: {
                    required: '必填'
                },
                nav_url: {
                    url: '请输入有效的链接'
                },
                nav_sort: {
                    number: '请输入数字',
                    range: '请输入0-255之间的数'
                }
            }
        });

    });

</script>