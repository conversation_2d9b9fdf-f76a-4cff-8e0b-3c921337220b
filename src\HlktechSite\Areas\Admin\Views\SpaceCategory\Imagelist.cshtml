﻿@{
}
<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>空间管理</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>相册列表</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("CreateAlbumCategory")','新增相册')"><span>新增相册分类</span></a></li>
                <li><a href="@Url.Action("Imagelist")" class="current"><span>图片列表</span></a></li>
                @*<li><a href="/index.php/admin/Goodsalbum/watermark.html"><span>水印设置</span></a></li>*@
            </ul>
        </div>
    </div>

    <div class="upload-con" id="uploader" style="display: none;">
        <form method="post" action="" id="fileupload" enctype="multipart/form-data">
            <div class="upload-con-div">
                选择文件：
                <div class="dssc-upload-btn">
                    <a href="javascript:void(0);">
                        <span>
                            <input type="file" hidefocus="true" size="1" class="input-file" name="file" multiple="multiple" />
                        </span>
                        <p><i class="iconfont">&#xe733;</i>上传图片</p>
                    </a>
                </div>
            </div>
            <div dstype="file_msg"></div>
            <div class="upload-pmgressbar" dstype="file_loading"></div>
            <div class="upload-txt"><span>支持Jpg、Gif、Png格式，大小不超过1024KB的图片上传；浏览文件时可以按住ctrl或shift键多选。</span> </div>
        </form>
    </div>
    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>请输入ID或完整名称</dt>
                <dd><input class="txt" name="keyword" id="keyword" value="@Model.key" type="text"></dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn " title="查询">查询</a>
                <a href="/index.php/admin/Goodsalbum/pic_list.html?" class="btn btn-default" title="取消">取消</a>
            </div>
        </div>
    </form>

    <div class="ds-search-form">
        <th>批量处理</th>
        <td>
            <a href="JavaScript:void(0);" class="dssc-btn-mini" onClick="checkAll()"><i class="iconfont">&#xe64d;</i>全选</a>
            <a href="JavaScript:void(0);" class="dssc-btn-mini" onClick="uncheckAll()"><i class="iconfont">&#xe64d;</i>取消</a>
            <a href="JavaScript:void(0);" class="dssc-btn-mini" onClick="switchAll()"><i class="iconfont">&#xe762;</i>反选</a>
            <a href="JavaScript:void(0);" class="dssc-btn-mini" onClick="submit_form('del')"><i class="iconfont">&#xe725;</i>删除</a>
            @*<a href="JavaScript:void(0);" class="dssc-btn-mini" onClick="submit_form('watermark')"><i class="iconfont">&#xe71f;</i>添加水印</a>*@
            <dd id="batchClass" style=" display:none;">
                <span>
                    只有一个相册，赶快去                <a href="JavaScript:void(0);" uri="/index.php/admin/Selleralbum/album_add.html" ds_type="dialog" dialog_title="创建相册">创建相册</a>
                    吧！
                </span>
            </dd>
        </td>
    </div>

    <div class="dssc-picture-list">
        <form name="checkboxform" id="checkboxform" method="POST" action="">
            <div class="alert alert-info"> <strong>注：在使用&lsquo;替换上传&rsquo;功能时，请保持图片的扩展名与被替换图片相同。</strong> </div>
            <ul>
                <!--<li>
                    <dl>
                        <dt>
                            <input class="checkitem checkbox" type="checkbox" name="id[]" value="99">
                            <div class="picture"><a data-lightbox="lightbox-image" data-title="0,上传于:2017-09-29" href="http://b2c1.gicisky.net/uploads/home/<USER>/goods/1_2017092901284880537.jpg"> <img id="img_99" src="http://b2c1.gicisky.net/uploads/home/<USER>/goods/1_2017092901284880537.jpg"></a></div>
                            <input id="99" class="editInput1" readonly onDblClick="$(this).unbind();_focus($(this));" value="1_2017092901284880537.jpg" title="双击名称可以进行编辑" style="cursor:pointer;">
                            <span onDblClick="_focus($(this).prev());" title="双击名称可以进行编辑"><i class="iconfont">&#xe731;</i></span>
                        </dt>
                        <dd class="date">
                            <p>上传时间：2017-09-29</p>
                            <p>原图尺寸：430x430</p>
                        </dd>
                        <dd class="buttons">
                            <div class="upload-btn">
                                <a href="javascript:void(0);">
                                    <span>
                                        <input type="file" name="file_99" id="file_99" class="input-file" size="1" hidefocus="true" dstype="replace_image" />
                                    </span>
                                    <div class="upload-button"><i class="iconfont">&#xe733;</i>替换上传</div>
                                    <input id="submit_button" style="display:none" type="button" value="slide_image_upload" onClick="submit_form($(this))" />
                                </a>
                            </div>-->
                            <!--<a href="javascript:dsLayerOpen('/index.php/admin/Goodsalbum/album_pic_move.html?id=99','转移相册')"><i class="iconfont">&#xe9cf;</i>转移相册</a>-->
                            <!--<a href="javascript:void(0)" onclick='submit_delete(99)'><i class="iconfont">&#xe725;</i>删除图片</a>-->
                        <!--</dd>
                    </dl>
                </li>-->
                @foreach (var item in Model.list)
                {
                    <li>
                        <dl>
                            <dt>
                                <input class="checkitem checkbox" type="checkbox" name="ids" value="@item.Id">
                                <div class="picture"><a data-lightbox="lightbox-image" data-title="0,上传于:@item.CreateTime" href="@item.Cover"> <img id="<EMAIL>" src="@item.Cover"></a></div>
                                <input id="@item.Id" class="editInput1" readonly onDblClick="$(this).unbind();_focus($(this));" value="@item.Name" title="双击名称可以进行编辑" style="cursor:pointer;">
                                <span onDblClick="_focus($(this).prev());" title="双击名称可以进行编辑"><i class="iconfont">&#xe731;</i></span>
                            </dt>
                            <dd class="date">
                                <p>上传时间：@item.CreateTime.ToString("yyyy-MM-dd")</p>
                                <p>原图尺寸：@item.Spec</p>
                            </dd>
                            <dd class="buttons">
                                <div class="upload-btn">
                                    <a href="javascript:void(0);">
                                        <span>
                                            <input type="file" name="<EMAIL>" id="<EMAIL>" class="input-file" size="1" hidefocus="true" dstype="replace_image" />
                                        </span>
                                        <div class="upload-button"><i class="iconfont">&#xe733;</i>替换上传</div>
                                        <input id="submit_button" style="display:none" type="button" value="slide_image_upload" onClick="submit_form($(this))" />
                                    </a>
                                </div>
                                <!--<a href="javascript:dsLayerOpen('/index.php/admin/Goodsalbum/album_pic_move.html?id=99','转移相册')"><i class="iconfont">&#xe9cf;</i>转移相册</a>-->
                                <!--<a href="javascript:void(0)" onclick='submit_delete(99)'><i class="iconfont">&#xe725;</i>删除图片</a>-->
                            </dd>
                        </dl>
                    </li>
                }



            </ul>
        </form>
    </div>
    @*<ul class="pagination"><li class="disabled"><span>&laquo;</span></li> <li class="active"><span>1</span></li><li><a href="/index.php/admin/Goodsalbum/album_pic_list.html?page=2">2</a></li><li><a href="/index.php/admin/Goodsalbum/album_pic_list.html?page=3">3</a></li><li><a href="/index.php/admin/Goodsalbum/album_pic_list.html?page=4">4</a></li> <li><a href="/index.php/admin/Goodsalbum/album_pic_list.html?page=2">&raquo;</a></li></ul>*@
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>
<script  asp-location="Footer">
    jQuery.browser = {};
    (function () {
        jQuery.browser.msie = false;
        jQuery.browser.version = 0;
        if (navigator.userAgent.match(/MSIE ([0-9]+)./)) {
            jQuery.browser.msie = true;
            jQuery.browser.version = RegExp.$1;
        }
    })();
</script>
<script src="/static/plugins/jquery.poshytip.min.js"></script>
<link rel="stylesheet" href="/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>
<script src="/static/plugins/js/fileupload/jquery.iframe-transport.js"></script>
<script src="/static/plugins/js/fileupload/jquery.ui.widget.js"></script>
<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>



<script asp-location="Footer">
    $(function () {
        $('.tip').poshytip({
            className: 'tip-yellowsimple',
            alignTo: 'target',
            alignX: 'center',
            alignY: 'bottom',
            offsetX: 0,
            offsetY: 5,
            allowTipHover: false
        });


        // 替换图片
        $('input[dstype="replace_image"]').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: ADMINSITEURL + '/Goodsalbum/replace_image_upload.html?id=' + $(this).attr('id'),
                done: function (e, data) {
                    var param = data.result;
                    if (param.state == 'true') {
                        img_refresh(param.id);
                    } else {
                        alert(param.message);
                    }
                }
            });
        });

    });
    // 重新加载图片，替换上传使用
    function img_refresh(id) {
        $('#img_' + id).attr('src', $('#img_' + id).attr('src') + "?" + 100 * Math.random());
    }
    //控制图片名称input焦点可编辑
    function _focus(o) {
        var name;
        obj = o;
        name = obj.val();
        obj.removeAttr("readonly");
        obj.attr('class', 'editInput2');
        obj.select();
        obj.blur(function () {
            if (name != obj.val()) {
                _save(this);
            } else {
                obj.attr('class', 'editInput1');
                obj.attr('readonly', 'readonly');
            }
        });
    }
    function _save(obj) {
        $.post("@Url.Action("ModifyName")", { id: obj.id, name: obj.value }, function (data) {
            if (data.success) {
                layer.msg('操作成功');
            } else {
                layer.msg(data.msg);
            }
        });
        obj.className = "editInput1";
        obj.readOnly = true;
    }
    //    function submit_delete(ids_str) {
    //        _uri = ADMINSITEURL + "/Goodsalbum/del_album_pic.html?apic_id=" + ids_str;
    //        dsLayerConfirm(_uri, '您确定要删除吗?');
    //    }
    // 全选
    function checkAll() {
        $('#batchClass').hide();
        $('input[type="checkbox"]').each(function () {
            $(this).prop('checked', true);
        });
    }
    // 取消
    function uncheckAll() {
        $('#batchClass').hide();
        $('input[type="checkbox"]').each(function () {
            $(this).prop('checked', false);
        });
    }
    // 反选
    function switchAll() {
        $('#batchClass').hide();
        $('input[type="checkbox"]').each(function () {
            $(this).prop('checked', !$(this).prop('checked'));
        });
    }
    function submit_form(type) {
        if (type != 'move') {
            $('#batchClass').hide();
        }
        var id = '';
        $('input[type=checkbox]:checked').each(function () {
            if (!isNaN($(this).val())) {
                id += $(this).val() + ',';
            }
        });
        if (id == '') {
            layer.msg('请选择图片');
            return false;
        }
        if (type == 'del') {
            if (!confirm('您确定进行该操作吗?\n注意：使用中的图片也将被删除')) {
                return false;
            }
        }
        if (type == 'move') {
            $('#checkboxform').append('<input type="hidden" name="cid" value="' + $('#cid').val() + '" />');
        }
        $('#checkboxform').attr('action','@Url.Action("DeletePic")');
        $('#checkboxform').submit();
    }
</script>