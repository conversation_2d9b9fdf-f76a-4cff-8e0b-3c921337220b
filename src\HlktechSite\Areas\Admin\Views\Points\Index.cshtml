﻿@{
    var SiteSettings = DH.Entity.SiteSettingInfo.SiteSettings;
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("积分管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("积分规则")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="post" name="settingForm" id="settingForm">
        <table class="ds-default-table">
            <thead>
                <tr class="space">
                    <th colspan="16">@T("积分规则如下"):</th>
                </tr>
                <tr class="thead">
                    <th>@T("项目")</th>
                    <th>@T("赠送积分")</th>
                </tr>
            </thead>
            <tbody>
                <tr class="hover">
                    <td class="w200">@T("会员注册")</td>
                    <td><input id="points_reg" name="points_reg" value="@SiteSettings.PointsReg" class="txt" type="text" style="width:60px;"></td>
                </tr>
                <tr class="hover">
                    <td>@T("会员每天登录")</td>
                    <td><input id="points_login" name="points_login" value="@SiteSettings.PointsLogin" class="txt" type="text" style="width:60px;"></td>
                </tr>
                <tr class="hover">
                    <td>@T("会员每天签到")</td>
                    <td><input id="points_signin" name="points_signin" value="@SiteSettings.PointsSignin" class="txt" type="text" style="width:60px;"></td>
                </tr>
                <tr class="hover">
                    <td>@T("订单商品评论")</td>
                    <td><input id="points_comments" name="points_comments" value="@SiteSettings.PointsComments" class="txt" type="text" style="width:60px;"></td>
                </tr>

                <tr class="hover">
                    <td>@T("邀请注册")</td>
                    <td><input id="points_invite" name="points_invite" value="@SiteSettings.PointsInvite" class="txt" type="text" style="width:60px;">@T("邀请非会员注册时给邀请人的积分数")                </td>
                </tr>
                <tr class="hover">
                    <td>@T("返利比例")</td>
                    <td><input id="points_rebate" name="points_rebate" value="@SiteSettings.PointsRebate" class="txt" type="text" style="width:35px;">% &nbsp;&nbsp;&nbsp;@T("被邀请会员购买商品时给邀请人返的积分数（例如设为5%，被邀请人购买100元商品，返给邀请人5积分）")                </td>
                </tr>
            </tbody>
        </table>
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th colspan="2">@T("购物并付款")</th>
                </tr>
            </thead>
            <tbody>
                <tr class="hover">
                    <td class="w200">@T("消费额与赠送积分比例")</td>
                    <td>
                        <input id="points_orderrate" name="points_orderrate" value="@SiteSettings.PointsOrderrate" class="txt" type="text" style="width:60px;">
                        @T("例:设置为10，表明消费10单位货币赠送1积分,建议设置0-100之间")
                    </td>
                </tr>
                <tr class="hover">
                    <td>@T("每订单最多赠送积分")</td>
                    <td>
                        <input id="points_ordermax" name="points_ordermax" value="@SiteSettings.PointsOrdermax" class="txt" type="text" style="width:60px;">
                        @T("例:设置为100，表明每订单赠送积分最多为100积分")
                    </td>
                </tr>


            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="2"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>

<script asp-location="Footer">

    $(document).ready(function () {
        $("#settingForm").validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                points_reg: {
                    number: true,
                    min: 0
                },
                points_login: {
                    number: true,
                    min: 0
                },
                points_comments: {
                    number: true,
                    min: 0
                },
                points_signin: {
                    number: true,
                    min: 0
                },
                points_invite: {
                    number: true,
                    min: 0
                },
                points_rebate: {
                    number: true,
                    min: 0
                },
                points_orderrate: {
                    number: true,
                    min: 0
                },
                points_ordermax: {
                    number: true,
                    min: 0
                }
            },
            messages: {
                points_reg: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                },
                points_login: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                },
                points_comments: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                },
                points_signin: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                },
                points_invite: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                },
                points_rebate: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                },
                points_orderrate: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                },
                points_ordermax: {
                    number: '请输入有效的数字',
                    min: '值不能小于0'
                }
            }
        });
    });
</script>