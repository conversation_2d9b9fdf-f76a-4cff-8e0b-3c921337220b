﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>下载文件</summary>
public partial class DownloadModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>下载文件名称</summary>
    public String? Name { get; set; }

    /// <summary>开发资料 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    public String? Development { get; set; }

    /// <summary>应用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    public String? Application { get; set; }

    /// <summary>通用软件 格式：名称|地址|下载次数_名称|地址|下载次数</summary>
    public String? GeneralSoftware { get; set; }

    /// <summary>产品型号Id，以逗号分隔开</summary>
    public String? MIds { get; set; }

    /// <summary>下载分类Id</summary>
    public Int32 DId { get; set; }

    /// <summary>点击数</summary>
    public Int32 Clicks { get; set; }

    /// <summary>排序</summary>
    public Int16 DisplayOrder { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDownload model)
    {
        Id = model.Id;
        Name = model.Name;
        Development = model.Development;
        Application = model.Application;
        GeneralSoftware = model.GeneralSoftware;
        MIds = model.MIds;
        DId = model.DId;
        Clicks = model.Clicks;
        DisplayOrder = model.DisplayOrder;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
