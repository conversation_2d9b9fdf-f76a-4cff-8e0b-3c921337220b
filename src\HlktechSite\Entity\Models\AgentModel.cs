﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>代理商申请</summary>
public partial class AgentModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>公司名称</summary>
    public String? CompanyName { get; set; }

    /// <summary>联系人</summary>
    public String? ContactPerson { get; set; }

    /// <summary>联系电话</summary>
    public String? Phone { get; set; }

    /// <summary>邮箱</summary>
    public String? Email { get; set; }

    /// <summary>联系地址</summary>
    public String? ContactAddress { get; set; }

    /// <summary>申请说明</summary>
    public String? Summary { get; set; }

    /// <summary>是否通过</summary>
    public Boolean IsThrough { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAgent model)
    {
        Id = model.Id;
        CompanyName = model.CompanyName;
        ContactPerson = model.ContactPerson;
        Phone = model.Phone;
        Email = model.Email;
        ContactAddress = model.ContactAddress;
        Summary = model.Summary;
        IsThrough = model.IsThrough;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
