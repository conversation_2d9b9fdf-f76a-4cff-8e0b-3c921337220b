.info-block {
	padding: 20px 0;
}
.basic-info {
}
.info-module-title {
	font-size: 16px;
	color: #00aaff;
}
.user-info-table {
	font-size: 12px;
}
.user-info-table tr {
	clear: both;
}
.user-info-table td {
	padding: 11px 0;
	padding-left: 12px;
	line-height: 30px;
}
.user-info-table input {
	padding-left: 10px;
	width: 270px;
}
.user-info-table input.aligning-input {
	width: 308px;
}
.info-item-name {
	width: 160px;
	text-align: right;
	color: #666;
}
/* 用于tab之下的input */
.tab-sub-input {
	width: 177px;
}
.necessary-mark {
	color: red;
}
.btn-reverse.company-verify {
	padding: 0 14px;
	margin-left: 12px;
	font-size: 12px;
}
.change-setting {
	margin-left: 15px;
}
.user-label {
	vertical-align: sub;
}
.save-button-outer {
	padding-left: 153px;
}
.account-block-title {
	margin: 36px 0;
	font-size: 14px;
}
.industry-tabs .ny-btn {
	min-width: 112px;
}
.account-state {
	display: inline-block;
	line-height: inherit;
	background: no-repeat left center;
	padding-left: 32px;
}
.account-verified {
	background-image: url(img/user_icon_verified.png);
}
.account-unverified {
	background-image: url(img/user_icon_unverified.png);
}
/* 安全设置 */
.safety-info-detail {
	width: 100%;
	height: 128px;
	border: 1px solid #eee;
	padding-left: 128px;
	padding-top: 32px;
	background: #f7f9fa url(img/user_avatar_large.png) no-repeat 24px center;
	color: #333;
	font-size: 12px;
}
.safety-info-detail > div {
	margin-top: 10px;
}
.safety-info-detail > div:first-child {
	margin-top: 0;
}
.safety-level-container {
}
.info-detail-label {
	display: inline-block;
	width: 68px;
	color: #a0a2a3;
}
.go-verify {
	margin-left: 15px;
}
.safety-level {
	height: 20px;
	line-height: 20px;
}
.safety-level-bar {
	display: inline-block;
	margin-bottom: 0;
	width: 260px;
}
.safety-level-text {
	margin-left: 15px;
}
.safety-bar-high {
	background: #14cc39;
}
.safety-bar-mid {
	background: #ffa200;
}
.safety-bar-low {
	background: red;
}
.safety-level-high {
	color: #14cc39;
}
.safety-level-mid {
	color: #ffa200;
}
.safety-level-low {
	color: red;
}
.user-settings-container {
	padding: 20px;
}
.settings-tier {
	height: 80px;
	line-height: 79px;
	border-bottom: 1px dashed #e6e6e6;
	white-space: nowrap;
}
.last-tier {
	border-bottom: none;
}
.settings-detail {
	width: 50%;
}
.setting-operate {
}
.setting-name {
	display: inline-block;
	margin-left: 16px;
	font-size: 12px;
}
.setting-intro {
	display: inline-block;
	color: #999;
	margin-left: 5%;
}
.setting-status {
	display: inline-block;
	height: 24px;
	width: 24px;
	background: no-repeat left center;
	vertical-align: middle;
}
.setting-success {
	background-image: url(img/account_status_success.png);
}
.setting-undone {
	background-image: url(img/account_status_warn.png);
}
.setting-appeal {
	background-image: url(img/account_status_appeal.png);
}
.setting-operate {
	width: 80px;
	white-space: nowrap;
	text-align: center;
}
.setting-operate a {
	font-size: 12px;
}

/* 修改密码 */
.password-table {
	margin-bottom: 20px;
}
.password-table .ny-input-reset {
	width: 400px;
}
.ny-form .send-captcha,
.show-captcha {
	height: 32px;
	line-height: 32px;
	font-size: 12px;
	/* 垂直方向对齐 */
	vertical-align: bottom;
}
.selectProvince {
	width: 110px;
	height: 30px;
}
.selectCity {
	width: 156px;
	height: 30px;
}
.password-item {
	height: 24px;
	line-height: 24px;
	padding-left: 15px;
	background: url(img/icon_item_dot.png) no-repeat left center;
	font-size: 12px;
	color: #999;
}
.password-strength-area {
	margin-top: 3px;
}
.pwd-strength-box {
	position: relative;
	padding-top: 0;
	line-height: 16px;
}
.pwd-strength {
	display: block;
	margin-left: 10px;
	margin-top: 9px;
}
.pwd-strength li {
	float: left;
	width: 60px;
	height: 4px;
	line-height: 12px;
	font-size: 12px;
	color: #fff;
	background-color: #ccc;
	text-align: center;
}
.pwd-strength li + li {
	margin-left: 4px;
}
li.levelbar-weak {
	background-color: #f00;
}
li.levelbar-medium {
	background-color: #ffa033;
}
li.levelbar-strong {
	background-color: #00c918;
}
.show-pwd-level {
	position: absolute;
	left: 204px;
	bottom: 15px;
	height: 12px;
	line-height: 12px;
	font-size: 12px;
}
.pwd-null {
	color: #ccc;
}
.pwd-short {
	color: #ccc;
}
.pwd-weak {
	color: #f00;
}
.pwd-medium {
	color: #ffa033;
}
.pwd-strong {
	color: #00c918;
}

