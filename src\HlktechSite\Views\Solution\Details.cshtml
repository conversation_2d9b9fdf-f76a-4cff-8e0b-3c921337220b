﻿@{
    Html.AppendCssFileParts("~/css/solutionDetails.css");

    var Titles = ViewBag.Titles as String;
    var SolutionModel = Model.SolutionModel as Solution;

    Html.AppendTitleParts(SolutionModel.Name + DG.Setting.Current.PageTitleSeparator + Titles + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}
<style>
    .solution-image-text img {
        width: 100%
    }
</style>

<div class="solution-top">
    <img src="@(CDN.GetCDN())/images/solutionDetails.png" />
    <div>
        <h2>@T("智能解决方案")</h2>
        <P>
            @T("智能解决方案能够帮助工业企业迅速建立远程连接和管理能力") 
        </P>
    </div>
</div>



<div class="solution-middle">
    <div class="solution-con">
        <h2>
            <span>@Model.SolutionModel.Name</span>
            <span>@Model.SolutionModel.CreateTime</span>
        </h2>
        <div class="bdsharebuttonbox bdshare-button-style0-24" style=" margin-top: 20px;" data-bd-bind="1597627930250">
            <a style="background:none;color:#333;padding-left:0px;">@T("分享到"):</a>
            <a href="#" class="bds_more" data-cmd="more"></a>
            <a href="#" class="bds_qzone" data-cmd="qzone" title="@T("分享到QQ空间")"></a>
            <a href="#" class="bds_tsina" data-cmd="tsina" title="@T("分享到新浪微博")"></a>
            <a href="#" class="bds_tqq" data-cmd="tqq" title="@T("分享到腾讯微博")"></a>
            <a href="#" class="bds_renren" data-cmd="renren" title="@T("分享到人人网")"></a>
            <a href="#" class="bds_weixin" data-cmd="weixin" title="@T("分享到微信")"></a>
            <a href="#" class="bds_douban" data-cmd="douban" title="@T("分享到豆瓣网")"></a>
        </div>
        <div class="solution-image-text">
            @*<p>asdasdasdalskhd ahjkshd kajhs </p>*@
            @Html.Raw(Model.SolutionModel.Content)
        </div>
        <p>
            @if (Model.previous != null)
            {
            <span>@T("上一篇")：<a href="@Url.DGAction("Details",new { Id=Model.previous.Id})">@Model.previous.Name</a></span>
            }
            else
            {
        <span>@T("上一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
            }
            @if (Model.Nex != null)
            {
        <span>@T("下一篇")：<a href="@Url.DGAction("Details",new { Id=Model.Nex.Id})">@Model.Nex.Name</a></span>
            }
            else
            {
        <span>@T("下一篇")：<a href="@Url.DGAction("Index")">@T("返回列表")</a></span>
            }


        </p>
    </div>
</div>


<script type="text/javascript"  asp-location="Footer">
    window._bd_share_config = {
        "common": {
            "bdSnsKey": {},
            "bdText": "",
            "bdMini": "2",
            "bdMiniList": false,
            "bdPic": "",
            "bdStyle": "0",
            "bdSize": "24"
        },
        "share": {}
    };
    with (document)
    0[(getElementsByTagName('head')[0] || body)
        .appendChild(createElement('script')).src = '/static/api/js/share.js?v=89860593.js?cdnversion='
        + ~(-new Date() / 36e5)];
</script>