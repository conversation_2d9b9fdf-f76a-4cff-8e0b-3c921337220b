﻿
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>导航管理</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="/index.php/admin/Navigation/index.html" class="current"><span>管理</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("CreateNavigation")','新增导航')"><span>新增</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>标题</dt>
                <dd><input type="text" value="@Model.name" name="nav_title" class="txt"></dd>
            </dl>
            <dl>
                <dt>导航位置</dt>
                <dd>
                    <select name="Location">
                        <option value="">请选择...</option>
                        <!option value="header" @(Model.Location=="header"?"selected":"") >头部</!option>
                        <!option value="middle" @(Model.Location=="middle"?"selected":"") >中部</!option>
                        <!option value="footer" @(Model.Location=="footer"?"selected":"") >底部</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="搜索">
                <a href="/index.php/admin/Navigation/index.html" class="btn btn-default" title="取消">取消</a>
            </div>
        </div>
    </form>
    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>排序</th>
                <th>标题</th>
                <th>导航链接</th>
                <th>导航位置</th>
                <th>新窗口打开</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in ViewBag.list)
            {
                <tr>
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id" id="<EMAIL>"/></td>
                    <td>@item.Sort</td>
                    <td>@item.Name</td>
                    <td>@item</td>
                    <td>@(item.Location== "header"?"头部": item.Location== "middle" ? "中部" : "底部")</td>
                    <td>@(item.NewOpen?"是":"否")</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("UpdateNavigation",new { Id=item.Id})','@item.Name')" class="dsui-btn-edit"><i class="iconfont"></i>编辑</a>
                        @*<a href="javascript:submit_delete(@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>删除</a>*@
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }


        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">全选</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>删除</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>

<script type="text/javascript" asp-location="Footer">

    function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }

</script>