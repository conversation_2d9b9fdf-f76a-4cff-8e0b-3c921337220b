﻿namespace AIAddress;
public class AddressModel {
    //{"lat":23.166727,"detail":"岑村松岗小区1巷6号菜鸟驿站（广州岑村1巷6号店）","town":"长兴街道","phonenum":"18412847425","city_code":"440100","province":"广东省","person":"蓝芝欢","lng":113.392258,"province_code":"440000","text":"蓝芝欢[5968],18412847425,广东省 广州市 天河区 长兴街道岑村松岗小区1巷6号菜鸟驿站（广州岑村1巷6号店）","county":"天河区","city":"广州市","county_code":"440106","town_code":"440106017","log_id":1735904047845093260}

    public Double lat { get; set; }

    public String? detail { get; set; }

    public String? town { get; set; }

    public String? phonenum { get; set; }

    public String? city_code { get; set; }

    public String? province { get; set; }

    public String? person { get; set; }

    public Double lng { get; set; }

    public String? province_code { get; set; }

    public String? text { get; set; }

    public String? county { get; set; }

    public String? city { get; set; }

    public String? county_code { get; set; }

    public String? town_code { get; set; }

    public String? log_id { get; set; }
}
