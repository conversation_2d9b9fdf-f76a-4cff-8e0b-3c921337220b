﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>成品跳转表多语言信息存放表</summary>
public partial class JumpProductLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>成品Id</summary>
    public Int32 JId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>产品名称</summary>
    public String? Name { get; set; }

    /// <summary>推文链接</summary>
    public String? InfoUrl { get; set; }

    /// <summary>广告词</summary>
    public String? AdWord { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IJumpProductLan model)
    {
        Id = model.Id;
        JId = model.JId;
        LId = model.LId;
        Name = model.Name;
        InfoUrl = model.InfoUrl;
        AdWord = model.AdWord;
    }
    #endregion
}
