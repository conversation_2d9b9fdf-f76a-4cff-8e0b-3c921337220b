﻿@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("上传设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("默认图片")</span></a></li>
                <li><a href="@Url.Action("Upload_Type")" class="current"><span>@T("上传设置")</span></a></li>
                <li><a href="@Url.Action("Upload_Type1")"><span>@T("私有上传设置")</span></a></li>
                <li><a href="@Url.Action("CDNSetting")"><span>@T("CDN设置")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("UpdateUploadType", "UploadSetting", FormMethod.Post, new { name = "form1", enctype = "multipart/form-data" }))
    {
        <div class="ncap-form-default">
            <dl>
                <dt>@T("商品图片存储方式")</dt>
                <dd>
                    <label class="radio-label">
                        <i class="radio-common @(OssSetting.Current.UploadType == 0 ? "selected" : "")">
                            <input type="radio" value="local" name="upload_type" id="upload_type_local" @(OssSetting.Current.UploadType == 0 ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </i>
                        <span>@T("本地存储")</span>
                    </label>
                    <label class="radio-label">
                        <i class="radio-common @(OssSetting.Current.UploadType == 1 ? "selected" : "")">
                            <input type="radio" value="alioss" name="upload_type" id="upload_type_alioss" @(OssSetting.Current.UploadType == 1 ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </i>
                        <span>@T("阿里云OSS存储")</span>
                    </label>
                    <label class="radio-label">
                        <i class="radio-common @(OssSetting.Current.UploadType == 2 ? "selected" : "")">
                            <input type="radio" value="qiniuoss" name="upload_type" id="upload_type_qiniuoss" @(OssSetting.Current.UploadType == 2 ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </i>
                        <span>@T("七牛云OSS存储")</span>
                    </label>
                </dd>
            </dl>
            <div class="localdb">
                <dl>
                    <dt>@T("存储路径")</dt>
                    <dd>
                        <input id="uploadpath" name="uploadpath" value="@DH.DHSetting.Current.UploadPath" class="input-txt" type="text">@T("默认位于wwwroot/Uploads目录下")
                    </dd>
                </dl>
            </div>
            <div class="alioss">
                <dl>
                    <dt>@T("是否CNAME指向域名")</dt>
                    <dd>
                        <div class="onoff">
                            <label for="aliendpoint_type_show1" class="cb-enable @(OssSetting.Current.AliOSS.IsAliEndPoint ? "selected" : "")">@T("是")</label>
                            <label for="aliendpoint_type_show0" class="cb-disable @(!OssSetting.Current.AliOSS.IsAliEndPoint ? "selected" : "")">@T("否")</label>
                            <input id="aliendpoint_type_show1" name="aliendpoint_type" value="1" type="radio" @(OssSetting.Current.AliOSS.IsAliEndPoint ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="aliendpoint_type_show0" name="aliendpoint_type" value="0" type="radio" @(!OssSetting.Current.AliOSS.IsAliEndPoint ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt>@T("阿里云OssAccessKeyId")</dt>
                    <dd>
                        <input id="alioss_accessid" name="alioss_accessid" value="@OssSetting.Current.AliOSS.OssAccessKeyId" class="input-txt" type="text">
                        @*<a href="http://www.csdeshang.com/home/<USER>/article/id/203.html" target="_blank">配置文档</a>*@
                    </dd>
                </dl>
                <dl>
                    <dt>@T("阿里云OssSecretAccess")</dt>
                    <dd>
                        <input id="alioss_accesssecret" name="alioss_accesssecret" value="@OssSetting.Current.AliOSS.OssSecretAccess" class="input-txt" type="text">
                    </dd>
                </dl>
                <dl>
                    <dt>@T("阿里云OssBucket")</dt>
                    <dd>
                        <input id="alioss_bucket" name="alioss_bucket" value="@OssSetting.Current.AliOSS.OssBucket" class="input-txt" type="text">
                    </dd>
                </dl>
                <dl>
                    <dt>@T("阿里云OssEndpoint")</dt>
                    <dd>
                        <input id="alioss_endpoint" name="alioss_endpoint" value="@OssSetting.Current.AliOSS.OssEndpoint" class="input-txt" type="text">
                    </dd>
                </dl>
            </div>
            <div class="qiniuoss">
                <dl>
                    <dt>@T("是否CNAME指向域名")</dt>
                    <dd>
                        <div class="onoff">
                            <label for="qiniuendpoint_type_show1" class="cb-enable @(OssSetting.Current.QiNiu.IsQiNiuEndPoint ? "selected" : "")">@T("是")</label>
                            <label for="qiniuendpoint_type_show0" class="cb-disable @(!OssSetting.Current.QiNiu.IsQiNiuEndPoint ? "selected" : "")">@T("否")</label>
                            <input id="qiniuendpoint_type_show1" name="qiniuendpoint_type" value="1" type="radio" @(OssSetting.Current.QiNiu.IsQiNiuEndPoint ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="qiniuendpoint_type_show0" name="qiniuendpoint_type" value="0" type="radio" @(!OssSetting.Current.QiNiu.IsQiNiuEndPoint ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt>@T("七牛云OssAccessKey")</dt>
                    <dd>
                        <input id="qiniuoss_accessid" name="qiniuoss_accessid" value="@OssSetting.Current.QiNiu.AccessKey" class="input-txt" type="text">
                    </dd>
                </dl>
                <dl>
                    <dt>@T("七牛云OssSecretKey")</dt>
                    <dd>
                        <input id="qiniuoss_accesssecret" name="qiniuoss_accesssecret" value="@OssSetting.Current.QiNiu.SecretKey" class="input-txt" type="text">
                    </dd>
                </dl>
                <dl>
                    <dt>@T("七牛云Oss存储空间块")</dt>
                    <dd>
                        <input id="qiniuoss_bucket" name="qiniuoss_bucket" value="@OssSetting.Current.QiNiu.Bucket" class="input-txt" type="text">
                    </dd>
                </dl>
                <dl>
                    <dt>@T("七牛云Oss存储区域")</dt>
                    <dd>
                        <input id="qiniuoss_bucket" name="qiniuoss_zone" value="@OssSetting.Current.QiNiu.Zone" class="input-txt" type="text">@T("&nbsp;&nbsp;华东、华北、华南、北美及东南亚")
                    </dd>
                </dl>
                <dl>
                    <dt>@T("七牛云Oss基本路径")</dt>
                    <dd>
                        <input id="qiniuoss_basepath" name="qiniuoss_basepath" value="@OssSetting.Current.QiNiu.BasePath" class="input-txt" type="text">
                    </dd>
                </dl>
                <dl>
                    <dt>@T("七牛云Oss绑定域名")</dt>
                    <dd>
                        <input id="qiniuoss_endpoint" name="qiniuoss_endpoint" value="@OssSetting.Current.QiNiu.Domain" class="input-txt" type="text">
                    </dd>
                </dl>
            </div>
            <dl>
                <dt></dt>
                <dd><input class="btn" type="submit" value="@T("提交")" /></dd>
            </dl>
        </div>
    }
</div>
<script asp-location="Footer">
    $(function () {
    @if (OssSetting.Current.UploadType == 0)
    {
        <text>
                if ($('#upload_type_local').prop('checked')) {
                $('.alioss').hide();
                $(".localdb").show();
                $('.qiniuoss').hide();
            }
        </text>
    }
    else if (OssSetting.Current.UploadType == 1)
    {
        <text>
                if ($('#upload_type_alioss').prop('checked')) {
                $('.alioss').show();
                $(".localdb").hide();
                $('.qiniuoss').hide();
            }
        </text>
    }
    else
    {
        <text>
                if ($('#upload_type_qiniuoss').prop('checked')) {
                $('.alioss').hide();
                $(".localdb").hide();
                $('.qiniuoss').show();
            }
        </text>
    }

            $('#upload_type_local').click(function () {
                $('.alioss').hide();
                $(".localdb").show();
                $('.qiniuoss').hide();
            });
        $('#upload_type_alioss').click(function () {
            $('.alioss').show();
            $(".localdb").hide();
            $('.qiniuoss').hide();
        })
        $('#upload_type_qiniuoss').click(function () {
            $('.alioss').hide();
            $(".localdb").hide();
            $('.qiniuoss').show();
        })
    });
</script>