﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";

    var Article = Model.Model as Article;
    var Titles = ViewBag.Titles as String;

    Html.AppendTitleParts(Article?.Name + DG.Setting.Current.PageTitleSeparator + Titles + DG.Setting.Current.PageTitleSeparator + "Hi_Link");

    var MIds = ViewBag.MId as String;

    var list = new List<Goods>();
}
<style>
    .journalism-detail-con img {
        max-width: 100%;
    }
</style>
<h2 class="journalism-h2">@Article.Name</h2>

<p class="journalism-msg">@T("时间")：@Article.CreateTime.ToString("yyyy-MM-dd") &nbsp;&nbsp;&nbsp;&nbsp; @("作者")：@Model.Model.CreateUser</p>

<div class="journalism-detail-con">
    @Html.Raw(Model.Model.Content)
</div>

<p class="flip-over">
    @if (Model.previous != null)
    {
        <span>@T("上一篇")：<a href="@Url.DGAction("Details","Journalism",new { Id=Model.previous.Id})">@Model.previous.Name</a></span>
    }
    else
    {
        <span>@T("上一篇")：<a href="@Url.DGAction("Index","Journalism")">@T("返回列表")</a></span>
    }
    @if (Model.Nex != null)
    {
        <span>@T("下一篇")：<a href="@Url.DGAction("Details","Journalism",new { Id=Model.Nex.Id})">@Model.Nex.Name</a></span>
    }
    else
    {
        <span>@T("下一篇")：<a href="@Url.DGAction("Index","Journalism")">@T("返回列表")</a></span>
    }
</p>
@if (!MIds.IsNullOrWhiteSpace())
{
    <p style="margin-left: 20px;">
        <span>关联产品：</span>
        @foreach (var item in MIds.SplitAsInt(","))
        {
            var m = ProductModel.FindById(item);
            var g = Goods.FindByMId(item);

            foreach (var row in g)
            {
                if (list.Contains(row)) continue;
                list.Add(row);

                <a href="@Url.DGAction("Details","Product",new {Id = row.Id})">@m.Name</a>
            }
        }
    </p>
}