﻿@{
    Html.AppendCssFileParts("~/css/Contact.css");


    Html.AppendTitleParts(T("联系我们").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}
<div class="top">
    <img src="@(CDN.GetCDN())/images/contact.png" />
    <div>
        <h2>@T("联系我们")</h2>
        <P>@T("创造并提供丰富的便民生活服务")</P>
    </div>
</div>

<div class="navigation">
    <div class="navigation-con">
        @await Component.InvokeAsync("Location", new { model = Model.Locations })
    </div>
</div>

<div class="con">
    <h2>@T("联系我们")</h2>


    @T("深圳市海凌科电子有限公司")
    @*<p>深圳市海凌科电子有限公司<i>(总部)</i></p>
        <div>
            <span>
                <b class="havebold">公司信息 ：</b>
                <b>地址：深圳龙华民治留仙大道24号彩悦大厦西大门三楼</b>
                <b>电话：0755-23152658；83575155</b>
                <b>传真：0755-83575189</b>
                <b>网址：www.hlktech.com</b>
                <b>邮箱：<EMAIL></b>
                <b>业务联系QQ: 2851395231 / 2851395234 / 2851395239</b>
            </span>
            <span>
                <b class="havebold">账户信息 :</b>
                <b>户名：深圳市海凌科电子有限公司</b>
                <b>开户行：中国光大银行深圳东海支行</b>
                <b>账号：3901 0188 0000 86065</b>
                <b class="havebold">找到我们 ：</b>
                <b>导航：彩悦大厦 西大门</b>
                <b>地铁：深圳北站东广场旁</b>
            </span>
        </div>*@


    @T("武汉极思灵创科技有限公司")
    @*<p>
            武汉极思灵创科技有限公司<i>(分部)</i>
        </p>
        <div>
            <span>
                <b class="havebold">公司信息 ：</b>
                <b>地址：湖北省武汉市洪山区街道口樱花大厦A座503</b>
                <b>电话：027-59761867</b>
                <b>微信：17786528309</b>
                <b>网址：http://www.gicisky.net</b>
                <b>邮箱：<EMAIL></b>
                <b>业务联系QQ: 2505111990</b>
            </span>
            <span>
                <b class="havebold">账户信息 :</b>
                <b>户名：武汉极思灵创科技有限公司</b>
                <b>开户行：中国农业银行股份有限公司武汉洪山支行</b>
                <b>账号：1703 8201 0400 20323</b>
                <b class="havebold">找到我们 ：</b>
                <b>导航：街道口 樱花大厦</b>
                <b>地铁：武汉市街道口地铁旁</b>
            </span>
        </div>*@





    @T("益坤泰实业（东莞）有限公司")

    @*<p>益坤泰实业（东莞）有限公司<i>(分部)</i></p>
        <div>
            <span>
                <b class="havebold">公司信息 ：</b>
                <b>地址：广东省东莞市东城街道狮长路3号2栋4楼</b>
                <b>电话：18898736510</b>
            </span>
            <span>
            </span>
        </div>*@
    @T("优秀代理商1")
    <div class="map-nav">
        <div class="btn-group">
            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                <b>@T("深圳市海凌科电子有限公司1")</b><span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <li><a href="javascript:;" data-type="1">@T("深圳市海凌科电子有限公司1")</a></li>
                <li><a href="javascript:;" data-type="2">@T("武汉极思灵创科技有限公司1")</a></li>
                <li><a href="javascript:;" data-type="3">@T("益坤泰实业有限公司1")</a></li>
            </ul>
        </div>
        <img src="@(CDN.GetCDN())/images/place.png" alt="Alternate Text" />
    </div>

    <div id="allmap" style="z-index:-1;position: relative;">

    </div>
    </div>
@* <script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=dPDoL4fF3ZGgxy8UBDpGU3684nWezyHi"></script>


    <script  asp-location="Footer">
        var map = new BMap.Map("allmap");  // 创建Map实例
        map.centerAndZoom(new BMap.Point(114.064415, 22.609933), 15);
        map.enableScrollWheelZoom();
        var marker = new BMap.Marker(new BMap.Point(114.064415, 22.609933));
        map.addOverlay(marker);//增加点
        var marker = new BMap.Marker(new BMap.Point(114.336432, 30.539215)); // 创建点
        map.addOverlay(marker);//增加点
        var marker = new BMap.Marker(new BMap.Point(113.818237, 23.071273));
        map.addOverlay(marker);//增加点

        $(".map-nav .dropdown-menu li").click(function () {
            $(".map-nav .btn-group button b").html($(this).find("a").html());

            var type = $(this).find("a").attr("data-type");
            if (type == "1") {
            map.panTo(new BMap.Point(114.064415, 22.609933));
            }
            if (type == "2") {
                map.panTo(new BMap.Point(114.336432, 30.539215));
            }
            if (type == "3") {
                map.panTo(new BMap.Point(113.818237, 23.071273));
            }
        });
    </script> *@
     <script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=4466b7f03b3ccc26a491b9f08d5ee6ef"></script>
    <script  asp-location="Footer">
        var map= new T.Map('allmap');
        var zoom = 15;
        map.centerAndZoom(new T.LngLat(114.054824, 22.610507), zoom);
       // 创建自定义图标
        var icon = new T.Icon({
           iconUrl: "http://api.tianditu.gov.cn/img/map/markerA.png",
           iconSize: new T.Point(19, 27),
           iconAnchor: new T.Point(10, 25)
        });
        // 创建标注并设置自定义图标和标题
        var marker = new T.Marker(new T.LngLat(114.053054, 22.607550), { icon: icon });
        // 将标注添加到地图上
        map.addOverLay(marker);
        var latlng = new T.LngLat(114.053054, 22.607550);
        var label = new T.Label({
             text: "深圳市海凌科电子有限公司<br>地址：广东省深圳市龙华区民治街道民乐社区星河WORLD E栋大厦17层1705、1706、1709A",
                position: latlng,
                offset: new T.Point(-9, 0)
            });
            //创建地图文本对象
        map.addOverLay(label);
        var marker = new T.Marker(new T.LngLat(114.324350, 30.536450)); // 创建点
        map.addOverLay(marker);//增加点
        var latlng = new T.LngLat(114.324350, 30.536450);
        var label = new T.Label({
          text: "武汉极思灵创科技有限公司<br>地址：武汉市武昌区中南国际城C座2单元701-702室",
                position: latlng,
                offset: new T.Point(-9, 0)
            });
            //创建地图文本对象
        map.addOverLay(label);
        var marker = new T.Marker(new T.LngLat(113.806557, 23.068383));
        map.addOverLay(marker);//增加点
        var latlng = new T.LngLat(113.806557, 23.068383);
        var label = new T.Label({
             text: "益坤泰实业有限公司<br>地址：广东省东莞市东城街道狮长路3号2栋4楼",
                position: latlng,
                offset: new T.Point(-9, 0)
            });
            //创建地图文本对象
        map.addOverLay(label);
        $(".map-nav .dropdown-menu li").click(function () {
            $(".map-nav .btn-group button b").html($(this).find("a").html());

            var type = $(this).find("a").attr("data-type");
            if (type == "1") {
                map.panTo(new T.LngLat(114.053054, 22.607550));
            }
            if (type == "2") {
                map.panTo(new T.LngLat(114.324350, 30.536450));
            }
            if (type == "3") {
                map.panTo(new T.LngLat(113.806557, 23.068383));
            }
        });
    </script>
    
