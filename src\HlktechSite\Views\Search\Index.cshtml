﻿@model IList<HlktechSite.SearchModel>

@{
    Layout = "~/Views/Shared/_Root.cshtml";

    Html.AppendCssFileParts("~/css/search.css");
}

<div class="top">
    <img src="@(CDN.GetCDN())/images/search.png" alt="Alternate Text" />
    <div>
        <p>@T("欢迎使用搜索服务")</p>
        <div class="seach-menu">
            @{
                var typeName = "";
                switch (ViewBag.SearchType)
                {
                    case 1:
                        typeName="知识库2";
                        break;
                    case 2:
                        typeName = "相关案例1";
                        break;
                    case 3:
                        typeName = "知识问答";
                        break;
                    case 4:
                        typeName = "解决方案";
                        break;
                    default:
                        typeName= "分类";
                        break;
                }
            }
            <span>@T($"{typeName}")<img src="@(CDN.GetCDN())/images/unfold.png" alt="Alternate Text" />
        </span>
        <input type="text" name="Key_Word" value="@ViewBag.KeyWord" />
        <a href="javascript:;"><img src="@(CDN.GetCDN())/images/search-btn.png" alt="Alternate Text" /></a>
        <dl>
            <dd>@T("请选择")</dd>
            <dd data-id="1">@T("知识库2")</dd>
            <dd data-id="2">@T("相关案例1")</dd>
            <dd data-id="3">@T("知识问答")</dd>
            <dd data-id="4">@T("解决方案")</dd>
        </dl>
        </div>
        <input type="text" name="type" value="@ViewBag.SearchType" style="display:none;" />
    </div>
</div>

<div class="navigation">
    <div class="navigation-con">
        <div class="navigation-con-left">
            @await Component.InvokeAsync("Location", new { model = ViewBag.Locations })
        </div>
        <div class="navigation-con-right">
        </div>
    </div>
</div>



<div class="seach-list">
    <h2>@T("相关案例")</h2>
    <ul>
        @foreach (var item in Model)
        {
            if (item.SearchType == 2)
            {
                <li>
                    <a href="@Url.DGAction("Details","Case",new { Id=item.Id})">@item.Name</a>
                    <i>@item.CreateTime</i>
                </li>
            }

        }
        @{ 
            var length1 = Model.Where(x => x.SearchType == 2).Count();
            if (length1 == 0)
            {
                <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
            }
        }
    </ul>
</div>
<div class="seach-list">
    <h2>@T("知识库")</h2>
    <ul>
        @foreach (var item in Model)
        {
            if (item.SearchType == 1)
            {
                <li>
                    <a href="@Url.DGAction("Details","Case",new { Id=item.Id})">@item.Name</a>
                    <i>@item.CreateTime</i>
                </li>
            }
        }
        @{
            var length2 = Model.Where(x => x.SearchType == 1).Count();
            if (length2 == 0)
            {
                <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
            }
        }
    </ul>
</div>
<div class="seach-list">
    <h2>@T("知识问答")</h2>
    <ul>
        @foreach (var item in Model)
        {
            if (item.SearchType == 3)
            {
                <li>
                    <a href="@Url.DGAction("Details","Case",new { Id=item.Id})">@item.Name</a>
                    <i>@item.CreateTime</i>
                </li>
            }

        }
        @{
            var length3 = Model.Where(x => x.SearchType == 2).Count();
            if (length3 == 0)
            {
                <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
            }
        }
    </ul>
</div>
<div class="seach-list">
    <h2>@T("解决方案")</h2>
    <ul>
        @foreach (var item in Model)
        {
            if (item.SearchType == 4)
            {
                <li>
                    <a href="@Url.DGAction("Details","Solution",new { Id=item.Id})">@item.Name</a>
                    <i>@item.CreateTime</i>
                </li>
            }
        }
        @{
            var length4 = Model.Where(x => x.SearchType == 4).Count();
            if (length4 == 0)
            {
                <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
            }
        }
    </ul>
</div>


<div class="seach-list">
    <h2>@T("订阅更多")</h2>
    <p>
        <span>
            <i><img src="@(CDN.GetCDN())/images/tb.png" />hilink @(T("旗舰店"))</i>
            <sub>
                <img src="@(CDN.GetCDN())/images/search-code.png" />
                <strong>
                    <b>~@T("快来看看吧")</b>
                    <b>@T("暂停您忙碌的的身影")</b>
                    <b>@T("这里有一个新世界哦")！</b>
                </strong>
            </sub>
        </span>
        <span>
            <i><img src="@(CDN.GetCDN())/images/tb.png" />@T("深圳市海凌科电子有限公司2")</i>
            <sub>
                <img src="@(CDN.GetCDN())/images/search-code2.png" />
                <strong>
                    <b>@T("关注我们")</b>
                    <b>@T("超多超值优惠活动")</b>
                    <b>@T("大额优惠等你来")</b>
                </strong>
            </sub>
        </span>
        <span>
            <i><img src="@(CDN.GetCDN())/images/tb.png" />@T("深圳市海凌科电子总店")</i>
            <sub>
                <img src="@(CDN.GetCDN())/images/search-code3.png" />
                <strong>
                    <b>@T("关注我们")</b>
                    <b>@T("超多超值优惠活动")</b>
                    <b>@T("大额优惠等你来")</b>
                </strong>
            </sub>
        </span>
    </p>
</div>
<script>
    $(".seach-menu span").click(() => {
        $(".seach-menu dl").slideToggle(100);
    });
    $(".seach-menu a").click(() => {
        window.location.href = "@Url.DGAction("Index")?Key_Word=" + $("[name=Key_Word]").val() + (!$("[name=type]").val() ? "" : "&type=" + $("[name=type]").val())
    });
    $(".seach-menu dd").click(function () {
        if ($(this).data("id")) {
            $("[name=type]").val($(this).data("id"));
        }
        else {
            $("[name=type]").val("");
        }
        $(".seach-menu span").html($(this).html() + '<img src="@(CDN.GetCDN())/images/unfold.png" alt="Alternate Text" />');
        $(".seach-menu dl").slideUp(100);
    });
    $("[name=Key_Word]").keydown((e) => {
        if (e.keyCode == 13) {
            $(".seach-menu a").click();
        }
    })
</script>

