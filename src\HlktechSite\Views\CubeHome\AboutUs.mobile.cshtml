﻿@model SingleArticle
@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";
    Html.AppendCssFileParts("~/css/mobile/AboutUs.css");

    Html.AppendTitleParts(T("关于我们1").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");

    var localizationSettings = LocalizationSettings.Current;
}

<div class="top">
    <img src="@(CDN.GetCDN())/images/cpzx.jpg" />
</div>
@if (localizationSettings.IsEnable)
{
    var ModelLan = SingleArticleLan.FindByAIdAndLIds(Model.Id, language.Id);
    @Html.Raw(ModelLan.Content);
}
else
{
    @Html.Raw(Model.Content);
}
@*<div class="AboutUs-con">
    <h2>@T("关于我们1")</h2>
    <span style="margin-top:57.1px;">@T("深圳市海凌科电子有限公司创建于2009年9月，是一家专业提供物联网智能家居WIFI模块产品的研发、生产、销售为一体的技术型企业。")</span>
    <span>@T("2016年10月20日在前海股权中心挂牌，股票代码：668537。")</span>
    <span>@T("2016年11月21日获得国家高新技术企业认证。")</span>
    <img src="@(CDN.GetCDN())/images/ole2.png">
    <span>@T("目前公司的产品主要产品有IOTWIFI模组，无线路由模组和电源模块。我们的产品被广泛的应用于各种智能控制行业和场合。")</span>
    <img src="@(CDN.GetCDN())/images/agentcom.png">
    <h2>@T("企业优势")</h2>
    <i style="margin-top:55px">
        @T("公司凭借自身的背景优势，积极开展自主研发，")
    </i>
    <i>@T("同时与国际众多知名厂商合作，努力拓展线上线下销售渠道，全面提升服务范围和质量。")</i>
    <i>@T("相信通过我们的不懈努力，必将开拓出更加宽广的市场前景，智能家居领域的明天一定更加美好！")</i>
    <div class="advantage">
        <div><img src="@(CDN.GetCDN())/images/advantage2.png"><h2>@T("开放的商务模式")</h2><p>@T("公司采取开放的商务模式，所有合作厂商均拥有产品品牌、定价、销售渠道的自主权。")</p></div>
        <div>
            <img src="@(CDN.GetCDN())/images/advantage3.png"><h2>@T("自主研发技术")</h2><p>@T("公司拥有专利技术，以及产品独立知识产权。研发实力过硬，具有一支技术量强大的研发团队。")</p>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/advantage1.png"><h2>@T("一站式云到端配套服务")</h2>
            <p>@T("公司具备完善成熟的云到端一站式配套，从互联网端的云服务平台，APP应用软件开发，到物联网领域的解决方案，家庭终端硬件设备，涵盖终端硬件设备，涵盖全面。")</p>
        </div>
    </div>
    <h2 style="padding-bottom:54px;">
        @T("企业资质")
    </h2>
    <i>@T("在市场竞争日益激烈的今天，拥有创新生产力才是制胜的关键法宝。公司拥有强大的研发团队和技术力量，")</i>
    <i>@T("产品已获得国家多项专利技术，为中国的智能家居行业建立了新的里程碑。")</i>
    <div class="certification">
        <div>
            <img src="@(CDN.GetCDN())/images/certification1.png">
            <span>@T("实用新型专利证书")</span>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/certification2.png">
            <span>@T("产品执行标准备案证书")</span>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/certification3.png">
            <span>@T("软件著作权")</span>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/certification4.png"><span>@T("软件著作权")</span>
        </div>
    </div>
    <div class="milestone">
        <h2>@T("里程碑")</h2>
        <img src="@(CDN.GetCDN())/images/milestone-mobile.png">
    </div>
    <div class="cooperation">
        <h2>@T("合作客户")</h2>
        <div>
            <img src="@(CDN.GetCDN())/images/cooperation1.png">
            <img src="@(CDN.GetCDN())/images/cooperation2.png">
            <img src="@(CDN.GetCDN())/images/cooperation3.png">
            <img src="@(CDN.GetCDN())/images/cooperation4.png">
            <img src="@(CDN.GetCDN())/images/cooperation5.png">
            <img src="@(CDN.GetCDN())/images/cooperation6.png">
            <img src="@(CDN.GetCDN())/images/cooperation7.png">
            <img src="@(CDN.GetCDN())/images/cooperation8.png">
            <img src="@(CDN.GetCDN())/images/cooperation9.png">
            <img src="@(CDN.GetCDN())/images/cooperation10.png">
            <img src="@(CDN.GetCDN())/images/cooperation11.png">
            <img src="@(CDN.GetCDN())/images/cooperation12.png">
        </div>
    </div>

</div>*@

