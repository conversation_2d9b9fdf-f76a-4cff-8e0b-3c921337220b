﻿using DG.Cube.BaseControllers;

using DH.Entity;
using DH.Models;

using Microsoft.AspNetCore.Mvc;
using Pek.Models;
using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>经验值管理</summary>
[DisplayName("经验值管理")]
[Description("用于会员经验值的管理")]
[AdminArea]
[DHMenu(70,ParentMenuName = "Members", CurrentMenuUrl = "~/{area}/ExpPoints", CurrentMenuName = "ExpPoints", CurrentIcon = "&#xe727;", LastUpdate = "20240125")]
public class ExpPointsController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 70;

    /// <summary>
    /// 经验值列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("经验值列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 经验值规则设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("经验值规则设置")]
    public IActionResult ExpSetting()
    {
        return View();
    }

    /// <summary>
    /// 经验值规则设置
    /// </summary>
    /// <param name="exp_login">会员每天第一次登录</param>
    /// <param name="exp_comments">订单商品评论</param>
    /// <param name="exp_orderrate">消费额与赠送经验值比例</param>
    /// <param name="exp_ordermax">每订单最多赠送经验值</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("经验值规则设置")]
    public IActionResult UpdateExpSetting(Int32 exp_login, Int32 exp_comments, Int32 exp_orderrate, Int32 exp_ordermax)
    {
        var model = SiteSettingInfo.SiteSettings;
        model.ExpLogin = exp_login;
        model.ExpComments = exp_comments;
        model.ExpOrderRate = exp_orderrate;
        model.ExpOrderMax = exp_ordermax;
        SiteSettingInfo.SaveChanges();

        return MessageTip(GetResource("保存成功"));
    }
}
