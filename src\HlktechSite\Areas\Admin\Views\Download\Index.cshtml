﻿@{
}
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("下载文件")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("下载文件名称")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("编号")</th>
                <th style="width:150px">@T("下载文件名称")</th>
                <th>@T("所属分类")</th>
                <th style="width:150px">@T("产品型号")</th>
                <th style="width:750px">@T("下载文件资源")</th>
                <th>@T("排序")</th>
                <th width="160px">@T("创建时间")</th>
                <th style="width: 80px;">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in (IEnumerable<Download>)ViewBag.list)
            {
                <tr id="<EMAIL>" style="background: rgb(255, 255, 255);">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.CategoryName</td>
                    <td>@item.ProductModels</td>
                    <td>@item.Development</td>
                    <td>@item.DisplayOrder</td>
                    <td>@item.CreateTime</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("EditDownText",new { Id=item.Id})','@T("编辑产品型号")')" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a></td>
                </tr>
            }
        </tbody>
        <tfoot>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
