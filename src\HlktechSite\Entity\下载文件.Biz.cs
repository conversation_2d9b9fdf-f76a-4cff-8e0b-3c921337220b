using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;

using DH;

using NewLife;
using NewLife.Data;
using NewLife.Model;

using Pek;

using XCode;
using XCode.Cache;
using XCode.Model;

namespace HlktechSite.Entity {
    /// <summary>下载文件</summary>
    public partial class Download : CubeEntityBase<Download>
    {
        #region 对象操作
        static Download()
        {
            // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
            var df = Meta.Factory.AdditionalFields;
            df.Add(nameof(Clicks));

            // 过滤器 UserModule、TimeModule、IPModule
            Meta.Modules.Add<UserModule>();
            Meta.Modules.Add<TimeModule>();
            Meta.Modules.Add<IPModule>();

            // 单对象缓存
            var sc = Meta.SingleCache;
            sc.FindSlaveKeyMethod = k => Find(_.Name == k);
            sc.GetSlaveKeyMethod = e => e.Name;
        }

        /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
        /// <param name="isNew">是否插入</param>
        public override void Valid(Boolean isNew)
        {
            // 如果没有脏数据，则不需要进行任何处理
            if (!HasDirty) return;

            // 在新插入数据或者修改了指定字段时进行修正
            // 处理当前已登录用户信息，可以由UserModule过滤器代劳
            /*var user = ManageProvider.User;
            if (user != null)
            {
                if (isNew && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
                if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
            }*/
            //if (isNew && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
            //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
            //if (isNew && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
            //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

            // 检查唯一索引
            // CheckExist(isNew, nameof(Name));
        }

        ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
        //[EditorBrowsable(EditorBrowsableState.Never)]
        //protected override void InitData()
        //{
        //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        //    if (Meta.Session.Count > 0) return;

        //    if (XTrace.Debug) XTrace.WriteLine("开始初始化Download[下载文件]数据……");

        //    var entity = new Download();
        //    entity.Id = 0;
        //    entity.Name = "abc";
        //    entity.Development = "abc";
        //    entity.Application = "abc";
        //    entity.GeneralSoftware = "abc";
        //    entity.MIds = "abc";
        //    entity.DId = 0;
        //    entity.Clicks = 0;
        //    entity.DisplayOrder = 0;
        //    entity.CreateUser = "abc";
        //    entity.CreateUserID = 0;
        //    entity.CreateTime = DateTime.Now;
        //    entity.CreateIP = "abc";
        //    entity.UpdateUser = "abc";
        //    entity.UpdateUserID = 0;
        //    entity.UpdateTime = DateTime.Now;
        //    entity.UpdateIP = "abc";
        //    entity.Insert();

        //    if (XTrace.Debug) XTrace.WriteLine("完成初始化Download[下载文件]数据！");
        //}

        ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
        ///// <returns></returns>
        //public override Int32 Insert()
        //{
        //    return base.Insert();
        //}

        ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
        ///// <returns></returns>
        //protected override Int32 OnDelete()
        //{
        //    return base.OnDelete();
        //}
        #endregion

        #region 扩展属性
        /// <summary>
        /// 获取下载分类
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public DownloadCategory? DownloadCategory => Extends.Get(nameof(DownloadCategory), k => DownloadCategory.FindById(DId));

        /// <summary>
        /// 下载分类名称
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public String? CategoryName => DownloadCategory?.Name;

        /// <summary>
        /// 产品型号名称集合
        /// </summary>
        [XmlIgnore, ScriptIgnore, IgnoreDataMember]
        public String? ProductModels => Extends.Get(nameof(ProductModels), k => ProductModel.FindByIds(MIds).Select(e => e.Name).Join());

        /// <summary>
        ///图片
        /// </summary>
        [XmlIgnore, ScriptIgnore]
        public String? ProImg { get; set; }
        #endregion

        #region 扩展查询

        /// <summary>根据编号列表查找</summary>
        /// <param name="Mid">编号列表</param>
        /// <returns>实体对象</returns>
        public static IList<Download> FindInIds(int Mid)
        {
            if (Mid <= 0) return new List<Download>();

            var Mid1 = "," + Mid + ",";
            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(x => x.MIds.SafeString().Contains(Mid1.ToString()));
            }

            return FindAll(_.MIds.Contains(Mid1.ToString()));
        }

        /// <summary>根据关联下载分类Id查找</summary>
        /// <param name="dId">关联下载分类Id</param>
        /// <returns>实体列表</returns>
        public static IEnumerable<Download> FindAllByDId(Int32 dId)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.DId == dId).OrderByDescending(e => e.DisplayOrder).ThenBy(e => e.Id);

            return FindAll(_.DId == dId, new PageParameter { OrderBy = "DisplayOrder desc, Id asc" });
        }

        /// <summary>根据名称模糊查找</summary>
        /// <param name="Name">问价名称</param>
        /// <returns>实体列表</returns>
        public static IList<Download> FindAllByName(string Name)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Name?.Contains(Name, StringComparison.OrdinalIgnoreCase) == true);

            return FindAll(_.Name.Contains(Name));
        }

        /// <summary>根据下载文件名称查找</summary>
        /// <param name="name">下载文件名称</param>
        /// <returns>实体对象</returns>
        public static Download? FindByNameWithNoCache(String name)
        {
            return Find(_.Name == name);
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="name"></param>
        /// <param name="search_ac_id"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        public static IEnumerable<Download> Searchs(string name, int search_ac_id, PageParameter page)
        {
            // 实体缓存
            if (Meta.Session.Count < 1000)
            {
                IEnumerable<Download> list;

                list = FindAllWithCache();
                if (name.IsNotNullAndWhiteSpace())
                {
                    list = list.Where(e => e.Name.Contains(name));
                }
                if (search_ac_id > 0)
                {
                    list = list.Where(e => e.DId == search_ac_id);
                }
              
                page.TotalCount = list.Count();

                list = list.OrderByDescending(e => e.Id).Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize);
                return list;
            }

            var exp = new WhereExpression();
            if (name.IsNotNullAndWhiteSpace())
            {
                exp &= _.Name.Contains(name);
            }
            if (search_ac_id > 0)
            {
                exp &= _.DId == search_ac_id;
            }
           
            return FindAll(exp, page);
        }
        #endregion

        #region 高级查询
        /// <summary>高级查询</summary>
        /// <param name="name">下载文件名称</param>
        /// <param name="mIds">关联产品型号Id，以逗号分隔开</param>
        /// <param name="dId">关联下载分类Id</param>
        /// <param name="key">关键字</param>
        /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
        /// <returns>实体列表</returns>
        public static IList<Download> Search(String name, String mIds, Int32 dId, String key, PageParameter page)
        {
            var exp = new WhereExpression();

            if (!name.IsNullOrEmpty()) exp &= _.Name == name;
            if (!mIds.IsNullOrEmpty()) exp &= _.MIds == mIds;
            if (dId >= 0) exp &= _.DId == dId;
            if (!key.IsNullOrEmpty()) exp &= _.Development.Contains(key) | _.Application.Contains(key) | _.GeneralSoftware.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

            return FindAll(exp, page);
        }

        // Select Count(Id) as Id,MIds From Download Where CreateTime>'2020-01-24 00:00:00' Group By MIds Order By Id Desc limit 20
        static readonly FieldCache<Download> _MIdsCache = new FieldCache<Download>(nameof(MIds))
        {
            //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
        };

        /// <summary>获取关联产品型号Id列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
        /// <returns></returns>
        public static IDictionary<String, String> GetMIdsList() => _MIdsCache.FindAllName();
        #endregion

        #region 业务操作
        /// <summary>
        /// 延迟队列
        /// </summary>
        private static readonly DeferredQueue _statCache = new EntityDeferredQueue { Name = "Download", Action = EntityActions.Save };
        public static void SaveStat(String Name)
        {
            var key = $"{Name}";

            _statCache.Commit(key);
        }

        /// <summary>
        /// 从延迟队列获取实体
        /// </summary>
        /// <param name="Name"></param>
        /// <returns></returns>
        public static Download FindFromQueueCacheByName(String Name)
        {
            var key = $"{Name}";

            return _statCache.GetOrAdd(key, k => FindByNameWithNoCache(Name) ?? new Download());
        }
        #endregion
    }
}