﻿using DG.Utils.Infrastructure;
using DG.Web.Framework.Routing;

using DH;
using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;

namespace HlktechSite;

public class RouteProvider : BaseRouteProvider, IRouteProvider
{
    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpointRouteBuilder">路由构造器</param>
    public void RegisterRoutes(IEndpointRouteBuilder endpointRouteBuilder)
    {
        var lang = GetLanguageRoutePattern();

        if (!DHSetting.Current.IsInstalled) return;

        var UrlSuffix = DG.Setting.Current.IsAllowUrlSuffix ? DG.Setting.Current.UrlSuffix : "";

        var pattern = $"{lang}/";

        var localizationSettings = LocalizationSettings.Current;

        // 产品中心
        endpointRouteBuilder.MapControllerRoute("ProductList1", "Product/c{CId:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Product", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("ProductList1", pattern + "Product/c{CId:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Product", action = "List" });
        }

        endpointRouteBuilder.MapControllerRoute("ProductList", "Product/c{CId:min(1)}" + UrlSuffix,
            new { controller = "Product", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("ProductList", pattern + "Product/c{CId:min(1)}" + UrlSuffix,
            new { controller = "Product", action = "List" });
        }

        endpointRouteBuilder.MapControllerRoute(name: "Product1", pattern: "Product/p{p:min(1)}" + UrlSuffix,
            new { controller = "Product", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute(name: "Product1", pattern: pattern + "Product/p{p:min(1)}" + UrlSuffix,
            new { controller = "Product", action = "Index" });
        }
        endpointRouteBuilder.MapControllerRoute(name: "Product", pattern: "Product" + UrlSuffix,
            new { controller = "Product", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute(name: "Product", pattern: pattern + "Product" + UrlSuffix,
            new { controller = "Product", action = "Index" });
        }

        //产品详情
        endpointRouteBuilder.MapControllerRoute("Goods", "Goods-{Id}" + UrlSuffix,
            new { controller = "Product", action = "Details" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Goods", pattern + "Goods-{Id}" + UrlSuffix,
            new { controller = "Product", action = "Details" });
        }

        // 解决方案
        endpointRouteBuilder.MapControllerRoute("SolutionList1", "Solution/c{CId:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Solution", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("SolutionList1", pattern + "Solution/c{CId:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Solution", action = "List" });
        }

        endpointRouteBuilder.MapControllerRoute("SolutionList", "Solution/c{CId:min(1)}" + UrlSuffix,
            new { controller = "Solution", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("SolutionList", pattern + "Solution/c{CId:min(1)}" + UrlSuffix,
            new { controller = "Solution", action = "List" });
        }

        endpointRouteBuilder.MapControllerRoute("Solution1", "Solution/p{p:min(1)}" + UrlSuffix,
            new { controller = "Solution", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Solution1", pattern + "Solution/p{p:min(1)}" + UrlSuffix,
            new { controller = "Solution", action = "Index" });
        }

        endpointRouteBuilder.MapControllerRoute("Solution", "Solution" + UrlSuffix,
            new { controller = "Solution", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Solution", pattern + "Solution" + UrlSuffix,
            new { controller = "Solution", action = "Index" });
        }

        // 解决方案详情
        endpointRouteBuilder.MapControllerRoute("SolutionInfo", "SolutionInfo-{Id}" + UrlSuffix,
            new { controller = "Solution", action = "Details" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("SolutionInfo", pattern + "SolutionInfo-{Id}" + UrlSuffix,
            new { controller = "Solution", action = "Details" });
        }

        // 案例
        endpointRouteBuilder.MapControllerRoute("CaseList1", "Case/t{Type:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Case", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("CaseList1", pattern + "Case/t{Type:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Case", action = "List" });
        }

        endpointRouteBuilder.MapControllerRoute("CaseList", "Case/t{Type:min(1)}" + UrlSuffix,
            new { controller = "Case", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("CaseList", pattern + "Case/t{Type:min(1)}" + UrlSuffix,
            new { controller = "Case", action = "List" });
        }

        endpointRouteBuilder.MapControllerRoute("Case1", "Case/p{p:min(1)}" + UrlSuffix,
            new { controller = "Case", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Case1", pattern + "Case/p{p:min(1)}" + UrlSuffix,
            new { controller = "Case", action = "Index" });
        }

        endpointRouteBuilder.MapControllerRoute("Case", "Case" + UrlSuffix,
            new { controller = "Case", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Case", pattern + "Case" + UrlSuffix,
            new { controller = "Case", action = "Index" });
        }

        // 案例详情
        endpointRouteBuilder.MapControllerRoute("CaseInfo", "CaseInfo-{Id}" + UrlSuffix,
            new { controller = "Case", action = "Details" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("CaseInfo", pattern + "CaseInfo-{Id}" + UrlSuffix,
            new { controller = "Case", action = "Details" });
        }

        // 代理
        endpointRouteBuilder.MapControllerRoute("Agent", "Agent" + UrlSuffix,
            new { controller = "Agent", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Agent", pattern + "Agent" + UrlSuffix,
            new { controller = "Agent", action = "Index" });
        }

        // 新闻
        endpointRouteBuilder.MapControllerRoute("JournalismList1", "Journalism/a{AId:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Journalism", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("JournalismList1", pattern + "Journalism/a{AId:min(1)}/p{p:min(1)}" + UrlSuffix,
            new { controller = "Journalism", action = "List" });
        }
        endpointRouteBuilder.MapControllerRoute("JournalismList", "Journalism/a{AId:min(1)}" + UrlSuffix,
            new { controller = "Journalism", action = "List" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("JournalismList", pattern + "Journalism/a{AId:min(1)}" + UrlSuffix,
            new { controller = "Journalism", action = "List" });
        }

        endpointRouteBuilder.MapControllerRoute("Journalism1", "Journalism/p{p:min(1)}" + UrlSuffix,
            new { controller = "Journalism", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Journalism1", pattern + "Journalism/p{p:min(1)}" + UrlSuffix,
            new { controller = "Journalism", action = "Index" });
        }
        endpointRouteBuilder.MapControllerRoute("Journalism", "Journalism" + UrlSuffix,
            new { controller = "Journalism", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Journalism", pattern + "Journalism" + UrlSuffix,
            new { controller = "Journalism", action = "Index" });
        }

        //新闻详情
        endpointRouteBuilder.MapControllerRoute("JournalismDetails", "NewsInfo-{Id}" + UrlSuffix,
            new { controller = "Journalism", action = "Details" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("JournalismDetails", pattern + "NewsInfo-{Id}" + UrlSuffix,
            new { controller = "Journalism", action = "Details" });
        }

        // 联系我们
        endpointRouteBuilder.MapControllerRoute("Contact", "Contact" + UrlSuffix,
            new { controller = "Contact", action = "Index" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Contact", pattern + "Contact" + UrlSuffix,
            new { controller = "Contact", action = "Index" });
        }

        // 关于我们
        endpointRouteBuilder.MapControllerRoute("About", "About" + UrlSuffix,
            new { controller = "CubeHome", action = "AboutUs" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("About", pattern + "About" + UrlSuffix,
            new { controller = "CubeHome", action = "AboutUs" });
        }

        // 定制申请
        endpointRouteBuilder.MapControllerRoute("Customization", "Customization" + UrlSuffix,
            new { controller = "CubeHome", action = "Customization" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("Customization", pattern + "Customization" + UrlSuffix,
            new { controller = "CubeHome", action = "Customization" });
        }


        // 单页
        endpointRouteBuilder.MapControllerRoute("App", "App/{Code}" + UrlSuffix,
                new { controller = "App", action = "GetInfo" });
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpointRouteBuilder.MapControllerRoute("App", pattern + "App/{Code}" + UrlSuffix,
            new { controller = "App", action = "GetInfo" });
        }
    }

    /// <summary>
    /// 获取路由提供者的优先级
    /// </summary>
    public int Priority => -1;
}
