﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>页面导航翻译</summary>
public partial class NavigationLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>页面导航Id</summary>
    public Int32 NId { get; set; }

    /// <summary>关联所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>页面导航标题</summary>
    public String? Name { get; set; }

    /// <summary>页面导航链接</summary>
    public String? Url { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(INavigationLan model)
    {
        Id = model.Id;
        NId = model.NId;
        LId = model.LId;
        Name = model.Name;
        Url = model.Url;
    }
    #endregion
}
