﻿

i{
    font-style:normal;
}

.title1 {
    color: #333 !important;
    background-color: #fff;
}

    .title1:hover {
        background-color: #eee !important;
    }

.title7 {
    color: #fff !important;
    background-color: #337ab7;
}

    .title7:hover {
        background-color: #337ab7 !important;
    }


.top {
    position: relative;
}

    .top>img{
        width:100%;
    }
    .top > div {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        bottom: 0px;
        color: #FFFFFF;
    }

        .top > div > h2 {
            margin-top: 6%;
            text-align: center;
            font-size: 56px;
        }

        .top > div > p {
            width: 868px;
            margin: 0 auto;
            margin-top: 35px;
            text-align: center;
            font-size: 20px;
        }

.AboutUs-nav {
    border-bottom: 1px solid #EEEEEE;
}

    .AboutUs-nav > p {
        width: 1200px;
        margin: 0 auto;
        padding-top: 24px;
        font-size: 16px;
        padding-bottom: 24px;
    }

        .AboutUs-nav > p > a {
            color: #343434;
        }

        .AboutUs-nav > p > a:hover{
            color: #343434;
            text-decoration:none
        }

.AboutUs-con{
    padding-top:91px;
    text-align:center;
}

    .AboutUs-con > h2 {
        font-size: 32px;
        font-weight: 400;
        text-align:center;
    }


    .AboutUs-con > p {
        font-size: 32px;
        font-weight: 400;
        text-align: center;
    }



    .AboutUs-con > span {
        text-align: center;
        display: block;
        margin-bottom: 30.1px;
        color: #666666;
        font-size:18px;
    }

        .AboutUs-con>img {
           display:inline-block;
           margin-bottom:73px;
        }

    .AboutUs-con > i {
        color: #666666;
        font-size:18px;
        display:block;
        font-style:normal;
    }
.advantage{
    width:1202px;
    display:flex;
    margin:0 auto;
    margin-top:52px;
    padding-bottom:128px;
}

    .advantage > div {
        margin-right: 38px;
        background: rgba(255,255,255,1);
        box-shadow: 1px 6px 36px 3px rgba(177,174,174,0.38);
    }

    .advantage > div:last-child {
        margin-right: 0px;
    }

        .advantage > div > h2 {
            font-size: 19px;
            font-weight: 400;
            padding-top:21px;
            padding-bottom:17px;
            margin:0;
        }

        .advantage > div > p {
            display: block;
            font-size: 15px;
            font-weight: 400;
            color: #868686;
            padding-left: 31px;
            padding-right: 29px;
            padding-bottom: 47px;
            margin: 0px;
            height: 112px;
            line-height: 1.1;
        }

.certification{
    padding-top:76px;
    padding-bottom:76px;
    width:1200px;
    margin:0 auto;
    display:flex;
}

.certification>div{
    margin-right:109px;
}
    .certification > div:last-child {
        margin-right: 0px;
    }


    .certification > div > span {
        display: block;
        color: #343434;
        text-align: center;
        font-size: 19px;
        padding-top: 18px;
    }


.milestone > h2 {
    color: #333;
    padding-top: 54px;
    background-color: #FAFAFA;
    margin: 0px;
    margin-bottom: -39px;
    position: relative;
}
.milestone>img{
    width:100%;
}


.cooperation {
    background-color: #FAFAFA;
}

    .cooperation>h2{
        text-align:center;
        padding-top:40px;
        padding-bottom:110px;
        margin:0px;
    }

    .cooperation > div {
        width: 1200px;
        margin: 0 auto;
        padding-bottom:127px;
    }

    .cooperation > div > img {
        margin-right: 84px;
        width: 234px;
        margin-bottom:38px;
    }

    .cooperation > div > img:nth-child(4n) {
        margin-right: 0px;
    }