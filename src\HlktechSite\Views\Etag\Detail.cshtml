﻿@{
    Html.AppendCssFileParts("~/css/product.css");
    Html.AppendCssFileParts("~/css/summarize.css");

    //var Product = Model.Product as Goods;
    //var Titles = ViewBag.Titles as String;

    Html.AppendTitleParts("产品详情");

    var cdn = CDN.GetCDN();

    //var ShopUrl =  Model.ShopUrl as List<String>;
    //var TaoBaoUrl1 = Model.TaoBaoUrl1 as List<String>;
    var ShopUrl = new List<string>() { "www.baidu.com", "www.taobao.com" };
    var TaoBaoUrl1 = new List<string>(){"www.baidu.com","www.taobao.com"};
}
<style>
    .Pro-detailed-top li {
        width: 15% !important;
    }
</style>

<div class="top">
    <img src="@(cdn)/images/cpzt2.jpg" />
    <div>
        <h2>@T("产品中心")</h2>
        <P>@T("深耕行业十数载，累计服务超过10W+客户")</P>
    </div>
</div>

<div class="navigation">
    <div class="navigation-con">
        <div class="navigation-con-left">
            <span>
                @*@await Component.InvokeAsync("Location", new { model = Model.Locations })*@
                <a href="#">首页</a>&gt; <span href="#">产品详情</span>
            </span>
        </div>
        <div class="navigation-con-right">
        </div>
    </div>
</div>
<div class="Pro-intr">
    <div>
        <div class="border">
            <div class="magnify">
                <div class="large" style="background:url('')"></div>
                <img class="Pro-intr-BigImg small" src="" alt="" />
            </div>
        </div>
        <center>
        </center>
        <div class="imgs-div">
            <ul>
               @* @foreach (var item in Model.ImgList as IEnumerable<HlktechSite.Entity.GoodsImages>)
                {
                    <li><img src="@item.Url" alt="" /></li>
                }*@
            </ul>
        </div>
    </div>
    <div class="Pro-intr-right">
        <h2 title="">GICISKY 云考勤打卡机 考勤机人脸识别无接触手机考勤门禁</h2>
        <span></span>
        <b>@T("浏览次数") : <i>1999</i></b>
        @*<p>@Model.Product.UsageScenarios</p>*@
        <p>1999</p>
        <p class="Pro-intr-right-menu">
            <a href="#" target="_blank">@T("咨询客服")</a>
            <a href="#ditch" class="tobuy">@T("在线购买")</a>
        </p>
        <div class="share">
            <div class="bdsharebuttonbox">
                <a href="#" class="bds_more" data-cmd="more"></a>
                <a href="#" class="bds_qzone" data-cmd="qzone" title="@T("分享到QQ空间")"></a>
                <a href="#" class="bds_tsina" data-cmd="tsina" title="@T("分享到新浪微博")"></a>
                <a href="#" class="bds_tqq" data-cmd="tqq" title="@T("分享到腾讯微博")"></a>
                <a href="#" class="bds_renren" data-cmd="renren" title="@T("分享到人人网")"></a>
                <a href="#" class="bds_weixin" data-cmd="weixin" title="@T("分享到微信")"></a>
                <a href="#" class="bds_douban" data-cmd="douban" title="@T("分享到豆瓣网")"></a>
            </div>
        </div>
    </div>
</div>
<div class="Pro-detailed">
    <div class="Pro-detailed-top">
        <ul>
            <li id="trait" class="liSelected">@T("图文介绍")</li>
            <li id="parameter">@T("参数规格")</li>
            <li id="datum">@T("资料下载")</li>
@*            <li id="exchange">@T("知识问答")</li>
            <li id="scheme">@T("解决方案")</li>*@
            <li id="ditch">@T("购买渠道")</li>
        </ul>
    </div>
    <div class="Pro-detailed-bottom">
        <div class="trait cut">
            <div class="trait-con">
               @* @Html.Raw(Model.Product.Content)*@
            </div>
        </div>
        <div class="parameter cut">
           @* @Html.Raw(Model.Product.Specifications)*@
        </div>
        <div id="datum" class="datum cut have-backgroup">
            <!--[if IE]> <![endif]-->  
            <!--

            
            <ul>
                <li>
                    <span>@T("开发资料")</span>
                    <span>
                       @* @foreach (var item in Model.Development)
                        {
                            <i>
                                @item.resource_name
                                <a href="http://h.hlktech.com@(item.resource_url)" target="_blank"><img src="@(cdn)/images/down.png" /></a>
                            </i>
                        }*@
                    </span>
                </li>
                <li>
                    <span>@T("软件应用")</span>
                    <span>
                     @*   @foreach (var item in Model.Application)
                        {
                            <i>
                                @item.resource_name
                                <a href="http://h.hlktech.com@(item.resource_url)" target="_blank"><img src="@(cdn)/images/down.png" /></a>
                            </i>
                        }*@

                    </span>
                </li>
                <li>
                    <span>@T("通用软件")</span>
                    <span>

                      @*  @foreach (var item in Model.GeneralSoftware)
                        {
                            <i>
                                @item.resource_name
                                <a href="http://h.hlktech.com@(item.resource_url)" target="_blank"><img src="@(cdn)/images/down.png" /></a>
                            </i>
                        }
*@

                    </span>
                </li>
                <li>
                    <span>@T("常见问题")</span>
                    <span>
                        @*<i>
                            RM04问题文档
                            <a href="#"><img src="@(cdn)/images/down.png" /></a>
                            </i>
                            <i>
                            常见问题
                            <a href="#"><img src="@(cdn)/images/down.png" /></a>
                            </i>*@
                    </span>
                </li>
            </ul>
            -->
        </div>
        <div class="exchange cut have-backgroup">
            <ul>
            @*    @foreach (var item in Model.knowledgeList)
                {
                    <li>
                        <span>@item.Name</span>
                        <span>
                            <img src="@(cdn)/images/time.png" /><i style="font-style:normal;text-align:left;width: 200px; display: inline-block;">@item.CreateTime</i>
                            <a href="@("http://h.hlktech.com/Knowledge/KnowledgeDetails/"+item.Id+".html")" target="_blank">@T("查看详情")></a>
                        </span>
                    </li>
                }*@
            </ul>
        </div>
        <div class="scheme cut have-backgroup">
            <ul>
               @* @foreach (var item in Model.SolutionList)
                {
                    <li>
                        <span>@item.Name</span>
                        <span>
                            <img src="@(cdn)/images/time.png" />@item.CreateTime
                            <a href="@Url">@T("查看详情")></a>
                        </span>
                    </li>
                }*@
            </ul>
        </div>
        <div class="ditch cut have-backgroup">
            <div>
                <span>
                    @T("在线购买")
                </span>
                <span>
                    @{
                        if (language.UniqueSeoCode == "cn")
                        {
                            if (ShopUrl.Any())
                            {
                                foreach (var item in ShopUrl)
                                {
                                    <a href="@item" target="_blank">
                                        <img src="@(cdn)/images/gfsc.png" />
                                        <Strong style="color: red;">@T("官方商城直达下单")</Strong>
                                    </a>
                                }
                            }
                            else
                            {
                                <a href="@T("商城链接")" target="_blank">
                                    <img src="@(cdn)/images/gfsc.png" />
                                    @T("官方商城")
                                </a>
                            }

                            // if (TaoBaoUrl1.Any())
                            // {
                            //     foreach (var item in TaoBaoUrl1)
                            //     {
                            //         <a href="@item" target="_blank">
                            //             <img src="@(cdn)/images/tbsc.png" />
                            //             <Strong style="color: red;">@T("淘宝直达下单")</Strong>
                            //         </a>
                            //     }
                            // }
                            // else
                            // {
                            //     <a href="https://mall.jd.com/index-12293539.html" target="_blank">
                            //         <img src="@(cdn)/images/jdimg.png" />
                            //         @T("京东店铺")
                            //     </a>
                            // }
                            <a href="https://mall.jd.com/index-12293539.html" target="_blank">
                                    <img src="@(cdn)/images/jdimg.png" />
                                    @T("京东店铺")
                                </a>

                            <a href="https://hilink.tmall.com/" target="_blank">
                                <img src="@(cdn)/images/tmsc.png" />@T("天猫商城")
                            </a>
                            <a href="https://hi-link.taobao.com/" target="_blank">
                                <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")1
                            </a>
                            <a href="https://hlktech.taobao.com/" target="_blank">
                                <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")2
                            </a>
                            <a href="https://shop57596328.taobao.com/" target="_blank">
                                <img src="@(cdn)/images/tbsc.png" />@T("淘宝店铺")3
                            </a>
                        }
                        else
                        {
                            <a href="https://www.aliexpress.com/store/911797719" target="_blank">
                                <img src="@(cdn)images/aliexpress.png" style="width: 30px;" />Aliexpress
                            </a>
                        }
                    }

                </span>
            </div>
            <div>
                <span>
                    @T("大客户通道")
                </span>
                <span>
                    <i>
                        @T("样品申请")
                        <b>
                            [@T("会2个工作日内回复")]
                        </b>
                        <a href="@Url.DGAction("Index","Apply")" target="_blank">@T("立即申请样品")</a>
                    </i>
                    <i>
                        @T("批量采购")
                        <b>
                            [@T("联系所属省份的大客户经理")]
                        </b>
                        <a href="@Url.DGAction("Index","Contact")" target="_blank">@T("查看联系方式")</a>
                    </i>
                    <i>
                        @T("定制申请")
                        <b>
                            [@T("会2个工作日内回复")]
                        </b>
                        <a href="@Url.DGAction("Index","Apply")" target="_blank">@T("立即申请定制")</a>
                    </i>
                </span>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" asp-location="Footer">
    $(function () {
        $(".Pro-detailed-top li").click(function () {
            $(".liSelected").removeClass("liSelected");
            $(this).addClass("liSelected");
            $(".cut").hide()
            $("." + $(this)[0].id).show()
        });

        $(".downlodad").click(function () {
            var down = $(".Pro-detailed-top li:eq(2)")
            $(".liSelected").removeClass("liSelected");
            $(down).addClass("liSelected");
            $(".cut").hide()
            $("." + $(down).attr("id")).show()
        })

        $(".tobuy").click(function () {
            var down = $(".Pro-detailed-top li:eq(5)")
            $(".liSelected").removeClass("liSelected");
            $(down).addClass("liSelected");
            $(".cut").hide()
            $("." + $(down).attr("id")).show()
        })

        // 定义图像的实际尺寸、

        var native_width = 0;

        var native_height = 0;

        // 首先、我们应该获得图像的实际尺寸、（本地的图片）
        $('.small').load(function () {
            // 这里我们需要重新创建一个和之前相同的图像对象、

            // 因为我们不能直接获得图像尺寸的宽高、

            // 因为我们在HTML里已经指定了图片宽度为200px、

            var img_obj = new Image();

            img_obj.src = $(this).attr('src');



            //  在这里这段代码写在这里是非常有必要的、

            //  如果在图像加载之前就访问的话、return的宽高值为0、

            native_width = img_obj.width;

            native_height = img_obj.height;



            // 现在、我来开始写鼠标移动的函数、mousemove()

            $('.magnify').mousemove(function (e) {

                // 获得鼠标X轴和Y轴的坐标

                //  先获得magnify相对与document的定位position

                var magnify_offset = $(this).offset();



                // 这里我们用鼠标相对与文档的位置减去鼠标相对于magnify这个人容器的位置 来得到鼠标的位置

                var mouse_x = e.pageX - magnify_offset.left;

                var mouse_y = e.pageY - magnify_offset.top;





                // 现在、我们来调整一下放大镜的隐藏与显示、

                if (mouse_x > 0 && mouse_y > 0 && mouse_x < $(this).width() && mouse_y < $(this).height()) {

                    $('.large').fadeIn(100);

                } else {

                    $('.large').fadeOut(100);

                }

                if ($('.large').is(':visible')) {

                    var rx = Math.round(mouse_x / $('.small').width() * native_width - $('.large').width() / 2) * -1;

                    var ry = Math.round(mouse_y / $('.small').height() * native_height - $('.large').height() / 2) * -1;

                    var bgp = rx + 'px ' + ry + 'px';

                    var gx = mouse_x - $('.large').width() / 2;

                    var gy = mouse_y - $('.large').height() / 2;

                    $('.large').css({

                        'left': gx,

                        'top': gy,

                        'backgroundPosition': bgp

                    })

                }

            })

        })


    })
    window._bd_share_config = {
        "common": {
            "bdSnsKey": {},
            "bdText": "",
            "bdMini": "2",
            "bdMiniList": false,
            "bdPic": "",
            "bdStyle": "0",
            "bdSize": "24"
        },
        "share": {}
    };
    with (document)
    0[(getElementsByTagName('head')[0] || body)
        .appendChild(createElement('script')).src = '/static/api/js/share.js?v=89860593.js?cdnversion='
        + ~(-new Date() / 36e5)];
    $(".imgs-div li").click(function () {
        $(".Pro-intr li").removeClass("selected");
        $(this).addClass("selected");
        $(".small").attr("src", $(this).find("img").attr("src"));
        $(".large").attr("style", `background:url(${$(this).find("img").attr("src")})`);
    });
    $(".imgs-div li:eq(0)").click();
    var scroll_width = 20;  // 设置每次滚动的长度，单位 px
    var scroll_events = "mousewheel DOMMouseScroll MozMousePixelScroll";  // 鼠标滚轮滚动事件名
    $(".imgs-div").on(scroll_events, function (e) {
        var delta = e.originalEvent.wheelDelta;  // 鼠标滚轮滚动度数
        // 滑轮向上滚动，滚动条向左移动，scrollleft-
        if (delta > 0) {
            $(".imgs-div").scrollLeft($(".imgs-div").scrollLeft() - scroll_width);
            // 这里的两个html是指包含横向滚动条的那一层
        }
        // 滑轮向下滚动，滚动条向右移动，scrollleft+
        else {
            $(".imgs-div").scrollLeft($(".imgs-div").scrollLeft() + scroll_width);
        }
        return false;
    });
</script>
