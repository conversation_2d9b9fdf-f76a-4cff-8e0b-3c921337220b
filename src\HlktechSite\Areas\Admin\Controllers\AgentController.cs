﻿using System.ComponentModel;
using System.Dynamic;

using DG.Cube;
using DG.Cube.BaseControllers;

using DH.Models;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife.Data;

using Pek;
using Pek.Models;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>代理商申请管理</summary>
[DisplayName("代理商申请")]
[Description("用于代理商申请管理")]
[AdminArea]
[DHMenu(45,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/Agent", CurrentMenuName = "AgentList", CurrentIcon = "&#xe71f;", LastUpdate = "20240125")]
public class AgentController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 45;

    /// <summary>
    /// 代理商申请列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("代理商申请列表")]
    public IActionResult Index(string name, int IsOK=-1,int page = 1)
    {
        name = name.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = Agent.Searchs(name, IsOK, pages);
        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;
        viewModel.IsOK = IsOK;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name },{ "IsOK", IsOK.ToString()} });
        return View(viewModel);
    }
  
    /// <summary>
    /// 批量删除数据
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("代理商申请管理删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        Agent.DelByIds(Ids.Trim(','));

        Loger.UserLog("删除代理商申请", "删除代理商申请，Ids：" + Ids);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 打开查看详情 修改状态页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("打开查看详情 修改状态页面")]
    public IActionResult UpdetaDetails(int Id)
    {
        var Model = Agent.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除!"));
        }
        return View(Model);
    }

    /// <summary>
    /// 打开查看详情 修改状态页面
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Status"></param>
    /// <param name="Name"></param>
    /// <param name="ContactPerson"></param>
    /// <param name="ContactAddress"></param>
    /// <param name="Phone"></param>
    /// <param name="Email"></param>
    /// <param name="Summary"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("打开查看详情 修改状态页面")]
    public IActionResult UpdetaDetails(int Id,int Status, string Name, string ContactPerson, string ContactAddress, string Phone, string Email, string Summary)
    {
        var Model = Agent.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除!"));
        }
        Model.IsThrough = Status == 1;
        Model.CompanyName = Name;
        Model.ContactPerson = ContactPerson;
        Model.ContactAddress = ContactAddress;
        Model.Phone = Phone;
        Model.Email = Email;
        Model.Summary = Summary;
        Model.Update();
        return MessageTip(GetResource("保存成功 "));
    }
}

