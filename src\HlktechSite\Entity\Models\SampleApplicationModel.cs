﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>样品申请</summary>
public partial class SampleApplicationModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>公司名称</summary>
    public String? ComName { get; set; }

    /// <summary>企业人数</summary>
    public String? ComPeople { get; set; }

    /// <summary>企业官网</summary>
    public String? Website { get; set; }

    /// <summary>年营业额</summary>
    public String? Turnover { get; set; }

    /// <summary>企业类型</summary>
    public String? ComType { get; set; }

    /// <summary>企业地址</summary>
    public String? Address { get; set; }

    /// <summary>联系人</summary>
    public String? Linkman { get; set; }

    /// <summary>职位</summary>
    public String? Position { get; set; }

    /// <summary>电话</summary>
    public String? Phone { get; set; }

    /// <summary>QQ</summary>
    public String? QQ { get; set; }

    /// <summary>邮箱</summary>
    public String? Email { get; set; }

    /// <summary>传真</summary>
    public String? Fax { get; set; }

    /// <summary>产品型号</summary>
    public String? ProModel { get; set; }

    /// <summary>月需求量</summary>
    public String? Demand { get; set; }

    /// <summary>申请主题</summary>
    public String? Theme { get; set; }

    /// <summary>需求描述 </summary>
    public String? Describe { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISampleApplication model)
    {
        Id = model.Id;
        ComName = model.ComName;
        ComPeople = model.ComPeople;
        Website = model.Website;
        Turnover = model.Turnover;
        ComType = model.ComType;
        Address = model.Address;
        Linkman = model.Linkman;
        Position = model.Position;
        Phone = model.Phone;
        QQ = model.QQ;
        Email = model.Email;
        Fax = model.Fax;
        ProModel = model.ProModel;
        Demand = model.Demand;
        Theme = model.Theme;
        Describe = model.Describe;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
