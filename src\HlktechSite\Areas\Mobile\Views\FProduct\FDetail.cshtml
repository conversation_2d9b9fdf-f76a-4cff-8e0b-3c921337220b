﻿@model JumpProduct
@{
    var id = (Int32)ViewBag.Id;
    var modelJumpProductLan = JumpProductLan.FindByJIdAndLId(id, language.Id);
}
<!DOCTYPE html>
<html style="height: 100%;">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover" />
    <title>@modelJumpProductLan?.Name - @T("成品新品推荐") - @T("海凌科电子")</title>
    <meta name="description" content="海凌科下载中心为您提供WIFI模块,电源模块,蓝牙模块,人脸识别模块,语音识别模块,3G模块,4G模块,串口服务器,GPRS模块,工业级路由器等产品的资料下载" />
    <meta name="keywords" content="@(modelJumpProductLan?.Name)开发资料下载,@(modelJumpProductLan?.Name)应用软件下载,@(modelJumpProductLan?.Name)通用软件下载" />
    <link href="~/css/weui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>
    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/lay/modules/jquery.js"></script>
    <script src="~/static/usercenter/pc/js/jquery-1.12.2.min.js"></script>
    <link href="~/iconfont/iconfont.css" rel="stylesheet" />
    <style type="text/css">
        .mytitle {
            text-align: center;
        }

        .tobuy {
            height: 50px;
            background-color: #FAFAFA;
        }

            .tobuy a {
                display: inline-block;
                text-align: center;
                height: 36px;
                line-height: 36px;
                color: #1967D2;
                font-size: 15px;
                margin-top: 7px;
                border: 1px solid #DADCE0;
                border-radius: 5px;
                padding: 0px 23px;
                margin-left: 22px;
            }

                .tobuy a:hover {
                    background-color: #E4ECFA;
                }

            .tobuy img {
                display: inline-block;
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }

        .dginfo {
            text-align: center;
            margin-top: 100px;
        }

            .dginfo div a {
                border: 1px solid #DADCE0;
                border-radius: 5px;
                height: 36px;
                line-height: 36px;
                padding: 10px;
            }
    </style>
</head>
<body>
    <div>
        <div style="width:100%;height:10px;" hidden="hidden" id="moduleId">@Model?.Id</div>
        <div style="width:100%;height:10px;"></div>
        <span class="layui-breadcrumb" style="visibility: visible; margin-left:20px;">
            <a href="@(ViewBag.IsCanBack ? "javascript:history.go(-1);" : Url.Action("Index", "FProduct"))">@T("返回")</a><span lay-separator="">/</span>
            <a><cite><Strong>@(modelJumpProductLan?.Name)</Strong></cite></a>
        </span>
        <div class="dginfo">
            <div>
                <a href="@Url.Action("Tui", "FProduct", new { Id = Model?.Id })">@T("产品介绍")</a>
            </div>
            <div style="margin-top: 100px;">
                <a href="@Url.Action("App", "FProduct", new { Id = Model?.Id })">@T("APP下载")</a>
            </div>
        </div>
    </div>
    <script>
        //注意：折叠面板 依赖 element 模块，否则无法进行功能性操作
        layui.use(['element', 'layer'], function () {
            var element = layui.element;
            var layer = layui.layer;

            reqResourceInfo();
        });

        function reqResourceInfo() {

        }
    </script>
</body>
</html>