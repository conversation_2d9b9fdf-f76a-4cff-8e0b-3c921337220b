﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>页面导航翻译</summary>
[Serializable]
[DataObject]
[Description("页面导航翻译")]
[BindIndex("IU_DG_NavigationLan_NId_LId", true, "NId,LId")]
[BindTable("DG_NavigationLan", Description = "页面导航翻译", ConnName = "DG", DbType = DatabaseType.None)]
public partial class NavigationLan : INavigationLan, IEntity<INavigationLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _NId;
    /// <summary>页面导航Id</summary>
    [DisplayName("页面导航Id")]
    [Description("页面导航Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("NId", "页面导航Id", "")]
    public Int32 NId { get => _NId; set { if (OnPropertyChanging("NId", value)) { _NId = value; OnPropertyChanged("NId"); } } }

    private Int32 _LId;
    /// <summary>关联所属语言Id</summary>
    [DisplayName("关联所属语言Id")]
    [Description("关联所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "关联所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>页面导航标题</summary>
    [DisplayName("页面导航标题")]
    [Description("页面导航标题")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Name", "页面导航标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Url;
    /// <summary>页面导航链接</summary>
    [DisplayName("页面导航链接")]
    [Description("页面导航链接")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Url", "页面导航链接", "")]
    public String? Url { get => _Url; set { if (OnPropertyChanging("Url", value)) { _Url = value; OnPropertyChanged("Url"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(INavigationLan model)
    {
        Id = model.Id;
        NId = model.NId;
        LId = model.LId;
        Name = model.Name;
        Url = model.Url;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "NId" => _NId,
            "LId" => _LId,
            "Name" => _Name,
            "Url" => _Url,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "NId": _NId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Url": _Url = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得页面导航翻译字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>页面导航Id</summary>
        public static readonly Field NId = FindByName("NId");

        /// <summary>关联所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>页面导航标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>页面导航链接</summary>
        public static readonly Field Url = FindByName("Url");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得页面导航翻译字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>页面导航Id</summary>
        public const String NId = "NId";

        /// <summary>关联所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>页面导航标题</summary>
        public const String Name = "Name";

        /// <summary>页面导航链接</summary>
        public const String Url = "Url";
    }
    #endregion
}
