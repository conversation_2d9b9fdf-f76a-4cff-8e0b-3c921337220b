﻿
.detail-panel {
    border: 1px solid #eee;
    background-color: #f5f9fa;
    padding: 20px;
}

.detail-item {
    width: 50%;
}

.detail-tier {
    margin-top: 10px;
}
/* 娌熼€氳褰� */
.talk-container-head {
    height: 36px;
    border-bottom: 1px solid #eee;
    background-color: #f5f9fa;
    padding-left: 20px;
    line-height: 36px;
    font-size: 14px;
}

    .talk-container-head > a {
        margin-right: 30px;
        font-weight: 400;
        font-size: 12px;
        color: #636566;
    }

        .talk-container-head > a:hover {
            text-decoration: none;
        }

        .talk-container-head > a .filter-icon {
            margin-left: 6px;
        }

.talk-item,
.talk-content {
    width: 100%;
}

.talk-item {
    padding: 30px 25px;
}

.talk-user-img {
    width: 50px;
    height: 50px;
}

    .talk-user-img.user-record,
    .talk-user-img.official-record {
        float: left;
        padding-right: 0 !important;
        background-size: cover;
    }

.talk-problem {
    float: left;
    padding-left: 20px;
    padding-top: 10px;
}

    .talk-problem h4 {
        font-size: 12px;
        font-weight: bold;
        color: #555;
    }

.staff-member.talk-item h4 {
    color: #00aaff;
}

.talk-problem-text {
    margin: 10px 0;
    font-size: 12px;
    color: #999999;
}

.staff-member.talk-item .talk-problem-text {
    color: #555;
}

.talk-time {
    float: right;
    font-size: 12px;
    color: #999;
}

.staff-member.talk-item {
    background-color: #f0faff;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.workorder-attachment-container .attachment-item {
    width: 90px;
    height: 90px;
}

.attachment-container {
    float: left;
    position: relative;
    width: 130px;
    height: 90px;
    border: 1px solid #d9dcde;
    background: url(img/attachment-icon.png) no-repeat center center;
}

    .attachment-container > a {
        position: absolute;
        left: -1px;
        bottom: -30px;
        width: 131px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #00aaff;
        border: 1px solid #00aaff;
    }

        .attachment-container > a:hover {
            text-decoration: none;
        }

.attachment-container-tips {
    padding-top: 12px;
    padding-left: 68px;
}

.talk-btn {
    margin-top: 30px;
    margin-left: 68px;
}
/* 绯荤粺閫氱煡鏉� */
.system-tip {
    position: relative;
    z-index: 1;
    padding-left: 20px;
    background: url(img/tip_icon_info_16.png) no-repeat left center;
    font-size: 12px;
}

    .system-tip:before,
    .system-tip:after {
        content: "";
        position: absolute;
        top: 50%;
        width: 80px;
        height: 1px;
        background-color: #ddd;
    }

    .system-tip:before {
        left: -90px;
    }

    .system-tip:after {
        right: -90px;
    }
/* 瀵硅瘽妗� */
.talk-record-box {
    padding: 20px 30px;
    font-size: 14px;
}

.talk-content-row {
    word-break: break-all;
}

.talk-record {
    max-width: 80%;
    min-height: 80px;
    margin-bottom: 20px;
    background-repeat: no-repeat;
}

.user-record {
    float: right;
    padding-right: 100px;
    background: url(img/avatar_talk_user_new.png) no-repeat right top;
}

    .user-record .talk-user {
        text-align: right;
    }

.talk-content {
    display: inline-block;
    position: relative;
    z-index: 1;
    border-radius: 2px;
    margin-top: 10px;
}

    .talk-content .talk-record-img {
        max-width: 100%;
        max-height: 300px;
    }

    .talk-content .service-img {
        width: 100px;
        height: 100px;
    }

.official-record {
    background: url(img/avatar_talk_system_new.png) no-repeat center center;
}

.official-secretary {
    background-image: url(img/avatar_talk_secretary.png);
}

.official-engineer {
    position: relative;
    z-index: 1;
    background-image: url(img/avatar_talk_man.png);
}

.engineer-avatar {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    width: 80px;
    height: 80px;
}

.official-record .talk-content {
    border: 1px solid #5ce577;
    background-color: #d9ffe0;
}

    .official-record .talk-content:before,
    .official-record .talk-content:after {
        content: "";
        position: absolute;
        z-index: 1;
        border: 12px solid transparent;
        border-right-color: #5ce577;
        border-top-width: 7px;
        border-bottom-width: 7px;
    }

    .official-record .talk-content:before {
        left: -24px;
        top: 10%;
    }

    .official-record .talk-content:after {
        left: -22px;
        top: 10%;
        border-right-color: #d9ffe0;
    }

.official-name {
    color: #ff8800;
}

.user-record .talk-content {
    border: 1px solid #00bbff;
    background-color: #d9f5ff;
}

    .user-record .talk-content:before,
    .user-record .talk-content:after {
        content: "";
        position: absolute;
        z-index: 1;
        border: 12px solid transparent;
        border-left-color: #00bbff;
        border-top-width: 7px;
        border-bottom-width: 7px;
    }

    .user-record .talk-content:before {
        right: -24px;
        top: 10%;
    }

    .user-record .talk-content:after {
        right: -22px;
        top: 10%;
        border-left-color: #d9f5ff;
    }

.talk-person span:first-child {
    margin-right: 15px;
}

.talk-user-name {
    color: #00bbff;
}

.talk-attachments {
    margin-top: 10px;
}

    .talk-attachments img {
        width: 120px;
        height: 90px;
        margin-right: 10px;
        background-color: #fff;
    }
/* 宸ュ叿鏉� */
.talk-tool-bar {
    height: 32px;
    line-height: 32px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    background-color: #f5f9fa;
}

.change-font {
    display: inline-block;
    width: 25px;
    height: 25px;
    background: url(img/change_font.png) no-repeat center;
    margin-left: 10px;
    margin-top: 3px;
}

    .change-font:hover {
        background-color: #e5e9ea;
    }
/* 杈撳叆妗� */
.textarea-container {
    padding-left: 10px;
    overflow: hidden;
}

.talk-textarea {
    width: 100%;
    padding: 15px 20px;
    resize: none;
    outline: none;
    border: 1px solid #d9dcde;
    line-height: 2em;
}

.textarea-desc {
    font-size: 12px;
    color: #999;
}

    .textarea-desc .ny-checkbox {
        margin-top: 10px;
        margin-left: 20px;
    }
/* 鎺ㄩ€佷俊鎭� */
.push-service {
    display: inline-block;
    padding: 10px;
    border: 1px solid #ddd;
    background-color: #fff;
    font-size: 12px;
}

.push-service-picture {
    width: 110px;
    height: 80px;
}

.push-buy {
    height: 20px;
    padding: 0 5px;
    font-size: 12px;
    line-height: 20px;
}

.push-info {
    padding-top: 4px;
    line-height: 2em;
}

.service-description {
    display: inline-block;
    max-width: 200px;
}
