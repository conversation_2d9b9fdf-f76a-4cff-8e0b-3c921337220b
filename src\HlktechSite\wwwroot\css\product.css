﻿
.title1 {
    color: #333 !important;
    background-color: #fff;
}

    .title1:hover {
        background-color: #eee !important;
    }

.title2 {
    color: #fff !important;
    background-color: #337ab7;
}

    .title2:hover {
        background-color: #337ab7 !important;
    }



.top > img {
    width: 100%;
}

.top {
    position: relative;
}

    .top > div {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        bottom: 0px;
        color: #FFFFFF;
    }

        .top > div > h2 {
            margin-top: 149px;
            text-align: center;
            font-size: 56px;
        }

        .top > div > p {
            width: 844px;
            margin: 0 auto;
            margin-top: 35px;
            text-align: center;
            font-size: 20px;
        }





.navigation {
    position: relative;
    width: 100%;
    height: 65px;
}

    .navigation::after {
        content: '';
        position: absolute;
        left: 0px;
        right: 0px;
        bottom: 0px;
        height: 1px;
        width: 100%;
        background-color: #EEEEEE;
    }

.navigation-con {
    position: relative;
    width: 1200px;
    margin: 0 auto;
}

.navigation-con-left {
    margin-top: 21px;
    float: left;
}

    .navigation-con-left a {
        color: #333333;
    }

        .navigation-con-left a:hover {
            text-decoration: none;
        }


.navigation-con-right {
    position: absolute;
    margin-top: 14px;
    right: 0px;
}

.input-group {
    margin-left: 21px;
    width: 279px;
}

.navigation-con-right .input-group {
    float: right;
}

.navigation-con-right .input-group-addon {
    background-color: #4E6EF2;
    border-radius: 0px 10px 10px 0px;
}

.Pro-intr {
    padding-top: 56px;
    width: 1200px;
    margin: 0 auto;
    padding-bottom: 104px;
}

    .Pro-intr > div {
        display: inline-block;
    }
.border {
    border: 1px solid rgba(0,0,0,.05);
    width: 420px;
    margin: 0 auto;
}
.Pro-intr-BigImg {
    width: 100%;
}

.Pro-intr ul {
    position: relative;
    overflow: hidden;
    padding-left: 0px;
    width: max-content;
    height: 122px;
    margin-top: 36px;
    left: 0px;
}

.Pro-intr .imgs-div {
    max-width: 637px;
    overflow: auto;
}

    .Pro-intr .imgs-div::-webkit-scrollbar {
        height: 10px;
        background-color: #ccc;
        border-radius: 10px;
    }

    .Pro-intr .imgs-div::-webkit-scrollbar-thumb {
        background-color: blue;
        background-clip: padding-box;
        border-radius: 10px;
        -webkit-transition: all .5s;
        -moz-transition: all .5s;
        transition: all .5s;
    }

.Pro-intr ul li {
    list-style-type: none;
    display: inline-block;
    position: relative;
    border: 1px solid #ccc;
    width: 88px;
    cursor: pointer;
    margin-right: 15px;
}

    .Pro-intr ul li:last-child {
        margin-right: 0px;
    }

.Pro-intr ul img {
    width: 100%;
}

.Pro-intr ul li.selected {
    border-color: blue;
}

.Pro-intr-right {
    float: right;
    width: 660px;
}

    .Pro-intr-right h2 {
        margin: 0px;
        font-size: 24px;
        font-weight: 700;
        color: #333333;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        word-break: break-all;
        white-space: pre-wrap;
    }

    .Pro-intr-right span, .Pro-intr-right b, .Pro-intr-right i, .Pro-intr-right p {
        font-size: 18px;
        font-weight: 400;
        font-style: normal;
        color: #333333;
    }

    .Pro-intr-right span {
        display: inline-block;
        margin-top: 17px;
        width: 100%;
    }

    .Pro-intr-right b {
        margin-top: 13px;
        display: inline-block;
    }

    .Pro-intr-right i {
        color: #FF0938;
    }

    .Pro-intr-right p {
        margin-top: 24px;
    }

.Pro-intr-right-menu a {
    display: inline-block;
    width: 134px;
    height: 39px;
    border-radius: 4px;
    background-color: #409EFF;
    line-height: 39px;
    text-align: center;
    color: #FFFFFF;
    margin-right: 18px;
    font-size: 16px;
    vertical-align: bottom;
}

    .Pro-intr-right-menu a:hover {
        text-decoration: none !important;
    }

    .Pro-intr-right-menu a:last-child {
        background-color: #F56C6C;
    }

.Pro-detailed-top {
    position: relative;
}

    .Pro-detailed-top:after {
        content: "";
        display: block;
        left: 0px;
        right: 0px;
        bottom: 0px;
        height: 2px;
        background-color: #EEEEEE;
    }

.Pro-detailed ul {
    overflow: hidden;
    width: 1200px;
    padding: 0px;
    margin: 0 auto;
}

.Pro-detailed li {
    list-style-type: none;
    float: left;
    width: 100px;
    text-align: center;
    height: 43px;
    font-size: 16px;
    position: relative;
}

    .Pro-detailed li:after {
        content: "";
        position: absolute;
        left: 0px;
        bottom: 0px;
        width: 0%;
        height: 3px;
        transition: all 0.3s;
        -moz-transition: all 0.3s;
        -webkit-transition: all 0.3s;
        -o-transition: all 0.3s;
    }

.liSelected {
    color: #005BAA;
}

.Pro-detailed .liSelected:after {
    background-color: #005BAA;
    width: 100%;
}


.Pro-detailed li:hover {
    cursor: pointer;
}

.trait-con {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 300px;
    padding-top: 67px;
}

.trait-con-bottom {
    height: 71px;
    width: 950px;
    margin: 0 auto;
}

.pageTurning {
    padding-bottom: 66px;
}


    .pageTurning a {
        display: inline-block;
        font-size: 16px;
        color: #434343;
        margin-top: 15px;
        cursor: pointer;
    }

        .pageTurning a:hover {
            text-decoration: none;
        }

        .pageTurning a:last-child {
            margin-top: 8px;
        }

.bdsharebuttonbox {
    display: inline-block;
}

.parameter.cut {
    width: 1200px;
    margin: 0 auto;
}

.cut {
    display: none;
}

.trait {
    display: block;
}
/*.ditch {
    display: block;
}*/

.parameter-con {
    width: 1231px;
    display: flex;
    flex-direction: row;
    min-height: 100px;
}

    .parameter-con > div {
        text-align: center;
        flex: 1
    }

.parameter-con-details {
    flex: none;
    width: 706px;
}

.parameter table {
    width: 100%;
    margin-top: 81px;
    margin: auto;
    border-collapse: collapse;
    text-align: center;
    border: none;
    border-bottom: 0;
    border-left: 0;
    margin-bottom: 80px;
}

    .parameter table td {
        border: 1px solid #CCC;
        vertical-align: middle;
    }

        .parameter table td p {
            margin: 15px 0 15px 0;
        }

.keepleft {
    text-align: left;
    padding-left: 20px;
}

.lastTd {
    width: 706px;
    height: 54px;
}

.datum ul {
    padding-top: 23px;
}

.datum li {
    display: block;
    width: 100%;
    height: auto;
    line-height: 64px;
    padding-left: 9px;
    position: relative;
    padding-top: 20px;
    padding-bottom: 20px;
}

    .datum li:before {
        content: "";
        display: block;
        height: 2px;
        line-height: 64px;
        padding-left: 9px;
        position: absolute;
        left: 0px;
        right: 0px;
        bottom: 0px;
        background-color: #d9dada;
    }

.have-backgroup {
    background-color: #f7f9Fa;
}

.cut {
    padding-bottom: 70px;
}

.datum li {
    display: flex;
}

    .datum li span:first-child {
        flex: 1;
        text-align: left;
        font-size: 18px;
        font-weight: bold;
        color: #444;
    }

    .datum li span:last-child {
        text-align: left;
        flex: 3
    }

        .datum li span:last-child > i {
            font-style: normal;
            font-size: 18px;
            display: inline-block;
            padding-left: 30px;
            font-weight: 400;
        }


            .datum li span:last-child > i img {
                margin-left: 14px;
                vertical-align: middle;
                margin-bottom: 3px;
            }


.exchange ul, .scheme ul {
    padding-top: 80px;
    font-size: 16px;
    color: #333;
    font-weight: 400;
}


    .exchange ul li, .scheme ul li {
        border-bottom: 2px solid #EEEEEE;
        padding-left: 9px;
        display: flex;
        float: none;
        text-align: left;
        width: 100%;
        height: 64px;
        line-height: 64px;
    }

        .exchange ul li:hover, .scheme ul li:hover {
            cursor: auto;
            background-color: #dddddd;
        }

        .exchange ul li span, .scheme ul li span {
            flex: 1;
        }

            .exchange ul li span:last-child, .scheme ul li span:last-child {
                text-align: right;
            }

                .exchange ul li span:last-child > img, .scheme ul li span:last-child > img {
                    margin-bottom: 3px;
                    margin-right: 6px;
                }

                .exchange ul li span:last-child > a, .scheme ul li span:last-child > a {
                    margin-left: 41px;
                    margin-right: 32px;
                    color: #333;
                }

                    .exchange ul li span:last-child > a:hover, .scheme ul li span:last-child > a:hover {
                        text-decoration: none;
                    }

.ditch {
    background-color: #f7f9Fa;
}

    .ditch > div {
        display: flex;
        max-width: 1200px;
        margin: 0 auto;
        padding-top: 50px;
        border-bottom: 2px solid #EEEEEE;
    }

        .ditch > div:first-child {
            padding-bottom: 47px;
        }

        .ditch > div:last-child {
            padding-bottom: 34px;
        }

        .ditch > div > span:first-child {
            font-size: 18px;
            color: #444;
            width: 207px;
            font-weight: bold;
        }


        .ditch > div > span:last-child {
            flex: 1;
            font-size: 16px;
        }



            .ditch > div > span:last-child > a {
                font-size: 16px;
                color: #666666;
            }

                .ditch > div > span:last-child > a:hover {
                    text-decoration: none;
                }

                .ditch > div > span:last-child > a > img {
                    margin-left: 18px;
                    margin-right: 7px;
                }

    .ditch i {
        padding-left: 18px;
        color: #444;
        display: block;
        font-style: normal;
        font-size: 18px;
        margin-bottom: 22px;
    }

        .ditch i:last-child {
            margin-bottom: 0px;
        }

        .ditch i b {
            font-size: 14px;
            font-weight: normal;
        }

        .ditch i a {
            margin-left: 15px;
            font-size: 18px;
        }

            .ditch i a:hover {
                text-decoration: none;
            }






/*产品中心样式*/
.detail-nav-top {
    position: relative;
    border-bottom: 1px solid #eeeeee;
}

    .detail-nav-top > div {
        width: 1178px;
        margin: 0 auto;
    }

        .detail-nav-top > div > div > div {
            display: block;
            position: absolute;
            left: 0px;
            right: 0px;
            background-color: #fff;
            height: 57px;
        }

            .detail-nav-top > div > div > div > ul {
                padding-left: 84px;
                width: 1178px;
                margin: 0 auto;
                height: 57px;
            }

                .detail-nav-top > div > div > div > ul > li {
                    line-height: 57px;
                    list-style: none;
                    min-width: 125px;
                    float: left;
                    text-align: center;
                    color: #90949A;
                    height: 57px;
                    margin-right: 4.9px;
                    position: relative;
                }

                    .detail-nav-top > div > div > div > ul > li > a {
                        height: 48px;
                        position: relative;
                        width: auto;
                        padding-left: 6px;
                        padding-right: 6px;
                    }

                    .detail-nav-top > div > div > div > ul > li:hover::after {
                        content: "";
                        position: absolute;
                        left: 0px;
                        right: 0px;
                        bottom: 0px;
                        height: 2px;
                        background-color: #414141;
                    }


.detail-nav .input-group-addon {
    cursor: pointer;
    background-color: #4E6EF2;
}


.detail-nav-top > div {
    display: flex;
    padding-top: 14px;
}

    .detail-nav-top > div > div:first-child {
        line-height: 34.44px;
        flex: 1;
        font-size: 16px;
    }

    .detail-nav-top > div > div:last-child {
        width: 280px;
    }

    .detail-nav-top > div i {
        font-style: normal;
    }

    .detail-nav-top > div a {
        display: inline-block;
        height: 40px;
        line-height: 40px;
        position: relative;
        color: #90949A;
        text-decoration: none;
        min-width: 84px;
        padding-left: 10px;
        padding-right: 10px;
        text-align: center;
    }

        .detail-nav-top > div a:hover, .detail-nav-top > div a:visited {
            text-decoration: none;
        }

.selected {
    color: #343434 !important;
}

    .selected:after {
        content: "";
        position: absolute;
        left: 0px;
        right: 0px;
        bottom: 0px;
        height: 2px;
    }

.detail-nav > p {
    padding-left: 84px;
    width: 1178px;
    margin: 0 auto;
}

    .detail-nav > p > a {
        height: 57px;
        display: inline-block;
        width: 125px;
        margin: 0px;
        text-align: center;
        line-height: 57px;
        color: #90949A;
        font-size: 16px;
        text-decoration: none;
    }


        .detail-nav > p > a:hover {
            text-decoration: none;
        }

.detail-list {
    padding-top: 75px;
    padding-bottom: 46px;
    background-color: #FAFAFA;
}

    .detail-list > div {
        width: 1200px;
        margin: 0 auto;
    }

    .detail-list a {
        text-decoration: none !important;
    }

    .detail-list div div {
        width: 273px;
        display: inline-block;
        padding-bottom: 18px;
        background-color: #fff;
        box-shadow: 1px 6px 19px 2px rgba(177,174,174,0.2);
        border: 1px solid #fff;
        margin-right: 31px;
        margin-bottom: 53px;
    }

    .detail-list > div > a:nth-child(4n) > div {
        margin-right: 0px;
    }

    .detail-list div div:hover {
        border: 1px solid #4E6EF2;
        box-shadow: 0px 6px 19px 1px rgba(177,174,174,0.4);
    }

    .detail-list div div img {
        width: 100%;
    }

    .detail-list div div p {
        color: #333333;
        height: 43px;
        padding: 3px 27.3px 0px 11px;
        margin-bottom: 0px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        word-break: break-all;
        word-wrap: break-word;
        white-space: pre-wrap;
    }

        .detail-list div div p a {
            color: #333333;
            display: block;
            height: 60px;
            padding: 3px 27.3px 17px 11px;
        }

            .detail-list div div p a:hover {
                color: #333333;
                text-decoration: none;
            }

    .detail-list div div i {
        display: block;
        padding-left: 11px;
        font-style: normal;
        padding-top: 17px;
        color: #F56C6C;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
    }


/*图片放大镜*/
.magnify {
    width: 314px;
    position: relative;
    margin: 0 auto;
}
/* 在这里我们来设置放大镜的样式 */

.large {
    width: 180px;
    height: 180px;
    position: absolute;
    z-index: 2;
    border-radius: 100%;
    box-shadow: 0 0 0 7px rgba(255,255,255,0.8), 0 0 7px 7px rgba(0,0,0,0.3), inset 0 0 40px 2px rgba(0,0,0,0.3);
    display: none;
    background-repeat: no-repeat !important;
}
/* 解决放大镜在放大的过程中边缘重叠的BUG */

.small {
    display: block;
}
