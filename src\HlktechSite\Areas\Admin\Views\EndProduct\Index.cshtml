﻿@{
}
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>

<script src="~/static/plugins/js/layui/layui.js"></script>
<script src="/static/plugins/mlselectionnew.js"></script>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("成品管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                @*<li><a href="@Url.Action("CreateGoods")"><span>@T("添加")</span></a></li>*@
                <li><a href="@Url.Action("CreateGoodsOne")"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("标题")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
                <dt>@T("是否推荐")</dt>
                <dd>
                    <select name="commend">
                        <!option value="0" @(Model.commend==0?"selected":"")>@T("全部")</!option>
                        <!option value="1" @(Model.commend==1?"selected":"")>@T("推荐产品")</!option>
                        <!option value="2" @(Model.commend==2?"selected":"")>@T("非推荐产品")</!option>
                    </select>
                </dd>
            </dl>
            @*<dl>
                    <dt>分类</dt>
                    <dd id="searchgc_td"></dd>
                    <input type="hidden" id="choose_gcid" name="choose_gcid" value="0" />
                </dl>*@
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("编号")</th>
                <th>@T("品牌&分类")</th>
                <th></th>
                <th>@T("广告词")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in (IEnumerable<HlktechSite.Entity.EndProducts>)ViewBag.list)
            {
                <tr id="<EMAIL>" style="background: rgb(255, 255, 255);">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td>@item.Id</td>

                    <td class="w60 picture"><div class="size-56x56"><span class="thumb size-56x56"><i></i><img src="@item.Image" width="56" height="56" /></span></div></td>
                    <td>
                        <dl class="goods-info">
                            <dt class="goods-name">@item.Name</dt>
                            <dd class="goods-type">
                                <i class="iconfont open" title="手机端成品详情">&#xe601;</i>
                            </dd>
                            <dd class="goods-store"></dd>
                        </dl>
                    </td>
                    <td>@item.AdvWord</td>
                    <td>
                        @*<a href="@Url.Action("EditGoods",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>*@
                        <a href="@Url.Action("UpdateGoods",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        @*<a href="javascript:;" onclick="submit_delete('@item.Id')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>*@
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>

<script asp-location="Footer">
     var CreateImg = "@Url.Action("UploadImg")";
    function submit_delete(IDS)
    {
        if (confirm("确定删除?")) {
            $.post("@Url.Action("Delete")", { Ids: IDS }, function (res) {
            if (!res.success) {
                alert(res.msg);
            } else {
                window.location.reload(); //刷新页面
            }

        })
        }


    }
     @*function submit_delete(ids_str) {
         _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }*@

     $(function () {
        var data2 = $.parseJSON('@Html.Raw(Model.jsonlist)');
         //init_gcselect([], data2);
    })
</script>
