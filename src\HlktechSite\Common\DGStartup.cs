﻿using DG;

using DH;
using DH.Entity;
using DH.SLazyCaptcha;
using DH.SLazyCaptcha.Generator;
using DH.WebHook;

using HlktechSite.Areas.Admin.Controllers;

using NewLife;
using NewLife.Caching;
using NewLife.Common;

using Pek.Infrastructure;
using Pek.VirtualFileSystem;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite;

public class DGStartup : IPekStartup
{
    public void Configure(IApplicationBuilder application)
    {
        var set = SysConfig.Current;
        if (set.Name == "NewLife.Cube" || set.DisplayName == "DG.Web.Framework.Views" || set.DisplayName == "创楚平台")
        {
            set.DisplayName = "DGSoft系统后台";
            set.Save();
        }

        LocaleStringResource.InitInsert("底部左下角联系方式",
            " <dl class=\"footer-article-item\" style=\"width: 339px;\"><dt>联系我们</dt><dd><span href=\"#\">电话 : 0755-********；0755-83575196；<span></span></span></dd><dd><span href=\"#\">传真 : 0755-********</span></dd><dd><span href=\"#\">地址 : 深圳龙华民治留仙大道24号彩悦大厦西大门三楼</span></dd><div class=\"henxian\"></div><dd><span href=\"#\">武汉分公司： 武汉极思灵创科技有限公司</span></dd><dd><span href=\"#\">地址： 湖北省武汉市洪山区街道口樱花大厦A座503</span></dd><dd><span href=\"#\">电话：027-********</span></dd></dl>", 
            " <dl class=\"footer-article-item\" style=\"width: 339px;\"><dt>联系我们</dt><dd><span href=\"#\">电话 : 0755-********；0755-83575196；<span></span></span></dd><dd><span href=\"#\">传真 : 0755-********</span></dd><dd><span href=\"#\">地址 : 深圳龙华民治留仙大道24号彩悦大厦西大门三楼</span></dd><div class=\"henxian\"></div><dd><span href=\"#\">武汉分公司： 武汉极思灵创科技有限公司</span></dd><dd><span href=\"#\">地址： 湖北省武汉市洪山区街道口樱花大厦A座503</span></dd><dd><span href=\"#\">电话：027-********</span></dd></dl>",
            " <dl class=\"footer-article-item\" style=\"width: 339px;\"><dt>联系我们</dt><dd><span href=\"#\">电话 : 0755-********；0755-83575196；<span></span></span></dd><dd><span href=\"#\">传真 : 0755-********</span></dd><dd><span href=\"#\">地址 : 深圳龙华民治留仙大道24号彩悦大厦西大门三楼</span></dd><div class=\"henxian\"></div><dd><span href=\"#\">武汉分公司： 武汉极思灵创科技有限公司</span></dd><dd><span href=\"#\">地址： 湖北省武汉市洪山区街道口樱花大厦A座503</span></dd><dd><span href=\"#\">电话：027-********</span></dd></dl>");

        //LocaleStringResource.InitInsert("产品服务", "<dl class=\"footer-article-item\" style=\"padding-left: 40px;\"><dt>产品服务</dt><dd><a href=\"#\">智能硬件开发</a></dd><dd><a href=\"#\">物联网开发</a></dd><dd><a href=\"#\">大数据分析</a></dd><dd><a href=\"#\">软件开发</a></dd><dd><a href=\"http://h.hlktech.com/mobile/download\">资料下载</a></dd><dd><a href=\"http://h.hlktech.com/apply/apply\">定制申请</a></dd> </dl>", "<dl class=\"footer-article-item\" style=\"padding-left: 40px;\"><dt>产品服务</dt><dd><a href=\"#\">智能硬件开发</a></dd><dd><a href=\"#\">物联网开发</a></dd><dd><a href=\"#\">大数据分析</a></dd><dd><a href=\"#\">软件开发</a></dd><dd><a href=\"http://h.hlktech.com/mobile/download\">资料下载</a></dd><dd><a href=\"http://h.hlktech.com/apply/apply\">定制申请</a></dd> </dl>", "<dl class=\"footer-article-item\" style=\"padding-left: 40px;\"><dt>产品服务</dt><dd><a href=\"#\">智能硬件开发</a></dd><dd><a href=\"#\">物联网开发</a></dd><dd><a href=\"#\">大数据分析</a></dd><dd><a href=\"#\">软件开发</a></dd><dd><a href=\"http://h.hlktech.com/mobile/download\">资料下载</a></dd><dd><a href=\"http://h.hlktech.com/apply/apply\">定制申请</a></dd> </dl>");
        LocaleStringResource.InitInsert("产品服务",
            "<dl class=\"footer-article-item\" style=\"padding-left: 40px;\"><dt>产品服务</dt><dd><a target=\"_blank\" href=\"http://h.hlktech.com/mobile/download\">资料下载</a></dd><dd><a target=\"_blank\" href=\"http://h.hlktech.com/apply/apply\">定制申请</a></dd> </dl>",
            "<dl class=\"footer-article-item\" style=\"padding-left: 40px;\"><dt>产品服务</dt><dd><a target=\"_blank\" href=\"http://h.hlktech.com/mobile/download\">资料下载</a></dd><dd><a target=\"_blank\" href=\"http://h.hlktech.com/apply/apply\">定制申请</a></dd> </dl>",
            "<dl class=\"footer-article-item\" style=\"padding-left: 40px;\"><dt>产品服务</dt><dd><a target=\"_blank\" href=\"http://h.hlktech.com/mobile/download\">资料下载</a></dd><dd><a target=\"_blank\" href=\"http://h.hlktech.com/apply/apply\">定制申请</a></dd> </dl>");

        LocaleStringResource.InitInsert("关于我们",
            "<dl class=\"footer-article-item\" style=\"padding-left: 60px;\"><dt>关于我们</dt><dd><a target=\"_blank\" href=\"http://h.hlktech.com/mobile/download\">下载中心</a></dd><dd><a href=\"/Agent/AgentMarket\">联系我们</a></dd><dd><a href=\"http://h.hlktech.com/search?SearchType=2\">开放工单</a></dd><dd><a target=\"_blank\" href=\"http://h.hlktech.com/usercenter/addorderindex\" > 提交工单</a></dd><dd><a href=\"/agent/Index\">代理招商</a></dd><dd><a href=\"/Case/Index\">应用场景</a></dd></dl>",
            "<dl class=\"footer-article-item\" style=\"padding-left: 60px;\"><dt>关于我们</dt><dd><a target=\"_blank\" href=\"http://h.hlktech.com/mobile/download\">下载中心</a></dd><dd><a href=\"/Agent/AgentMarket\">联系我们</a></dd><dd><a href=\"http://h.hlktech.com/search?SearchType=2\">开放工单</a></dd><dd><a target=\"_blank\" href=\"http://h.hlktech.com/usercenter/addorderindex\" > 提交工单</a></dd><dd><a href=\"/agent/Index\">代理招商</a></dd><dd><a href=\"/Case/Index\">应用场景</a></dd></dl>",
            "<dl class=\"footer-article-item\" style=\"padding-left: 60px;\"><dt>关于我们</dt><dd><a target=\"_blank\" href=\"http://h.hlktech.com/mobile/download\">下载中心</a></dd><dd><a href=\"/Agent/AgentMarket\">联系我们</a></dd><dd><a href=\"http://h.hlktech.com/search?SearchType=2\">开放工单</a></dd><dd><a target=\"_blank\" href=\"http://h.hlktech.com/usercenter/addorderindex\" > 提交工单</a></dd><dd><a href=\"/agent/Index\">代理招商</a></dd><dd><a href=\"/Case/Index\">应用场景</a></dd></dl>");

        LocaleStringResource.InitInsert("深圳市海凌科电子有限公司",
            "<p>深圳市海凌科电子有限公司<i>(总部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：深圳龙华民治留仙大道24号彩悦大厦西大门三楼</b><b>电话：0755-********；********</b><b>传真：0755-********</b><b>网址：www.hlktech.com</b><b>邮箱：<EMAIL></b><b>业务联系QQ: ********** / ********** / **********</b></span><span><b class=\"havebold\">账户信息 :</b><b>户名：深圳市海凌科电子有限公司</b><b>开户行：中国光大银行深圳东海支行</b><b>账号：3901 0188 0000 86065</b><b class=\"havebold\">找到我们 ：</b><b>导航：彩悦大厦 西大门</b><b>地铁：深圳北站东广场旁</b></span></div>",
            "<p>深圳市海凌科電子有限公司<i>(總部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：深圳龍華民治留仙大道24號彩悅大廈西大門三樓</b><b>電話：0755-********；********</b><b>傳真：0755-********</b><b>網址：www.hlktech.com</b><b>郵箱：<EMAIL></b><b>業務聯繫QQ: ********** / ********** / **********</b></span><span><b class=\"havebold\">賬戶信息 :</b><b>戶名：深圳市海凌科電子有限公司</b><b>開戶行：中國光大銀行深圳東海支行</b><b>賬號：3901 0188 0000 86065</b><b class=\"havebold\">找到我們 ：</b><b>導航：彩悅大廈 西大門</b><b>地鐵：深圳北站東廣場旁</b></span></div>",
            "<p>Shenzhen Hilink Electronics Co., Ltd.<i>(Headquarters)</i></p><div><span><b class=\"havebold\">Company Information ：</b><b>Address: 3rd Floor, West Gate of Caiyue Building, No. 24 Liuxian Avenue, Minzhi, Longhua, Shenzhen</b><b>Phone：0755-********；********</b><b>Fax：0755-********</b><b>URL：www.hlktech.com</b><b>Headquarters：<EMAIL></b><b>Business Contact QQ: ********** / ********** / **********</b></span><span><b class=\"havebold\">account information :</b><b>Account Name: Shenzhen Hailingke Electronics Co., Ltd.</b><b>Account Bank: China Everbright Bank Shenzhen Donghai Branch</b><b>Account number：3901 0188 0000 86065</b><b class=\"havebold\">Find us ：</b><b>Navigation: Caiyue Building West Gate</b><b>Subway: Beside the East Square of Shenzhen North Railway Station</b></span></div>");

        LocaleStringResource.InitInsert("武汉极思灵创科技有限公司",
            "<p> 武汉极思灵创科技有限公司<i>(分部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：湖北省武汉市洪山区街道口樱花大厦A座503</b><b>电话：027-********</b><b>微信：***********</b><b>网址：http://www.gicisky.net</b><b>邮箱：<EMAIL></b><b>业务联系QQ: **********</b></span><span><b class=\"havebold\">账户信息 :</b><b>户名：武汉极思灵创科技有限公司</b><b>开户行：中国农业银行股份有限公司武汉洪山支行</b><b>账号：1703 8201 0400 20323</b><b class=\"havebold\">找到我们 ：</b><b>导航：街道口 樱花大厦</b><b>地铁：武汉市街道口地铁旁</b></span></div>",
            "<p> 武汉极思灵创科技有限公司<i>(分部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：湖北省武汉市洪山区街道口樱花大厦A座503</b><b>电话：027-********</b><b>微信：***********</b><b>网址：http://www.gicisky.net</b><b>邮箱：<EMAIL></b><b>业务联系QQ: **********</b></span><span><b class=\"havebold\">账户信息 :</b><b>户名：武汉极思灵创科技有限公司</b><b>开户行：中国农业银行股份有限公司武汉洪山支行</b><b>账号：1703 8201 0400 20323</b><b class=\"havebold\">找到我们 ：</b><b>导航：街道口 樱花大厦</b><b>地铁：武汉市街道口地铁旁</b></span></div>",
            "<p> 武汉极思灵创科技有限公司<i>(分部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：湖北省武汉市洪山区街道口樱花大厦A座503</b><b>电话：027-********</b><b>微信：***********</b><b>网址：http://www.gicisky.net</b><b>邮箱：<EMAIL></b><b>业务联系QQ: **********</b></span><span><b class=\"havebold\">账户信息 :</b><b>户名：武汉极思灵创科技有限公司</b><b>开户行：中国农业银行股份有限公司武汉洪山支行</b><b>账号：1703 8201 0400 20323</b><b class=\"havebold\">找到我们 ：</b><b>导航：街道口 樱花大厦</b><b>地铁：武汉市街道口地铁旁</b></span></div>");

        LocaleStringResource.InitInsert("益坤泰实业（东莞）有限公司",
            "<p>益坤泰实业（东莞）有限公司<i>(分部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：广东省东莞市东城街道狮长路3号2栋4楼</b><b>电话：18898736510</b></span><span></span></div></div>",
            "<p>益坤泰实业（东莞）有限公司<i>(分部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：广东省东莞市东城街道狮长路3号2栋4楼</b><b>电话：18898736510</b></span><span></span></div></div>",
            "<p>益坤泰实业（东莞）有限公司<i>(分部)</i></p><div><span><b class=\"havebold\">公司信息 ：</b><b>地址：广东省东莞市东城街道狮长路3号2栋4楼</b><b>电话：18898736510</b></span><span></span></div></div>");

        LocaleStringResource.InitInsert("海凌科通行证", "海凌科通行证", "海凌科通行證", "Hilink Pass");
        LocaleStringResource.InitInsert("首页", "首页", "首頁", "Home");
        LocaleStringResource.InitInsert("产品中心", "产品中心", "產品中心", "Product Center");
        LocaleStringResource.InitInsert("关于我们1", "关于我们", "關於我們", "About us");
        LocaleStringResource.InitInsert("解决方案", "解决方案", "解決方案", "Solution");
        LocaleStringResource.InitInsert("客户案例", "客户案例", "客戶案例", "Customer case");
        LocaleStringResource.InitInsert("代理招商", "代理招商", "代理招商", "Agent Investment");
        LocaleStringResource.InitInsert("联系我们", "联系我们", "聯繫我們", "Contact Us");
        LocaleStringResource.InitInsert("在线购买", "在线购买", "在線購買", "Buy online");
        LocaleStringResource.InitInsert("新闻资讯", "新闻资讯", "新聞資訊", "News");
        LocaleStringResource.InitInsert("技术支持中心", "技术支持中心", "技術支持中心", "Technical Support");
        LocaleStringResource.InitInsert("快速购买", "快速购买", "快速購買", "Quick purchase");
        LocaleStringResource.InitInsert("新闻中心", "新闻中心", "新聞中心", "News Center");
        LocaleStringResource.InitInsert("产品中心", "产品中心", "產品中心", "Product Center");
        LocaleStringResource.InitInsert("官方商城", "官方商城", "官方商城", "Official mall");
        LocaleStringResource.InitInsert("天猫商城", "天猫商城", "天猫商城", "TMALL");
        LocaleStringResource.InitInsert("淘宝商城", "淘宝商城", "淘寶商城", "TaoBao");
        LocaleStringResource.InitInsert("技术支持", "技术支持", "技術支持", "Technical Support");
        LocaleStringResource.InitInsert("资料下载", "资料下载", "資料下載", "Download");
        LocaleStringResource.InitInsert("一站式解决方案", "一站式解决方案", "一站式解決方案", "One-stop solution");
        LocaleStringResource.InitInsert("智能模块", "智能模块", "智能模塊", "Smart module");
        LocaleStringResource.InitInsert("智能产品", "智能产品", "智能產品", "Smart products");
        LocaleStringResource.InitInsert("智能APP", "智能APP", "智能APP", "Smart APP");
        LocaleStringResource.InitInsert("智能云服务", "智能云服务", "智能雲服務", "Smart Cloud Service");
        LocaleStringResource.InitInsert("大数据开发", "大数据开发", "大數據開發", "Big data development");
        LocaleStringResource.InitInsert("开放生态平台", "开放生态平台", "開放生態平台", "Open ecological platform");
        LocaleStringResource.InitInsert("推荐产品", "推荐产品", "推薦產品", "Recommended Products");
        LocaleStringResource.InitInsert("应用场景", "应用场景", "應用場景", "Application scenario");
        LocaleStringResource.InitInsert("智能制造", "智能制造", "智能製造", "Smart manufacturing");
        LocaleStringResource.InitInsert("智慧物流", "智慧物流", "智慧物流", "Smart logistics");
        LocaleStringResource.InitInsert("智能产品", "智能产品", "智能產品", "Smart products");
        LocaleStringResource.InitInsert("电源模块", "电源模块", "電源模塊", "Power module");
        LocaleStringResource.InitInsert("智能安防", "智能安防", "智能安防", "Smart security");
        LocaleStringResource.InitInsert("智慧农业", "智慧农业", "智慧農業", "Smart agriculture");
        LocaleStringResource.InitInsert("相关案例", "相关案例", "相關案例", "Related case");
        LocaleStringResource.InitInsert("公司新闻", "公司新闻", "公司新聞", "Company news");
        LocaleStringResource.InitInsert("行业新闻", "行业新闻", "行業新聞", "Industry News");
        LocaleStringResource.InitInsert("应用案例", "应用案例", "應用案例", "Applications");
        LocaleStringResource.InitInsert("关于我们2",
            " 海凌科电子潜心产品的技术研发，注重产品品质和诚信服务，更与全球顶级网络芯片厂商保持战略合作，积极整合利用其优质技术资源，打造企业产品核心技术竞争能力，公司自成立至今 专注于WIFI领域，致力于让每个智能产品能更方便更快捷更可靠，为客户提供安全，优质，可靠的产品使我们永恒的追求。",
            " 海凌科電子潛心產品的技術研發，注重產品品質和誠信服務，更與全球頂級網絡芯片廠商保持戰略合作，積極整合利用其優質技術資源，打造企業產品核心技術競爭能力，公司自成立至今專注於WIFI領域，致力於讓每個智能產品能更方便更快捷更可靠，為客戶提供安全，優質，可靠的產品使我們永恆的追求。", 
            "Hilink Electronics concentrates on product technology research and development, pays attention to product quality and integrity services, and maintains strategic cooperation with the world’s top network chip manufacturers, actively integrates and utilizes its high-quality technical resources, and builds the core technical competitiveness of corporate products. Since its establishment, the company has focused on In the WIFI field, we are committed to making every smart product more convenient, faster and more reliable. Providing customers with safe, high-quality and reliable products is our eternal pursuit");


        LocaleStringResource.InitInsert("关注微信订阅号", "关注微信订阅号", "關注微信訂閱號", "Follow WeChat subscription account");
        LocaleStringResource.InitInsert("业务创新与技术服务提供商", "业务创新与技术服务提供商", "業務創新與技術服務提供商", "Business Innovation and Technology Service Provider");

        LocaleStringResource.InitInsert("海凌科针对工业物联网设备连接需求",
            "海凌科针对工业物联网设备连接需求，广泛地应用于智能机床、智能电机、智能产线、智能车间、 现场工控等智能制造应用场景，以及智慧园区、智慧交通、智慧楼宇、智慧安防、智慧环保、智慧物流等泛工业应用场景。", 
            "海凌科針對工業物聯網設備連接需求，廣泛地應用於智能機床、智能電機、智能產線、智能車間、 現場工控等智能製造應用場景，以及智慧園區、智慧交通、智慧樓宇、智慧安防、智慧環保、智慧物流等泛工業應用場景。", 
            "Hilink is widely used in smart manufacturing application scenarios such as smart machine tools, smart motors, smart production lines, smart workshops, and on-site industrial control, as well as smart parks, smart transportation, smart buildings, smart security, smart Pan-industrial application scenarios such as environmental protection and smart logistics.");
        LocaleStringResource.InitInsert("智慧赋能，打造高效稳定的便民生活服务。", "智慧赋能，打造高效稳定的便民生活服务。", "智慧賦能，打造高效穩定的便民生活服務。", "Wisdom empowers and creates efficient and stable convenient life services.");


        LocaleStringResource.InitInsert("为合作伙伴提供信息系统，数据开放服务帮助合伙伴开发个性化增值服务", 
            "为合作伙伴提供信息系统，数据开放服务帮助合伙伴开发个性化增值服务", 
            "為合作夥伴提供信息系統，數據開放服務幫助合夥伴開發個性化增值服務", 
            "Provide partners with information systems and open data services to help partners develop personalized value-added services");
        LocaleStringResource.InitInsert("接入平台的智能硬件进行数据分析和呈现，结合用户行为进行深度分析",
            "接入平台的智能硬件进行数据分析和呈现，结合用户行为进行深度分析",
            "接入平台的智能硬件進行數據分析和呈現，結合用戶行為進行深度分析", 
            "Intelligent hardware connected to the platform for data analysis and presentation, combined with user behavior for in-depth analysis");
        LocaleStringResource.InitInsert("强大的云存储，专有云，云计算一体化能力，服务开放合作伙伴", 
            "强大的云存储，专有云，云计算一体化能力，服务开放合作伙伴", 
            "強大的雲存儲，專有云，雲計算一體化能力，服務開放合作夥伴",
            "Powerful cloud storage, proprietary cloud, cloud computing integration capabilities, open service partners");
        LocaleStringResource.InitInsert("APP开发和维护，管理智能硬件，享受方便快捷的智能生活", "APP开发和维护，管理智能硬件，享受方便快捷的智能生活", "APP開發和維護，管理智能硬件，享受方便快捷的智能生活", "APP development and maintenance, manage smart hardware, and enjoy a convenient and fast smart life");
        LocaleStringResource.InitInsert("海凌科提供一站式产品智能解决方案", "海凌科提供一站式产品智能解决方案", "海凌科提供一站式產品智能解決方案", "Hilink provides one-stop product intelligence solutions");
        LocaleStringResource.InitInsert("提供智能wifi芯片技术，让你的硬件快速智能化", "提供智能wifi芯片技术，让你的硬件快速智能化", "提供智能wifi芯片技術，讓你的硬件快速智能化", "Provide smart wifi chip technology to make your hardware quick and intelligent");
        LocaleStringResource.InitInsert("与客户的沟通，站在用户的角度", "与客户的沟通，站在用户的角度，基于多年的行业经验，以更好的创意，提供最佳的解决方案。", "與客戶的溝通，站在用戶的角度，基於多年的行業經驗，以更好的創意，提供最佳的解決方案。", "To communicate with customers, from the perspective of users, based on years of industry experience, we will provide the best solutions with better creativity.");
        LocaleStringResource.InitInsert("缩短产品上市时间提高生产效率和灵活性", "缩短产品上市时间提高生产效率和灵活性", "縮短產品上市時間提高生產效率和靈活性", "Shorten time to market, improve production efficiency and flexibility");
        LocaleStringResource.InitInsert("智能制造 创造未来", "智能制造 创造未来", "智能製造 創造未來", "Intelligent manufacturing creates the future");
        LocaleStringResource.InitInsert("深圳市海凌科电子有限公司1", "深圳市海凌科电子有限公司", "深圳市海凌科電子有限公司", "Shenzhen Hilink Electronics Co., Ltd.");
        LocaleStringResource.InitInsert("产品中心", "产品中心", "產品中心", "Product Center");
        LocaleStringResource.InitInsert("产品分类", "产品分类", "產品分類", "Product category");
        LocaleStringResource.InitInsert("全部", "全部", "全部", "All");
        LocaleStringResource.InitInsert("请输入搜索关键词", "请输入搜索关键词", "請輸入搜索關鍵詞", "Enter Search Keywords");
        LocaleStringResource.InitInsert("浏览次数", "浏览次数", "瀏覽次數", "Views");
        LocaleStringResource.InitInsert("样机申请", "样机申请", "樣機申請", "Prototype application");
        LocaleStringResource.InitInsert("概述特点", "概述特点", "概述特點", "Overview features");
        LocaleStringResource.InitInsert("参数规格", "参数规格", "參數規格", "Specifications");
        LocaleStringResource.InitInsert("知识问答", "知识问答", "知識問答", "Quiz");
        LocaleStringResource.InitInsert("解决方案", "解决方案", "解決方案", "Solution");
        LocaleStringResource.InitInsert("购买渠道", "购买渠道", "購買渠道", "The way of buying");
        LocaleStringResource.InitInsert("分享到QQ空间", "分享到QQ空间", "分享到QQ空間", "Share to QQ space");
        LocaleStringResource.InitInsert("分享到新浪微博", "分享到新浪微博", "分享到新浪微博", "share to Sina Weibo");
        LocaleStringResource.InitInsert("分享到腾讯微博", "分享到腾讯微博", "分享到騰訊微博", "Share to Tencent Weibo");
        LocaleStringResource.InitInsert("分享到人人网", "分享到人人网", "分享到人人網", "Share on Renren");
        LocaleStringResource.InitInsert("分享到微信", "分享到微信", "分享到微信", "Share to WeChat");
        LocaleStringResource.InitInsert("分享到豆瓣网", "分享到豆瓣网", "分享到豆瓣網", "Share on Douban");
        LocaleStringResource.InitInsert("上一篇", "上一篇", "上一篇", "Previous");
        LocaleStringResource.InitInsert("下一篇", "下一篇", "下一篇", "Next");
        LocaleStringResource.InitInsert("返回列表", "返回列表", "返回列表", "Back to list");
        LocaleStringResource.InitInsert("暂无上一篇(点击返回列表)", "暂无上一篇(点击返回列表)", "暫無上一篇（點擊返回列表）", "No previous article (click to return to list)");
        LocaleStringResource.InitInsert("暂无下一篇(点击返回列表)", "暂无下一篇(点击返回列表)", "暫無下一篇（點擊返回列表）", "No next article (click to return to list)");
        LocaleStringResource.InitInsert("开发资料", "开发资料", "開發資料", "Development Information");
        LocaleStringResource.InitInsert("软件应用", "软件应用", "軟件應用", "Software Application");
        LocaleStringResource.InitInsert("通用软件", "通用软件", "通用軟件", "Universal Software");
        LocaleStringResource.InitInsert("常见问题", "常见问题", "常見問題", "Common Problem");
        LocaleStringResource.InitInsert("查看详情", "查看详情", "查看詳情", "See Details");
        LocaleStringResource.InitInsert("淘宝店铺", "淘宝店铺", "淘寶店鋪", "Taobao shop");
        LocaleStringResource.InitInsert("大客户通道", "大客户通道", "大客戶通道", "Key Account Channel");
        LocaleStringResource.InitInsert("样品申请", "样品申请", "樣品申請", "Sample Application");
        LocaleStringResource.InitInsert("会2个工作日内回复", "会2个工作日内回复", "會2個工作日內回复", "Will reply within 2 working days");
        LocaleStringResource.InitInsert("立即申请样品", "立即申请样品", "立即申請樣品", "Request samples now");
        LocaleStringResource.InitInsert("批量采购", "批量采购", "批量採購", "Bulk purchases");
        LocaleStringResource.InitInsert("查看联系方式", "查看联系方式", "查看聯繫方式", "View Contact");
        LocaleStringResource.InitInsert("定制申请", "定制申请", "定制申請", "Custom Application");
        LocaleStringResource.InitInsert("分享到", "分享到", "分享到", "Share to");
        LocaleStringResource.InitInsert("销售网络", "销售网络", "銷售網絡", "Sales network");
        LocaleStringResource.InitInsert("地址", "地址", "地址", "Address");
        LocaleStringResource.InitInsert("联系电话", "联系电话", "聯繫電話", "Contact number");
        LocaleStringResource.InitInsert("联系人", "联系人", "聯繫人", "Contact person");
        LocaleStringResource.InitInsert("立即申请定制", "立即申请定制", "立即申請定制", "Apply for customization now");
        LocaleStringResource.InitInsert("海凌科电子在全国进行招商代理是为了充分整合代理商的资源优势", 
            "海凌科电子在全国进行招商代理是为了充分整合代理商的资源优势，为其创造良好的市场环境和企业综合竞争力。海凌科电子专心于产品研发和服务支持，不断打造更具市场竞争力的产品，为代理商提供更优质的产品和服务。",
            "海凌科電子在全國進行招商代理是為了充分整合代理商的資源優勢，為其創造良好的市場環境和企業綜合競爭力。海凌科電子專心於產品研發和服務支持，不斷打造更具市場競爭力的產品，為代理商提供更優質的產品和服務。",
            "Hilink Electronics conducts investment agents nationwide in order to fully integrate the resource advantages of agents and create a good market environment and comprehensive corporate competitiveness for them. Hilink Electronics concentrates on product research and development and service support, constantly creating more market-competitive products, and providing agents with better products and services.");

        LocaleStringResource.InitInsert("热点/痛点/观点 连点成线，物联大事件脉络尽在掌握", "热点/痛点/观点 连点成线，物联大事件脉络尽在掌握", "熱點/痛點/觀點 連點成線，物聯大事件脈絡盡在掌握", "Hotspots/pain points/viewpoints are connected, and the context of the IOT events is under control");




        LocaleStringResource.InitInsert("联系所属省份的大客户经理", "联系所属省份的大客户经理", "聯繫所屬省份的大客戶經理", "Contact the key account manager of the province");
        LocaleStringResource.InitInsert("代理商申请条件", "代理商申请条件", "代理商申請條件", "Agent application conditions");
        LocaleStringResource.InitInsert("商业信誉", "商业信誉", "商業信譽", "Commercial reputation");
        LocaleStringResource.InitInsert("市场资源", "市场资源", "市場資源", "Market resources");
        LocaleStringResource.InitInsert("人员充足", "人员充足", "人員充足", "Sufficient staff");
        LocaleStringResource.InitInsert("经营目标", "经营目标", "經營目標", "Business objectives");
        LocaleStringResource.InitInsert("智能解决方案", "智能解决方案", "智能解決方案", "Smart solution");
        LocaleStringResource.InitInsert("代理商在线申请", "代理商在线申请", "代理商在線申請", "Agent online application");
        LocaleStringResource.InitInsert("公司名称", "公司名称", "公司名稱", "Company name");
        LocaleStringResource.InitInsert("单位名称", "单位名称", "單位名稱", "Company name");
        LocaleStringResource.InitInsert("邮箱", "邮箱", "郵箱", "Mailbox");
        LocaleStringResource.InitInsert("联系地址", "联系地址", "聯繫地址", "Contact Address");
        LocaleStringResource.InitInsert("申请说明", "申请说明", "申請說明", "Application Instructions");
        LocaleStringResource.InitInsert("验证码", "验证码", "驗證碼", "Verification Code");
        LocaleStringResource.InitInsert("请输入验证码", "请输入验证码", "請輸入驗證碼", "Please enter verification code");
        LocaleStringResource.InitInsert("立即提交", "立即提交", "立即提交", "Submit Now");
        LocaleStringResource.InitInsert("代理商", "代理商", "代理商", "Agent");
        LocaleStringResource.InitInsert("查询", "查询", "查詢", "Inquire");
        LocaleStringResource.InitInsert("查看详情", "查看详情", "查看詳情", "See Details");
        LocaleStringResource.InitInsert("深圳市海凌科电子有限公司1", "深圳市海凌科电子有限公司", "深圳市海凌科電子有限公司", "Shenzhen Hilink Electronics Co., Ltd.");
        LocaleStringResource.InitInsert("武汉极思灵创科技有限公司1", "武汉极思灵创科技有限公司", "武漢極思靈創科技有限公司", "Wuhan Jisi Lingchuang Technology Co., Ltd.");
        LocaleStringResource.InitInsert("益坤泰实业有限公司1", "益坤泰实业有限公司", "益坤泰實業有限公司", "Yikuntai Industrial Co., Ltd.");



        LocaleStringResource.InitInsert("代理申请联系方式", 
            "联系方式：座机：（0775）********-809   QQ：18857817   市场部：杨先生",
            "聯繫方式：座機：（0775）********-809   QQ：18857817   市場部：楊先生",
            "Contact: Landline: (0775) ********-809 QQ: 18857817 Marketing Department: Mr. Yang");




        LocaleStringResource.InitInsert("智能解决方案能够帮助工业企业迅速建立远程连接和管理能力", 
            "智能解决方案能够帮助工业企业迅速建立远程连接和管理能力，支持企业开展远程运维服务，利用产品服务化扩大市场，实现产品增值。", 
            "智能解決方案能夠幫助工業企業迅速建立遠程連接和管理能力，支持企業開展遠程運維服務，利用產品服務化擴大市場，實現產品增值。",
            "Haizhi's solution can help industrial enterprises quickly establish remote connection and management capabilities, support enterprises to carry out remote operation and maintenance services, use product service to expand the market, and realize product value-added.");


        LocaleStringResource.InitInsert("认同我方经营理念与价值观", "认同我方经营理念与价值观，有信心和能力保证销售目标的实施与达成", "認同我方經營理念與價值觀，有信心和能力保證銷售目標的實施與達成", "Agree with our business philosophy and values, and have the confidence and ability to ensure the implementation and achievement of sales targets");
        LocaleStringResource.InitInsert("具有足够的人员力量开展业务", "具有足够的人员力量开展业务，要求最少1名销售人员， 1名技术支持工程师", "具有足夠的人員力量開展業務，要求最少1名銷售人員， 1名技術支持工程師", "Have sufficient personnel to carry out business, requiring at least 1 salesperson and 1 technical support engineer");
        LocaleStringResource.InitInsert("具备智能家居/电子产品的销售经验或区域资源，具有良好的市场意识和开拓精神", "具备智能家居/电子产品的销售经验或区域资源，具有良好的市场意识和开拓精神", "具備智能家居/電子產品的銷售經驗或區域資源，具有良好的市場意識和開拓精神", "Have smart home/electronic product sales experience or regional resources, have a good market awareness and pioneering spirit");
        LocaleStringResource.InitInsert("具有独立法人资格的合法企业，具有良好的咨信条件及商业信誉", "具有独立法人资格的合法企业，具有良好的咨信条件及商业信誉", "具有獨立法人資格的合法企業，具有良好的諮信條件及商業信譽", "A legal enterprise with independent legal personality, with good consulting conditions and business reputation");

        LocaleStringResource.InitInsert("深耕行业十数载，累计服务超过10W+客户", "深耕行业十数载，累计服务超过10W+客户，WiFi模组、蓝牙模组、电源模块等产品远80多个国家和地区，为客户提供专业可靠的IoT模块产品、强大的技术支持和贴心的售后服务保障，产品广泛的应用于智能家居、智能医疗、智能办公、汽车电子及工业控制等领域。", "深耕行業十數載，累計服務超過10W+客戶，WiFi模組、藍牙模組、電源模塊等產品遠80多個國家和地區，為客戶提供專業可靠的IoT模塊產品、強大的技術支持和貼心的售後服務保障，產品廣泛的應用於智能家居、智能醫療、智能辦公、汽車電子及工業控制等領域。", "Deeply cultivated in the industry for more than ten years, cumulatively serving more than 10W+ customers, WiFi modules, Bluetooth modules, power modules and other products are exported to more than 80 countries and regions, providing customers with professional and reliable IoT module products, strong technical support and thoughtful after-sales Service guarantee, products are widely used in smart home, smart medical, smart office, automotive electronics and industrial control fields.");
        LocaleStringResource.InitInsert("站点设置", "站点设置", "站點設置", "Site settings");
        LocaleStringResource.InitInsert("控制台", "控制台", "控制台", "Console");
        LocaleStringResource.InitInsert("会员", "会员", "會員", "member");
        LocaleStringResource.InitInsert("商品分类", "商品分类", "商品分類", "Categories");
        LocaleStringResource.InitInsert("会员管理", "会员管理", "會員管理", "Member Manage");
        LocaleStringResource.InitInsert("底部技术支持", "技术支持：<a href=\"https://www.gicisky.net\" target=\"_blank\">极思灵创</a>", "技術支持：<a href=\"https://www.gicisky.net\" target=\"_blank\">極思靈創</a>", "Technical Support：<a href=\"https://www.gicisky.net\" target=\"_blank\">Gicisky</a>");
    }

    /// <summary>
    /// 将区域路由写入数据库
    /// </summary>
    public void ConfigureArea()
    {
        AreaBase.SetRoute<HomeController>(AdminArea.AreaName);
    }

    public void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
    {
        if (UtilSetting.Current.RedisEnabled)
            services.AddSingleton(new FullRedis(UtilSetting.Current.RedisConnectionString, UtilSetting.Current.RedisPassWord, UtilSetting.Current.RedisDatabaseId)); // 全局注入Redis连接

        if (WebHookSetting.Current.DingTalkSendUrl.IsNullOrWhiteSpace())
        {
            WebHookSetting.Current.DingTalkSendUrl = "https://oapi.dingtalk.com/robot/send?access_token=c0a66acf134d4f08581c8c3d834dfc1155f8c90b2b8955e0d7c244ce305a301a";
            WebHookSetting.Current.Save();
        }

        // 验证码
        // 内存缓存
        //services.AddCaptcha(configuration);
        services.AddCaptcha(configuration, option =>
        {
            option.CaptchaType = CaptchaType.WORD_NUMBER_UPPER; // 验证码类型
            option.CodeLength = 4; // 验证码长度, 要放在CaptchaType设置后.  当类型为算术表达式时，长度代表操作的个数
            option.ExpirySeconds = 60; // 验证码过期时间
            option.IgnoreCase = true; // 比较时是否忽略大小写
            option.StoreageKeyPrefix = ""; // 存储键前缀

            option.ImageOption.Animation = true; // 是否启用动画
            option.ImageOption.FrameDelay = 120; // 每帧延迟,Animation=true时有效, 默认30

            option.ImageOption.Width = 150; // 验证码宽度
            option.ImageOption.Height = 50; // 验证码高度
            option.ImageOption.BackgroundColor = SkiaSharp.SKColors.White; // 验证码背景色

            option.ImageOption.BubbleCount = 2; // 气泡数量
            option.ImageOption.BubbleMinRadius = 5; // 气泡最小半径
            option.ImageOption.BubbleMaxRadius = 15; // 气泡最大半径
            option.ImageOption.BubbleThickness = 1; // 气泡边沿厚度

            option.ImageOption.InterferenceLineCount = 2; // 干扰线数量

            option.ImageOption.FontSize = 36; // 字体大小
            option.ImageOption.FontFamily = DefaultFontFamilys.Instance.Actionj; // 字体

            /* 
             * 中文使用kaiti，其他字符可根据喜好设置（可能部分转字符会出现绘制不出的情况）。
             * 当验证码类型为“ARITHMETIC”时，不要使用“Ransom”字体。（运算符和等号绘制不出来）
             */

            option.ImageOption.TextBold = true;// 粗体，该配置2.0.3新增
        });
    }

    public void ConfigureVirtualFileSystem(DHVirtualFileSystemOptions options)
    {
    }

    /// <summary>
    /// 调整菜单
    /// </summary>
    public void ChangeMenu()
    {
        var model = Menu.FindByName("Home");
        if (model != null && model.ParentID == 0)
        {
            var model1 = Menu.FindByName("Console");
            if (model1.Visible)
            {
                model1.Visible = false;
                model1.Ex1 = 1; // 为1时禁用菜单
                model1.Save();
            }

            model.Url = $"~/{AdminArea.AreaName}/Home/DashBoard".ToLower();
            model.Save();
            Menu.Meta.Cache.Clear("");
        }
        //model = Menu.FindByName("Member");
        //if (model != null && model.ParentID == 0)
        //{
        //    var model1 = Menu.FindAllByParentID(model.ID).Find(e => e.Url == model.Url);
        //    model1.Visible = false;
        //    model1.Save();

        //    model.DisplayName = "工厂用户";
        //    model.Url = "~/Admin/Member/Manager";
        //    model.Save();
        //}
    }

    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpoints">路由生成器</param>
    public void UseDHEndpoints(IEndpointRouteBuilder endpoints)
    {
        //endpoints.MapGrpcService<GreeterService>().RequireCors(CubeService.corsPolicy).EnableGrpcWeb();
    }

    public void Update()
    {
        var updateinfo = DHSetting.Current.UpdateInfo.Split("_");
        var versions = "1.01";

        if (new Version(DHSetting.Current.CurrentVersion) == new Version(versions))
        {
            if (updateinfo[0] != versions || (updateinfo[0] == versions && updateinfo[1] == "0"))
            {
                var list = Language.GetAll();
                foreach (var item in list)
                {
                    if (item.LanguageCulture == "zh-CN")
                    {
                        item.LangAbbreviation = "zh";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "zh-TW")
                    {
                        item.LangAbbreviation = "cht";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "en-US")
                    {
                        item.LangAbbreviation = "en";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "en-GB")
                    {
                        item.LangAbbreviation = "en";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "pt-BR")
                    {
                        item.LangAbbreviation = "pt";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "ru-RU")
                    {
                        item.LangAbbreviation = "ru";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "ja-JP")
                    {
                        item.LangAbbreviation = "jp";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "vi-VN")
                    {
                        item.LangAbbreviation = "vie";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "fr-FR")
                    {
                        item.LangAbbreviation = "fra";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "pl-PL")
                    {
                        item.LangAbbreviation = "pl";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "it-IT")
                    {
                        item.LangAbbreviation = "it";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "tr-TR")
                    {
                        item.LangAbbreviation = "tr";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "ko-KR")
                    {
                        item.LangAbbreviation = "kor";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "th-TH")
                    {
                        item.LangAbbreviation = "th";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "de-DE")
                    {
                        item.LangAbbreviation = "de";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "es-MX")
                    {
                        item.LangAbbreviation = "spa";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "es-ES")
                    {
                        item.LangAbbreviation = "spa";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "ar-EG")
                    {
                        item.LangAbbreviation = "ara";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "cs-CZ")
                    {
                        item.LangAbbreviation = "cs";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "de-CH")
                    {
                        item.LangAbbreviation = "de";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "da-DK")
                    {
                        item.LangAbbreviation = "dan";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "fi-FI")
                    {
                        item.LangAbbreviation = "fin";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "el-GR")
                    {
                        item.LangAbbreviation = "el";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "hu-HU")
                    {
                        item.LangAbbreviation = "hu";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "nl-NL")
                    {
                        item.LangAbbreviation = "nl";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "nn-NO")
                    {
                        item.LangAbbreviation = "nor";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "ro-RO")
                    {
                        item.LangAbbreviation = "rom";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "sv-SE")
                    {
                        item.LangAbbreviation = "swe";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "en-ZA")
                    {
                        item.LangAbbreviation = "en";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "id-ID")
                    {
                        item.LangAbbreviation = "id";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "en-AU")
                    {
                        item.LangAbbreviation = "en";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "en-SG")
                    {
                        item.LangAbbreviation = "en";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "en-IN")
                    {
                        item.LangAbbreviation = "en";
                        item.SaveAsync();
                    }
                    else if (item.LanguageCulture == "en-PH")
                    {
                        item.LangAbbreviation = "en";
                        item.SaveAsync();
                    }
                }

                DHSetting.Current.UpdateInfo = $"{versions}_1";
                DHSetting.Current.Save();
            }
        }
    }

    public void AfterAuth(IApplicationBuilder application)
    {
    }

    public void BeforeRouting(IApplicationBuilder application)
    {
    }

    public void ConfigureMiddleware(IApplicationBuilder application)
    {

    }

    /// <summary>
    /// 处理数据
    /// </summary>
    public void ProcessData()
    {
    }

    /// <summary>
    /// 获取此启动配置实现的顺序
    /// </summary>
    public int StartupOrder => 999; //常见服务应在错误处理程序之后加载

    /// <summary>
    /// 获取此启动配置实现的顺序。主要针对ConfigureMiddleware、UseRouting前执行的数据、UseAuthentication或者UseAuthorization后面
    /// Endpoints前执行的数据
    /// </summary>
    public int ConfigureOrder => 100;
}
