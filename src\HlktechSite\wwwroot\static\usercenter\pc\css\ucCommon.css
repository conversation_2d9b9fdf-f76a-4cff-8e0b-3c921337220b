﻿/* UserCenter 通用样式 */
body {
    min-width: 1200px;
    padding-top: 56px;
    padding-left: 168px;
    background: #fff;
    color: #4d4d4d;
    font-size: 14px;
}

a {
    text-decoration: none;
    color: #00aaff;
}

    a:hover {
        text-decoration: underline;
        color: #0089d9;
    }

    a.disabled {
        text-decoration: none;
        color: #aaa;
    }

body.graceful-menu {
    padding-left: 64px;
}

ul {
    margin: 0;
    padding: 0;
}

li {
    list-style: none;
}

i {
    font-style: normal;
    color: #999;
}
/* 头部 */
.ny-header {
    min-width: 1200px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1200;
    right: 0;
    height: 56px;
    background-color: #00aaff;
}

.ny-logo {
    width: 168px;
    height: 100%;
    background: url(/uploads/common/usercenter_logo.png) no-repeat center;
}

.graceful-menu .ny-logo {
    width: 64px;
    background: url(/uploads/common/usercenter_mini_logo.png) no-repeat center;
}

.ny-logo a {
    display: block;
    width: 100%;
    height: 100%;
}

.header-main {
    position: absolute;
    top: 0;
    left: 168px;
    right: 0;
    min-width: 900px;
    height: 100%;
    line-height: 56px;
    -webkit-box-shadow: 0 2px 4px rgba(189, 189, 189, 0.72);
    -moz-box-shadow: 0 2px 4px rgba(189, 189, 189, 0.72);
    -ms-box-shadow: 0 2px 4px rgba(189, 189, 189, 0.72);
    -o-box-shadow: 0 2px 4px rgba(189, 189, 189, 0.72);
    shadow: 0 2px 4px rgba(189, 189, 189, 0.72);
}

.graceful-menu .header-main {
    left: 64px;
}
/*.header-search {
	display: inline-block;
	position: relative;
	z-index: 10;
	height: 100%;
	margin-left: 30px;
	background-color: #008cbf;
	line-height: 1;
}
.search-input-outer {
	position: absolute;
	top: 50%;
	left: 0;
	z-index: 10;
	width: 200px;
	height: 26px;
	padding: ;
	margin-top: -13px;
	border-radius: 13px;
	line-height: 1;
	background-color: #008cbf;
}
.search-input {
	height: 26px;
	background-color: transparent;
}*/
.menu-control-outer {
    display: inline-block;
    height: 100%;
}

.menu-collapse-control {
    display: block;
    border-left: 1px solid #039ce9;
    width: 63px;
    height: 56px;
    line-height: 1;
    background: url(img/uc/header_icon_fold.png?v=2) no-repeat center;
    vertical-align: middle;
}

.graceful-menu .menu-collapse-control {
    background-image: url(img/uc/header_icon_expand.png?v=2);
}

.hello-user,
.header-link {
    text-align: center;
    color: #fff;
}

.hello-user {
    display: inline-block;
    margin-right: 30px;
}

.header-link {
    position: relative;
    float: left;
    padding: 0 24px;
    border-left: 1px solid #039ce9;
    text-indent: 25px;
    background-repeat: no-repeat;
    background-position: 24px center;
    font-size: 14px;
}

    .header-link:hover,
    .header-link:focus {
        background-color: #0099e5;
        color: #fff;
    }

.header-left .header-link {
    font-size: 16px;
}

.header-link.header-link-drop {
    position: relative;
    padding-right: 50px;
}

    .header-link.header-link-drop:after {
        content: "";
        position: absolute;
        z-index: 10;
        right: 24px;
        top: 50%;
        margin-top: -3px;
        background: url(img/uc/header_icon_drop_arrow.png);
        width: 10px;
        height: 6px;
    }

.header-link-overview {
    background-image: url(img/uc/header_icon_overview.png);
    width: 96px;
    padding-left: 20px;
    text-indent: 17px;
    background-position: 17px center;
}

.header-link-products {
    background-image: url(img/uc/header_icon_products.png);
    border-right: 1px solid #039ce9;
}

.header-link-message {
    position: relative;
    background-image: url(img/uc/header_icon_message.png);
}

.header-message-count {
    position: absolute;
    top: 9px;
    right: 7px;
    width: 24px;
    height: 18px;
    background: red;
    line-height: 18px;
    font-size: 12px;
    border-radius: 8px;
    text-indent: 0;
}

.header-link-workorder {
    background-image: url(img/uc/header_icon_workorder.png);
}

.header-link-help {
    background-image: url(img/uc/header_icon_help.png);
}

.header-link-payment {
    background-image: url(img/uc/header_icon_bills.png?v=2);
    background-image: url(img/uc/menu_icon_money_active.png?v=2);
}

.header-link-user {
    width: 160px;
    text-indent: 0;
    text-align: left;
    padding-left: 64px;
    background-image: url(img/uc/user_avatar_small.png);
}

.header-username {
    line-height: 1;
    display: inline-block;
    max-width: 56px;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: nowrap;
    text-indent: 0;
}
/* 头部下拉 */
.header-drop-list {
    display: none;
    position: absolute;
    top: 56px;
    border: 1px solid #ddd;
    border-top: none;
    background: #fff;
    box-shadow: 0px 2px 5px 1px rgba(0, 0, 0, .12);
    font-size: 12px;
    text-indent: 0;
    color: #a0a2a3;
    line-height: 32px;
    text-align: left;
}

.header-link:hover .header-drop-list {
    display: block;
}

.header-drop-list a.black-link {
    color: #262829;
}

    .header-drop-list a.black-link:hover {
        color: #00aaff;
    }
/* 产品下拉 */
.header-list-products {
    left: -1px;
    width: 576px;
    padding: 16px 24px 40px 24px;
}

    .header-list-products ul {
        float: left;
        width: 128px;
    }

    .header-list-products li {
        height: 32px;
        line-height: 32px;
        text-align: left;
        text-indent: 0;
    }

li.header-products-title {
    height: 24px;
    line-height: 24px;
    margin-bottom: 8px;
    color: #a0a2a3;
}

.header-list-products a {
    display: block;
    width: 100%;
    height: 100%;
    padding-left: 24px;
    background: no-repeat left center;
    color: #262829;
}

    .header-list-products a:hover {
        color: #00aaff;
    }

    .header-list-products a.closed,
    .header-list-products a.closed:hover {
        color: #ccc;
    }

a.header-product-database {
    background-image: url(img/uc/menu_icon_database.png);
}

    a.header-product-database:hover {
        background-image: url(img/uc/menu_icon_database_blue.png);
    }

a.header-product-server {
    background-image: url(img/uc/menu_icon_server.png);
}

    a.header-product-server:hover {
        background-image: url(img/uc/menu_icon_server_blue.png);
    }

a.header-product-slb {
    background-image: url(img/uc/menu_icon_slb.png);
}

    a.header-product-slb:hover {
        background-image: url(img/uc/menu_icon_slb_blue.png);
    }

a.header-product-host {
    background-image: url(img/uc/menu_icon_host.png?v=2);
}

    a.header-product-host:hover {
        background-image: url(img/uc/menu_icon_host_blue.png);
    }

a.header-product-ssl {
    background-image: url(img/uc/menu_icon_ssl.png);
}

    a.header-product-ssl:hover {
        background-image: url(img/uc/menu_icon_ssl_blue.png);
    }

a.header-product-domain {
    background-image: url(img/uc/menu_icon_domain.png?v=2);
}

    a.header-product-domain:hover {
        background-image: url(img/uc/menu_icon_domain_blue.png);
    }

a.header-product-cdn {
    background-image: url(img/uc/menu_icon_cdn.png?v=2);
}

    a.header-product-cdn:hover {
        background-image: url(img/uc/menu_icon_cdn_blue.png);
    }

a.header-product-idc {
    background-image: url(img/uc/menu_icon_idc.png?v=2);
}

    a.header-product-idc:hover {
        background-image: url(img/uc/menu_icon_idc_blue.png);
    }

a.header-product-icp {
    background-image: url(img/uc/menu_icon_icp.png?v=2);
}

    a.header-product-icp:hover {
        background-image: url(img/uc/menu_icon_icp_blue.png);
    }

a.header-product-oss {
    background-image: url(img/uc/menu_icon_oss.png?v=2);
}

    a.header-product-oss:hover {
        background-image: url(img/uc/menu_icon_oss_blue.png);
    }

a.header-product-sms {
    background-image: url(img/uc/menu_icon_sms.png?v=2);
}

    a.header-product-sms:hover {
        background-image: url(img/uc/menu_icon_sms_blue.png);
    }

a.header-product-trademark {
    background-image: url(img/uc/menu_icon_trade.png?v=2);
}

    a.header-product-trademark:hover {
        background-image: url(img/uc/menu_icon_trade_blue.png);
    }

a.header-product-aipage {
    background-image: url(img/uc/menu_icon_aipage.png?v=2);
}

    a.header-product-aipage:hover {
        background-image: url(img/uc/menu_icon_aipage_blue.png);
    }
/* 帮助下拉 */
.header-list-help {
    padding: 24px;
    width: 400px;
    right: -1px;
}

.header-help-form {
    position: relative;
}

.header-help-input {
    width: 352px;
    height: 40px;
    line-height: 40px;
    padding-right: 40px;
    /*background: url(img/uc/header_icon_search.png) no-repeat 330px center;*/
    color: #262829;
}

    .header-help-input:focus {
        /*background-image: url(img/uc/header_icon_search_active.png);*/
        color: #262829;
    }

.submit-header-help {
    position: absolute;
    right: 0;
    top: 0;
    width: 40px;
    height: 40px;
    border: none;
    outline: none;
    line-height: 40px;
    background: url(img/uc/header_icon_search.png) no-repeat center;
}

    .submit-header-help:hover {
        background-image: url(img/uc/header_icon_search_active.png);
    }

.header-list-help form {
    margin-bottom: 16px;
}

.header-doc-links {
    line-height: 16px;
}

    .header-doc-links a {
        display: inline-block;
        border-left: 1px solid #ccc;
        width: 110px;
        height: 16px;
        text-align: center;
        line-height: 16px;
        color: #262829;
    }

        .header-doc-links a:first-child {
            border-left: none;
        }

        .header-doc-links a:hover {
            color: #00aaff;
        }

.header-finance-links {
    margin-top: 10px;
}

    .header-user-links,
    .header-finance-links li {
        line-height: 32px;
    }

        .header-user-links li a,
        .header-finance-links li a {
            display: inline-block;
            height: 100%;
            line-height: 32px;
            color: #262829;
        }

            .header-user-links li a:hover,
            .header-finance-links li a:hover {
                color: #00aaff;
            }

.header-finance-last-link {
    border-top: 1px solid #eee;
    margin-top: 10px;
    padding-top: 8px;
}
/* 财务下拉 */
.header-list-finance {
    width: 240px;
    right: -1px;
    padding: 10px 24px 8px 20px;
}

.header-finance-money {
    font-size: 24px;
}

    .header-finance-money span {
    }

.header-finance-btns .ny-btn {
    position: relative;
    width: 97px;
    margin-left: -1px;
}

    .header-finance-btns .ny-btn:hover {
        z-index: 2;
    }
/* 消息下拉 */
.header-list-messages {
    right: -1px;
    width: 336px;
    padding: 0 23px;
}

    /*    .header-list-messages ul {
        padding-top: 16px;
    }*/

    .header-list-messages li {
        height: 74px;
        /*        border-bottom: 1px solid #eee;*/
    }

        .header-list-messages li:hover .header-message-time {
            display: none;
        }

        .header-list-messages li:hover .header-message-read {
            display: block !important;
        }

.header-message-type {
    margin-bottom: 8px;
    line-height: 24px;
    height: 24px;
}

.header-message-all {
    line-height: 48px;
}

.message-empty {
    background: url(img/uc/tip_icon_info_16.png) no-repeat 30px center;
    padding: 24px 0;
    color: #636566;
}
/* 工单 */
.header-list-workorder {
    left: -1px;
    right: -1px;
    padding: 16px 0;
}

    .header-list-workorder li {
        line-height: 32px;
        text-align: center;
    }

        .header-list-workorder li a {
            color: #262829;
        }

            .header-list-workorder li a:hover {
                color: #00aaff;
            }

.header-user-links {
    left: -1px;
    right: -1px;
    padding: 16px 24px 10px 24px;
}
/* 菜单栏 */
.sidebar-limit {
    overflow-x: hidden;
    position: fixed;
    left: 0;
    top: 56px;
    bottom: 0;
    z-index: 120;
    width: 168px;
    background-color: #2d3438;
}

.graceful-menu .sidebar-limit {
    width: 64px;
    overflow: visible;
}

.sidebar-fix-scroll,
.sidebar-scroll-container {
    overflow: auto;
    overflow-x: hidden;
    width: 200px;
    height: 100%;
}

.graceful-menu .sidebar-scroll-container {
    width: 64px;
    overflow: visible;
}

.sidebar-main {
    width: 168px;
    color: #85919e;
}

.graceful-menu .sidebar-main {
    width: 64px;
}

.ny-menu {
    border-top: 1px solid #35404d;
}

.sidebar-fix-scroll {
    background: #f0f3f5;
}
/* menu下重置panel样式 */
.ny-menu .panel {
    position: relative;
    border: none;
    border-radius: 0;
    background-color: transparent;
    margin: 0;
}

.ny-menu a {
    color: #85919e;
}

.sub-menu {
    padding-left: 0;
    margin-bottom: 0;
}

    .sub-menu li {
        position: relative;
    }

    .sub-menu a {
        display: block;
        width: 100%;
        height: 48px;
        padding-left: 48px;
        background-position: 24px center;
        background-repeat: no-repeat;
        background-color: #2d3438;
        line-height: 48px;
        font-size: 14px;
        color: #fff;
    }

        .sub-menu a:hover,
        .sidebar-main .sub-menu a.menu-active {
            background-color: #00aaff;
            color: #fff;
        }

        .sub-menu a .beta-icon {
            display: none;
            width: 30px;
            height: 18px;
            margin-left: 5px;
            vertical-align: super;
            background: url(img/uc/beta-icon.png) no-repeat center center;
        }

.graceful-menu .sub-menu a {
    padding-left: 0;
    text-align: center;
}

.upper-menu-wrap {
    position: relative;
}

a.upper-menu {
    position: relative;
    display: block;
    height: 40px;
    padding-left: 24px;
    line-height: 40px;
    background: #3d4448;
    font-size: 12px;
    color: #879399;
}

.upper-menu.collapsed {
    color: #879399;
}

.upper-menu .upper-menu-arrow {
    position: absolute;
    width: 10px;
    height: 6px;
    top: 50%;
    right: 27px;
    margin-top: -3px;
    background: url(img/uc/icon_upper_menu_drop.png) no-repeat center;
}

.upper-menu.collapsed .upper-menu-arrow {
    transform: rotate(-90deg);
}

.upper-menu:hover {
    color: #fff;
}

    .upper-menu:hover .upper-menu-arrow {
        background: url(img/uc/icon_upper_menu_drop_hover.png) no-repeat center;
    }

.graceful-menu .upper-menu > span,
.graceful-menu .sub-menu a > span {
    display: none;
}
/*.graceful-menu .upper-menu {
	border-left: 3px solid transparent;
	background-position: 27px center;
	text-indent: -1000px;
	text-align: left;
	-webkit-transition-property: none;
	-moz-transition-property: none;
	-ms-transition-property: none;
	-o-transition-property: none;
	transition-property: none;
}
.graceful-menu .panel:hover .upper-menu{
	padding-left: 110px;
	width: 200px;
	background-color: #222d39;
	text-indent: 0;
}
.graceful-menu .sub-menu {
	display: none;
	position: absolute;
	top: 52px;
	left: 64px;
	width: 120px;
}
.graceful-menu .panel:hover .sub-menu {
	display: block;
}
.graceful-menu .panel:hover .collapse {
	display: block;
}*/
.upper-menu.menu-active,
.upper-menu:hover {
}

.menu-reminder {
    display: none;
    position: absolute;
    right: -128px;
    top: 50%;
    width: 114px;
    height: 32px;
    margin-top: -16px;
    padding-left: 16px;
    line-height: 32px;
    background: #2d3438;
    color: #fff;
    text-align: left;
    font-size: 14px;
}

    .menu-reminder:before {
        content: "";
        position: absolute;
        left: -12px;
        top: 50%;
        border: 6px solid transparent;
        margin-top: -6px;
        border-right-color: #2d3438;
    }
/* menu icons begin */

.menu-link-firmware {
    background-image: url(img/uc/firmware.png);
}

    .menu-link-firmware[class~=menu-active], .menu-link-firmware:hover {
        background-image: url(img/uc/firmware-hove.png);
    }


/* 云服务器 */
.menu-link-server {
    background-image: url(img/uc/menu_icon_server.png);
}

    .menu-link-server.menu-active,
    .menu-link-server:hover {
        background-image: url(img/uc/menu_icon_server_active.png);
    }
/* 托管 */
.menu-link-idc {
    background-image: url(img/uc/menu_icon_idc.png);
}

    .menu-link-idc.menu-active,
    .menu-link-idc:hover {
        background-image: url(img/uc/menu_icon_idc_active.png);
    }
/* 裸金属 */
.menu-link-baremetal {
    background-image: url(img/uc/menu_icon_baremetal.png);
}

    .menu-link-baremetal.menu-active,
    .menu-link-baremetal:hover {
        background-image: url(img/uc/menu_icon_baremetal_active.png);
    }
/* 虚拟主机 */
.menu-link-host {
    background-image: url(img/uc/menu_icon_host.png?v=2);
}

    .menu-link-host.menu-active,
    .menu-link-host:hover {
        background-image: url(img/uc/menu_icon_host_active.png?v=2);
    }
/* SSL证书 */
.menu-link-ssl {
    background-image: url(img/uc/menu_icon_ssl.png);
}

    .menu-link-ssl.menu-active,
    .menu-link-ssl:hover {
        background-image: url(img/uc/menu_icon_ssl_active.png);
    }
/* 域名 */
.menu-link-domain {
    background-image: url(img/uc/menu_icon_domain.png);
}

    .menu-link-domain.menu-active,
    .menu-link-domain:hover {
        background-image: url(img/uc/menu_icon_domain_active.png);
    }
/* 负载均衡 */
.menu-link-slb {
    background-image: url(img/uc/menu_icon_slb.png);
}

    .menu-link-slb.menu-active,
    .menu-link-slb:hover {
        background-image: url(img/uc/menu_icon_slb_active.png);
    }
/* 负载均衡 */
.menu-link-database {
    background-image: url(img/uc/menu_icon_database.png);
}

    .menu-link-database.menu-active,
    .menu-link-database:hover {
        background-image: url(img/uc/menu_icon_database_active.png);
    }
/* 云资源监控 */
.menu-link-monitor {
    background-image: url(img/uc/menu_icon_monitor.png);
}

    .menu-link-monitor.menu-active,
    .menu-link-monitor:hover {
        background-image: url(img/uc/menu_icon_monitor_active.png);
    }
/* 备案 */
.menu-link-icp {
    background-image: url(img/uc/menu_icon_icp.png?v=3);
}

    .menu-link-icp.menu-active,
    .menu-link-icp:hover {
        background-image: url(img/uc/menu_icon_icp_active.png?v=3);
    }
/* 对象存储 */
.menu-link-oss {
    background-image: url(img/uc/menu_icon_oss.png?v=3);
}

    .menu-link-oss.menu-active,
    .menu-link-oss:hover {
        background-image: url(img/uc/menu_icon_oss_active.png?v=3);
    }
/* 商标注册 */
.menu-link-trademark {
    background-image: url(img/uc/menu_icon_trade.png?v=3);
}

    .menu-link-trademark.menu-active,
    .menu-link-trademark:hover {
        background-image: url(img/uc/menu_icon_trade_active.png?v=3);
    }

/* 市场 */
.menu-link-market {
    background-image: url(img/uc/menu_icon_market.png);
}

    .menu-link-market.menu-active,
    .menu-link-market:hover {
        background-image: url(img/uc/menu_icon_market_active.png);
    }
/* 百度智能建站 */
.menu-link-Aipage {
    background-image: url(img/uc/menu_icon_aipage.png);
}

    .menu-link-Aipage.menu-active,
    .menu-link-Aipage:hover {
        background-image: url(img/uc/menu_icon_aipage_active.png);
    }
/* 财务 */
.menu-link-finance {
    background-image: url(img/uc/menu_icon_money.png);
}

    .menu-link-finance.menu-active,
    .menu-link-finance:hover {
        background-image: url(img/uc/menu_icon_money_active.png);
    }
/* 续费管理 */
.menu-link-money {
    background-image: url(img/uc/menu_icon_finance.png?v=2);
}

    .menu-link-money.menu-active,
    .menu-link-money:hover {
        background-image: url(img/uc/menu_icon_finance_active.png?v=2);
    }
/* 代理分销 */
.menu-link-cps {
    background-image: url(img/uc/menu_icon_cps.png);
}

    .menu-link-cps.menu-active,
    .menu-link-cps:hover {
        background-image: url(img/uc/menu_icon_cps_active.png);
    }
/* 代理分销 */
.menu-link-workorder {
    background-image: url(img/uc/menu_icon_workorder.png);
}
.menu-link-client {
    background-image: url(img/uc/menu_icon_client.png);
}

    .menu-link-client.menu-active,
    .menu-link-client:hover {
        background-image: url(img/uc/menu_icon_client_active.png);
    }
    .menu-link-workorder.menu-active,
    .menu-link-workorder:hover {
        background-image: url(img/uc/menu_icon_workorder_active.png);
    }
/* 账号设置 */
.menu-link-account {
    background-image: url(img/uc/menu_icon_user.png);
}

    .menu-link-account.menu-active,
    .menu-link-account:hover {
        background-image: url(img/uc/menu_icon_user_active.png);
    }

.menu-link-showGroup {
    background-image: url(img/uc/menu_icon_showgroup.png);
}

    .menu-link-showGroup.menu-active,
    .menu-link-showGroup:hover {
        background-image: url(img/uc/menu_icon_showgroup_active.png);
    }

/* cdn */
.menu-link-cdn {
    background-image: url(img/uc/menu_icon_cdn.png);
}

    .menu-link-cdn.menu-active,
    .menu-link-cdn:hover {
        background-image: url(img/uc/menu_icon_cdn_active.png);
    }

/* 短信服务 */
.menu-link-sms {
    background-image: url(img/uc/menu_icon_sms.png);
}

    .menu-link-sms.menu-active,
    .menu-link-sms:hover {
        background-image: url(img/uc/menu_icon_sms_active.png);
    }
/* menu icon end */
.sidebar-fix-outer {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 168px;
    z-index: 100;
    width: 160px;
    overflow: hidden;
    background: #f0f3f5;
}

.hide-sidebar .sidebar-fix-outer {
    width: 24px;
    background: none;
}

.hide-sidebar .sidebar-fix-scroll {
    background: none;
}

.hide-sidebar .sidebar-fix {
    display: none;
}

.sidebar-fix {
    /*position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;*/
    position: relative;
    background: #f0f3f5;
    border-right: 1px solid #e1e4e6;
    width: 160px;
    height: 100%;
}

    .sidebar-fix:after {
        position: absolute;
        content: "";
        left: 0;
        right: 0;
        top: 0;
        height: 100%;
        background: #f0f3f5;
        z-index: -1;
    }

.graceful-menu .sidebar-fix-outer {
    left: 64px;
}

.sidebar-title {
    border-bottom: 1px solid #e1e4e6;
    height: 40px;
    width: 160px;
    line-height: 40px;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    color: #a0a2a3;
}

.sidebar-drop-trigger a {
    background: url(img/uc/icon_submenu_drop.png) no-repeat 124px center;
    padding-left: 24px;
    color: #262829;
    font-size: 13px;
    text-decoration: none;
}

    .sidebar-drop-trigger a:hover {
        color: #00aaff;
        background: url(img/uc/icon_submenu_drop_hover.png) no-repeat 124px center;
    }

.expand .sidebar-drop-trigger a {
    background-image: url(img/uc/icon_submenu_drop_reverse.png);
}

    .expand .sidebar-drop-trigger a:hover {
        background-image: url(img/uc/icon_submenu_drop_reverse_hover.png);
    }

.single-link.sidebar-drop-trigger a {
    background-image: none;
}

.sidebar-drop-block {
    width: 160px;
}

    .sidebar-drop-block li {
        width: 160px;
        height: 48px;
    }

    .sidebar-drop-block a {
        display: block;
        width: 100%;
        height: 48px;
        line-height: 48px;
    }

        .sidebar-drop-block a:hover {
            background-color: #f7f9fa;
            text-decoration: none;
            color: #00aaff;
        }

i.outer-link {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url(img/uc/icon_menu_outer_link.png) no-repeat center;
    margin-left: 10px;
}

.sidebar-drop-list {
    display: none;
}

.expand .sidebar-drop-list {
    display: block;
}

.sidebar-drop-list a {
    background: url(img/uc/icon_submenu_prefix.png) no-repeat 26px center;
    padding-left: 40px;
    font-size: 12px;
    color: #636566;
}

    .single-link a.menu-active,
    .sidebar-drop-list a.menu-active {
        position: relative;
        background-color: #fff;
        color: #00aaff;
    }

.sidebar-handle {
    position: absolute;
    top: 50%;
    right: -9px;
    z-index: 10;
    background: url(img/uc/menu_handle.png) no-repeat left center;
    width: 28px;
    height: 56px;
    margin-top: -28px;
    cursor: pointer;
}

    .sidebar-handle:hover {
        right: -5px;
        background-image: url(img/uc/menu_handle_hover.png);
    }

.hide-sidebar .sidebar-handle {
    right: 1px;
    background-image: url(img/uc/menu_handle_expand.png);
}

    .hide-sidebar .sidebar-handle:hover {
        right: -3px;
        background-image: url(img/uc/menu_handle_expand_hover.png);
    }
/* menu icon - 负载均衡 */
.menu-icon-slb.collapsed {
    background-image: url(img/uc/menu_icon_slb.png);
}

.menu-icon-slb,
.menu-icon-slb:hover,
.menu-icon-slb.menu-active {
    background-image: url(img/uc/menu_icon_slb_active.png);
}

.graceful-menu .menu-icon-slb,
.graceful-menu .menu-icon-slb.collapsed {
    background-image: url(img/uc/menu_icon_large_slb.png);
}

    .graceful-menu .menu-icon-slb:hover,
    .graceful-menu .menu-icon-slb.menu-active {
        background-image: url(img/uc/menu_icon_large_slb_active.png);
    }
/* menu icon end */

.sidebar-part {
    display: none;
    padding-bottom: 20px;
}

/* 主区域 */
.ny-main {
    min-width: 1000px;
    font-size: 12px;
    padding-left: 160px;
}

.hide-sidebar .ny-main {
    padding-left: 0;
}

.ny-panel {
    padding: 24px 40px;
    background-color: #fff;
}

.ny-panel-heading {
}

.ny-panel-title {
    display: inline-block;
    margin: 0;
    height: 40px;
    line-height: 40px;
    font-size: 22px;
    color: #272829;
}

.selected-content {
    font-size: 12px;
    color: #636566;
}

.dropdown-toggle .selected-content {
    color: #262829;
}

.ny-panel-heading .btn-icon-return {
    margin-left: 20px;
    vertical-align: text-bottom;
}

.ny-panel-body {
    padding: 20px 0;
}

.ny-panel-list {
    line-height: 2.5;
    padding: 10px;
}
/* 工单、订单页面 详情描述面板 */
.detail-panel {
    border: 1px solid #eee;
    background-color: #f5f9fa;
    padding: 20px;
}

.detail-tier {
    margin-top: 10px;
}

.detail-item {
    width: 33.3%;
}
/* 通用icon */
.user-label {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: no-repeat center;
    vertical-align: middle;
}

.user-verified-origin {
    background-image: url(img/uc/home_icon_vip_gray.png);
}

.user-verify-personal {
    background-image: url(img/uc/home_icon_vip_personal.png);
}

.user-verify-company {
    background-image: url(img/uc/home_icon_vip_company.png);
}

.my-auth {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-repeat: no-repeat;
    background-position: center;
    vertical-align: sub;
}

.user-favorable-none {
    background-image: url(img/uc/home_icon_hui.png);
}

.user-favorable-open {
    background-image: url(img/uc/home_icon_hui_active.png);
}

.filter-icon {
    /*position: absolute;
	top: 13px;
	right: 0;*/
    display: inline-block;
    width: 14px;
    height: 14px;
    background: no-repeat center;
    background-image: url(img/uc/filter_none.png);
    vertical-align: middle;
    margin-top: -3px;
    margin-left: 9px;
}

.filter-asc {
    background-image: url(img/uc/filter_asc.png);
}

.filter-desc {
    background-image: url(img/uc/filter_desc.png);
}
/* 带icon状态文字 */
.ny-state-icon {
    /*display: inline-block;*/
    padding-left: 25px;
    background: no-repeat left center;
}

.state-icon-success,
.state-icon-done {
    background-image: url(img/uc/state_icon_success.png);
}

.state-icon-cancel {
    background-image: url(img/uc/state_icon_cancel.png);
}

.state-icon-money {
    background-image: url(img/uc/state_icon_money.png);
}

.state-icon-waiting {
    background-image: url(img/uc/state_icon_waiting.png);
}

.state-icon-dealing {
    background-image: url(img/uc/state_icon_dealing.png);
}

.state-icon-loading {
    background-image: url(img/uc/server_state_loading.gif);
}

.state-icon-fail {
    background-image: url(img/uc/state_icon_cross.png);
}

.state-icon-error {
    background-image: url(img/uc/state_icon_error.png);
}

.state-icon-refuse {
    background-image: url(img/uc/state_icon_refuse.png);
}

.state-icon-end {
    background-image: url(img/uc/status_icon_end.png);
}

.state-icon-examine {
    background-image: url(img/uc/state_icon_edit.png);
}

.state-icon-evaluate {
    background-image: url(img/uc/state_icon_value.png);
}

.state-icon-payment {
    background-image: url(img/uc/state_icon_card.png);
}

.state-icon-cert-fill {
    background-image: url(img/uc/state_icon_cert_write.png?v=2);
}

.state-icon-cert-dealing {
    background-image: url(img/uc/state_icon_cert_clock.png);
}

.state-icon-cert-run {
    background-image: url(img/uc/state_icon_cert_run.png);
}

.state-icon-cert-error {
    background-image: url(img/uc/state_icon_cert_error.png);
}

.state-icon-cert-stop {
    background-image: url(img/uc/state_icon_cert_stop.png);
}

.state-icon-cert-waiting {
    background-image: url(img/uc/state_icon_cert_waiting.png);
}

.state-icon-cert-correct {
    background-image: url(img/uc/state_icon_cert_correct.png);
}

.state-icon-cert-list {
    background-image: url(img/uc/state_icon_cert_list.png);
}
/* 带有icon的按钮 */
.btn-reverse.btn-icon-return {
    height: 20px;
    line-height: 19px;
    padding: 0 29px 0 10px;
    font-size: 12px;
}

    .btn-reverse.btn-icon-return,
    .btn-reverse.btn-icon-return:focus,
    .btn-reverse.btn-icon-return:active {
        background-image: url(img/uc/btn_icon_return.png);
        background-repeat: no-repeat;
        background-position: 44px center;
    }

        .btn-reverse.btn-icon-return:focus,
        .btn-reverse.btn-icon-return:active {
            background-color: #aee9ff;
        }

.btn-operate {
    padding-left: 30px;
}

.btn-operate-add,
.btn-operate-add:focus,
.btn-operate-add:active {
    padding-left: 30px;
    background: #00aaff url(img/uc/btn_icon_plus.png) 10px center no-repeat !important;
    filter: none;
    font-size: 12px;
}

.btn-operate-key,
.btn-operate-key:focus,
.btn-operate-key:active:focus {
    padding-left: 30px;
    background: #00aaff url(img/uc/btn_icon_key.png) 10px center no-repeat;
    filter: none;
    font-size: 12px;
}

.btn-operate-export,
.btn-operate-export:focus,
.btn-operate-export:active:focus {
    padding-left: 30px;
    background: #ff8800 url(img/uc/btn_icon_export.png) 10px center no-repeat;
    filter: none;
    font-size: 12px;
}

.btn-operate-complete,
.btn-operate-complete:focus,
.btn-operate-complete:active:focus {
    padding-left: 30px;
    background: #00aaff url(img/uc/step_complete.png) 10px center no-repeat;
    filter: none;
    font-size: 12px;
}

.send-captcha {
    height: 32px;
}
/*布局原子类*/
.body-reset,
.graceful-menu.body-reset {
    min-width: auto;
    padding: 20px;
    margin: 0;
    background-color: #fff;
}

.inline-block {
    display: inline-block;
}

.padding-0 {
    padding: 0 !important;
}

.padding-20 {
    padding: 20px !important;
}

.padding-top-0 {
    padding-top: 0 !important;
}

.padding-lr-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.padding-bottom-0 {
    padding-bottom: 0 !important;
}

.margin-0 {
    margin: 0 !important;
}

.margin-top-10 {
    margin-top: 10px !important;
}

.margin-top-20 {
    margin-top: 20px !important;
}

.margin-top-30 {
    margin-top: 30px !important;
}

.margin-top-40 {
    margin-top: 40px !important;
}

.margin-top-50 {
    margin-top: 50px !important;
}

.margin-top-60 {
    margin-top: 60px !important;
}

.margin-top-70 {
    margin-top: 70px !important;
}

.margin-top-80 {
    margin-top: 80px !important;
}

.margin-bottom-0 {
    margin-bottom: 0 !important;
}

.margin-bottom-10 {
    margin-bottom: 10px !important;
}

.margin-bottom-16 {
    margin-bottom: 16px !important;
}

.margin-bottom-20 {
    margin-bottom: 20px !important;
}

.margin-bottom-24 {
    margin-bottom: 24px !important;
}

.margin-bottom-30 {
    margin-bottom: 30px !important;
}

.margin-bottom-35 {
    margin-bottom: 35px !important;
}

.margin-bottom-40 {
    margin-bottom: 40px !important;
}

.margin-bottom-60 {
    margin-bottom: 60px !important;
}

.margin-left-60 {
    margin-left: 60px !important;
}

.margin-left-40 {
    margin-left: 40px !important;
}

.margin-left-24 {
    margin-left: 24px !important;
}

.margin-left-20 {
    margin-left: 20px !important;
}

.margin-left-10 {
    margin-left: 10px !important;
}

.margin-left-8 {
    margin-left: 8px !important;
}

.margin-left-0 {
    margin-left: 0px !important;
}

.margin-right-8 {
    margin-right: 8px !important;
}

.margin-right-10 {
    margin-right: 10px !important;
}

.margin-right-15 {
    margin-right: 15px !important;
}

.margin-right-20 {
    margin-right: 20px !important;
}

.input-reset {
    display: inline-block;
    line-height: 1;
    padding: 0;
    margin: 0;
    border: none;
    outline: none;
    float: none;
}

.input-contain {
    display: inline-block;
    width: 300px;
}

.pre-reset {
    display: block;
    padding: 0;
    margin: 0;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    word-break: break-all;
    word-wrap: break-word;
    background-color: transparent;
    border: none;
    border-radius: 0;
}
/* 功能原子类 */
.strong {
    font-weight: bold;
}

.text-unindent {
    text-indent: 0;
}

.line-height-1 {
    line-height: 1;
}

.line-height-2 {
    line-height: 2 !important;
}

.nowrap {
    white-space: nowrap;
}

.normal-wrap {
    white-space: normal;
}

.text-overflow {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.font-weight-normal {
    font-weight: normal;
}

.scroll-x {
    overflow-x: auto;
    overflow-y: hidden;
}

.link-undecorated,
.link-undecorated a {
    text-decoration: none;
}

.bg-transparent,
.bg-transparent:focus,
.bg-transparent:hover,
.bg-transparent:active {
    background-color: transparent;
}

.font-size-12 {
    font-size: 12px;
}

.font-size-14 {
    font-size: 14px;
}

.font-size-16 {
    font-size: 16px;
}
/* 业务原子类 */
.show-captcha {
    cursor: pointer;
}
/* 金额整数部分字体 */
.money-int {
    font-size: 24px;
}

/* 异步请求loading效果 */
.ny-loading-tip {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
}

.ny-loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.6;
    filter: alpha(opacity=60);
    background: #000;
    width: 100%;
    height: 100%;
}

.loading-tip-head {
    height: 50px;
    line-height: 50px;
    background-color: #f7f9fa;
    border-radius: 2px 2px 0 0;
    padding-left: 20px;
    color: #636566;
}

.ny-loading-box {
    position: absolute;
    top: 33%;
    left: 50%;
    min-width: 360px;
    line-height: 100px;
    border-radius: 2px 2px 0 0;
    margin-top: -30px;
    margin-left: -150px;
    background: #fff;
    color: #666;
    font-size: 16px;
    box-shadow: 2px 2px 3px rgba(0,0,0,0.6);
}

    .ny-loading-box > i {
        float: left;
        width: 40px;
        height: 40px;
        margin: 10px 20px 0 20px;
        margin-top: 29px;
        margin-left: 60px;
        background: url(img/uc/ny_loading.gif?v=2) no-repeat center center;
    }
/* 预定义 transition */
.default-transition,
.default-transition-fast,
.default-transition-slow,
.default-transition-children > *,
.default-transition-children-fast > *,
.default-transition-children-slow > *,
.default-transition-tree *,
.default-transition-tree-fast *,
.default-transition-tree-slow * {
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    -ms-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
    /*默认transition-timing-function值是 ease，可以省略*/
    -webkit-transition-timing-function: ease;
    -moz-transition-timing-function: ease;
    -ms-transition-timing-function: ease;
    -o-transition-timing-function: ease;
    transition-timing-function: ease;
    /* 有的元素通过 z-index 来做突出层级，则失去 hover时也会失去 z-index，这时部分过渡效果会受到影响。所以这里加上z-index，维持与其它属性的过渡的共同作用 */
    -webkit-transition-property: visibility, height, background-color, background-position, color, border-color, opacity, z-index, transform, -webkit-box-shadow, bottom;
    -moz-transition-property: visibility, height, background-color, background-position, color, border-color, opacity, z-index, transform, -moz-box-shadow, bottom;
    -ms-transition-property: visibility, height, background-color, background-position, color, border-color, opacity, z-index, transform, -ms-box-shadow, bottom;
    -o-transition-property: visibility, height, background-color, background-position, color, border-color, opacity, z-index, transform, -o-box-shadow, bottom;
    transition-property: visibility, height, background-color, background-position, color, border-color, opacity, z-index, transform, box-shadow, bottom;
}

.default-transition-fast,
.default-transition-children-fast > *,
.default-transition-tree-fast * {
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    -ms-transition-duration: 0.2s;
    -o-transition-duration: 0.2s;
    transition-duration: 0.2s;
}

.default-transition-slow,
.default-transition-children-slow > *,
.default-transition-tree-slow * {
    -webkit-transition-duration: 0.6s;
    -moz-transition-duration: 0.6s;
    -ms-transition-duration: 0.6s;
    -o-transition-duration: 0.6s;
    transition-duration: 0.6s;
}

.clear-transition,
.clear-transition.default-transition,
.clear-transition.default-transition-fast,
.clear-transition.default-transition-slow,
.clear-transition.default-transition-children > *,
.clear-transition.default-transition-children-fast > *,
.clear-transition.default-transition-children-slow > *,
.clear-transition.default-transition-tree *,
.clear-transition.default-transition-tree-fast *,
.clear-transition.default-transition-tree-slow * {
    -webkit-transition-property: none;
    -moz-transition-property: none;
    -ms-transition-property: none;
    -o-transition-property: none;
    transition-property: none;
}

/* 通用icon类 */
/* tip-icon-主题-大小 分类 */
.tip-icon-success-huge {
    background: url(img/uc/tip_icon_success_50.png) no-repeat;
}

.tip-icon-success-large {
    background: url(img/uc/tip_icon_success_40.png) no-repeat;
}

.tip-icon-success-small {
    background: url(img/uc/tip_icon_success_20.png) no-repeat;
}

.tip-icon-success-mini {
    background: url(img/uc/tip_icon_success_16.png) no-repeat;
}

.tip-icon-warn-huge {
    background: url(img/uc/tip_icon_warn_50.png) no-repeat;
}

.tip-icon-warn-large {
    background: url(img/uc/tip_icon_warn_40.png) no-repeat;
}

.tip-icon-warn-small {
    background: url(img/uc/tip_icon_warn_20.png) no-repeat;
}

.tip-icon-warn-mini {
    background: url(img/uc/tip_icon_warn_16.png) no-repeat;
}

.tip-icon-warn-mini-solid {
    background: url(img/uc/tip_icon_warn_16_solid.png) no-repeat;
}

.tip-icon-error-huge {
    background: url(img/uc/tip_icon_error_50.png) no-repeat;
}

.tip-icon-error-large {
    background: url(img/uc/tip_icon_error_40.png) no-repeat;
}

.tip-icon-error-small {
    background: url(img/uc/tip_icon_error_20.png) no-repeat;
}

.tip-icon-error-mini {
    background: url(img/uc/tip_icon_error_16.png) no-repeat;
}

.tip-icon-info-mini {
    background: url(img/uc/tip_icon_info_16.png) no-repeat;
}

.tip-icon-info-large {
    background: url(img/uc/tip_icon_info_40.png) no-repeat;
}
/* 临时新增 */
.huidao-guoqu {
    display: inline-block;
    width: 80px;
    height: 28px;
    background: url(img/uc/header_back_past.png) no-repeat center;
    vertical-align: middle;
    margin-left: 20px;
}

.guide-mask-outer {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
}

.guide-mask {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.6;
    filter: alpha(opacity=60);
    background: #000;
    width: 100%;
    height: 100%;
}

.guide-detail {
    position: absolute;
    top: 0;
    left: 214px;
    width: 569px;
    height: 176px;
    background: url(img/uc/header_guide_detail.png);
    cursor: pointer;
}

select.select {
    width: auto;
    padding: 6px 3px;
    padding-left: 10px;
    border: 1px solid #ddd;
    border-radius: 2px;
    background-color: #fff;
    outline: none;
}

.btn.btn-upload {
    font-size: 14px;
    padding: 0 30px;
    margin-top: auto;
    position: relative;
    cursor: pointer;
}

    .btn.btn-upload input {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 30px;
        opacity: 0;
        cursor: pointer;
    }

.ban-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.list-item-tips {
    color: #a0a2a3;
}

.add-link {
    display: inline-block;
    padding-left: 20px;
    color: #00aaff;
    background: url(img/uc/icon_plus.png) no-repeat left center;
    cursor: pointer;
}
/*列表上方设置按钮*/
.list-settings-container button,
.ny-back-btn {
    position: relative;
    float: left;
    display: block;
    width: 32px;
    height: 32px;
    margin-left: 8px;
    border: 1px solid #ddd;
    outline: none;
}

    .ny-back-btn:hover {
        border-color: #00aaff;
        background: url(img/uc/return-icon-hover.png) no-repeat center center;
    }

    .ny-back-btn:active,
    .ny-back-btn:focus {
        border-color: #00aaff;
        background: #d6f1ff url(img/uc/return-icon-hover.png) no-repeat center center;
    }

.ny-back-btn {
    width: 40px;
    margin-top: 4px;
    margin-right: 15px;
    margin-left: 0;
    background: url(img/uc/return-icon.png) no-repeat center center;
}

.snap-related .ny-panel-heading {
    border: none;
}

button.settings-reload {
    background: url(img/uc/reload-icon.png) no-repeat center center;
}

    button.settings-reload:hover,
    button.settings-reload:active {
        border-color: #00aaff;
        background: #ebf8ff url(img/uc/reload-icon-hover.png) no-repeat center center;
    }

button.settings-down {
    background: url(img/uc/down-icon.png) no-repeat center center;
}

button.settings-setting {
    background: url(img/uc/settings-icon.png) no-repeat center center;
}

    button.settings-setting:hover,
    button.settings-setting:active {
        border-color: #00aaff;
        background: #ebf8ff url(img/uc/settings-icon-hover.png) no-repeat center center;
    }
/*任意地方的说明型文字*/
.text-tips {
    font-size: 12px;
    color: #a0a2a3;
}
/*每页显示*/
.btn-group-page {
    margin: 0 8px;
    height: 28px;
    line-height: 28px;
    width: 40px;
    width: 45px\9;
    width: 45px\0;
    text-align: center;
    font-size: 12px;
    color: #666;
    border: 1px solid transparent;
    outline: none;
}

    .btn-group-page:hover {
        border-color: #ddd;
    }

.server-help {
    margin-top: 10px;
    padding-left: 24px;
    color: #00aaff;
    font-size: 14px;
    background: url(img/uc/server_help.png) no-repeat left center;
}
/*多选框组件*/
.ny-checkbox {
    display: inline-block;
    height: 0;
    margin: 0;
    padding: 0;
}

    .ny-checkbox label {
        min-width: 16px;
        height: 16px;
        line-height: 16px;
        vertical-align: text-top;
        font-size: 12px;
        color: #262829;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-repeat: no-repeat;
        background-position: left center;
        background-image: url(img/components/checkbox-normal.png);
    }

        .ny-checkbox label span {
            padding-left: 20px;
            white-space: nowrap;
        }

    .ny-checkbox.checked label,
    .ny-checkbox.checked label:hover {
        background-image: url(img/components/checkbox-checked.png);
    }

    .ny-checkbox.checked.disabled label,
    .ny-checkbox.checked.disabled label:hover {
        cursor: no-drop;
        background-image: url(img/components/checkbox-checked-disabled.png);
    }

    .ny-checkbox.disabled label,
    .ny-checkbox.disabled label:hover {
        cursor: no-drop;
        background-image: url(img/components/checkbox-disabled.png);
    }

    .ny-checkbox label:hover {
        background-image: url(img/components/checkbox-hover.png);
    }

    .ny-checkbox input {
        display: none;
    }


/*单选框*/
.ny-radio {
    display: inline-block;
    margin: 0;
    padding: 0;
    height: 16px;
    line-height: 16px;
    vertical-align: middle;
}

    .ny-radio label {
        min-width: 16px;
        height: 16px;
        line-height: 16px;
        vertical-align: text-top;
        font-size: 12px;
        color: #262829;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-repeat: no-repeat;
        background-position: left center;
        background-image: url(img/components/radio-normal.png);
    }

        .ny-radio label span {
            padding-left: 20px;
            white-space: nowrap;
        }

    .ny-radio.checked label,
    .ny-radio.checked label:hover {
        background-image: url(img/components/radio-checked.png);
    }

    .ny-radio.checked.disabled label,
    .ny-radio.checked.disabled label:hover {
        cursor: no-drop;
        background-image: url(img/components/radio-checked-disabled.png);
    }

    .ny-radio.disabled label,
    .ny-radio.disabled label:hover {
        cursor: no-drop;
        background-image: url(img/components/radio-disabled.png);
    }

    .ny-radio label:hover {
        background-image: url(img/components/radio-hover.png);
    }

    .ny-radio input {
        display: none;
    }

/*列表设置*/
.table-column-set {
    min-width: 480px;
}

    .table-column-set ul li {
        float: left;
        width: 25%;
        margin-top: 10px;
        margin-bottom: 20px;
    }

.ny-tabs-container-new {
    border-bottom: none;
}

    .ny-tabs-container-new ul {
        margin-bottom: 25px;
        background-color: #f0f3f5;
        border-bottom: 1px solid #e1e4e6;
    }

    .ny-tabs-container-new .ny-tab {
        margin-right: 0;
        border-right: none;
        border-left: none;
        color: #262829;
        background-color: transparent;
        text-align: center;
    }

    .ny-tabs-container-new .ny-tab--current {
        border-right: 1px solid #e1e4e6;
        border-left: 1px solid #e1e4e6;
    }
/*默认图标样式与设置为默认按钮样式*/
.text-default-icon {
    border: 1px solid #ff6600;
    padding: 3px 10px;
    text-align: center;
    color: #ff6600;
    background: #ffdac2;
    font-size: 12px;
}

.set-default-btn {
    border: 1px solid #eb5e00;
    padding: 3px 10px;
    text-align: center;
    color: #fff;
    background: #ff6600;
    font-size: 12px;
}

    .set-default-btn:hover {
        color: #fff;
        text-decoration: none;
    }

.ny-table-has-border {
    border: 1px solid #e5e5e5;
}

.table-space {
    margin-top: -20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.table-list {
    width: 100%;
}

    .table-list .ny-th-title {
        width: 95px;
        margin-left: 36px;
        text-align: left;
    }

@media screen and (max-width: 1400px) {
    .table-list .ny-th-title {
        margin-left: 7px;
        margin-right: 0;
    }
}

.table-list tbody tr {
    height: 46px;
    line-height: 46px;
}

    .table-list tbody tr:hover,
    .table-no-bg tbody tr:hover {
        background: #fff;
    }

.table-list td {
    padding: 0;
}
/*列表下拉选择手型样式*/
.has-sort-list {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
/* 弹窗头部帮助链接样式 */
.aui_title {
    position: relative;
}

.dialog-help-link {
    position: absolute;
    top: 17px;
    background: url(img/uc/icon_question.png);
    width: 16px;
    height: 16px;
    margin-left: 16px;
    cursor: pointer;
}

/* NY.js 上传插件IE浏览器用来隐藏上传按钮所用*/
.attachment-add-disappear {
    display: none;
}

/*续费,升级代金券提示框*/
.server-cash-coupon-tip {
    margin-left: 22px;
    display: inline-block;
    position: relative;
    border: 1px solid #dadbdc;
    padding: 0 12px 0 31px;
    height: 32px;
    line-height: 30px;
    font-size: 12px;
    color: #646568;
    background: url(img/uc/cashcoupon_tip_icon.png) no-repeat 7px center;
}

    .server-cash-coupon-tip:before {
        position: absolute;
        z-index: 10;
        display: block;
        content: " ";
        border: 8px solid transparent;
        border-right-color: #fff;
        bottom: 7px;
        left: -7px;
        z-index: 10;
        margin-left: -8px;
    }

    .server-cash-coupon-tip:after {
        position: absolute;
        z-index: 9;
        display: block;
        content: " ";
        border: 8px solid transparent;
        border-right-color: #ccc;
        bottom: 7px;
        left: -8px;
        z-index: 9;
        margin-left: -8px;
    }

/*防爆破验证码弹窗*/
.security-code-pop-wrapper.ny-form .ny-control-label {
    width: 60px;
    font-size: 12px;
}

.security-code-pop-wrapper.ny-form .form-group {
    margin-bottom: 0;
}

.security-code-pop-wrapper.ny-form .captcha-input {
    width: 120px;
}

.security-code-pop-wrapper.ny-form .captcha-input {
    font-size: 12px;
}

.security-code-pop-wrapper.ny-form .show-captcha {
    position: relative;
    top: -1px;
}
/* 全局物流信息弹窗 */
.express-info-outer {
    max-height: 600px;
    min-width: 300px;
    width: 700px;
    line-height: 1.8em;
    overflow: auto;
    font-size: 12px;
    color: #666;
}

.express-info-top {
    padding-bottom: 15px;
    border-bottom: 1px solid #e6e6e6;
}

.express-info-outer .express-final {
    color: #00aaff;
}

.express-info-outer ul {
    padding-top: 15px;
}

    .express-info-outer ul li {
        position: relative;
        padding-left: 40px;
        padding-bottom: 20px;
    }

        .express-info-outer ul li:before {
            content: "";
            position: absolute;
            left: 8px;
            top: 6px;
            z-index: 10;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #d9d9d9;
        }

        .express-info-outer ul li.express-final:before {
            background-color: #0080ff;
        }

        .express-info-outer ul li:after {
            content: "";
            position: absolute;
            width: 2px;
            top: 22px;
            left: 12px;
            bottom: 0;
            background-color: #e6e6e6;
        }


/* table里面内容过长 */
.td-tooltip {
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

    .td-tooltip.cell {
        overflow: hidden;
    }

/* textarea表单校验 */
textarea.error-input, textarea.error-input:focus {
    border-color: red;
    background-color: #fff5f5;
}



.header-list-messages {
    padding: 0px;
}


    .header-list-messages > ul {
        display: flex;
        padding-top: 0px;
    }

        .header-list-messages > ul li {
            position: relative;
            height: 50px;
            line-height: 50px;
            flex: 1;
            color: #606266;
            font-size: 14px;
            text-align: center;
            cursor: pointer;
            background-color: #f0f3f5;
        }

            .header-list-messages > ul li:hover {
                background-color: #fff;
            }

    .header-list-messages .selected {
        background-color: #fff;
    }

    .header-list-messages > ul li i {
        position: absolute;
        background-color: red;
        color: #fff;
        left: 89px;
        display: block;
        top: 7px;
        /* height: 20px; */
        line-height: 18px;
        width: 24px;
        height: 18px;
        text-align: center;
        border-radius: 8px;
    }

    /*            .header-list-messages ul li:hover {
                background-color: #ecf5ff;
                color: #66b1ff;
            }*/


    .header-list-messages > div {
        padding-left: 10px;
        padding-right: 10px;
        padding-bottom: 5px;
        display: none;
    }

    .header-list-messages #0 {
        display: block;
    }

.f-success {
    background: url(img/success.png) no-repeat 20px center;
    padding-left: 40px !important;
}
.f-down {
    background: url(img/f-down.png) no-repeat left center
}
.f-down:hover {
    background: url(img/f-down-hover.png) no-repeat left center
}

.f-wait {
    background: url(img/wait.png) no-repeat 20px center;
    padding-left: 40px !important;
}

.f-bebeing {
    background: url(img/bebeing.png) no-repeat 20px center;
    padding-left: 40px !important;
}

.f-not-down {
    background: url(img/wait.png) no-repeat 0px center;
    color: #0089d9;
}