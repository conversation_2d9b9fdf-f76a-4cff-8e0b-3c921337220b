﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>知识问答</summary>
public partial interface IKnowledgeQuiz
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>标题</summary>
    String? Name { get; set; }

    /// <summary>内容</summary>
    String? Content { get; set; }

    /// <summary>标签</summary>
    String? Tags { get; set; }

    /// <summary>产品型号Id</summary>
    Int32 MId { get; set; }

    /// <summary>产品型号内容</summary>
    String? MIdName { get; set; }

    /// <summary>点击数</summary>
    Int32 Clicks { get; set; }

    /// <summary>点赞数</summary>
    Int32 HelpFuls { get; set; }

    /// <summary>数据状态 0为不可用，1为已发表</summary>
    Int32 Status { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
