﻿using System.ComponentModel;
using System.Dynamic;

using DG.Cube;
using DG.Cube.BaseControllers;

using DH.Helpers;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Models;

using XCode.Membership;

using YRY.Web.Controllers;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>产品型号</summary>
//[DisplayName("产品型号")]
//[Description("用于产品型号的管理")]
//[AdminArea]
//[DGMenu(ParentMenuName = "ProductMenu", ParentMenuDisplayName = "商品", ParentMenuUrl = "~/{area}/ProductModel", ParentMenuOrder = 70, CurrentMenuUrl = "~/{area}/ProductModel", CurrentMenuName = "ProductModelList", CurrentIcon = "&#xe71f;")]
public class ProductModelController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 产品型号列表
    /// </summary>
    /// <returns></returns>
    /// <param name="name">型号搜索</param>
    /// <param name="page">当前页码</param>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("产品型号列表")]
    public IActionResult Index(string name, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = ProductModel.Searchs(name, pages);
        ViewBag.list = list;
        viewModel.page = page;
        viewModel.name = name;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });

        return View(viewModel);
    }

    /// <summary>
    /// 产品型号新增页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("产品型号新增页面")]
    public IActionResult CreateModel()
    {
        var DisplayOrder = ProductModel.FindMax("DisplayOrder");
        ViewBag.DisplayOrder = DisplayOrder + 1;
        var Model = DictionariesCategory.FindByCode("HomeModuleCategory");
        var List = DataDictionary.FindAllByDId(Model.Id).OrderBy(x => x.Id);
        ViewBag.Plist = List;
        return View();
    }

    /// <summary>
    /// 新增产品型号接口
    /// </summary>
    /// <param name="Name">产品型号名称</param>
    /// <param name="DisplayOrder">排序</param>
    /// <param name="select">数据字典ID</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增产品型号接口")]
    public IActionResult CreateModel(string Name, short DisplayOrder, int select)
    {
        if (Name.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("型号名称不能为空！") });
        }

        Name = Name.Trim();
        var ex = ProductModel.FindByName(Name);
        if (ex != null)
        {
            return Prompt(new PromptModel { Message = GetResource("型号名称已存在！") });
        }

        using (var tran1 = ProductModel.Meta.CreateTrans())
        {
            var Model = new ProductModel();
            Model.Name = Name;
            Model.DisplayOrder = DisplayOrder;
            Model.Insert();

            if (select > 0)
            {
                var Modele = new ProductModelEx();
                var DicModel = DataDictionary.FindById(select);
                if (DicModel != null)
                {
                    Modele.CodeType = DicModel.CodeType;
                    Modele.Id = Model.Id;
                    Modele.Insert();
                }
                else
                {
                    return Prompt(new PromptModel { Message = GetResource("首页工单类别！") });
                }
            }
            tran1.Commit();
        }
        ProductModel.Meta.Cache.Clear("");
        ProductModelEx.Meta.Cache.Clear("");

        Loger.UserLog("新增", $"新增产品型号：{Name}");

        return MessageTip(GetResource("新增成功"));
    }

    /// <summary>
    /// 修改产品型号
    /// </summary>
    /// <param name="Id">型号Id</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改产品型号")]
    public IActionResult EditModel(int Id)
    {
        var Model = ProductModel.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
        }

        var Models = DictionariesCategory.FindByCode("HomeModuleCategory");
        var List = DataDictionary.FindAllByDId(Models.Id).OrderBy(x => x.Id);
        ViewBag.Plist = List;
        return View(Model);
    }

    /// <summary>
    /// 产品型号修改
    /// </summary>
    /// <param name="Name"></param>
    /// <param name="DisplayOrder"></param>
    /// <param name="Id"></param>
    /// <param name="select"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("产品型号修改")]
    public IActionResult EditModel(string Name, short DisplayOrder, int Id, int select)
    {
        if (Name.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("型号名称不能为空") });
        }

        var Model = ProductModel.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("产品型号不存在,或已被删除"));
        }

        Name = Name.Trim();

        var ex = ProductModel.FindByName(Name);
        if (ex != null && ex.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("型号名称已存在") });
        }

        using (var tran1 = ProductModel.Meta.CreateTrans())
        {
            Model.Name = Name;
            Model.DisplayOrder = DisplayOrder;
            Model.Update();

            if (select > 0)
            {
                var Modele = ProductModelEx.FindById(Model.Id);
                if (Modele == null)
                {
                    Modele = new ProductModelEx();
                    Modele.Id = Model.Id;
                }
                
                var DicModel = DataDictionary.FindById(select);
                if (DicModel != null)
                {
                    Modele.CodeType = DicModel.CodeType;
                }
                
                Modele.Save();
            }
            tran1.Commit();
        }
        
        ProductModelEx.Meta.Cache.Clear("");
        ProductModel.Meta.Cache.Clear("");
        Loger.UserLog("修改", $"修改产品型号：{Name}");

        return MessageTip(GetResource("修改成功"));
    }

    /// <summary>
    /// 产品型号删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("产品型号删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        using (var tran1 = ProductModel.Meta.CreateTrans())
        {
            ProductModel.DelByIds(Ids.Trim(','));
            ProductModelEx.DelByIds(Ids.Trim(','));
            tran1.Commit();
        }
        ProductModelEx.Meta.Cache.Clear("");
        ProductModel.Meta.Cache.Clear("");
        Loger.UserLog("删除", $"删除产品型号：{Ids}");

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 模糊查询产品型号数据
    /// </summary>
    /// <param name="Key"></param>
    /// <returns></returns>
    [DHAuthorize]
    [DisplayName("模糊查询产品型号数据")]
    public IActionResult GetlikeName(string Key)
    {
        if (Key.IsNullOrWhiteSpace())
        {
            return Json(new List<ProductModel>());
        }
        var List1 = ProductModel.FindAllByLikeName(Key.SafeString().Trim());
        var list = new List<Xmsekect>();

        foreach (var item in List1)
        {
            var model = new Xmsekect {name = item.Name, value = item.Id};
            list.Add(model);
        }
        return Json(list);
    }
}
