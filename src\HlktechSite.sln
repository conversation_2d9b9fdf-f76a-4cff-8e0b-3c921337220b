﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31606.5
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{22CD266C-6FF7-4451-BB7D-D5FEF628C180}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{4C74CFA3-A011-4E0E-90B5-65FA4A57ACFD}"
	ProjectSection(SolutionItems) = preProject
		..\global.json = ..\global.json
		..\NuGet.config = ..\NuGet.config
		..\说明.txt = ..\说明.txt
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{1061BC78-5EFA-41B5-8F8E-7A06EA5A34E3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HlktechSite", "HlktechSite\HlktechSite.csproj", "{C631E49E-A44E-4499-AF1C-F1EF31FDF437}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HlktechSite.Entity", "HlktechSite.Entity\HlktechSite.Entity.csproj", "{663C88C6-4FC0-4AE9-B825-70BF91A15193}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Demo", "Demo", "{AE32D6D9-D4F4-4580-AA27-386749B6D9A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ConsoleApp1", "..\Demo\ConsoleApp1\ConsoleApp1.csproj", "{0B1294A1-C3F2-43F7-AEA1-D7C4AD725ABF}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{ACECF638-1A97-4EDA-8678-2D623FA355E2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AIAddress", "AIAddress\AIAddress.csproj", "{6D25E46E-5273-46B6-B19E-A9B0B6B10268}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C631E49E-A44E-4499-AF1C-F1EF31FDF437}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C631E49E-A44E-4499-AF1C-F1EF31FDF437}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C631E49E-A44E-4499-AF1C-F1EF31FDF437}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C631E49E-A44E-4499-AF1C-F1EF31FDF437}.Release|Any CPU.Build.0 = Release|Any CPU
		{663C88C6-4FC0-4AE9-B825-70BF91A15193}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{663C88C6-4FC0-4AE9-B825-70BF91A15193}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{663C88C6-4FC0-4AE9-B825-70BF91A15193}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{663C88C6-4FC0-4AE9-B825-70BF91A15193}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B1294A1-C3F2-43F7-AEA1-D7C4AD725ABF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B1294A1-C3F2-43F7-AEA1-D7C4AD725ABF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B1294A1-C3F2-43F7-AEA1-D7C4AD725ABF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B1294A1-C3F2-43F7-AEA1-D7C4AD725ABF}.Release|Any CPU.Build.0 = Release|Any CPU
		{ACECF638-1A97-4EDA-8678-2D623FA355E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ACECF638-1A97-4EDA-8678-2D623FA355E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ACECF638-1A97-4EDA-8678-2D623FA355E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ACECF638-1A97-4EDA-8678-2D623FA355E2}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D25E46E-5273-46B6-B19E-A9B0B6B10268}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D25E46E-5273-46B6-B19E-A9B0B6B10268}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D25E46E-5273-46B6-B19E-A9B0B6B10268}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D25E46E-5273-46B6-B19E-A9B0B6B10268}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C631E49E-A44E-4499-AF1C-F1EF31FDF437} = {22CD266C-6FF7-4451-BB7D-D5FEF628C180}
		{663C88C6-4FC0-4AE9-B825-70BF91A15193} = {1061BC78-5EFA-41B5-8F8E-7A06EA5A34E3}
		{0B1294A1-C3F2-43F7-AEA1-D7C4AD725ABF} = {AE32D6D9-D4F4-4580-AA27-386749B6D9A4}
		{6D25E46E-5273-46B6-B19E-A9B0B6B10268} = {AE32D6D9-D4F4-4580-AA27-386749B6D9A4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4489639F-9F46-48DE-93BF-957D843C6A17}
	EndGlobalSection
EndGlobal
