﻿@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";
    Html.AppendCssFileParts("~/css/mobile/prodetails.css");
}
<style type="text/css">
    body > img {
        width: 100%;
    }
</style>
<img src="@(CDN.GetCDN())/@Model.Product.Image" alt="Alternate Text" />

<p class="down-details-title">@T("资料下载")</p>

<div class="pro-content">
    <div id="c-id3" style="display:block;">
        <ul>
            <li>
                <span>@T("开发资料")</span>
                <p>

                    @foreach (var item in Model.Development)
                    {
                        <i>
                            @item.resource_name
                            <a href="@item.resource_url"><img src="@(CDN.GetCDN())/images/down.png" /></a>
                        </i>
                    }
                </p>
            </li>
            <li>
                <span>@T("软件应用")</span>
                <p>
                    @foreach (var item in Model.Application)
                    {
                        <i>
                            @item.resource_name
                            <a href="@item.resource_url"><img src="@(CDN.GetCDN())/images/down.png" /></a>
                        </i>
                    }
                </p>
            </li>
            <li>
                <span>@T("通用软件")</span>
                <p>
                    @foreach (var item in Model.GeneralSoftware)
                    {
                        <i>
                            @item.resource_name
                            <a href="@item.resource_url"><img src="@(CDN.GetCDN())/images/down.png" /></a>
                        </i>
                    }
                </p>
            </li>
            <li>
                <span>@T("常见问题")</span>
                <p>
                    @*@foreach (var item in Model.Application)
                        {
                            <i>
                                @item.resource_name
                                <a href="@item.resource_url"><img src="@(CDN.GetCDN())/images/down.png" /></a>
                            </i>
                        }*@
                </p>
            </li>
        </ul>
    </div>
</div>