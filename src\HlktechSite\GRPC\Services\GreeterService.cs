using DG;
using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using NewLife.Caching;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HlktechSite;

public class GreeterService : Greeter.GreeterBase
{
    public GreeterService()
    {
    }

    [Authorize("jwt")]
    public override Task<HelloReply> <PERSON><PERSON><PERSON>(HelloRequest request, ServerCallContext context)
    {
        return Task.FromResult(new HelloReply
        {
            Message = "Hello " + request.Name
        });
    }

}
