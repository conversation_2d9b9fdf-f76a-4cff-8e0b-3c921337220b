﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>产品分类</summary>
public partial interface IProductCategory
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>分类名称</summary>
    String? Name { get; set; }

    /// <summary>所属父级Id</summary>
    Int32 ParentId { get; set; }

    /// <summary>父级Id集合</summary>
    String? ParentIdList { get; set; }

    /// <summary>当前层级</summary>
    Int32 Level { get; set; }

    /// <summary>排序</summary>
    Int16 DisplayOrder { get; set; }

    /// <summary>类型ID。是否联动</summary>
    Boolean TypeId { get; set; }

    /// <summary>类型名称。联动或者空</summary>
    String? TypeName { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
