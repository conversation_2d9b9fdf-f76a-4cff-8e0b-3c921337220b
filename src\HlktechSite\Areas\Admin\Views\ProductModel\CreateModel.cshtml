﻿@model HlktechSite.Entity.ProductModel
<style asp-location="true">
    .page {
        min-height: 410px;
        height: 100%;
    }

    .html {
        background-color: #FFF;
    }
</style>
<div class="page">
    <form id="ProductModel_form" method="post" action='' name="adminForm">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required w120"><label class="validation" for="nav_title">@T("名称")</label></td>
                    <td><input type="text" name="Name" id="Name" value="" class="w200"></td>
                    <td></td>
                </tr>
            <tr class="noborder">
                <td class="required w120">@T("首页工单类别")</td>
                <td class="vatop rowform">
                    <select name="select" id="select">
                        <option value="0">@T("不更改所属类别（更改下拉）")</option>
                        @foreach (var item in (IEnumerable<HlktechSite.Entity.DataDictionary>)ViewBag.Plist)
                        {
                            <option value="@item.Id">&nbsp;&nbsp; @item.Name</option>
                        }
                    </select>
                </td>

            </tr>
                <tr class="noborder">
                    <td class="required w120">@T("排序")</td>
                    <td><input type="text" name="DisplayOrder" id="DisplayOrder" value="@ViewBag.DisplayOrder" class="w200"></td>
                    <td></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="2"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
