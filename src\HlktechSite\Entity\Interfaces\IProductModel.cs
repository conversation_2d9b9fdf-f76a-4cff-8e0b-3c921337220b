﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>产品型号</summary>
public partial interface IProductModel
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>型号</summary>
    String? Name { get; set; }

    /// <summary>排序</summary>
    Int16 DisplayOrder { get; set; }

    /// <summary>商城Pc链接</summary>
    String? PcShopUrl { get; set; }

    /// <summary>商城移动端链接</summary>
    String? MobileShopUrl { get; set; }

    /// <summary>淘宝产品链接</summary>
    String? TaobaoShopUrl1 { get; set; }

    /// <summary>小程序二维码图片链接</summary>
    String? AppletQrUrl { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
