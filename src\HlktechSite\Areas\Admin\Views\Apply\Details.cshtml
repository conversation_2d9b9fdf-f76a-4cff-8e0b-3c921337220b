﻿@model Customization
<style asp-location="true">
    .page {
        min-height: 415px
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("定制申请详情")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Details",new { Id=Model.Id})" class="current"><span>@T("定制申请详情")</span></a></li>
            </ul>
        </div>
    </div>
    <form id="goods_class_form" enctype="multipart/form-data" method="post">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("公司名称")</td>
                    <td class="vatop rowform">@Model.ComName</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("主营业务")</td>
                    <td class="vatop rowform">@Model.Business</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("申请人职务")</td>
                    <td class="vatop rowform">@Model.Business</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("邮箱")</td>
                    <td class="vatop rowform">@Model.Email</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("公司网址")</td>
                    <td class="vatop rowform">@Model.ComUrl</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("公司联系人")</td>
                    <td class="vatop rowform">@Model.Linkman</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("手机号")</td>
                    <td class="vatop rowform">@Model.Phone</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("定制类型")</td>
                    <td class="vatop rowform">@(Model.Type==1? "产品" : "解决方案")</td>
                    <td class="vatop tips"></td>
                </tr>
                @if (Model.Type == 1)
                {

                    <tr class="noborder">
                        <td class="required w120">@T("定制基于产品型号")</td>
                        <td class="vatop rowform">@Model.Model</td>
                        <td class="vatop tips"></td>
                    </tr>
                    <tr class="noborder">
                        <td class="required w120">@T("定制产品首批采购量")</td>
                        <td class="vatop rowform">@Model.Purchase</td>
                        <td class="vatop tips"></td>
                    </tr>
                    <tr class="noborder">
                        <td class="required w120">@T("定制产品预计年均需求量")</td>
                        <td class="vatop rowform">@Model.Predict</td>
                        <td class="vatop tips"></td>
                    </tr>
                }
                <tr class="noborder">
                    <td class="required w120">@T("定制需求")</td>
                    <td class="vatop rowform">@Model.Demand</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("应用项目背景")</td>
                    <td class="vatop rowform">@Model.Setting</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("提交时间")</td>
                    <td class="vatop rowform">@Model.CreateTime</td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
        </table>
    </form>
</div>