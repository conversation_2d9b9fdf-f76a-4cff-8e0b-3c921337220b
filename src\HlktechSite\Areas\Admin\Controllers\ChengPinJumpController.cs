﻿using DG.Cube;
using DG.Cube.BaseControllers;

using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>成品跳转管理</summary>
[DisplayName("成品跳转管理")]
[Description("成品的推文链接以及APP下载链接管理")]
[AdminArea]
[DHMenu(80,ParentMenuName = "ProductMenu", CurrentMenuUrl = "~/{area}/ChengPinJump", CurrentMenuName = "ChengPinJumpList", CurrentIcon = "&#xe652;", LastUpdate = "20240125")]
public class ChengPinJumpController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 80;

    /// <summary>
    /// 成品跳转列表
    /// </summary>
    /// <param name="key"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("成品跳转列表")]
    public IActionResult Index(string key, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        viewModel.list = JumpProduct.Search(key, null, null, "", pages);


        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key } });

        viewModel.page = page;
        viewModel.key = key;

        return View(viewModel);
    }

    /// <summary>
    /// 成品添加
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品跳转添加")]
    public IActionResult Add()
    {
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View();
    }

    /// <summary>
    /// 成品添加
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("成品跳转添加")]
    [HttpPost]
    public IActionResult Add(String Name, String InfoUrl, String Content, String AndroidPaths, String AndroidPaths1, String IosPaths, String AdWord, IFormFile pic)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("产品名称不能为空！") });
        }

        Name = Name.SafeString().Trim();
        var exit = JumpProduct.FindByName(Name);
        if (exit != null)
        {
            return Prompt(new PromptModel { Message = GetResource("产品名称已存在！") });
        }

        var model = new JumpProduct();
        model.Name = Name;
        model.InfoUrl = InfoUrl;
        model.Content = Content;
        model.AndroidPaths = AndroidPaths;
        model.AndroidPaths1 = AndroidPaths1;
        model.IosPaths = IosPaths;
        model.AdWord = AdWord;

        if (pic != null)
        {
            var bytes = pic.OpenReadStream().ReadBytes(pic.Length);
            if (!bytes.IsImageFile())
            {
                return Prompt(new PromptModel { Message = GetResource("非法操作，请上传正常图片！"), IsOk = false });
            }

            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(pic.FileName)}";
            var filepath = DH.DHSetting.Current.UploadPath.CombinePath($"productcategory/{filename}");
            var saveFileName = DH.DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
            saveFileName.EnsureDirectory();
            pic.SaveAs(saveFileName);

            model.AppLogo = filepath.Replace("\\", "/");
        }

        model.Insert();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            var filea = Request.Form.Files;
            var list = filea.Count();
            foreach (var item in Languagelist)
            {
                var modelJumpProductLan = new JumpProductLan();
                modelJumpProductLan.Name = GetRequest($"[{item.Id}].Name").SafeString().Trim();
                modelJumpProductLan.InfoUrl = GetRequest($"[{item.Id}].InfoUrl").SafeString().Trim();
                modelJumpProductLan.AdWord = GetRequest($"[{item.Id}].AdWord").SafeString().Trim();
                modelJumpProductLan.LId = item.Id;
                modelJumpProductLan.JId = model.Id;
                modelJumpProductLan.Insert();
            }
        }

        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 成品编辑
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("成品跳转编辑")]
    public IActionResult Edit(Int32 Id)
    {
        var Model = JumpProduct.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }

        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        return View(Model);
    }

    /// <summary>
    /// 成品编辑
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("成品跳转编辑")]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String Name, String InfoUrl, String Content, String AndroidPaths, String AndroidPaths1, String IosPaths, String AdWord, IFormFile pic)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("产品名称不能为空！") });
        }

        var model = JumpProduct.FindById(Id);
        if (model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }

        Name = Name.SafeString().Trim();
        var EXIT = JumpProduct.FindByName(Name);
        if (EXIT != null && EXIT.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("产品名称已存在！") });
        }

        model.Name = Name;
        model.InfoUrl = InfoUrl;
        model.Content = Content;
        model.AndroidPaths = AndroidPaths;
        model.AndroidPaths1 = AndroidPaths1;
        model.IosPaths = IosPaths;
        model.AdWord = AdWord;

        if (pic != null)
        {
            var bytes = pic.OpenReadStream().ReadBytes(pic.Length);
            if (!bytes.IsImageFile())
            {
                return Prompt(new PromptModel { Message = GetResource("非法操作，请上传正常图片！"), IsOk = false });
            }

            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(pic.FileName)}";
            var filepath = DH.DHSetting.Current.UploadPath.CombinePath($"productcategory/{filename}");
            var saveFileName = DH.DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
            saveFileName.EnsureDirectory();
            pic.SaveAs(saveFileName);

            model.AppLogo = filepath.Replace("\\", "/");
        }

        model.Update();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

            var lanlist = JumpProductLan.FindAllByJId(model.Id);
            foreach (var item in Languagelist)
            {
                var modelJumpProductLan = lanlist.Find(x => x.LId == item.Id);
                if (modelJumpProductLan == null)
                {
                    modelJumpProductLan = new JumpProductLan();
                }

                modelJumpProductLan.Name = GetRequest($"[{item.Id}].Name").SafeString().Trim();
                modelJumpProductLan.InfoUrl = GetRequest($"[{item.Id}].InfoUrl").SafeString().Trim();
                modelJumpProductLan.AdWord = GetRequest($"[{item.Id}].AdWord").SafeString().Trim();
                modelJumpProductLan.LId = item.Id;
                modelJumpProductLan.JId = model.Id;

                modelJumpProductLan.Save();
            }
        }

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 知识问答数据删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("知识问答数据删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();

        JumpProduct.DelByIds(Ids);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
