﻿
@{
    Html.AppendCssFileParts("~/css/Download.css");
}



<div class="download-top">
    <img src="@(CDN.GetCDN())/images/downBanner.png" />
    <div>
        <h2>@T("下载中心")</h2>
        <P>@T("深耕行业十数载，累计服务超过10W+客户")</P>
    </div>
</div>

<div class="navigation">
    <div class="navigation-con">
        <div class="navigation-con-left">
            @("下载分类"):
            <div class="btn-group">
                @{
                    var CName = "全部";

                    foreach (var item in Model.ProTypelist as IList<ProductCategory>)
                    {
                        if (Model.CId == item.Id)
                            CName = item.Name;
                    }

                }
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    @CName <span class="caret"></span>
                </button>
                <ul class="dropdown-menu pro-type-dropdown">
                    <li><a href="JavaScript:;" data-id="0">全部</a></li>
                    @foreach (var item in Model.ProTypelist)
                    {
                        <li><a href="javascript:;" data-id="@item.Id">@item.Name</a></li>
                    }
                </ul>
            </div>
        </div>
        <div class="navigation-con-right">
            @T("按产品型号搜索"):
            <div class="input-group">
                <input type="text" class="form-control" id="KeyVal" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2" value="@Model.Key">
                <span class="input-group-addon" id="basic-addon2"><img src="@(CDN.GetCDN())/images/fdj.png" /></span>
            </div>
        </div>
    </div>
</div>

<div class="download-list">


    @foreach (var item in Model.list as IEnumerable<Goods>)
    {
        <div>
            <img src="@(CDN.GetCDN())@item.Image" />
            <h2>@item.Name</h2>
            <a href="@Url.DGAction("Details",new { Id=item.Id})">@T("资料下载")</a>
        </div>
    }

    @*<div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>
        <div>
            <img src="@(CDN.GetCDN())/images/productdemo2.png" />
            <h2>HLK-RM04</h2>
            <a href="@Url.DGAction("Details","downLoad")">资料下载</a>
        </div>*@
</div>

<div class="paging" style="text-align: center;padding-bottom:70px">
    <ul class="pagination">
        @Html.Raw(Model.Str)
    </ul>
</div>
<script asp-location="Footer">
    $("#basic-addon2").click(function () {
        window.location.href = '@Url.DGAction("Index")?key=' + $("#KeyVal").val()+"&CId=@(Model.CId)";
    })
    $(".pro-type-dropdown li").click(function () {
        window.location.href = '@Url.DGAction("Index")?key=' + $("#KeyVal").val()+"&CId="+ $(this).find("a").attr("data-id");
    });
</script>