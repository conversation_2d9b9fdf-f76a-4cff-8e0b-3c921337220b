using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using DG.Entity;

using NewLife;
using NewLife.Data;

using Pek;

using XCode;

namespace HlktechSite.Entity;

/// <summary>商品表</summary>
public partial class Goods : CubeEntityBase<Goods>
{
    #region 对象操作
    static Goods()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        var df = Meta.Factory.AdditionalFields;
        df.Add(nameof(Clicks));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add<UserModule>();
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add<IPModule>();
    }

    /// <summary>验证数据，通过抛出异常的方式提示验证失败。</summary>
    /// <param name="isNew">是否插入</param>
    public override void Valid(Boolean isNew)
    {
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return;

        // 在新插入数据或者修改了指定字段时进行修正
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化Goods[商品表]数据……");

    //    var entity = new Goods();
    //    entity.Id = 0;
    //    entity.Name = "abc";
    //    entity.AdvWord = "abc";
    //    entity.MId = 0;
    //    entity.CId = 0;
    //    entity.Cid1 = 0;
    //    entity.Cid2 = 0;
    //    entity.Cid3 = 0;
    //    entity.Image = "abc";
    //    entity.Content = "abc";
    //    entity.MobileContent = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化Goods[商品表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性

    /// <summary>
    /// 获取型号
    /// </summary>
    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public IList<ProductModel>? ProductModels => Extends.Get(nameof(ProductModels), k => ProductModel.FindByIds(MId));

    /// <summary>
    /// 获取型号名称
    /// </summary>
    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public String? ProductModelName => ProductModels?.Select(e => e.Name).Join();

    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public String? Images { get; set; }
    #endregion

    #region 扩展查询
    public static IList<Goods> FindByIds(string ids)
    {
        if (String.IsNullOrEmpty(ids)) return new List<Goods>();

        var list = new List<Goods>();
        var arr = ids.SplitAsInt(",");
        
        if (Meta.Session.Count <= 1000)
        {
            return Meta.Cache.FindAll(e => arr.Contains(e.Id));
        }
        
        return FindAll(_.Id.In(arr));
    }

    /// <summary>
    /// 根据产品分类查询
    /// </summary>
    /// <param name="CId">商品分类ID</param>
    /// <param name="Key">查询的关键字</param>
    /// <param name="page">分页的数据</param>
    /// <returns></returns>
    public static IEnumerable<Goods> SearchByCId(String Key, int CId, PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var list1 = FindAllWithCache();

            list1 = list1.Where(x => x.Shelf == true && (!Key.IsNotNullAndWhiteSpace() || x.Name.Contains(Key)) && (CId == 0 || x.CId == CId)).OrderByDescending(x => x.CreateTime).ToList();
            page.TotalCount = list1.Count();
            list1 = list1.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
            return list1;
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.Name.Contains(Key);
            exp &= _.Shelf == true;
            exp &= _.CId == CId;
            return FindAll(exp, page).OrderByDescending(x => x.CreateTime);
        }

    }

    /// <summary>
    /// 分页查询多个CId
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="CIds"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    public static IEnumerable<Goods> SearchByCIds(String Key, String CIds, PageParameter page)
    {
        var CidArray = Array.ConvertAll(CIds.Split(","), int.Parse);

        if (Meta.Session.Count < 1000)
        {
            var list1 = FindAllWithCache();

            list1 = list1.Where(x => x.Shelf == true && (!Key.IsNotNullAndWhiteSpace() || x.Name.Contains(Key)) && CidArray.Contains(x.CId)).OrderByDescending(x => x.CreateTime).ToList();
            page.TotalCount = list1.Count();
            //list1 = list1.Skip(0).Take(page.PageSize).ToList();
            list1 = list1.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
            return list1;
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.Shelf == true;
            exp &= _.Name.Contains(Key);
            exp &= _.CId.In(CIds.Split(","));
            return FindAll(exp, page).OrderByDescending(x => x.CreateTime);
        }
    }

    /// <summary>
    /// 查询产品
    /// </summary>
    /// <param name="page"></param>
    /// <returns></returns>
    public static IEnumerable<Goods> Searchs(PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var list = Meta.Cache.FindAll(x => x.Commend == true && x.Shelf == true).Skip(0).Take(page.PageSize);
            return list;
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.Commend == true;
            exp &= _.Shelf == true;
            return FindAll(exp, page);
        }
    }


    /// <summary>
    /// 产品中心(查询上架的产品)
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    public static IEnumerable<Goods> SearchByShelf(String Key, PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var list = Meta.Cache.FindAll(x => x.Shelf == true && (Key.IsNullOrWhiteSpace() || x.Name.Contains(Key))).OrderByDescending(x => x.Sort).ThenByDescending(x=>x.CreateTime);
            page.TotalCount = list.Count();
            return list.Skip(--page.PageIndex * page.PageSize).Take(page.PageSize);
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.Shelf == true;
            return FindAll(exp, page);
        }
    }

    /// <summary>根据商品分类ID查找</summary>
    /// <param name="cId">商品分类ID</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> FindAllByCId(Int32 cId)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.CId == cId);

        return FindAll(_.CId == cId);
    }

    /// <summary>根据一级分类ID查找</summary>
    /// <param name="cid1">一级分类ID</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> FindAllByCid1(Int32 cid1)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Cid1 == cid1);

        return FindAll(_.Cid1 == cid1);
    }

    /// <summary>根据二级分类ID查找</summary>
    /// <param name="cid2">二级分类ID</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> FindAllByCid2(Int32 cid2)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Cid2 == cid2);

        return FindAll(_.Cid2 == cid2);
    }

    /// <summary>根据三级分类ID查找</summary>
    /// <param name="cid3">三级分类ID</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> FindAllByCid3(Int32 cid3)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Cid3 == cid3);

        return FindAll(_.Cid3 == cid3);
    }

    /// <summary>
    /// 根据ID集合删除数据
    /// </summary>
    /// <param name="Ids">ID集合</param>
    public static void DelByIds(String Ids)
    {
        if (Delete(_.Id.In(Ids)) > 0)
            Meta.Cache.Clear("");
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="name">产品名</param>
    /// <param name="commend">是否推荐</param>
    /// <param name="page"></param>
    /// <param name="CId"></param>
    /// <param name="CId1"></param>
    /// <param name="CId2"></param>
    /// <param name="CId3"></param>
    /// <returns></returns>
    public static IEnumerable<Goods> Searchs(string name, int commend, int CId, int CId1, int CId2, int CId3, PageParameter page)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000)
        {
            IEnumerable<Goods> list;

            list = Meta.Cache.FindAll(x => (name.IsNullOrWhiteSpace() || x.Name.ToLower().Contains(name.ToLower())));
            if (commend != 0)
            {
                list = list.Where(e => e.Commend == (commend == 1));
            }

            if (CId > 0)
            {
                list = list.Where(e => e.CId == CId);
            }
            if (CId1 > 0)
            {
                list = list.Where(e => e.Cid1 == CId1);
            }
            if (CId2 > 0)
            {
                list = list.Where(e => e.Cid2 == CId2);
            }
            if (CId3 > 0)
            {
                list = list.Where(e => e.Cid3 == CId3);
            }
            page.TotalCount = list.Count();

            list = list.OrderByDescending(e => e.Id).Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize);
            return list;
        }

        var exp = new WhereExpression();
        if (name.IsNotNullAndWhiteSpace())
        {
            exp &= _.Name.Contains(name);
        }
        if (CId >= 0)
        {
            exp &= _.CId == CId;
        }
        if (CId1 >= 0)
        {
            exp &= _.Cid1 == CId1;
        }

        if (CId1 >= 0)
        {
            exp &= _.Cid1 == CId1;
        }
        if (CId2 >= 0)
        {
            exp &= _.Cid2 == CId2;
        }
        if (CId3 >= 0)
        {
            exp &= _.Cid3 == CId3;
        }

        return FindAll(exp, page);
    }

    /// <summary>
    /// 根据标题查询
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public static Goods? FindByName(String name)
    {
        if (name.IsNullOrEmpty()) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

        return Find(_.Name == name);
    }

    /// <summary>
    /// 根据产品型号集合查询
    /// </summary>
    /// <param name="MId"></param>
    /// <returns></returns>
    public static IList<Goods> FindByMId(Int32 MId)
    {
        if (MId <= 0) return new List<Goods>();

        return Meta.Cache.FindAll(e => e.MId?.SplitAsInt(",").Contains(MId) == true);
    }

    /// <summary>
    /// 前台分页查询
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    public static IEnumerable<Goods> SearchQ(String Key, PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var list1 = FindAllWithCache();
            if (Key.IsNullOrWhiteSpace())
            {
                list1 = (IList<Goods>)list1.Where(x => x.Name.Contains(Key));
            }

            page.TotalCount = list1.Count();
            var list = list1.OrderByDescending(x => x.CreateTime).Skip(0).Take(page.PageSize);
            return list;
        }
        else
        {
            var exp = new WhereExpression();
            if (Key.IsNullOrWhiteSpace())
            {
                exp &= _.Name.Contains(Key);
            }
            return FindAll(exp, page).OrderByDescending(x => x.CreateTime);
        }

    }

    /// <summary>
    /// 查询所有
    /// </summary>
    /// <returns></returns>
    public static IList<Goods> GetAll()
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return FindAllWithCache();

        // 单对象缓存
        return FindAll();
    }
    /// <summary>
    /// 获取最大的排序
    /// </summary>
    /// <returns></returns>
    public static Goods GetMaxSort()
    {
        if (Meta.Session.Count < 1000)
        {
            return FindAllWithCache().OrderByDescending(x => x.Sort).Skip(0).Take(1).FirstOrDefault();
        }
        else
        {
            return FindAll(new WhereExpression(),new PageParameter { Desc = true, Sort = _.Sort, PageSize = 1 }).FirstOrDefault();
        }
    }

    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="mId">产品型号Id</param>
    /// <param name="cId">商品分类ID</param>
    /// <param name="cid1">一级分类ID</param>
    /// <param name="cid2">二级分类ID</param>
    /// <param name="cid3">三级分类ID</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> Search(Int32 mId, Int32 cId, Int32 cid1, Int32 cid2, Int32 cid3, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (mId >= 0) exp &= _.MId == mId;
        if (cId >= 0) exp &= _.CId == cId;
        if (cid1 >= 0) exp &= _.Cid1 == cid1;
        if (cid2 >= 0) exp &= _.Cid2 == cid2;
        if (cid3 >= 0) exp &= _.Cid3 == cid3;
        if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key) | _.AdvWord.Contains(key) | _.Image.Contains(key) | _.Content.Contains(key) | _.MobileContent.Contains(key);

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,Category From Goods Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<Goods> _CategoryCache = new FieldCache<Goods>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();


    /// <summary>
    /// 获取推荐的上一篇下一篇是否推荐的产品
    /// </summary>
    /// <param name="time"></param>
    /// <param name="Commend"></param>
    /// <returns></returns>
    public static Goods Findprevious(DateTime time, bool Commend)
    {

        // 实体缓存
        if (Meta.Session.Count < 1000)
        {
            return Meta.Cache.FindAll(e => e.CreateTime > time && e.Commend == Commend).OrderBy(o => o.CreateTime).FirstOrDefault();
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.CreateTime > time & _.Commend == Commend;
            return FindAll(exp, new PageParameter { Sort = _.CreateTime, Desc = true, PageSize = 1 }).FirstOrDefault();
        }
    }
    /// <summary>根据文章ID查找下一篇是否推荐的产品</summary>
    /// <returns>实体对象列表</returns>
    public static Goods FindNext(DateTime time, bool Commend)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000)
        {
            return Meta.Cache.FindAll(e => e.CreateTime < time && e.Commend == Commend).OrderByDescending(o => o.CreateTime).FirstOrDefault();
        }
        else
        {
            var exp = new WhereExpression();
            exp &= _.CreateTime < time & _.Commend == Commend;
            return FindAll(exp, new PageParameter { Sort = _.CreateTime, Desc = false, PageSize = 1 }).FirstOrDefault();
        }
    }

    #endregion

    #region 业务操作
    #endregion
}