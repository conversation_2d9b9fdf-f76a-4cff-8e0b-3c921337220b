﻿/*搜索下拉的样式*/

.carousel-inner button {
    border: none;
    height: 70px;
    border-radius: 35px;
}

.open > .dropdown-toggle.btn-default:hover, .open > .dropdown-toggle.btn-default:focus, .btn-default:active:focus, .btn-default:active:hover {
    background-color: #fff;
    box-shadow: none;
}

.container .btn-group {
    left: 0;
    top: 70px;
    z-index: 1;
    border-radius: 35px;
}

.search-wrapper {
    top: 20%;
}

.carousel-inner button:hover {
    background-color: #fff;
}

.carousel-inner input {
    margin-left: 90px;
}

.input-holder {
    background-color: #fff;
}


    /*搜索文本框*/
    .input-holder input {
        width: 55% !important;
        padding: 0px !important;
    }



/*搜索下拉的样式*/
ul.dropdown-menu a {
    padding-left: 10px;
}

/*中间的大div*/
.custom-con {
    max-width: 1363px;
    margin: 0 auto;
    background-color: #FFFFFF;
    border-radius: 25px;
    box-shadow: 0px 8px 29px 0px rgba(97,97,97,0.24);
    padding-top: 54px;
}

.lj1 {
    font-size: 30px;
    padding-top: 129px;
    color: rgba(51,51,51,1);
    font-weight: normal;
}

.custom-type {
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
    margin-top: 87px;
    padding-bottom: 73px;
}

    .custom-type .col-md-2 p {
        padding-top: 15px;
        margin: 0px;
        font-size: 16px;
        margin-bottom: 50px;
        color: rgba(11,10,10,1);
    }

.custom-bottom {
    display: flex;
}

    .custom-bottom > div {
        flex: 1;
        text-align: center;
        padding-bottom: 333px;
    }


        .custom-bottom > div > img {
            width: 344px;
            height: 346px;
            margin-bottom: 84px;
        }

        .custom-bottom > div:last-child > img {
            width: 394px;
        }

        .custom-bottom > div > span, .custom-bottom > div > i {
            display: block;
            text-align: center;
            font-style: normal;
        }

        .custom-bottom > div > span {
            font-size: 20px;
            padding-bottom: 28px;
        }

        .custom-bottom > div > i {
            font-size: 16px;
            padding-bottom: 32px;
        }

        .custom-bottom > div > a {
            display: inline-block;
            width: 146px;
            height: 43px;
            color: rgba(255,255,255,1);
            font-size: 20px;
            line-height: 43px;
            border-radius: 22px;
            background: rgba(58,117,255,1);
            box-shadow: 0px 8px 15px 5px rgba(76,78,83,0.24);
        }

@media screen and (max-width:768px) {
    .input-holder input {
        width: 23% !important;
    }

    .lj1 {
        padding-top: 50px;
        padding-left: 0px;
        text-align: center;
    }

    .lj2 {
        padding-right: 0px;
        padding-top: 50px;
        padding-bottom: 50px;
    }

        .lj2 > img {
            width: 80%;
            margin: 0 auto;
        }

    .custom-bottom {
        display: block;
    }

        .custom-bottom > div {
            padding-bottom: 50px;
        }

            .custom-bottom > div > img {
                margin-bottom: 30px;
            }
}
