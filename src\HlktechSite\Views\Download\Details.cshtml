﻿
@{
    Html.AppendCssFileParts("~/css/Download.css");
}


<div class="download-top">
    <img src="@(CDN.GetCDN())/images/downBanner.png" />
    <div>
        <h2>@T("下载中心")</h2>
        <P>@T("深耕行业十数载，累计服务超过10W+客户")</P>
    </div>
</div>
<div class="navigation">
    <div class="navigation-con">
        <div class="navigation-con-left">
            <span>
                @await Component.InvokeAsync("Location", new { model = Model.Locations })
            </span>
        </div>
        @*<div class="navigation-con-right">
            <div class="input-group">
                <input type="text" class="form-control" id="KeyVal" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2">
                <span class="input-group-addon" id="basic-addon2"><img src="@(CDN.GetCDN())/images/fdj.png"></span>
            </div>
        </div>*@
    </div>
</div>
<div class="download-details">
    <p><span>@T("资料下载")</span><span><a href="#">@T("查看详情")</a><a href="#">@T("在线购买")</a></span></p>
    <ul>
        <li>
            <span>@T("开发资料")</span>
            <span>
                @foreach (var item in Model.Development)
                {
                    <i>
                        @item.resource_name
                        <a href="@item.resource_url"><img src="@(CDN.GetCDN())/images/down.png" /></a>
                    </i>
                }
            </span>
        </li>
        <li>
            <span>@T("软件应用")</span>
            <span>
                @*<i>
                        P2P透传测试 APP
                        <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
                    </i>
                    <i>
                        APP源码
                        <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
                    </i>*@
                @foreach (var item in Model.Application)
                {
                    <i>
                        @item.resource_name
                        <a href="@item.resource_url"><img src="@(CDN.GetCDN())/images/down.png" /></a>
                    </i>
                }
            </span>
        </li>
        <li>
            <span>@T("通用软件")</span>
            <span>
                @*<i>
            串口&amp;TCP_UDP调试工具
            <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
        </i>
        <i>
            TCP&amp;UDP测试工具
            <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
        </i>
        <i>
            串口工具
            <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
        </i>
        <i>
            串口配置工具
            <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
        </i>*@
                @foreach (var item in Model.GeneralSoftware)
                {
                    <i>
                        @item.resource_name
                        <a href="@item.resource_url"><img src="@(CDN.GetCDN())/images/down.png" /></a>
                    </i>
                }
            </span>
        </li>
        <li>
            <span>@T("常见问题")</span>
            <span>
                @*<i>
                    RM04问题文档
                    <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
                </i>
                <i>
                    常见问题
                    <a href="#"><img src="@(CDN.GetCDN())/images/down.png"></a>
                </i>*@
            </span>
        </li>
    </ul>
</div>
<script asp-location="Footer">
    $("#basic-addon2").click(function () {
        window.location.href = '@Url.DGAction("Index")?key=' + $("#KeyVal").val();
    })

</script>
