﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>数据字典</summary>
public partial interface IDataDictionary
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>字典类型Id</summary>
    Int32 DId { get; set; }

    /// <summary>字典值——标识</summary>
    String? CodeType { get; set; }

    /// <summary>字典值——名称</summary>
    String? Name { get; set; }

    /// <summary>字典值——状态</summary>
    Boolean Status { get; set; }

    /// <summary>字典值——排序</summary>
    Int32 Sort { get; set; }

    /// <summary>字典值——描述</summary>
    String? Content { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
