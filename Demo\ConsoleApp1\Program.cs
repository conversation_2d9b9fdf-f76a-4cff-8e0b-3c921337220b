﻿using DG.Caching;
using NewLife.Log;
using NewLife.Security;
using System;

namespace ConsoleApp1
{
    class Program
    {
        static void Main(string[] args)
        {
            XTrace.UseConsole();
            var key = "test";
            var _cache = DGMemoryCache.Instance;
            var exp = 10;
            var s = _cache.GetOrAddAsync<String>(key, () =>
            {
                XTrace.WriteLine($"测试测试_{exp}");
                var ss = "12345678910" + Rand.NextString(4);
                XTrace.WriteLine($"获取到的数据aaaaaa：{_cache.Get<String>(key)}");
                return ss;
            }, exp);
            XTrace.WriteLine($"获取到的数据：{s}");

            var _cache1 = new DGMemoryCache();
            var cache1 = _cache1.Get<String>(key);
            XTrace.WriteLine($"获取到的数据1111：{cache1}");

            var _cache2 = DGMemoryCache.Instance;
            var cache2 = _cache2.Get<String>(key);
            XTrace.WriteLine($"获取到的数据2222：{cache2}");

            Console.ReadKey();
        }
    }
}
