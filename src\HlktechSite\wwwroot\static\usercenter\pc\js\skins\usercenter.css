@charset "utf-8";
/*
 * artDialog skin
 * http://code.google.com/p/artdialog/
 * (c) 2009-2011 TangBin, http://www.planeArt.cn
 *
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 *
 * 2016-05-04 会员中心2016版弹窗皮肤 by <PERSON>
 */

/* common start */
body {
	_margin: 0;
	_height: 100%; /*IE6 BUG*/
}
.aui_outer {
	text-align: left;
}
.aui_inner {
	padding: 0;
}
table.aui_border, table.aui_dialog {
	border: 0;
	margin: 0;
	border-collapse: collapse;
	width: auto;
}
.aui_header, .aui_buttons button {
	font: 12px/1.11 'Microsoft Yahei', Tahoma, Arial, Helvetica, STHeiti;
	_font-family: 'Microsoft Yahei', Tahoma, Arial, Helvetica, STHeiti;
	-o-font-family: 'Microsoft Yahei', Tahoma, Arial;
}
.aui_header {
	border-radius: 2px 2px 0 0;
}
.aui_title {
	overflow: hidden;
	margin:0 auto;
	text-overflow: ellipsis;
}
.aui_state_noTitle .aui_title {
	display: none;
}
.aui_close {
	display: block;
	position: absolute;
	text-decoration: none;
	outline: none;
	_cursor: pointer;
}
.aui_close:hover {
	text-decoration: none;
}
.aui_main {
	text-align: center;
	min-width: 9em;
	min-width: 0 \9/*IE8 BUG*/;
}
.aui_content {
	display: inline-block;
	*zoom: 1;
	*display: inline;
	text-align: left;
	border: none 0;
	font-size: 16px;
	padding-top: 44px;
}
.aui_content p {
	margin: 0;
	font-size: 16px;
	color: #4c4c4c;
}
.aui_content .sub-tip {
	margin-top: 30px;
	font-size: 12px;
	color: #999;
}
.aui_content.aui_state_full {
	display: block;
	width: 100%;
	margin: 0;
	padding: 0 !important;
	height: 100%;
}
.aui_loading {
	width: 96px;
	height: 32px;
	text-align: left;
	text-indent: -999em;
	overflow: hidden;
	background: url(icons/loading.gif) no-repeat center center;
}
.aui_icon {
	position: relative;
	padding-left: 60px;
	width: 120px;
}
.aui_icon div {
	position: absolute;
	top: 40px;
	right: 15px;
	width: 40px;
	height: 40px;
	background-position: center center;
	background-repeat: no-repeat;
}
.aui_buttons {
	/*padding: 11px 15px 10px 18px;*/
	padding-right: 25px;
	height: 55px;
	line-height: 55px;
	border-top: 1px solid #eee;
	background-color: #f5f9fa;
	text-align: right;
	white-space: nowrap;
}
.aui_buttons button {
	padding: 0px 20px;
	height: 30px;
	cursor: pointer;
	display: inline-block;
	text-align: center;
	*padding: 4px 10px;
	*height: 2em; /*letter-spacing:2px;*/
	font-family: "Microsoft Yahei", Tahoma, Arial/9 !important;
	width: auto;
	overflow: visible;
	*width: 1;
	color: #333;
	background: #f7f7f7;
	border: 1px solid #d9d9d9;
	font-size: 14px;
	-webkit-transition: background 0.3s ease;
	-moz-transition: background 0.3s ease;
	-o-transition: background 0.3s ease;
	transition: background 0.3s ease;
}
.aui_buttons button + button {
	margin-left: 20px;
}
.aui_buttons button::-moz-focus-inner {
	border: 0;
	padding: 0;
	margin: 0;
}
.aui_buttons button:focus {
	outline: none 0;
}
.aui_buttons button:hover {
	background: #fff;
}
.aui_buttons button:active {
	background: linear-gradient(top, #dddddd, #fff);
	background: -moz-linear-gradient(top, #dddddd, #fff);
	background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#dddddd), to(#fff));
}
.aui_buttons button[disabled],
.aui_state_highlight[disabled]:hover {
	cursor: not-allowed;
	color: #666;
	background: #DDD;
	border: solid 1px #999;
	filter: alpha(opacity=50);
	opacity: .5;
	box-shadow: none;
}
button.aui_state_highlight {
	color: #FFF;
	background: #00aaff;
	border-color: #009ceb;
}
button.aui_state_highlight:hover {
	color: #FFF;
	background: #0089d9;
}
button.aui_state_highlight:active {
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#328fc1', endColorstr='#22aff8');
	background: linear-gradient(top, #328fc1, #22aff8);
	background: -moz-linear-gradient(top, #328fc1, #22aff8);
	background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#328fc1), to(#22aff8));
}
/* common end */

.aui_inner {
	background: #FFF;
	border-radius: 2px 2px 0 0;
}
.aui_border {
	box-shadow: inset 0 0 1px rgba(255, 255, 255, .9);
}
.aui_state_focus .aui_outer {
	box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}
/*设置对话框阴影*/
.aui_state_lock .aui_border {
	box-shadow: 0 3px 26px rgba(12, 12, 12, 0.9);
}
.aui_state_drag .aui_outer, .aui_outer:active {
	box-shadow: none;
}
.aui_titleBar {
	position: relative;
	height: 100%;
	margin: 0 auto;
	background-color: #f7f9fa;
	border-radius: 2px 2px 0 0;
}
.aui_title {
	line-height: 48px;
	padding: 0px 0px 0px 24px;
	font-weight: normal;
	color: #636566;
	font-family: "Microsoft Yahei", Tahoma, Arial/9 !important;
	border-bottom: 1px solid #eee;
	font-size: 18px;
}
.aui_state_focus .aui_title {
	color: #636566;
}
.aui_state_drag .aui_title {
}
.aui_state_drag .aui_titleBar {
	box-shadow: none;
}
.aui_close,
.aui_close:focus {
	padding: 0;
	top: 31%;
	right: 22px;
	width: 21px;
	height: 21px;
	line-height: 21px;
	font-size: 30px;
	color: #636566;
	text-align: center;
	font-family: Helvetica, STHeiti;
	_font-family: Tahoma, '\u9ed1\u4f53', 'Book Antiqua', Palatino;
}
.aui_close:hover {
	color: #636566;
}
.aui_close:active {
	-webkit-transform: scale(0.9);
}
.aui_content {
	color: #666;
}
.aui_state_focus .aui_content {
	color: #4d4d4d;
}
.aui_state_noTitle .aui_inner {
	background: #FFF;
}
.aui_state_noTitle .aui_outer {
	border: none 0;
	box-shadow: none;
}
.aui_state_noTitle .aui_close {
	top: 0;
	right: 0;
	width: 18px;
	height: 18px;
	line-height: 18px;
	text-align: center;
	text-indent: 0;
	font-size: 18px;
	text-decoration: none;
	color: #214FA3;
	background: none;
	filter: !important;
}
.aui_state_noTitle .aui_close:hover, .aui_state_noTitle .aui_close:active {
	text-decoration: none;
	color: #900;
}
.aui_state_noTitle .aui_dialog {
	box-shadow: none;
}
