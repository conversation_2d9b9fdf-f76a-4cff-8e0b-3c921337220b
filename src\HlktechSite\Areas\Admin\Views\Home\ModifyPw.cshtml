﻿@{
}

<div class="page">
    <form id="admin_form" method="post" action='' name="adminForm">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td colspan="2" class="required"><label class="validation" for="oldPassword">@T("原密码")<!-- 原密码 -->:</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input id="oldPassword" name="oldPassword" class="infoTableInput" type="password"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr>
                    <td colspan="2" class="required"><label class="validation" for="password">@T("新密码")<!-- 新密码 -->:</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input id="password" name="password" class="infoTableInput" type="password"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr>
                    <td colspan="2" class="required"><label class="validation" for="repassword">@T("确认密码")<!-- 确认密码-->:</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input id="repassword" name="repassword" class="infoTableInput" type="password"></td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="2"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>

<script asp-location="Footer">
        $(document).ready(function () {
            $("#admin_form").validate({
                errorPlacement: function (error, element) {
                    error.appendTo(element.parent().parent().prev().find('td:first'));
                },
                rules: {
                    oldPassword: {
                        required: true
                    },
                    password: {
                        required: true,
                        minlength: 6,
                        maxlength: 20
                    },
                    repassword: {
                        required: true,
                        minlength: 6,
                        maxlength: 20,
                        equalTo: '#password'
                    }
                },
                messages: {
                    oldPassword: {
                        required: '@T("密码不能为空")'
                    },
                    password: {
                        required: '@T("密码不能为空")',
                        minlength: '@T("密码长度为6-20")',
                        maxlength: '@T("密码长度为6-20")'
                    },
                    repassword: {
                        required: '@T("密码不能为空")',
                        minlength: '@T("密码长度为6-20")',
                        maxlength: '@T("密码长度为6-20")',
                        equalTo: '@T("两次输入的密码不一致，请重新输入")'
                    }
                }
            });
        });
</script>
