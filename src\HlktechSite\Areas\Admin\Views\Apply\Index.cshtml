﻿@model IEnumerable<Customization>
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("定制申请")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("查找")</dt>
                <dd><input type="text" value="@ViewBag.key" name="key" class="txt" placeholder="公司名/公司联系人"></dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th>@T("公司名称")</th>
                <th>@T("主营业务")</th>
                <th>@T("邮箱")</th>
                <th>@T("公司网址")</th>
                <th>@T("公司联系人")</th>
                <th>@T("手机号")</th>
                <th>@T("添加时间")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
          @foreach (var item in Model)
          {
            <tr>
                <td>@item.ComName</td>
                <td>@item.Business</td>
                <td>@item.Email</td>
                <td>@item.ComUrl</td>
                <td>@item.Linkman</td>
                <td>@item.Phone</td>
                <td>@item.CreateTime</td>
                <td><a href="@Url.Action("Details",new { id=item.Id})">查看详情</a></td>
            </tr>
          }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(ViewBag.Str)
    </ul>
</div>
<script asp-location="Footer">
</script>
