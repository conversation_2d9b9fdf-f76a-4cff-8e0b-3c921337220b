﻿@{
    // script
    Html.AppendScriptParts(ResourceLocation.Footer, "/static/admin/js/xm-select.js");
}
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("知识库管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("CreateKnowledge")"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("标题")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
            </dl>
            <dl>
                <dt>@T("产品型号")</dt>
                <dd style="width:200px">
                    <div id="demo1"></div>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("编号")</th>
                <th>@T("标题")</th>
                <th>@T("产品型号")</th>
                <th>@T("点击数")</th>
                <th>@T("点赞数")</th>
                <th>@T("状态")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.list)
            {
                <tr id="<EMAIL>" style="background: rgb(255, 255, 255);">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.Pname</td>
                    <td>@item.Clicks</td>
                    <td>@item.HelpFuls</td>
                    <td>@(item.Status==1? "已发表" : "不可用")</td>
                    <td>
                        <a href="@Url.Action("EditKnowledge",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        @*<a href="javascript:;" onclick="submit_delete('@item.Id')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>*@
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="7">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script asp-location="Footer">

      function submit_delete(ids_str) {
          _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }

    @*function submit_delete(ID)
    {
        $.post("@Url.Action("Delete")", { Ids: ID }, function (res) {
            if (!res.success) {
                alert(res.msg);
            } else {
                window.location.reload(); //刷新页面
            }

        })
    }*@
     @*var data1 = $.parseJSON('@Html.Raw(ViewBag.List)')
     var demo1 = xmSelect.render({
        el: '#demo1',
        radio: true,
        autoRow: true,
        toolbar: { show: true },
        clickClose: true,
        filterable: true,
        remoteSearch: true,
        remoteMethod: function (val, cb, show) {
            //这里如果val为空, 则不触发搜索
            if (!val) {
                return cb(data1);
            }
            $.ajax({
                type: 'get',
                url: '@Url.Action("GetlikeName", "ProductModel")',
                data: {
                    Key: val
                },
                success(data) {
                    cb(data);
                },
                error: function (arg1) {
                    cb([]);
                }

            })
        }
    });*@

    var data1 = $.parseJSON('@Html.Raw(ViewBag.List)');

    var demo1 = xmSelect.render({
        el: '#demo1',
        radio: true,
        paging: true,
        pageSize: 10,
        filterable: true,
        filterMethod: function (val, item, index, prop) {
            if (item.name.toLowerCase().indexOf(val.toLowerCase()) != -1) {//名称中包含的大小写都搜索出来
                return true;
            }
            return false;//其他的就不要了
        },
        pageEmptyShow: false,
        clickClose: true,
        data: data1
    });

</script>
