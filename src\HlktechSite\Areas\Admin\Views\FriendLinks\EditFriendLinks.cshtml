﻿@{
    var localizationSettings = LocalizationSettings.Current;
}
@model HlktechSite.Entity.FriendLinks
<style asp-location="true">
    .type-file-preview {
        z-index: 99999
    }
    ul.layui-tab-title {
        width: 95%;
        margin: 0 auto;
    }
</style>

<div class="page">
    @*<div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("友情链接管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("修改")</span></a></li>
            </ul>
        </div>
    </div>*@
    @using (Html.BeginForm("EditFriendLinks", "FriendLinks", FormMethod.Post, new { id = "form1", enctype = "multipart/form-data" }))
    {
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="ncap-form-default">
                        <dl>
                            <dt>@T("合作伙伴")</dt>
                            <dd>
                                <input id="Title" name="Title" value="@Model.Name" class="input-txt" type="text">
                                <span class="err"></span>
                                @*<p class="notic">@T("系统名称, 将显示在后台顶部欢迎信息等位置")</p>*@
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("链接地址")</dt>
                            <dd>
                                <input id="Urls" name="Urls" value="@Model.Url" class="input-txt" type="text">
                                <span class="err"></span>
                                @*<p class="notic">@T("系统名称, 将显示在后台顶部欢迎信息等位置")</p>*@
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("图片标识")</dt>
                            <dd>
                                <span class="type-file-show">
                                    @if (Model.Pic != null)
                                    {
                                        <img class="show_image" src="/static/admin/images/preview.png">
                                        <div class="type-file-preview"><img src="@ViewBag.Images"></div>
                                    }
                                </span>
                                <span class="type-file-box">
                                    <input type='text' name='textfield' id='textfield' class='type-file-text' />
                                    <input type='button' name='fileupload' id='fileupload' value='上传' class='type-file-button' />
                                    <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("排序")</dt>
                            <dd>
                                <input id="Sort" name="Sort" value="@Model.Sort" class="input-txt" type="text">
                                <span class="err"></span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("类型")</dt>
                            <dd>
                                <input id="brand_showtype_0" type="radio" @(Model.FType==0?"checked":"") value="0" style="margin-bottom:6px;" name="FType">
                                <label for="brand_showtype_0">@T("文字")</label>
                                <input id="brand_showtype_1" type="radio" @(Model.FType==1?"checked":"") value="1" style="margin-bottom:6px;" name="FType">
                                <label for="brand_showtype_1">@T("图片")</label>
                            </dd>
                        </dl>
                    </div>
                </div>

                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var modelLan = HlktechSite.Entity.FriendLinksLan.FindByFIdAndLId(Model.Id, item.Id, false);

                        <div class="layui-tab-item">
                            <div class="ncap-form-default">
                                <dl>
                                    <dt>@T("合作伙伴")</dt>
                                    <dd>
                                        <input id="[@item.Id].Title" name="[@item.Id].Title" value="@modelLan.Title" class="input-txt" type="text">
                                        <span class="err"></span>

                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("链接地址")</dt>
                                    <dd>
                                        <input id="[@item.Id].Url" name="[@item.Id].Url" value="@modelLan.Url" class="input-txt" type="text">
                                        <span class="err"></span>

                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("图片标识")</dt>
                                    <dd>
                                        <span class="type-file-show">
                                                <img class="show_image" src="/static/admin/images/preview.png">
                                                <div class="type-file-preview"><img src="@modelLan.Pic"></div>
                                            </span>
                                        <span class="type-file-box">
                                            <input type='text' name='[@item.Id].textfield' id='[@item.Id].textfield' class='type-file-text' />
                                            <input type='button' name='[@item.Id].fileupload' id='[@item.Id].fileupload' data="@item.Id" value='上传' class='type-file-button' />
                                            <input name="[@item.Id].default_user_portrait" type="file" data="@item.Id" class="type-file-file" id="[@item.Id].default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                        </span>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("排序")</dt>
                                    <dd>
                                        <input id="[@item.Id].Sort" name="[@item.Id].Sort" value="@modelLan.Sort" class="input-txt" type="text">
                                        <span class="err"></span>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("类型")</dt>
                                    <dd>
                                        <input id="brand_showtype_0" type="radio"  value="0" @(modelLan.FType==0?"checked":"") style="margin-bottom:6px;" name="[@item.Id].FType">
                                        <label for="brand_showtype_0">@T("文字")</label>
                                        <input id="brand_showtype_1" type="radio" value="1"  @(modelLan.FType==1?"checked":"") style="margin-bottom:6px;" name="[@item.Id].FType">
                                        <label for="brand_showtype_1">@T("图片")</label>
                                    </dd>
                                </dl>

                            </div>
                        </div>
                    }
                }
                <div class="ncap-form-default">
                    <dl>
                        <dt></dt>
                        <dd><input class="btn" type="submit" value="提交"></dd>
                    </dl>
                </div>
            </div>
        </div>

    }
</div>
<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>

<script src="~/static/admin/js/xm-select.js"></script>
<script type="text/javascript" asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,

            element = layui.element;
    })
    $(function () {
        $("#default_user_portrait").change(function () {
            $("#textfield").val($("#default_user_portrait").val());
        });

        $(".type-file-file").change(function () {
            var id = $(this).attr("data");
            var obj = $(this).siblings().eq(0);
            obj.val($(this).val());
        })

         // 图片上传
        $('#fileupload').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: "@Url.Action("UploadImg", new { Id = 0 })",
                done: function (e, data) {
                    if (data != 'error') {
                        add_uploadedfile(data.result);
                    }
                }
            });
        });
    });
    
</script>
