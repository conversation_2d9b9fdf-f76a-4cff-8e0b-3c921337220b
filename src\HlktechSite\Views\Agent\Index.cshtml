﻿@model SingleArticle
@{
    Layout = "~/Views/Shared/_Root.cshtml";

    Html.AppendCssFileParts("~/css/agent.css");

    Html.AppendTitleParts(T("代理招商").Text + DG.Setting.Current.PageTitleSeparator + "Hi_Link");
}

<div class="agent-top">
    <img src="@(CDN.GetCDN())/images/Agent.png" />
    <div>
        <h2>@Model.Name</h2>
        <P>@T("海凌科电子在全国进行招商代理是为了充分整合代理商的资源优势")</P>
    </div>
</div>

<div class="navigation-div">
    <p class="navigation">
        @await Component.InvokeAsync("Location", new { model = ViewBag.Locations })
    </p>
</div>

@Html.Raw(Model.Content)

<div class="condition">
    <h2>@T("代理商申请条件")</h2>
    <ul>
        <li>
            <span>
                @T("商业信誉")
            </span>
            <i>
                @T("具有独立法人资格的合法企业，具有良好的咨信条件及商业信誉")
            </i>
            <img src="@(CDN.GetCDN())/images/condition1.png" />
        </li>
        <li>
            <span>
                @T("市场资源")
            </span>
            <i>
                @T("具备智能家居/电子产品的销售经验或区域资源，具有良好的市场意识和开拓精神")
            </i>
            <img src="@(CDN.GetCDN())/images/condition2.png" />
        </li>
        <li>
            <span>
                @T("人员充足")
            </span>
            <i>
                @T("具有足够的人员力量开展业务")
            </i>
            <img src="@(CDN.GetCDN())/images/condition3.png" />
        </li>
        <li>
            <span>
                @T("经营目标")
            </span>
            <i>
                @T("认同我方经营理念与价值观，有信心和能力保证销售目标的实施与达成")
            </i>
            <img src="@(CDN.GetCDN())/images/condition4.png" />
        </li>
    </ul>
    <form id="agentForm" class="condition">
        <div>

            <h2>@T("代理商在线申请")</h2>
            <span>@T("代理申请联系方式")</span>
            <div class="input-group">
                <i>@T("公司名称")</i>
                <input type="text" class="form-control" name="Name" placeholder="@T("单位名称")" aria-describedby="basic-addon2">
            </div>
            <div class="input-group">
                <i>@T("联系人")</i>
                <input type="text" class="form-control" name="ContactPerson" placeholder="@T("联系人")" aria-describedby="basic-addon2">
            </div>
            <div class="input-group">
                <i>@T("联系电话")</i>
                <input type="text" class="form-control" name="Phone" placeholder="@T("联系电话")" aria-describedby="basic-addon2">
            </div>
            <div class="input-group">
                <i>@T("邮箱")</i>
                <input type="text" class="form-control" name="Email" placeholder="@T("邮箱")" aria-describedby="basic-addon2">
            </div>
            <div class="input-group">
                <i>@T("联系地址")</i>
                <input type="text" class="form-control" name="ContactAddress" placeholder="@T("联系地址")" aria-describedby="basic-addon2">
            </div>
            <div class="input-group">
                <i>@T("申请说明")</i>
                <input type="text" class="form-control" name="Summary" placeholder="@T("申请说明")" aria-describedby="basic-addon2">
            </div>
            <div class="input-group">
                <i>@T("验证码")</i>
                <input type="text" class="form-control" name="Code" placeholder="@T("请输入验证码")" aria-describedby="basic-addon2">
                <img id="change_captcha" src="@(DG.Setting.Current.CaptChaUrl)" class="verificaimg">
            </div>
            <a href="javascript:;" id="Tijiao"> @T("立即提交")</a>

            <p>
                @T("代理商")
                <a href="@Url.DGAction("AgentMarket")">@T("查询")</a>
            </p>

        </div>
    </form>
</div>
<script asp-location="Footer">

    $("#change_captcha").click(function () {
      $(this).attr('src', '@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()));
    })


    $("#Tijiao").click(function () {
        $.post("@Url.DGAction("SubmitApplication")", $("#agentForm").serialize(), function (res) {
            if (res.success) {
                alert(res.msg);
            } else {
                alert(res.msg);
                $("#change_captcha").attr('src', '@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()));
            }
        })
    })
</script>