﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>定制申请</summary>
[Serializable]
[DataObject]
[Description("定制申请")]
[BindTable("DG_Customization", Description = "定制申请", ConnName = "DG", DbType = DatabaseType.None)]
public partial class Customization : ICustomization, IEntity<ICustomization>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _ComName;
    /// <summary>公司名称</summary>
    [DisplayName("公司名称")]
    [Description("公司名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ComName", "公司名称", "")]
    public String? ComName { get => _ComName; set { if (OnPropertyChanging("ComName", value)) { _ComName = value; OnPropertyChanged("ComName"); } } }

    private String? _Business;
    /// <summary>主营业务</summary>
    [DisplayName("主营业务")]
    [Description("主营业务")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Business", "主营业务", "")]
    public String? Business { get => _Business; set { if (OnPropertyChanging("Business", value)) { _Business = value; OnPropertyChanged("Business"); } } }

    private String? _Job;
    /// <summary>申请人职务</summary>
    [DisplayName("申请人职务")]
    [Description("申请人职务")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Job", "申请人职务", "")]
    public String? Job { get => _Job; set { if (OnPropertyChanging("Job", value)) { _Job = value; OnPropertyChanged("Job"); } } }

    private String? _Email;
    /// <summary>邮箱</summary>
    [DisplayName("邮箱")]
    [Description("邮箱")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Email", "邮箱", "")]
    public String? Email { get => _Email; set { if (OnPropertyChanging("Email", value)) { _Email = value; OnPropertyChanged("Email"); } } }

    private String? _ComUrl;
    /// <summary>公司网址</summary>
    [DisplayName("公司网址")]
    [Description("公司网址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ComUrl", "公司网址", "")]
    public String? ComUrl { get => _ComUrl; set { if (OnPropertyChanging("ComUrl", value)) { _ComUrl = value; OnPropertyChanged("ComUrl"); } } }

    private String? _Linkman;
    /// <summary>公司联系人</summary>
    [DisplayName("公司联系人")]
    [Description("公司联系人")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Linkman", "公司联系人", "")]
    public String? Linkman { get => _Linkman; set { if (OnPropertyChanging("Linkman", value)) { _Linkman = value; OnPropertyChanged("Linkman"); } } }

    private String? _Phone;
    /// <summary>手机号</summary>
    [DisplayName("手机号")]
    [Description("手机号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Phone", "手机号", "")]
    public String? Phone { get => _Phone; set { if (OnPropertyChanging("Phone", value)) { _Phone = value; OnPropertyChanged("Phone"); } } }

    private Int32 _Type;
    /// <summary>定制类型 0:解决方案 1:产品</summary>
    [DisplayName("定制类型0")]
    [Description("定制类型 0:解决方案 1:产品")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Type", "定制类型 0:解决方案 1:产品", "")]
    public Int32 Type { get => _Type; set { if (OnPropertyChanging("Type", value)) { _Type = value; OnPropertyChanged("Type"); } } }

    private String? _Model;
    /// <summary>定制基于产品型号</summary>
    [DisplayName("定制基于产品型号")]
    [Description("定制基于产品型号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Model", "定制基于产品型号", "")]
    public String? Model { get => _Model; set { if (OnPropertyChanging("Model", value)) { _Model = value; OnPropertyChanged("Model"); } } }

    private String? _Purchase;
    /// <summary>定制产品首批采购量</summary>
    [DisplayName("定制产品首批采购量")]
    [Description("定制产品首批采购量")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Purchase", "定制产品首批采购量", "")]
    public String? Purchase { get => _Purchase; set { if (OnPropertyChanging("Purchase", value)) { _Purchase = value; OnPropertyChanged("Purchase"); } } }

    private String? _Predict;
    /// <summary>定制产品预计年均需求量</summary>
    [DisplayName("定制产品预计年均需求量")]
    [Description("定制产品预计年均需求量")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Predict", "定制产品预计年均需求量", "")]
    public String? Predict { get => _Predict; set { if (OnPropertyChanging("Predict", value)) { _Predict = value; OnPropertyChanged("Predict"); } } }

    private String? _Demand;
    /// <summary>定制需求</summary>
    [DisplayName("定制需求")]
    [Description("定制需求")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Demand", "定制需求", "")]
    public String? Demand { get => _Demand; set { if (OnPropertyChanging("Demand", value)) { _Demand = value; OnPropertyChanged("Demand"); } } }

    private String? _Setting;
    /// <summary>应用的项目背景</summary>
    [DisplayName("应用的项目背景")]
    [Description("应用的项目背景")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Setting", "应用的项目背景", "")]
    public String? Setting { get => _Setting; set { if (OnPropertyChanging("Setting", value)) { _Setting = value; OnPropertyChanged("Setting"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ICustomization model)
    {
        Id = model.Id;
        ComName = model.ComName;
        Business = model.Business;
        Job = model.Job;
        Email = model.Email;
        ComUrl = model.ComUrl;
        Linkman = model.Linkman;
        Phone = model.Phone;
        Type = model.Type;
        Model = model.Model;
        Purchase = model.Purchase;
        Predict = model.Predict;
        Demand = model.Demand;
        Setting = model.Setting;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "ComName" => _ComName,
            "Business" => _Business,
            "Job" => _Job,
            "Email" => _Email,
            "ComUrl" => _ComUrl,
            "Linkman" => _Linkman,
            "Phone" => _Phone,
            "Type" => _Type,
            "Model" => _Model,
            "Purchase" => _Purchase,
            "Predict" => _Predict,
            "Demand" => _Demand,
            "Setting" => _Setting,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "ComName": _ComName = Convert.ToString(value); break;
                case "Business": _Business = Convert.ToString(value); break;
                case "Job": _Job = Convert.ToString(value); break;
                case "Email": _Email = Convert.ToString(value); break;
                case "ComUrl": _ComUrl = Convert.ToString(value); break;
                case "Linkman": _Linkman = Convert.ToString(value); break;
                case "Phone": _Phone = Convert.ToString(value); break;
                case "Type": _Type = value.ToInt(); break;
                case "Model": _Model = Convert.ToString(value); break;
                case "Purchase": _Purchase = Convert.ToString(value); break;
                case "Predict": _Predict = Convert.ToString(value); break;
                case "Demand": _Demand = Convert.ToString(value); break;
                case "Setting": _Setting = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    #endregion

    #region 字段名
    /// <summary>取得定制申请字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>公司名称</summary>
        public static readonly Field ComName = FindByName("ComName");

        /// <summary>主营业务</summary>
        public static readonly Field Business = FindByName("Business");

        /// <summary>申请人职务</summary>
        public static readonly Field Job = FindByName("Job");

        /// <summary>邮箱</summary>
        public static readonly Field Email = FindByName("Email");

        /// <summary>公司网址</summary>
        public static readonly Field ComUrl = FindByName("ComUrl");

        /// <summary>公司联系人</summary>
        public static readonly Field Linkman = FindByName("Linkman");

        /// <summary>手机号</summary>
        public static readonly Field Phone = FindByName("Phone");

        /// <summary>定制类型 0:解决方案 1:产品</summary>
        public static readonly Field Type = FindByName("Type");

        /// <summary>定制基于产品型号</summary>
        public static readonly Field Model = FindByName("Model");

        /// <summary>定制产品首批采购量</summary>
        public static readonly Field Purchase = FindByName("Purchase");

        /// <summary>定制产品预计年均需求量</summary>
        public static readonly Field Predict = FindByName("Predict");

        /// <summary>定制需求</summary>
        public static readonly Field Demand = FindByName("Demand");

        /// <summary>应用的项目背景</summary>
        public static readonly Field Setting = FindByName("Setting");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得定制申请字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>公司名称</summary>
        public const String ComName = "ComName";

        /// <summary>主营业务</summary>
        public const String Business = "Business";

        /// <summary>申请人职务</summary>
        public const String Job = "Job";

        /// <summary>邮箱</summary>
        public const String Email = "Email";

        /// <summary>公司网址</summary>
        public const String ComUrl = "ComUrl";

        /// <summary>公司联系人</summary>
        public const String Linkman = "Linkman";

        /// <summary>手机号</summary>
        public const String Phone = "Phone";

        /// <summary>定制类型 0:解决方案 1:产品</summary>
        public const String Type = "Type";

        /// <summary>定制基于产品型号</summary>
        public const String Model = "Model";

        /// <summary>定制产品首批采购量</summary>
        public const String Purchase = "Purchase";

        /// <summary>定制产品预计年均需求量</summary>
        public const String Predict = "Predict";

        /// <summary>定制需求</summary>
        public const String Demand = "Demand";

        /// <summary>应用的项目背景</summary>
        public const String Setting = "Setting";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
