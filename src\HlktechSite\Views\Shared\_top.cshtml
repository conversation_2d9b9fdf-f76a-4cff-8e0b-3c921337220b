﻿@{ 
    var cdn = CDN.GetCDN();
}
<nav style="background-color: #545c64;">
    <div class="yemei">
        <a href="@Url.DGAction("Index", "CubeHome", IsHtml:true)"><img src="@(cdn)/images/logo.png" class="logo"></a>
        <ul class="nav nav-pills">
            <li role="presentation"><a class="title1" href="@Url.DGAction("Index", "CubeHome", IsHtml:true)">@T("首页")</a></li>
            <li role="presentation"><a class="title2" href="@Url.RouteUrl("Product")">@T("产品中心")</a></li>
            <li role="presentation"><a class="title3" href="@Url.RouteUrl("Solution")">@T("解决方案")</a></li>
            @* <li role="presentation"><a class="title4" href="@Url.DGAction("Index", "Case", IsHtml:true)">@T("客户案例")</a></li> *@
            <li role="presentation"><a class="title5" href="@Url.DGAction("Index", "Agent", IsHtml:true)">@T("代理招商")</a></li>
            <li role="presentation"><a class="title6" href="@Url.DGAction("Index", "Contact", IsHtml:true)">@T("联系我们")</a></li>
            <li role="presentation"><a class="title7" href="@Url.DGAction("AboutUs", "CubeHome", IsHtml:true)">@T("关于我们1")</a></li>
            <li role="presentation"><a class="title8" href="@T("商城链接")" target="_blank">@T("在线购买")</a></li>
            <li role="presentation"><a class="title9" href="@Url.DGAction("Index", "Journalism", IsHtml:true)">@T("新闻资讯")</a></li>
            <li role="presentation"><a class="title10" target="_blank" href="//ask.hlktech.com">@T("技术支持中心")</a></li>
            <li role="presentation"><img src="@(cdn)/images/santiaogangq.png" id="stg1" class="stg"></li>
        </ul>
    </div>
    <div style="height:84px;"></div>
</nav>
