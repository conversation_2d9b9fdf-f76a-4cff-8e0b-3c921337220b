{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "DGB2B2C": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true, "useSSL": true}, "WSL": {"commandName": "WSL2", "launchBrowser": true, "launchUrl": "https://localhost:5001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:5001;http://localhost:5000"}, "distributionName": ""}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:55343", "sslPort": 44312}}}