﻿{
  "Configuration": {
    "CacheConfig": {
      "DefaultCacheTime": 60,
      "ShortTermCacheTime": 3,
      "BundledFilesCacheTime": 120
    },
    "CommonConfig": {
      "DisplayFullErrorStack": false,
      "UserAgentStringsPath": "~/App_Data/browscap.xml",
      "CrawlerOnlyUserAgentStringsPath": "~/App_Data/browscap.crawlersonly.xml",
      "UseSessionStateTempDataProvider": false,
      "MiniProfilerEnabled": false,
      "ScheduleTaskRunTimeout": null,
      "StaticFilesCacheControl": "public,max-age=31536000",
      "PluginStaticFileExtensionsBlacklist": "",
      "ServeUnknownFileTypes": false
    },
    "DistributedCacheConfig": {
      "DistributedCacheType": "redis",
      "Enabled": false,
      "ConnectionString": "127.0.0.1:6379,ssl=False",
      "SchemaName": "dbo",
      "TableName": "DistributedCache"
    },
    "HostingConfig": {
      "UseProxy": false,
      "ForwardedProtoHeaderName": "",
      "ForwardedForHeaderName": "",
      "KnownProxies": ""
    },
    "PluginConfig": {
      "UseUnsafeLoadAssembly": true
    }
  }
}