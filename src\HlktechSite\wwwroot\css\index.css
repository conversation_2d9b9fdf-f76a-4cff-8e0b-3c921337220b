﻿.body1 {
  overflow: hidden;
  width: 100%;
  height: 600px !important;
  position: absolute;
}

.body1 > p,
.body1 > span {
  color: #fff;
  position: absolute;
  left: 0px;
  right: 0px;
  display: inline-block;
  margin: 0px;
  text-align: center;
}

.body1 > p {
  top: 177px;
  font-size: 40px;
}

.body1 > span {
  top: 253px;
  font-size: 20px;
}

.ac {
  display: none !important;
}

.hezi {
  width: 100%;
  height: 600px;
  margin-top: 0px;
}

.zhu<PERSON>oti {
  font-size: 30px;
  color: #313131;
}

.bthz {
  margin: 0 auto;
  padding: 60px 0 0 0;
  text-align: center;
  height: 30px;
}

.cptjbt {
  margin: 0 auto;
  padding: 60px 0 0 0;
  text-align: center;
  height: 30px;
}

.bthz1 {
  margin: 0 auto;
  text-align: center;
  padding: 50px 0 65px 0;
}

.section .row .solve {
  display: flex;
}

.section .row .solve:nth-child(4) {
  margin-top: 61px;
}

.section .row .solve div {
  flex: 1;
  margin-right: 135px;
}

.section .row .solve div i {
  display: block;
  width: 135px;
  margin: 0 auto;
  height: 135px;
  border-radius: 50%;
  box-shadow: 0px 3px 14px -5px #7a7a7a;
}

.section .row .solve div:nth-child(3n) {
  margin-right: 0px;
}

.section .row .solve div span {
  display: block;
  text-align: center;
}

.section .row .solve div span {
  color: #090909;
  font-size: 20px;
  font-weight: bold;
  margin-top: 27px;
}

.section .row .solve div .iconbtwz1 {
  color: #999999;
  font-size: 16px;
  font-weight: 400;
  margin-top: 11px;
}

.bthz2 {
  margin: 0 auto;
  text-align: center;
  padding: 50px 0 0 0;
}

.container {
  padding-bottom: 50px;
}

.bthz4 {
  margin: 0 auto;
  padding: 50px 0 0 0;
  margin-bottom: 44px;
  text-align: center;
}

.bthz3 {
  margin: 0 auto;
  text-align: center;
  padding: 50px 0 0 0;
  width: 1019px;
  color: #434a50;
  padding-bottom: 73px;
}

.tbbox {
  width: 400px;
  height: 240px;
  margin-top: 60px;
  float: left;
}

.tbbox:hover {
  cursor: pointer;
}

.icon {
  position: absolute;
  border-radius: 100px 100px 100px 100px;
  width: 135px;
  height: 135px;
  left: calc(50% - 67.5px);
  -moz-box-shadow: 0px 3px 14px -5px #595959;
  -webkit-box-shadow: 0px 3px 14px -5px #595959;
  box-shadow: 0px 3px 14px -5px #595959;
  -webkit-transition: all 0.3s cubic-bezier(0, 0.105, 0.035, 1.57);
  -moz-transition: all 0.3s cubic-bezier(0, 0.105, 0.035, 1.57);
  transition: all 0.3s cubic-bezier(0, 0.105, 0.035, 1.57);
  -webkit-transition-delay: 0.3s;
  -moz-transition-delay: 0.3s;
  transition: top 0.3s;
}

.icon:hover {
  transition: top 0.3s;
  /*top: -20px;*/
  -moz-box-shadow: 0px 2px 20px -4px #5e5e5e;
  -webkit-box-shadow: 0px 2px 20px -4px #5e5e5e;
  box-shadow: 0px 2px 20px -4px #5e5e5e;
}

.iconbtwz {
  font-size: 20px;
  font-family: 微软雅黑;
  font-weight: 600;
  color: #313131;
}

.iconbt {
  width: 80px;
  margin: 0 auto;
  padding: 25px 0 0 0;
  margin-top: 135px;
}

.iconbt1 {
  padding: 10px 0 0 0;
  width: 240px;
  margin: 0 auto;
  text-align: center;
  padding-bottom: 60px;
}

.iconbtwz1 {
  color: #999999;
  font-size: 16px;
  font-family: 微软雅黑;
  text-align: center;
}

.cptj {
  background-color: #f9f9f9;
  width: 100%;
}

.cptjgf a div {
  width: 33.3333%;
  float: left;
  margin-right: 26px;
}

.cptjgf {
  width: 1200px;
  margin: 0 auto;
}

.cptjgf a div {
  width: calc(25% - 19.5px);
  float: left;
  margin-right: 26px;
}

.cptjgf a:nth-child(4n + 2) div {
  margin-right: 0px;
}

.fangkuai {
  overflow: hidden;
  padding-right: 17px;
  padding-left: 17px;
  background-color: #ffffff;
  margin-top: 60px;
  -moz-box-shadow: 0px 2px 9px -2px #7a7a7a;
  -webkit-box-shadow: 0px 2px 9px -2px #7a7a7a;
  box-shadow: 0px 2px 9px -2px #e5e5e5;
  padding: 0px;
  padding-bottom: 20px;
  cursor: pointer;
}

.fangkuai img {
  width: 100%;
}

.fangkuai:hover,
.fangkuai1:hover {
  -moz-box-shadow: 0px 2px 10px 1px #7a7a7a;
  -webkit-box-shadow: 0px 2px 10px 1px #7a7a7a;
  box-shadow: 0px 2px 10px 1px #ccc;
}

.cptjgf a .cpxqwzhf {
  width: 100%;
  padding-left: 19px;
  padding-top: 15px;
  padding-right: 19px;
  margin-right: 0px;
}

.cpxqbt {
  height: 47px;
  font-size: 16px;
  color: #333333;
  font-family: 微软雅黑;
  margin-bottom: 0px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.yycj {
  width: 100%;
  padding-bottom: 80px;
  background-color: #ffffff;
}

.yycjgf {
  margin: 0 auto;
}

.yycjtp {
  margin-top: 50px;
}

.yycjjjfa {
  height: 310px;
  float: left;
}

.yycjwz {
  color: #ffffff;
  font-size: 20px;
  font-family: 微软雅黑；;
}

.xgal {
  width: 100%;
  overflow: hidden;
  background-color: #f9f9f9;
}

.xgalgf {
  width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  padding-top: 40px;
  padding-top: 40px;
}

.xgalgf > a > div {
  width: calc(33.3333% - 19px);
  float: left;
  margin-right: 28px;
  text-align: center;
  margin-bottom: 64px;
  background-color: #fff;
  box-shadow: 0px 6px 36px 2px rgba(177, 174, 174, 0.5);
  margin-right: 28px;
  padding: 0px;
  cursor: pointer;
  padding-bottom: 25px;
}

.xgalgf > a:nth-child(3n) > div {
  margin-right: 0px;
}

.xgalgf a div img {
  width: 100%;
}

.not-have-margin-right {
  margin-right: 0px !important;
}

.xgalgf .col-md-4 .cpxqwz {
  height: 80px;
}

.xgalgf .col-md-4 img {
  display: inline-block;
  width: 100%;
}

.cpxqwz {
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 10px;
  margin-bottom: 0px;
  white-space: nowrap;
  height: 15px;
  overflow: hidden;
  line-height: 1.1;
  font-size: 14px;
  color: #858585;
  margin-left: 10px;
  margin-right: 10px;
}

.xwzx {
  width: 100%;
}

.xwzxgf {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  padding-top: 100px;
  padding-bottom: 116px;
}

.xwzxtpgf {
  height: 400px;
  overflow: hidden;
  background-color: blue;
  float: left;
  margin-right: 80px;
}

.xwtp {
  padding-top: 60px;
  margin-left: 50px;
}

.xwtp:hover {
  cursor: pointer;
}

.xwzxfk {
  width: 50%;
  margin-right: 20px;
  padding-left: 20px;
  padding-bottom: 20px;
  background-color: #ffffff;
  float: left;
  border-radius: 10px 10px 10px 10px;
  -moz-box-shadow: 0px 6px 20px -4px #b1aeae;
  -webkit-box-shadow: 0px 6px 20px -4px #b1aeae;
  box-shadow: 0px 6px 20px -4px #b1aeae;
}

.xwzxfk > p {
  padding-left: 38px;
  height: 56px;
  position: relative;
}

.xwzxfk > p > span {
  display: inline-block;
  text-align: center;
  float: none;
  height: 56px;
  padding-top: 30px;
  position: relative;
  cursor: pointer;
  padding-left: 20px;
  padding-right: 20px;
  transition: all 0.5s;
}

.xwzxfk > p > span:hover {
  font-size: 22px;
  font-weight: bold;
  padding-top: 20px;
}

.xwzxfk > p > span:first-child:after {
  content: "";
  position: absolute;
  background-color: #333;
  width: 1px;
  right: 1px;
  bottom: 3px;
  height: 100%;
  max-height: 23px;
  transition: all 0.5s;
}
.xwzxfk > p > span:first-child:hover:after {
  bottom: 9px;
}

.xwzxfk > p > span:last-child:before {
  content: "";
  position: absolute;
  background-color: #333;
  width: 1px;
  left: 1px;
  bottom: 3px;
  height: 100%;
  max-height: 23px;
  transition: all 0.5s;
}
.xwzxfk > p > span:last-child:hover:before {
  bottom: 9px;
}

.xgalwz {
  min-height: 300px;
}

.xgalwz li {
  list-style: none;
  display: flex;
}

.xgalwz > .xwwzxl > span:first-child {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 6%;
}

.xgalwz > .xwwzxl > span > a {
  color: #666666;
  text-decoration: none;
}
.xgalwz > .xwwzxl > span > a:hover {
  color: #333;
  text-decoration: none;
}

.xgalwz > .xwwzxl:first-child {
  margin-top: 0px;
  padding-top: 15px;
}

.xwbt,
.shu {
  float: left;
}

.xwbt {
  font-size: 16px;
  color: #333333;
  font-family: "微软雅黑" ；;
}

.shu {
  width: 1px;
  height: 18px;
  background-color: #333333;
  margin: 2px 10px 0 10px;
}

.xwwzxl {
  margin-top: 16px;
  font-size: 16px;
  color: #666666;
  padding-right: 20px;
}

.xwwz:hover {
  color: #0e90d2;
  cursor: pointer;
}

.xgalwz {
  display: none;
}

.xgalwz:nth-child(2) {
  display: block;
}

clearfix:after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}

/*轮播图样式*/
.swiper-contione {
  width: 600px;
  height: 400px;
  overflow: hidden;
  margin: 20px auto;
  position: relative;
}

.swiper {
  width: 9999999px;
  height: 400px;
  transition: all 1s ease;
}

.swiper-item {
  width: 600px;
  height: 400px;
  float: left;
  position: relative;
}

.swiper-item img {
  width: 600px;
  height: 400px;
}

.swiper-item p {
  width: 100%;
  height: 50px;
  position: absolute;
  text-align: left;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  text-align: left;
  line-height: 50px;
  padding-left: 29px;
  font-size: 20px;
  cursor: pointer;
}

.swiper-point {
  width: 100%;
  height: 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.9);
  margin-right: 5px;
  float: left;
}

.point.active {
  background: #fff;
}

.swiper-left {
  width: 60px;
  height: 100%;
  position: absolute;
  top: 0;
  left: -60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
}

.swiper-right {
  width: 60px;
  height: 100%;
  position: absolute;
  top: 0;
  right: -60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
}

.swiper-left img,
.swiper-right img {
  cursor: pointer;
}

.swiper-contione:hover .swiper-left {
  left: 0;
}

.swiper-contione:hover .swiper-right {
  right: 0;
}
