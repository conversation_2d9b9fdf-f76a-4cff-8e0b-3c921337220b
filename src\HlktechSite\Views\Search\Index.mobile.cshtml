﻿@model IList<HlktechSite.SearchModel>
@{
    Layout = "~/Views/Shared/_Root.Mobile.cshtml";
    var length1 = Model.Where(x => x.SearchType == 2).Count();
    var length2 = Model.Where(x => x.SearchType == 1).Count();
    var length3 = Model.Where(x => x.SearchType == 2).Count();
    var length4 = Model.Where(x => x.SearchType == 4).Count();

}
<style>
    .seach-list h2 {
        background-color: #E7E6EA;
        display: block;
        width: 86.13%;
        text-align: left;
        font-size: 16px;
        padding-left: 2.79%;
        margin: 0 auto;
        color: #505050;
        height: 34px;
        line-height: 34px;
        position: relative;
    }

        .seach-list h2:after {
            content: ">>";
            color: #505050;
            font-size: 16px;
            position: absolute;
            right: 0px;
            padding-right: 5%;
        }

    .search-msg {
        width: 90%;
        text-align: left;
        font-size: 13.7px;
        color: #FE1212;
        padding-top: 14px;
        padding-bottom: 11.5px;
        margin: 0 auto;
    }

    .seach-list ul {
        list-style: none;
        padding: 0px;
        width: 84.27%;
        margin: 0 auto;
    }

    .seach-list li {
        display: flex;
    }

        .seach-list li a {
            padding-left: 0.78%;
            text-align: left;
            height: 34px;
            line-height: 34px;
            color: #525252;
            font-size: 13.48px;
            overflow: hidden;
            text-overflow: ellipsis;
            text-decoration: none;
            white-space: nowrap;
        }

    .seach-list p {
        display: flex;
        width: 92%;
        margin: 0 auto;
        margin-top: 15px;
        padding-bottom: 11px;
    }

        .seach-list p span {
            flex: 1;
        }

            .seach-list p span i {
                display: block;
                font-size: 13px;
            }

                .seach-list p span i img {
                    width: 18px;
                    height: 18px;
                    margin-right: 2%;
                }

            .seach-list p span:nth-child(1) sub img {
                width: 66px;
                height: 66px;
                display: inline-block;
            }

            .seach-list p span:nth-child(2) {
                flex: auto;
                float: right;
                max-width: 178px;
            }

                .seach-list p span:nth-child(2) sub img {
                    width: 72px;
                    display: inline-block;
                    height: 72px;
                }

            .seach-list p span sub {
                display: flex;
            }

            .seach-list p span strong {
                display: flex;
                flex-direction: column;
                margin-left: 2%;
            }

        .seach-list p strong b {
            flex: 1;
            line-height: 19px;
            font-weight: normal;
            font-size: 11px;
        }
</style>
<div style="background-color:#F4F3F9;">
    <div class="top">
        <img src="@(CDN.GetCDN())/images/search.png" />
        <div>
            <h2>@T("欢迎使用搜索服务")</h2>
        </div>
    </div>

    <div class="input-group seach-div">
        <input type="text" class="form-control" id="KeyVal" placeholder="@T("请输入搜索关键词")" aria-describedby="basic-addon2" value="@ViewBag.KeyWord">
        <a href="javascript:;">@T("搜索")</a>
    </div>

    <ul class="type-menu">
        <li style="color:#000;">@T("分类")</li>
        <li class="@(ViewBag.SearchType==0?"selected":"")" id="0"><a href="@Url.DGAction("Index")">@T("全部")</a></li>
        <li class="@(ViewBag.SearchType==1?"selected":"")" id="1"><a href="@Url.DGAction("Index",new {type=1})">@T("知识库")</a></li>
        <li class="@(ViewBag.SearchType==2?"selected":"")" id="2"><a href="@Url.DGAction("Index",new {type=2})">@T("相关案例")</a></li>
        <li class="@(ViewBag.SearchType==3?"selected":"")" id="3"><a href="@Url.DGAction("Index",new {type=3})">@T("知识问答")</a></li>
        <li class="@(ViewBag.SearchType==4?"selected":"")" id="4"><a href="@Url.DGAction("Index",new {type=4})">@T("解决方案")</a></li>
    </ul>

    <p class="search-msg">
        @T("为您找到相关搜索结果")：@T("知识库") @length1 @(T("条")) @T("相关案例") @length2 @(T("条")) @T("知识问答") @length3 @(T("条")) @T("解决方案") @length4 @(T("条"))
    </p>

    <div class="seach-list">
        <h2>@T("相关案例")</h2>
        <ul>
            @foreach (var item in Model)
            {
                if (item.SearchType == 2)
                {
                    <li>
                        <a href="@Url.DGAction("Details","Case",new { Id=item.Id})">@item.Name</a>
                    </li>
                }
            }
            @{
                if (length1 == 0)
                {
                    <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
                }
            }
        </ul>
    </div>

    <div class="seach-list">
        <h2>@T("知识库")</h2>
        <ul>
            @foreach (var item in Model)
            {
                if (item.SearchType == 1)
                {
                    <li>
                        <a href="@Url.DGAction("Details","Case",new { Id=item.Id})">@item.Name</a>
                        <i>@item.CreateTime</i>
                    </li>
                }
            }
            @{
                if (length2 == 0)
                {
                    <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
                }
            }
        </ul>
    </div>

    <div class="seach-list">
        <h2>@T("知识问答")</h2>
        <ul>
            @foreach (var item in Model)
            {
                if (item.SearchType == 3)
                {
                    <li>
                        <a href="@Url.DGAction("Details","Case",new { Id=item.Id})">@item.Name</a>
                    </li>
                }
            }
            @{
                if (length3 == 0)
                {
                    <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
                }
            }
        </ul>
    </div>
    <div class="seach-list">
        <h2>@T("解决方案")</h2>
        <ul>
            @foreach (var item in Model)
            {
                if (item.SearchType == 4)
                {
                    <li>
                        <a href="@Url.DGAction("Details","Solution",new { Id=item.Id})">@item.Name</a>
                        <i>@item.CreateTime</i>
                    </li>
                }
            }
            @{
                if (length4 == 0)
                {
                    <li style="text-align:center;"><span style="display: block;margin: 0 auto;">@T("暂无对应数据")</span></li>
                }
            }
        </ul>
    </div>
    <div class="seach-list">
        <h2>@T("订阅更多")</h2>
        <p>
            <span>
                <i><img src="@(CDN.GetCDN())/images/tb.png" />hilink @(T("旗舰店"))</i>
                <sub>
                    <img src="@(CDN.GetCDN())/images/search-code.png" />
                    <strong>
                        <b>~@T("快来看看吧")</b>
                        <b>@T("暂停您忙碌的的身影")</b>
                        <b>@T("这里有一个新世界哦")！</b>
                    </strong>
                </sub>
            </span>
            <span>
                <i><img src="@(CDN.GetCDN())/images/tb.png" />@T("深圳市海凌科电子有限公司2")</i>
                <sub>
                    <img src="@(CDN.GetCDN())/images/search-code2.png" />
                    <strong>
                        <b>@T("关注我们")</b>
                        <b>@T("超多超值优惠活动1")</b>
                        <b>@T("大额优惠等你来1")</b>
                    </strong>
                </sub>
            </span>
        </p>
    </div>
</div>

<script type="text/javascript">
    $(() => {
        $(".seach-div a").click(function(){
            window.location.href = "@(Url.DGAction("index"))?Key_Word=" + $("#KeyVal").val() + `&type=${$(".type-menu .selected").attr("id")}`;
        });
        $("#KeyVal").keyup(function (e) {
            if (e.keyCode == 13) {
                window.location.href = "@(Url.DGAction("index"))?Key_Word=" + $("#KeyVal").val() + `&type=${$(".type-menu .selected").attr("id")}`;
            }
        })
    })
</script>