﻿@{
}
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("客服管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("CreateModel")','@T("添加客服")')"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("客服号码")</dt>
                <dd><input type="text" value="@Model.Key" name="Key" class="txt"></dd>
            </dl>
            <dl>
                <dt>@T("客服类型")</dt>
                <dd>
                    <select name="type">
                        <!option value="">全部</!option>
                        <!option value="0" @(Model.type == "0" ? "selected" : "")>QQ</!option>
                        <!option value="1" @(Model.type == "1" ? "selected" : "")>@T("阿里旺旺")</!option>
                        <!option value="2" @(Model.type == "2" ? "selected" : "")>Skype</!option>
                        <!option value="3" @(Model.type == "3" ? "selected" : "")>Whatsapp</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("编号")</th>
                <th>@T("姓名")</th>
                <th>@T("号码")</th>
                <th>@T("客服平台类型")</th>
                <th>@T("客服所属设备平台")</th>
                <th>@T("创建时间")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in (IEnumerable<HlktechSite.Entity.OnlineKeFu>)Model.list)
            {
                <tr id="<EMAIL>" style="background: rgb(255, 255, 255);">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td>@item.Id</td>
                    <td>@item.OName</td>
                    <td>@item.ONumber</td>
                    <td>@(item.OType==0?"QQ": item.OType == 1?"阿里旺旺":item.OType==2?"Skype":"Whatsapp")</td>
                    <td>@(item.Location==0?"全部": item.Location == 1?"仅PC端":item.Location==2?"仅手机端":"已关闭")</td>
                    <td>@item.CreateTime</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("EditModel",new { Id=item.Id})','@T("编辑产品型号")')" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        @*<a href="javascript:;" onclick="submit_delete('@item.Id')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>*@
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script src="~/static/plugins/js/layui/lay/modules/layer.js"></script>
<script asp-location="Footer">
    function submit_delete(ids_str)
    {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }
</script>
