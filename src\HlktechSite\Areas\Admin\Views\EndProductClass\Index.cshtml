﻿@{
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
}
<script asp-location="Head">
    var changeNameUrl = "@Url.Action("ChangeName")";
    var getData = "@Url.Action("GetSubordinateData")";
    var createData = "@Url.Action("AddEndProductClass")";
    var editData = "@Url.Action("EditEndProductClass")";
    var deleteData = "@Url.Action("Delete")";
</script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品分类")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("AddEndProductClass")"><span>@T("添加")</span></a></li>
                @*<li><a href="/index.php/admin/goodsclass/tag.html"><span>标签</span></a></li>*@
            </ul>
        </div>
    </div>

    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("店主添加产品的时候，可以选择产品的分类，用户可以根据产品的分类来查询产品列表")</li>
            <li>@T("点击产品分类名前“+”符号，显示当前商品分类的下级分类")</li>
            <li><a>@T("对商品分类作任何更改后，都需要到 设置 -> 清理缓存，新的设置才会生效")</a></li>
        </ul>
    </div>

    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th></th>
                <th>@T("排序")</th>
                <th>@T("分类名称")</th>
                <th></th>
                <th></th>
                <th></th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in (IEnumerable<HlktechSite.Entity.EndProductClass>)Model.list)
            {
                <tr class="hover edit" id="<EMAIL>">
                    <td class="w36">
                        <input type="checkbox" name="check_gc_id[]" value="@item.Id" class="checkitem">
                        <img fieldid="@item.Id" status="open" ds_type="flex" src="/static/admin/images/treetable/@T(item.subset?"tv-expandable.gif":"tv-item.gif")">
                    </td>
                    <td class="w48 sort"><span title="可编辑" ajax_branch="goods_class_sort" datatype="number" fieldid="@item.Id" fieldname="gc_sort" ds_type="inline_edit" class="editable ">@item.DisplayOrder</span></td>
                    <td class="w50pre name">
                        <span title="可编辑" required="1" fieldid="@item.Id" ajax_branch="goods_class_name" fieldname="gc_name" ds_type="inline_edit" class="editable ">@item.Name</span>
                        <a class="btn-add-nofloat marginleft" href="@Url.Action("AddEndProductClass",new {parent_id=item.Id })"><span>@T("新增下级")</span></a>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                <td class="w96">
                    <a href="@Url.Action("EditEndProductClass",new { Id=item.Id})">@T("编辑")</a> |
                    @*<a href="/index.php/admin/goodsclass/goods_class_edit.html?gc_id=9">编辑</a> |*@
                    @*<a href="javascript:dsLayerConfirm('/index.php/admin/goodsclass/goods_class_del.html?gc_id=9','@T("您确定要删除吗")?',9)">@T("删除")</a>*@
                    <a href="javascript:dsLayerConfirm('@Url.Action("delete",new {Ids=item.Id})','@T("您确定要删除该数据吗，会一起删除它的子集数据")?',1)">@T("删除")</a>
                </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkall_2"></td>
                <td id="batchAction" colspan="15">
                    <span class="all_checkbox">
                        <label for="checkall_2">@T("全选")</label>
                    </span>&nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
</div>

<script type="text/javascript" src="/static/admin/js/jquery.edit.js" charset="utf-8"></script>
<script src="~/static/admin/js/jquery.goods_class1.js"></script>
<script type="text/javascript" asp-location="Footer">
    var ADMINSITEURL = "/@adminarea";
    var BASESITEURL = "/@adminarea";
    var ADMINSITEROOT = "/static/admin";
    function submit_delete(ids_str) {
        _uri = ADMINSITEURL + "/Goodsclass/goods_class_del.html?gc_id=" + ids_str;
        dsLayerConfirm(_uri, '@T("您确定要删除吗")?');
    }
</script>