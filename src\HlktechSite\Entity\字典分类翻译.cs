﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechSite.Entity;

/// <summary>字典分类翻译</summary>
[Serializable]
[DataObject]
[Description("字典分类翻译")]
[BindIndex("IU_DG_DictionariesCategoryLan_DId_LId", true, "DId,LId")]
[BindTable("DG_DictionariesCategoryLan", Description = "字典分类翻译", ConnName = "DG", DbType = DatabaseType.None)]
public partial class DictionariesCategoryLan : IDictionariesCategoryLan, IEntity<IDictionariesCategoryLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _DId;
    /// <summary>字典分类Id</summary>
    [DisplayName("字典分类Id")]
    [Description("字典分类Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DId", "字典分类Id", "")]
    public Int32 DId { get => _DId; set { if (OnPropertyChanging("DId", value)) { _DId = value; OnPropertyChanged("DId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>分类名称</summary>
    [DisplayName("分类名称")]
    [Description("分类名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Name", "分类名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDictionariesCategoryLan model)
    {
        Id = model.Id;
        DId = model.DId;
        LId = model.LId;
        Name = model.Name;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "DId" => _DId,
            "LId" => _LId,
            "Name" => _Name,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "DId": _DId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据字典分类Id查找</summary>
    /// <param name="dId">字典分类Id</param>
    /// <returns>实体列表</returns>
    public static IList<DictionariesCategoryLan> FindAllByDId(Int32 dId)
    {
        if (dId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.DId == dId);

        return FindAll(_.DId == dId);
    }
    #endregion

    #region 字段名
    /// <summary>取得字典分类翻译字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>字典分类Id</summary>
        public static readonly Field DId = FindByName("DId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>分类名称</summary>
        public static readonly Field Name = FindByName("Name");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得字典分类翻译字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>字典分类Id</summary>
        public const String DId = "DId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>分类名称</summary>
        public const String Name = "Name";
    }
    #endregion
}
