﻿using System.Dynamic;

using DG.Cube;
using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;

using HlktechSite.DTO;
using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;

namespace HlktechSite.Controllers;

//客户案例控制器
public class CaseController : DGBaseControllerX
{
    /// <summary>
    /// 客户案例首页
    /// </summary>
    /// <returns></returns>
    public IActionResult Index(string key, int type, int p = 1)
    {
        key = key.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        IEnumerable<Case> CaseList;
        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 9,
            RetrieveTotalCount = true
        };
        IEnumerable<CaseCategory> CaseTypelist;

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
            CaseTypelist = CaseCategoryLan.FindAllByLevel(WorkingLanguage.Id).Select(x=>new CaseCategory {
                Id = x.CId,
                Name =  x.Name.IsNullOrWhiteSpace()?x.caseCategory.Name:x.Name
            });
        else
            CaseTypelist = CaseCategory.FindAllByLevel(0);
        viewModel.CaseTypelist = CaseTypelist;
        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("客户案例"), IsLast = true });
        viewModel.Locations = navigations;
        if (localizationSettings.IsEnable)
        {
            CaseList = CaseLan.Searchs(key, pages, type, WorkingLanguage.Id).Select(x => new Case
            {
                Id = x.CId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.caseModel?.Name : x.Name,
                Url = x.caseModel.Url.SafeString(),
                Pic = x.Pic.IsNullOrWhiteSpace() ? x.caseModel?.Pic : x.Pic,
                Summary = x.Summary.IsNullOrWhiteSpace() ? x.caseModel?.Summary : x.Summary,
                CreateTime = x.caseModel.CreateTime
            });
        }
        else
        {
            CaseList = Case.Search(key, type, pages);
        }
        viewModel.SolutionList = CaseList;
        viewModel.key = key;
        viewModel.type = type;

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());

        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "Index", "Case", "", dic, "p");
        return DGView(viewModel, true);
    }

    /// <summary>
    /// 客户案例分类页
    /// </summary>
    /// <returns></returns>
    public IActionResult List(string key, int type, int p = 1)
    {
        key = key.SafeString().Trim();
        dynamic viewModel = new ExpandoObject();
        IEnumerable<Case> CaseList;
        var pages = new PageParameter()
        {
            PageIndex = p,
            PageSize = 9,
            RetrieveTotalCount = true
        };
        IEnumerable<CaseCategory> CaseTypelist;

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
            CaseTypelist = CaseCategoryLan.FindAllByLevel(WorkingLanguage.Id).Select(x => new CaseCategory
            {
                Id = x.CId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.caseCategory.Name : x.Name
            });
        else
            CaseTypelist = CaseCategory.FindAllByLevel(0);
        viewModel.CaseTypelist = CaseTypelist;
        var navigations = new List<NavigationUrl>();
        navigations.Add(new NavigationUrl { Name = GetResource("客户案例"), IsLast = true });
        viewModel.Locations = navigations;
        if (localizationSettings.IsEnable)
        {
            CaseList = CaseLan.Searchs(key, pages, type, WorkingLanguage.Id).Select(x => new Case
            {
                Id = x.CId,
                Name = x.Name.IsNullOrWhiteSpace() ? x.caseModel?.Name : x.Name,
                Url = x.caseModel.Url.SafeString(),
                Pic = x.Pic.IsNullOrWhiteSpace() ? x.caseModel?.Pic : x.Pic,
                Summary = x.Summary.IsNullOrWhiteSpace() ? x.caseModel?.Summary : x.Summary,
                CreateTime = x.caseModel.CreateTime
            });
        }
        else
        {
            CaseList = Case.Search(key, type, pages);
        }
        viewModel.SolutionList = CaseList;
        viewModel.key = key;
        viewModel.type = type;
        viewModel.Model = CaseTypelist.FirstOrDefault(e => e.Id == type);

        var dic = HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
        dic.TryAdd("type", type.ToString());

        viewModel.Str = PageHelper.CreatePage(Url, p, pages.PageCount, "List", "Case", "", dic, "p");
        return DGView(viewModel, "Index", true);
    }

    /// <summary>
    /// 客户案例详情
    /// </summary>
    /// <returns></returns>
    public IActionResult Details(int Id)
    {
        dynamic viewModel = new ExpandoObject();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var Model = CaseLan.FindByCIdAndLId(Id, WorkingLanguage.Id);
            if (Model == null)
            {
                return View404();
            }
            var caseModel = new Case
            {
                Name = Model.Name.IsNullOrWhiteSpace() ? Model.caseModel?.Name : Model.Name,
                Summary = Model.Summary.IsNullOrWhiteSpace() ? Model.caseModel?.Summary : Model.Summary,
                Content = (Model.Content.SafeString().IsNullOrWhiteSpace() ? Model.caseModel?.Content : Model.Content),
                CreateTime = Model.caseModel.CreateTime
            };
            viewModel.SolutionModel = caseModel;
            viewModel.previous = Case.Findprevious(Model.caseModel.CreateTime);
            viewModel.Nex = Case.FindNext(Model.caseModel.CreateTime);
            ViewBag.Titles = CaseCategoryLan.FindNameByCIdAndlId(Model.caseModel.CId, WorkingLanguage.Id);

            return DGView(viewModel, true);
        }
        else
        {
            var Model = Case.FindById(Id);
            if (Model == null)
            {
                return View404();
            }
            viewModel.SolutionModel = Model;
            viewModel.previous = Case.Findprevious(Model.CreateTime);
            viewModel.Nex = Case.FindNext(Model.CreateTime);
            ViewBag.Titles = CaseCategory.FindById(Model.CId)?.Name;
            return DGView(viewModel, true);
        }

    }
}
