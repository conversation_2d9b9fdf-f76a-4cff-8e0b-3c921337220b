﻿using DG;

using DH;
using DH.Caching;

using NewLife.Caching;

namespace HlktechSite.Common;

public class CDN
{
    public static String GetCDN()
    {
        return CDNOptionSetting.Current.Enable ? CDNOptionSetting.Current.Url : $"{DHSetting.Current.CurDomainUrl}/";
    }

    public static String? Domain()
    {
        var key = CacheKeyFiled.Instance.Get("Domain");
        var _cache = Cache.Default;

        var result = _cache.GetOrAdd<String>(key, e =>
        {
            return DHSetting.Current.CurDomainUrl.Replace("http://", "").Replace("https://", "");
        });

        return result;
    }

}
