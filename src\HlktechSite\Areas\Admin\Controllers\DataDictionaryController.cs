﻿using DG.Cube;
using DG.Cube.BaseControllers;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

using YRY.Web.Controllers;
using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>字典管理</summary>
[DisplayName("字典管理")]
[Description("用于字典的管理")]
[AdminArea]
[DHMenu(48,ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/DataDictionary", CurrentMenuName = "DataDictionaryList", CurrentIcon = "&#xe71d;", LastUpdate = "20240125")]
public class DataDictionaryController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 48;

    /// <summary>
    /// 字典列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("字典列表")]
    public IActionResult Index(string name, int search_ac_id = -1, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = DataDictionary.Search(search_ac_id, name, pages).Select(x => new { x.Id, x.Name, DName = x.DictionariesCategory?.Name, x.Sort, x.CodeType, x.Status, x.CreateTime }).ToDynamicList();
        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;
        viewModel.search_ac_id = search_ac_id;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name },{ "search_ac_id", search_ac_id.ToString() }
        });

        var List = new List<DictionariesCategory>();
        var live1 = DictionariesCategory.FindAllByLevel(0); //1级数据
        GetCategoryList(live1, List);
      
        viewModel.Claslist = live1;

        return View(viewModel);
    }

    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IList<DictionariesCategory> levelList, IList<DictionariesCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = DictionariesCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 新增字典页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增字典页面")]
    public IActionResult AddDataDictionary()
    {

        dynamic viewModel = new ExpandoObject();
        var List = new List<DictionariesCategory>();
        var live1 = DictionariesCategory.FindAllByLevel(0);
        GetCategoryList(live1, List);
        var Sort = DataDictionary.FindMax("Sort");
        ViewBag.Sort = Sort + 1;
        viewModel.Plist = List;

        return View(viewModel);
    }


    /// <summary>
    /// 新增字典
    /// </summary>
    /// <param name="code_title"></param>
    /// <param name="gc_class_id"></param>
    /// <param name="code_type"></param>
    /// <param name="code_status"></param>
    /// <param name="code_sort"></param>
    /// <param name="code_remark"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增字典")]
    public IActionResult CreatDataDictionary(string code_title, int gc_class_id, string code_type, int code_status, int code_sort, string code_remark)
    {
        if (code_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("字典值不能为空") });
        }
        if (code_type.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("字典值类型不能为空") });
        }
        if (gc_class_id <= 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属字典分类") });
        }

        var EXIT = DataDictionary.FindByCodetypeAndDId(code_type.SafeString().Trim(), gc_class_id);
        if (EXIT != null)
        {
            return Prompt(new PromptModel { Message = GetResource("当前字典分类已存在改字典值分类名称") });
        }
        var Model = new DataDictionary();
        Model.Name = code_title.SafeString().Trim();
        Model.CodeType = code_type.SafeString().Trim();
        Model.Content = code_remark;
        Model.Sort = code_sort;
        Model.DId = gc_class_id;
        Model.Status = code_status == 1 ? true : false;
        Model.Insert();
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });

    }


    /// <summary>
    /// 打开修改页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改页面")]
    public IActionResult EditDataDictionary(int Id)
    {

        var Model = DataDictionary.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        dynamic viewModel = new ExpandoObject();
        var List = new List<DictionariesCategory>();
        var live1 = DictionariesCategory.FindAllByLevel(0);//一级数据

        GetCategoryList(live1, List);
        viewModel.Plist = List;
        viewModel.Model = Model;
        return View(viewModel);
    }


    /// <summary>
    /// 修改接口
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="code_title"></param>
    /// <param name="gc_class_id"></param>
    /// <param name="code_status"></param>
    /// <param name="code_sort"></param>
    /// <param name="code_remark"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改接口")]
    public IActionResult UpdateDataDictionary(int Id, string code_title, int gc_class_id, int code_status, int code_sort, string code_remark)
    {

        if (code_title.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("字典值不能为空") });
        }

        if (gc_class_id <= 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属字典分类") });
        }
        var Model = DataDictionary.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
        }
        Model.Name = code_title.SafeString().Trim();
        //Model.CodeType = code_type.SafeString().Trim();
        Model.Content = code_remark;
        Model.Sort = code_sort;
        Model.DId = gc_class_id;
        Model.Status = code_status == 1 ? true : false;
        Model.Update();
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }


    /// <summary>
    /// 字典数据删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("字典数据删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        DataDictionary.DelByIds(Ids.Trim(','));
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 模糊查询字典值类型数据
    /// </summary>
    /// <param name="Key"></param>
    /// <returns></returns>
    [DHAuthorize]
    [DisplayName("模糊查询字典值类型数据")]
    public IActionResult GetlikeName(string Key)
    {
        if (Key.IsNullOrWhiteSpace())
        {
            return Json(new List<Xmsekect>());
        }
        var List1 = DataDictionary.FindAllByLikeCodeType(Key.SafeString().Trim());
        var list = new List<Xmsekect>();

        foreach (var item in List1)
        {
            var model = new Xmsekect();
            model.name = item.CodeType;
            model.value = item.Id;
            list.Add(model);
        }
        return Json(list);
    }

}
