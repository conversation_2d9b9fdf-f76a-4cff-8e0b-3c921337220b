﻿using DG.Cube.BaseControllers;
using DG.Cube.Models;

using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek;
using Pek.Helpers;
using Pek.Iot;
using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>文章分类</summary>
[DisplayName("文章分类")]
[Description("用于文章分类的管理")]
[AdminArea]
[DHMenu(85,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/ArticleClass", CurrentMenuName = "ArticleClassList", CurrentIcon = "&#xe652;", LastUpdate = "20240125")]
public class ArticleClassController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 85;
    /// <summary>
    /// 文章分类列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("文章分类列表")]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var list = ArticleCategory.FindAllByLevel(0);
        foreach (var item in list)
        {
            var Model = ArticleCategory.FindAllByParentId(item.Id);
            if (Model.Count != 0)
            {
                item.subset = true;
            }
        }
        viewModel.list = list;
        return View(viewModel);
    }
    /// <summary>
    /// 获取文章分类表下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取文章分类表下级数据")]
    public IActionResult GetSubordinateData(string Id)
    {
        var zList = new List<Hierarchy>();
        if (Id.IsNullOrWhiteSpace())
        {
            return Json(new { });
        }
        var list = ArticleCategory.FindAllByParentId(Id.ToInt());
        foreach (var item in list)
        {
            var model = new Hierarchy();
            model.gc_name = item.Name.SafeString().Trim();
            model.gc_id = item.Id;
            model.gc_parent_id = item.ParentId;
            var exc = ArticleCategory.FindAllByParentId(item.Id);
            if (exc.Count > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.gc_sort = item.DisplayOrder;
            model.deep = item.Level;
            zList.Add(model);
        }
        return Json(new { zList });
    }
    /// <summary>
    /// 打开修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("打开修改页面")]
    public IActionResult EditArticleClass(string Id)
    {
        dynamic viewModel = new ExpandoObject();
        var Model = ArticleCategory.FindById(Id.ToInt());
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        var List = new List<ArticleCategory>();
        var live1 = ArticleCategory.FindAllByLevel(0);
        GetCategoryList(live1, List);
        viewModel.Plist = List.Select(x => new ArticleCategory { Name = x.ParentList.Select(e => e.Name).Join(" => "), Id = x.Id, Level = x.Level });
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        viewModel.Model = Model;
        return View(viewModel);
    }



    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IList<ArticleCategory> levelList, IList<ArticleCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = ArticleCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }




    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取是否存在该名称")]
    public IActionResult GetByName(string gc_name, int gc_id)
    {
        var Model = ArticleCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }
    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    public IActionResult GetByNames(string gc_name, int gc_id)
    {
        var Model = ArticleCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }
    /// <summary>
    /// 修改文章分类
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="gc_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改分类接口")]
    public IActionResult EditArticleClass(string gc_name, int gc_parent_id, int gc_id, int DisplayOrder)
    {
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("文章名称不能为空") });
        }
        var Model = ArticleCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("该下载分类名称已存在") });
        }
        if (gc_parent_id == gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("父级栏目不能为自身") });
        }
        var models = ArticleCategory.FindById(gc_id);
        if (models == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
        }
        models.Name = gc_name.SafeString().Trim();
        models.ParentId = gc_parent_id.ToInt();
        models.DisplayOrder = DisplayOrder.ToShort();
        var pmodel = ArticleCategory.FindById(gc_parent_id);
        if (pmodel == null)
        {
            models.ParentIdList = gc_id.ToString();
            models.Level = 0;
        }
        else
        {
            models.ParentIdList = pmodel.ParentIdList + "," + gc_id.ToString();
            models.Level = pmodel.Level + 1;
        }
        if (models.Level > 2)
        {
            return Prompt(new PromptModel { Message = GetResource("分类不能超过三层") });
        }
        models.Update();
        ArticleCategory.Meta.Cache.Clear("");//清楚缓存
        //循环修改子集的父级Id集合
        var zList = ArticleCategory.FindAllByParentId(gc_id);
        foreach (var item in zList)
        {
            item.ParentIdList = models.ParentIdList + "," + item.Id;
            var slist = ArticleCategory.FindAllByParentId(item.Id);
            foreach (var row in slist)
            {
                item.ParentIdList = item.ParentIdList + "," + row.Id;
            }
            slist.Save();
        }
        zList.Save();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言               
            foreach (var item in Languagelist)
            {
                var articleCategoryLan = ArticleCategoryLan.FindByAIdAndLId(models.Id, item.Id);
                articleCategoryLan = articleCategoryLan ?? new ArticleCategoryLan();

                articleCategoryLan.Name = GetRequest($"[{item.Id}].gc_name").SafeString().Trim();
                articleCategoryLan.AId = Model.Id;
                articleCategoryLan.LId = item.Id;
                articleCategoryLan.Save();
            }
            ArticleCategoryLan.Meta.Cache.Clear("");
        }
        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }
    /// <summary>
    /// 新增页面打开
    /// </summary>
    /// <param name="parent_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("文章分类分类页面打开")]
    public IActionResult AddArticleClass(int parent_id = 0)
    {
        dynamic viewModel = new ExpandoObject();
        var List = new List<ArticleCategory>();
        viewModel.ParentId = parent_id;
        var live1 = ArticleCategory.FindAllByLevel(0);//一级数据
        GetCategoryList(live1, List);
        viewModel.Plist = List.Select(x => new ArticleCategory { Name = x.Name, Id = x.Id, Level = x.Level });
        var DisplayOrder = 0;
        if (parent_id != 0)
        {
            DisplayOrder = (Int32)ArticleCategory.FindMax("DisplayOrder", ArticleCategory._.ParentId == parent_id);
        }
        else
        {
            DisplayOrder = (Int32)ArticleCategory.FindMax("DisplayOrder");
        }
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        ViewBag.DisplayOrder = DisplayOrder + 1;
        return View(viewModel);
    }
    /// <summary>
    /// 文章分类新增
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("文章分类新增")]
    public IActionResult AddArticleClass(string gc_name, int gc_parent_id, int DisplayOrder)
    {
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("文章分类名称不能为空") });
        }

        var model = ArticleCategory.FindByName(gc_name.SafeString().Trim());
        if (model != null)
        {
            return Prompt(new PromptModel { Message = GetResource("文章分类名称已存在") });
        }
        var Pmodel = ArticleCategory.FindById(gc_parent_id);
        if (Pmodel != null)
        {
            if (Pmodel.Level >= 2)
            {
                return Prompt(new PromptModel { Message = GetResource("文章分类最多存在三级,创建失败！") });
            }
        }

        var Model = new ArticleCategory();
        Model.Name = gc_name.SafeString().Trim();
        Model.ParentId = gc_parent_id;
        Model.DisplayOrder = DisplayOrder.ToShort();
        Model.Insert();

        if (Pmodel == null)
        {
            Model.Level = 0;
            Model.ParentIdList = Model.Id.ToString();
        }
        else
        {
            Model.Level = Pmodel.Level + 1;
            Model.ParentIdList = Pmodel.ParentIdList + "," + Model.Id;
        }
        Model.Update();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言               
            foreach (var item in Languagelist)
            {
                var articleCategoryLan = new ArticleCategoryLan();

                articleCategoryLan.Name = GetRequest($"[{item.Id}].gc_name").SafeString().Trim();
                articleCategoryLan.AId = Model.Id;
                articleCategoryLan.LId = item.Id;
                articleCategoryLan.Insert();
            }
            ArticleCategoryLan.Meta.Cache.Clear("");
        }

        Article.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }


    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();

        var list = ArticleCategory.FindByIds(Ids);
        var dellist = new List<ArticleCategory>();

        res = DeleteArticleCategory(res, dellist, list);

        if (!res.msg.IsNullOrWhiteSpace())
        {
            return Json(res);
        }
        foreach (var item in dellist)
        {
            ArticleCategoryLan.Delete(ArticleCategoryLan._.AId == item.Id);
        }
        if (dellist.Delete(true) > 0)
        {
            ArticleCategory.Meta.Cache.Clear("");
            SolutionCategory.Meta.Cache.Clear("");
        }
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }


    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private DResult DeleteArticleCategory(DResult res, IList<ArticleCategory> dellist, IList<ArticleCategory> list)
    {
        if (list.Count > 0)
        {
            foreach (var item in list)
            {
                var listKnowledge = Article.FindAllByAId(item.Id);
                if (listKnowledge.Count > 0)
                {
                    res.msg = String.Format(GetResource("选中的{0}有关联文章数据 不允许被删除"), item.Name);
                    return res;
                }
                else
                {
                    dellist.Add(item);
                    var childlist = ArticleCategory.FindAllByParentId(item.Id);
                    res = DeleteArticleCategory(res, dellist, childlist);
                }
            }
        }
        return res;
    }


    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = ArticleCategory.FindById(Id);

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = ArticleCategory.FindByName(value);
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.DisplayOrder = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }

}
