﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>数据字典翻译</summary>
public partial interface IDataDictionaryLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>字典Id</summary>
    Int32 DId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>字典值——名称</summary>
    String? Name { get; set; }

    /// <summary>字典值——描述</summary>
    String? Content { get; set; }
    #endregion
}
