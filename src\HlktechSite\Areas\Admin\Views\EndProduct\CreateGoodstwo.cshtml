﻿@{
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();

    var localizationSettings = LocalizationSettings.Current;
}
<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<style>
    #fixedNavBar {
        filter: progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#CCFFFFFF', endColorstr='#CCFFFFFF');
        background: rgba(255,255,255,0.8);
        width: 90px;
        margin-left: 510px;
        border-radius: 4px;
        position: fixed;
        z-index: 999;
        top: 172px;
        right: 0%;
    }

        #fixedNavBar h3 {
            font-size: 12px;
            line-height: 24px;
            text-align: center;
            margin-top: 4px;
        }

        #fixedNavBar ul {
            width: 80px;
            margin: 0 auto 5px auto;
        }

        #fixedNavBar li {
            margin-top: 5px;
        }

            #fixedNavBar li a {
                font-family: Arial, Helvetica, sans-serif;
                font-size: 12px;
                line-height: 20px;
                background-color: #F5F5F5;
                color: #999;
                text-align: center;
                display: block;
                height: 20px;
                border-radius: 10px;
            }

                #fixedNavBar li a:hover {
                    color: #FFF;
                    text-decoration: none;
                    background-color: #27a9e3;
                }
</style>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>成品管理</h3>
                <h5></h5>
            </div>
            <ul class="add-goods-step">
                <li>
                    <i class="iconfont">&#xe600;</i>
                    <h6>STEP.1</h6>
                    <h2>选择成品分类</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li class="current">
                    <i class="icon iconfont">&#xe731;</i>
                    <h6>STEP.2</h6>
                    <h2>填写成品详情</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li>
                    <i class="icon iconfont">&#xe6a2;</i>
                    <h6>STEP.3</h6>
                    <h2>上传成品图片</h2>
                    <i class="iconfont">&#xe687;</i>
                </li>
                <li>
                    <i class="icon iconfont">&#xe64d;</i>
                    <h6>STEP.4</h6>
                    <h2>成品发布成功</h2>
                </li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>


    <div id="fixedNavBar">
        <h3>页面导航</h3>
        <ul>
            <li><a id="demo1Btn" href="#demo1" class="demoBtn">基本信息</a></li>
            <li><a id="demo2Btn" href="#demo2" class="demoBtn">详情描述</a></li>
            <li><a id="demo6Btn" href="#demo6" class="demoBtn">其他信息</a></li>
        </ul>
    </div>
    <div>
        
    </div>

    <div class="item-publish">
        <form method="post" id="goods_form" action="@Url.Action("CreateGoodstwos")">
            <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                @if (localizationSettings.IsEnable)
                {
                    <ul class="layui-tab-title">
                        <li data="" class="layui-this">标准 </li>
                        @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                        {
                            <li data="@item.Id" class="LId">@item.DisplayName</li>
                        }
                    </ul>
                }
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="dssc-form-goods">
                            <h3 id="demos">@T("成品基本信息")</h3>

                            <dl>
                                <dt><i class="required">*</i>@T("成品名称")：</dt>
                                <dd>
                                    <input name="g_name" type="text" class="text w400" value="" />
                                    <span></span>
                                    <p class="hint">@T("成品标题名称长度至少3个字符，最长50个汉字")</p>
                                </dd>
                            </dl>
                            <dl>
                                <dt>@T("成品广告词")：</dt>
                                <dd>
                                    <textarea name="g_jingle" class="textarea h60 w400"></textarea>
                                    <span></span>
                                    <p class="hint">@T("成品卖点最长不能超过140个汉字")</p>
                                </dd>
                            </dl>
                            <dl>
                                <dt>@T("成品简介")：</dt>
                                <dd>
                                    <textarea name="summary" class="textarea h60 w400"></textarea>
                                    <span></span>
                                    <p class="hint"></p>
                                </dd>
                            </dl>
                            <dl>
                                <dt>@T("使用场景")：</dt>
                                <dd>
                                    <textarea name="UsageScenarios" class="textarea h60 w400"></textarea>
                                    <span></span>
                                    <p class="hint"></p>
                                </dd>
                            </dl>
                            <dl>
                                <dt>@T("是否上架")：</dt>
                                <dd class="onoff">
                                    <label for="sms_login_shang1" class="cb-enable selected">@T("是")</label>
                                    <label for="sms_login_shang0" class="cb-disable">@T("否")</label>
                                    <input id="sms_login_shang1" name="shelf" value="1" type="radio" checked="checked">
                                    <input id="sms_login_shang0" name="shelf" value="0" type="radio">
                                </dd>
                            </dl>
                            <dl>
                                <dt><i class="required">*</i>@T("是否推荐")：</dt>
                                <dd class="onoff">
                                    <label for="sms_login_show1" class="cb-enable ">@T("是")</label>
                                    <label for="sms_login_show0" class="cb-disable selected">@T("否")</label>
                                    <input id="sms_login_show1" name="nav_new_open" value="1" type="radio">
                                    <input id="sms_login_show0" name="nav_new_open" value="0" type="radio" checked="checked">
                                </dd>
                            </dl>
                            <dl>
                                <dt>@T("排序")：</dt>
                                <dd>
                                    <input name="sort" type="number" class="text w400" value="@(ViewBag.Sort==0?0:++ViewBag.Sort)" />
                                    <span></span>
                                </dd>
                            </dl>

                            <dl>
                                <dt>@T("成品图片")：</dt>
                                <dd>
                                    <div class="dssc-goods-default-pic">
                                        <div class="goodspic-uplaod">
                                            <div class="upload-thumb"> <img dstype="goods_image" src="~/uploads/home/<USER>/default_goods_image.jpg" /> </div>
                                            <input type="hidden" name="image_path" id="image_path" dstype="goods_image" value="" />
                                            <span></span>
                                            <p class="hint">@T("最多可发布5张成品图片。上传成品默认主图，如多规格值时将默认使用该图或分规格上传各规格主图；支持jpg、gif、png格式上传或从图片空间中选择，建议使用")<font color="red">@T("尺寸800x800像素以上、大小不超过1M的正方形图片")</font>，@T("上传后的图片将会自动保存在图片空间的默认分类中。")</p>
                                            <div class="handle">
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span>
                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="goods_image" id="goods_image">
                                                        </span>
                                                        <p><i class="iconfont">&#xe733;</i>@T("图片上传")</p>
                                                    </a>
                                                </div>
                                                <a class="dssc-btn mt5 show_image" dstype="show_image" href="@Url.Action("Piclist")"><i class="iconfont">&#xe72a;</i>@T("从图片空间选择")</a> <a href="javascript:void(0);" dstype="del_goods_demo" class="dssc-btn mt5 del_goods_demo" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="demo"></div>
                                </dd>
                            </dl>
                            <h3 id="demo2">@T("成品详情描述")</h3>

                            <dl>
                                <dt>@T("成品描述")：</dt>
                                <dd id="dsProductDetails">
                                    <div class="tabs">
                                        <ul class="ui-tabs-nav">
                                            <li class="ui-tabs-selected"><a href="#panel-1"><i class="iconfont">&#xe60c;</i> @T("电脑端")</a></li>
                                            <li class="selected"><a href="#panel-2"><i class="iconfont">&#xe60e;</i>@T("手机端")</a></li>
                                        </ul>
                                        <div id="panel-1" class="ui-tabs-panel">
                                            <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                                            <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                                            <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                                            <script type="text/javascript">

                                                var ue = UE.getEditor('goods_body');
                                                if ("") {
                                                    ue.ready(function () {
                                                        this.setContent("");
                                                    })
                                                }

                                            </script>                <textarea name="goods_body" id="goods_body"></textarea>
                                            <div class="hr8">
                                                <a class="dssc-btn mt5" dstype="show_desc" href="@Url.Action("PiclistConten",new { type = "Pc" })"><i class="iconfont">&#xe72a;</i>@T("")插入相册图片</a> <a href="javascript:void(0);" dstype="del_desc" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                            </div>
                                            <p id="des_demo"></p>
                                        </div>
                                        <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                            <div>
                                               
                                            </div>
                                            <script type="text/javascript">

                                                var ue = UE.getEditor('goods_Mobilebody');
                                                if ("") {
                                                    ue.ready(function () {
                                                        this.setContent("");
                                                    })
                                                }
                                            </script>
                                            <textarea name="goods_Mobilebody" id="goods_Mobilebody"></textarea>
                                            <div class="hr8">
                                                <a class="dssc-btn mt5" dstype="show_desca" href="@Url.Action("PiclistConten",new { type = "Mobile" })"><i class="iconfont">&#xe72a;</i>@T("插入相册图片")</a> <a href="javascript:void(0);" dstype="del_desca" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                            </div>
                                            <p id="des_Mobieldemo"></p>
                                        </div>
                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span>
                                                    <input type="file" hidefocus="true" size="1" class="input-file" name="add_album" id="add_album" multiple="multiple">
                                                </span>
                                                <p><i class="iconfont" data_type="0" dstype="add_album_i">&#xe733;</i>@T("图片上传")</p>
                                            </a>
                                        </div>
                                    </div>
                                </dd>
                            </dl>

                            <dl>
                                <dt>规格参数：</dt>
                                <dd id="dsProductDetailss">
                                    <div class="tabs">

                                        <div id="panel-2" class="ui-tabs-panel">
                                            <script type="text/javascript">
                                                var us = UE.getEditor('spec');
                                                us.ready(function () {
                                                    this.setContent('');
                                                })
                                            </script>
                                            <textarea name="spec" id="spec"></textarea>
                                            <div class="hr8">

                                                <a class="dssc-btn mt5" dstype="show_descs" href="@Url.Action("PiclistContens",new { type = "Pc" })"><i class="iconfont">&#xe72a;</i>插入相册图片</a> <a href="javascript:void(0);" dstype="del_descs" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                            </div>
                                            <p id="des_demos"></p>
                                        </div>

                                        <div class="dssc-upload-btn">
                                            <a href="javascript:void(0);">
                                                <span>
                                                    <input type="file" hidefocus="true" size="1" class="input-file" name="add_albums" id="add_albums" multiple="multiple">
                                                </span>
                                                <p><i class="iconfont" data_type="0" dstype="add_album_is">&#xe733;</i>图片上传</p>
                                            </a>
                                        </div>
                                    </div>
                                </dd>
                            </dl>

                        </div>

                    </div>
                    @if (localizationSettings.IsEnable)
                    {
                        @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                        {
                            var LanSort = (EndProductLan.GetMAxSort(item.Id) ?? new EndProductLan()).Sort;
                            <div class="layui-tab-item">
                                <div class="dssc-form-goods">
                                    <h3 id="[@item.Id].demo1">@T("成品基本信息")</h3>
                                    <dl>
                                        <dt><i class="required">*</i>@T("成品名称")：</dt>
                                        <dd>
                                            <input name="[@item.Id].g_name" type="text" class="text w400" value="" />
                                            <span></span>
                                            <p class="hint">@T("成品标题名称长度至少3个字符，最长50个汉字")</p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("成品卖点")：</dt>
                                        <dd>
                                            <textarea name="[@item.Id].g_jingle" class="textarea h60 w400"></textarea>
                                            <span></span>
                                            <p class="hint">@T("成品卖点最长不能超过140个汉字")</p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("成品简介")：</dt>
                                        <dd>
                                            <textarea name="[@item.Id].summary" class="textarea h60 w400"></textarea>
                                            <span></span>
                                            <p class="hint"></p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("使用场景")：</dt>
                                        <dd>
                                            <textarea name="[@item.Id].UsageScenarios" class="textarea h60 w400"></textarea>
                                            <span></span>
                                            <p class="hint"></p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("是否上架")：</dt>
                                        <dd class="onoff">
                                            <label for="sms_login_shang1@(item.Id)" class="cb-enable selected">@T("是")</label>
                                            <label for="sms_login_shang0@(item.Id)" class="cb-disable">@T("否")</label>
                                            <input id="sms_login_shang1@(item.Id)" name="[@item.Id].shelf" value="1" type="radio" checked="checked">
                                            <input id="sms_login_shang0@(item.Id)" name="[@item.Id].shelf" value="0" type="radio">
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt><i class="required">*</i>@T("是否推荐")：</dt>
                                        <dd class="onoff">
                                            <label for="sms_login_show1@(item.Id)" class="cb-enable ">@T("是")</label>
                                            <label for="sms_login_show0@(item.Id)" class="cb-disable selected">@T("否")</label>
                                            <input id="sms_login_show1@(item.Id)" name="[@(item.Id)].nav_new_open" value="1" type="radio">
                                            <input id="sms_login_show0@(item.Id)" name="[@(item.Id)].nav_new_open" value="0" type="radio" checked="checked">
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("排序")：</dt>
                                        <dd>
                                            <input name="[@item.Id].sort" type="number" class="text w400" value="@(LanSort==0?0:++LanSort)" />
                                            <span></span>
                                            <p class="hint">@T("倒序")</p>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("成品图片")：</dt>
                                        <dd>
                                            <div class="dssc-goods-default-pic">
                                                <div class="goodspic-uplaod">
                                                    <div class="upload-thumb"> <img dstype="goods_image@(item.Id)" src="~/uploads/home/<USER>/default_goods_image.jpg" /> </div>
                                                    <input type="hidden" name="[@item.Id].image_path" id="[@item.Id].image_path" dstype="goods_image@(item.Id)" value="" />
                                                    <span></span>
                                                    <p class="hint">@T("最多可发布5张成品图片。上传成品默认主图，如多规格值时将默认使用该图或分规格上传各规格主图；支持jpg、gif、png格式上传或从图片空间中选择，建议使用")<font color="red">@T("尺寸800x800像素以上、大小不超过1M的正方形图片")</font>，@T("上传后的图片将会自动保存在图片空间的默认分类中。")</p>
                                                    <div class="handle">
                                                        <div class="dssc-upload-btn">
                                                            <a href="javascript:void(0);">
                                                                <span>
                                                                    <input type="file" hidefocus="true" size="1" class="input-file" name="goods_image@(item.Id)" id="goods_image@(item.Id)">
                                                                </span>
                                                                <p><i class="iconfont">&#xe733;</i>@T("图片上传")</p>
                                                            </a>
                                                        </div>
                                                        <a class="dssc-btn mt5  show_image" dstype="show_image@(item.Id)" href="@Url.Action("Piclist")"><i class="iconfont">&#xe72a;</i>@T("从图片空间选择")</a> <a href="javascript:void(0);" dstype="del_goods_demo@(item.Id)" class="dssc-btn mt5 del_goods_demo" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="demo@(item.Id)" class="demo"></div>
                                        </dd>
                                    </dl>
                                    <h3 id="[@item.Id].demo2">@T("成品详情描述")</h3>

                                    <dl>
                                        <dt>@T("成品描述")：</dt>
                                        <dd id="[@item.Id].dsProductDetails">
                                            <div class="tabs">
                                                <ul class="ui-tabs-nav">
                                                    <li class="ui-tabs-selected"><a href="#panel-1"><i class="iconfont">&#xe60c;</i> @T("电脑端")</a></li>
                                                    <li class="selected"><a href="#panel-2"><i class="iconfont">&#xe60e;</i>@T("手机端")</a></li>
                                                </ul>
                                                <div id="panel-1" class="ui-tabs-panel">
                                                    <script type="text/javascript">
                                                        var ue@(item.Id) = UE.getEditor('<EMAIL>');
                                                        if ("") {
                                                            ue@(item.Id).ready(function () {
                                                                this.setContent("");
                                                            })
                                                        }

                                                    </script><textarea name="<EMAIL>" id="<EMAIL>"></textarea>
                                                    <div class="hr8">

                                                        <a class="dssc-btn mt5" dstype="show_desc@(item.Id)" href="@Url.Action("PiclistConten",new { type = "Pc" })"><i class="iconfont">&#xe72a;</i>@T("")插入相册图片</a> <a href="javascript:void(0);" dstype="del_desc@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                                    </div>
                                                    <p id="des_demo@(item.Id)"></p>
                                                </div>
                                                <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                                    <div>
                                                        
                                                    </div>
                                                    <script type="text/javascript">
                                                        var ue@(item.Id) = UE.getEditor('goods_Mobilebody_@(item.Id)');
                                                        if ("") {
                                                            ue@(item.Id).ready(function () {
                                                                this.setContent("");
                                                            })
                                                        }
                                                    </script>
                                                    <textarea name="goods_Mobilebody_@(item.Id)" id="goods_Mobilebody_@(item.Id)"></textarea>
                                                    <div class="hr8">
                                                        <a class="dssc-btn mt5" dstype="show_desca@(item.Id)" href="@Url.Action("PiclistConten",new { type = "Mobile" })"><i class="iconfont">&#xe72a;</i>@T("插入相册图片")</a> <a href="javascript:void(0);" dstype="del_desca@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                                    </div>
                                                    <p id="des_Mobieldemo@(item.Id)"></p>


                                                </div>
                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span>
                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].add_album" id="add_album@(item.Id)" multiple="multiple">
                                                        </span>
                                                        <p><i class="iconfont" data_type="0" dstype="add_album_i@(item.Id)">&#xe733;</i>@T("图片上传")</p>
                                                    </a>
                                                </div>
                                            </div>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("规格参数")：</dt>
                                        <dd id="dsProductDetailss@(item.Id)">
                                            <div class="tabs">

                                                <div id="panel-2@(item.Id)" class="ui-tabs-panel">
                                                    <script type="text/javascript">
                                                        var us = UE.getEditor('<EMAIL>');
                                                           us.ready(function () {
                                                               this.setContent('');
                                                           })
                                                    </script>
                                                    <textarea name="[@item.Id].spec" id="spec_@(item.Id)"></textarea>
                                                    <div class="hr8">
                                                        <a class="dssc-btn mt5" dstype="show_descs@(item.Id)" href="@Url.Action("PiclistContens",new { type = "Pc" })"><i class="iconfont">&#xe72a;</i>插入相册图片</a> <a href="javascript:void(0);" dstype="del_descs@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>关闭相册</a>
                                                    </div>
                                                    <p id="des_demos@(item.Id)"></p>
                                                </div>

                                                <div class="dssc-upload-btn">
                                                    <a href="javascript:void(0);">
                                                        <span>
                                                            <input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].add_albums" id="add_albums@(item.Id)" multiple="multiple">
                                                        </span>
                                                        <p><i class="iconfont" data_type="0" dstype="add_album_is@(item.Id)">&#xe733;</i>图片上传</p>
                                                    </a>
                                                </div>
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>


            <div class="dssc-form-goods">
                <h3 id="demo">@T("成品基本信息")</h3>
                <dl style="overflow: inherit;">
                    <dt><i class="required">*</i>@T("成品型号")：</dt>
                    <dd>
                        <div id="demo000" style="width:50%"></div>
                    </dd>
                </dl>
                <dl>
                    <dt>@T("成品分类")：</dt>
                    <dd id="gcategory">
                        @ViewBag.className            <a class="dssc-btn" href="@Url.Action("CreateGoodsOne")">@T("编辑")</a>
                        <input type="hidden" id="cate_id" name="cate_id" value="" class="text" />
                        <input type="hidden" name="cate_name" value="@ViewBag.className" class="text" />
                    </dd>
                </dl>
            </div>


            <input type="hidden" name="commonid" value="" />
            <input type="hidden" name="type_id" value="0" />

            <div class="bottom tc hr32">
                <input type="submit" class="btn" value="@T("下一步，上传成品图片")" />
            </div>
        </form>
    </div>


    

    <script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
    <script src="/static/plugins/mlselection.js"></script>
    <script src="/static/plugins/js/fileupload/jquery.iframe-transport.js"></script>
    <script src="/static/plugins/js/fileupload/jquery.ui.widget.js"></script>
    <script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
    @*<script src="/static/home/<USER>/sellergoods_add_step2.js"></script>*@
    <script src="~/static/admin/home/<USER>"></script>
    <script src="~/static/admin/js/xm-select.js"></script>

    <link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
    <script src="~/static/plugins/js/layui/layui.js"></script>

    <script asp-location="Footer">
        var ADMINSITEURL = "/@adminarea";
        var HOMESITEROOT = "/@adminarea";
        var BASESITEURL = "/@adminarea";
        var Selleralbum = "@Url.Action("PiclistConten",new { type= "Mobile" })"
        var ADMINSITEROOT = "/static/admin";
        //var WWWROOT = "/images";
        var Querytheson = "@Url.Action("Querytheson", "EndProductClass")";
        var createData = "@Url.Action("CreateGoods")";
        var CreateImg = "@Url.Action("UploadImg")";
        var CreateImgs = "@Url.Action("UploadImg","SpaceCategory",new { type=1})";

        var DEFAULT_GOODS_IMAGE = "/uploads/home/<USER>/default_goods_image.jpg";


            layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
                var $ = layui.jquery,
                    form = layui.form,
                    layer = layui.layer,
                    upload = layui.upload,
                    layer = layui.layer,
                    element = layui.element;
            })

            var lid = "";
            var demo = "";
            var show_image = "";
            var del_goods_demo = "";
            var goods_image = "";
            var show_desc = "";
            var des_demo = "";
            var del_desc = "";
            var del_descs = "";
            var des_Mobieldemo = "#des_Mobieldemo";
            var show_desca = "";
            var del_desca = "";
            var show_descs = "";
            var des_demos = "";

            $(function () {
                $(".layui-tab-title").on("click", "li", function () {
                    console.log($(this).attr("data"));
                    lid = $(this).attr("data");
                    demo = "#demo" + lid;
                    show_image = "show_image" + lid;
                    del_goods_demo = "del_goods_demo" + lid;
                    goods_image = "goods_image" + lid;
                    show_desc = "show_desc" + lid;
                    des_demo = "#des_demo" + lid;
                    del_desc = "del_desc" + lid;
                    add_album = "add_album" + lid;
                    add_album_i = "add_album_i" + lid;
                    del_descs = "del_descs" + lid;
                    show_descs = "show_descs" + lid;
                    des_Mobieldemo = "#des_Mobieldemo" + lid;
                    show_desca = "show_desca" + lid;
                    del_desca = "del_desca" + lid;
                    des_demos = "#des_demos" + lid;

                    //主图打开图片空间
                    $('a[dstype="' + show_image + '"]').unbind().ajaxContent({
                        event: 'click', //mouseover
                        loaderType: "img",
                        loadingMsg: "/images/loading.gif",
                        //target: '#demo'
                        target: "#demo" + lid
                    }).click(function () {
                        $('a[dstype="' + del_goods_demo + '"]').show();
                        $(this).hide();
                    });
                    $('a[dstype="' + del_goods_demo + '"]').unbind().click(function () {
                        $('' + demo + '').html('');
                        $('a[dstype="' + show_image + '"]').show();
                        $(this).hide();

                    });


                    //参数规格使用
                    //成品规格参数使用
                    $('a[dstype="' + show_descs + '"]').unbind().ajaxContent({
                        event: 'click', //mouseover
                        loaderType: "img",
                        loadingMsg: "/images/loading.gif",
                        target: des_demos
                    }).click(function () {
                        $(this).hide();
                        $('a[dstype="' + del_descs + '"]').show();
                    });

                    $('a[dstype="' + del_descs + '"]').click(function () {
                        $('' + des_demos+ '').html('');
                        $(this).hide();
                        $('a[dstype="' + show_descs + '"]').show();
                    });


                    //参数手机端使用
                    //成品手机端内容使用
                    $('a[dstype="' + show_desca + '"]').unbind().ajaxContent({
                        event: 'click', //mouseover
                        loaderType: "img",
                        loadingMsg: "/images/loading.gif",
                        target: des_Mobieldemo
                    }).click(function () {
                        $(this).hide();
                        $('a[dstype="' + del_desca + '"]').show();
                    });

                    $('a[dstype="' + del_desca + '"]').click(function () {
                        $('' + des_Mobieldemo + '').html('');
                        $(this).hide();
                        $('a[dstype="' + des_Mobieldemo + '"]').show();
                    });

                    /* 插入成品描述 */
                    // 成品描述使用
                    $('a[dstype="' + show_desc + '"]').unbind().ajaxContent({
                        event: 'click', //mouseover
                        loaderType: "img",
                        loadingMsg: "/images/loading.gif",
                        target: des_demo
                    }).click(function () {
                        $(this).hide();
                        $('a[dstype="' + del_desc + '"]').show();
                    });

                    $('a[dstype="' + del_desc + '"]').click(function () {
                        $('' + des_demo + '').html('');
                        $(this).hide();
                        $('a[dstype="' + des_demo + '"]').show();
                    });

                    $('#' + add_album + '').fileupload({
                        dataType: 'json',
                        //url: HOMESITEURL+'/Sellergoodsadd/image_upload.html',
                        url: CreateImgs,
                        formData: { name: '' + add_album + '' },
                        add: function (e, data) {
                            $('i[dstype="' + add_album_i + '"]').html("&#xe717;").addClass('rotate').attr('data_type', parseInt($('i[dstype="' + add_album_i + '"]').attr('data_type')) + 1);
                            data.submit();
                        },
                        done: function (e, data) {
                            var _counter = parseInt($('i[dstype="' + add_album_i + '"]').attr('data_type'));
                            _counter -= 1;
                            if (_counter == 0) {
                                $('i[dstype="' + add_album_i + '"]').removeClass('rotate').html("&#xe733;");
                                $('a[dstype="' + show_desc + '"]').click();
                            }
                            $('i[dstype="' + add_album_i + '"]').attr('' + show_desc + '', _counter);
                        }
                    });

                    //上传成品主图
                    $('#' + goods_image + '').fileupload({
                        dataType: 'json',
                        //url: HOMESITEURL + '/Sellergoodsadd/image_upload.html?upload_type=uploadedfile',
                        //url: CreateImg,
                        url: CreateImgs,
                        formData: { name: $("#ModelId").val() },
                        add: function (e, data) {
                            $('img[dstype="' + goods_image + '"]').attr('src', '/images/loading.gif');
                            data.submit();
                        },
                        done: function (e, data) {
                            var param = data.result;
                            if (!param.success) {
                                alert(param.meg);
                                $('img[dstype="' + goods_image + '"]').attr('src', DEFAULT_GOODS_IMAGE);
                            } else {
                                //$('input[dstype="goods_image"]').val(param.name);
                                $('input[dstype="' + goods_image + '"]').val(param.file_id);
                                //$('img[dstype="goods_image"]').attr('src',param.thumb_name);
                                $('img[dstype="' + goods_image + '"]').attr('src', param.file_path);
                            }
                        }
                    });







                    //$('a[dstype="' + show_desc + '"]').unbind().ajaxContent({
                    //    event: 'click', //mouseover
                    //    loaderType: "img",
                    //    loadingMsg: "/images/loading.gif",
                    //    target: des_demo
                    //}).click(function () {
                    //    $(this).hide();
                    //    $('a[dstype="' + del_desc + '"]').show();
                    //});

                    //$('a[dstype="' + del_desc + '"]').click(function () {
                    //    $('' + des_demo + '').html('');
                    //    $(this).hide();
                    //    $('a[dstype="' + des_demo + '"]').show();
                    //});



                    //参数规格使用
                    //成品规格参数使用
                    //$('a[dstype="' + show_descs + '"]').unbind().ajaxContent({
                    //    event: 'click', //mouseover
                    //    loaderType: "img",
                    //    loadingMsg: "/images/loading.gif",
                    //    target: des_demos
                    //}).click(function () {
                    //    $(this).hide();
                    //    $('a[dstype="' + del_descs + '"]').show();
                    //});

                    //$('a[dstype="' + del_descs + '"]').click(function () {
                    //    $('' + del_descs + '').html('');
                    //    $(this).hide();
                    //    $('a[dstype="' + show_descs + '"]').show();
                    //});



                })
                })

                //$('a[dstype="show_desca"]').unbind().ajaxContent({
                //    event: 'click', //mouseover
                //    loaderType: "img",
                //    loadingMsg: "/images/loading.gif",
                //    target: "#des_Mobieldemo"
                //}).click(function () {
                //    $(this).hide();
                //    $('a[dstype="del_desca"]').show();
                //});

                //$('a[dstype="del_desca"]').click(function () {
                //    $('des_Mobieldemo').html('');
                //    $(this).hide();
                //    $('a[dstype="des_Mobieldemo"]').show();
                //});


            insert_mobile_img = function (file_path) {
                if (!lid) {
                    var ue = UE.getEditor('goods_Mobilebody');
                    ue.execCommand('insertimage', { src: file_path });
                }
                else {
                    var ue = UE.getEditor('goods_Mobilebody_' + lid);
                    ue.execCommand('insertimage', { src: file_path });
                }
            }



            function insert_img(name, src) {
                if (!goods_image)
                {
                    goods_image = "goods_image";
                }
                console.log("insert_img事件触发");
                $('input[dstype="' + goods_image + '"]').val(name);
                $('img[dstype="' + goods_image + '"]').attr('src', src);
                //$("#imageType").val("1");//验证是从从相册图片选取的
            }

            function insert_editor(file_path) {
                if (!lid) {
                    var ue = UE.getEditor('goods_body');
                    ue.execCommand('insertimage', { src: file_path });
                }
                else {
                    var ue = UE.getEditor('goods_body_' + lid);
                    ue.execCommand('insertimage', { src: file_path });
                }

            }
            function insert_editor1(file_path) {
                if (!lid) {
                    var ue = UE.getEditor('spec');
                    ue.execCommand('insertimage', { src: file_path });
                } else {
                    var ue = UE.getEditor('spec_' + lid);
                    ue.execCommand('insertimage', { src: file_path });
                }
            }


            $(function () {
                $("#region").ds_region({ show_deep: 2, tip_type: 1 });
                //电脑端手机端tab切换
                $(".tabs").tabs();

                //$(".LId").each(function () {
                //    console.log($(this).attr("data"));
                //})
            })

            var data1 = $.parseJSON('@Html.Raw(ViewBag.List)');

            var demossa = xmSelect.render({
                el: '#demo000',
                
                paging: true,
                pageSize: 10,
                filterable: true,
                filterMethod: function (val, item, index, prop) {
                    if (item.name.toLowerCase().indexOf(val.toLowerCase()) != -1) {//名称中包含的大小写都搜索出来
                        return true;
                    }
                    return false;//其他的就不要了
                },
                pageEmptyShow: false,
                //clickClose: true,
                data: data1
            });

    </script>
</div>
