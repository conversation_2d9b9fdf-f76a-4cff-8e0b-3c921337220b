﻿.title1 {
    color: #333 !important;
    background-color: #fff;
}

    .title1:hover {
        background-color: #eee !important;
    }

body {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.top {
    position: relative;
}

    .top > img {
        width: 100%;
        min-height: 520px;
    }


    .top > div {
        width: 100%;
        position: absolute;
        left: 0px;
        right: 0px;
        top: 25%;
        text-align: center;
    }


        .top > div > P {
            font-size: 48.82px;
            color: #FFFFFF;
        }

.seach-menu {
    max-width: 750px;
    margin: 0 auto;
    position: relative;
    display: flex;
    margin-top: 58px;
}

    .seach-menu dl {
        position: absolute;
        display: none;
        top: 70px;
        width: 17.44%;
        background-color: #fff;
        border-radius: 5px;
        padding-top: 6px;
        padding-bottom: 6px;
        box-shadow: 0px 3px 14px -5px #7A7A7A;
    }

    .seach-menu dd {
        text-align: left;
        padding: 10px;
        cursor: pointer;
        color: #000;
    }

        .seach-menu dd:hover {
            background-color: #ccc;
        }

    .seach-menu > span {
        display: inline-block;
        background-color: #235AA6;
        width: 17.44%;
        text-align: center;
        height: 64px;
        line-height: 64px;
        color: #fff;
        font-size: 18.3px;
        cursor: pointer;
    }

        .seach-menu > span img {
            margin-left: 15px;
        }

    .seach-menu > input {
        flex: 1;
        outline: none;
        padding-left: 2%;
    }

    .seach-menu > a {
        width: 17.58%;
        text-align: center;
        background-color: #235AA6;
        height: 64px;
        line-height: 64px;
    }

.navigation-con {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    padding-top: 14px;
    padding-bottom: 14px;
}

.navigation-con-left {
    flex: 1;
    line-height: 34.4px;
}

    .navigation-con-left > a {
        color: #333333;
    }

.navigation-con-right {
    width: 280px;
}

.seach-list {
    max-width: 1200px;
    margin: 0 auto;
}

    .seach-list h2 {
        display: block;
        padding-left: 0.68%;
        font-size: 22px;
        color: #474747;
        background-color: #ECECED;
        height: 52px;
        line-height: 52px;
    }

    .seach-list ul {
        list-style: none;
        padding: 0px;
    }

    .seach-list li {
        display: flex;
    }

        .seach-list li a {
            padding-left: 0.78%;
            width: 70.57%;
            text-align: left;
            height: 50px;
            line-height: 50px;
            color: #525252;
            font-size: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            text-decoration: none;
            white-space: nowrap;
        }

        .seach-list li i {
            flex: 1;
            text-align: right;
            height: 50px;
            line-height: 50px;
            color: #525252;
            font-size: 18px;
            font-style: normal;
            padding-right: 2.08%;
        }

    .seach-list p {
        display: flex;
        padding-top: 30px;
        padding-left: 0.89%;
    }

        .seach-list p span {
            flex: 1;
        }

            .seach-list p span i {
                font-style: normal;
                display: block;
                font-size: 18px;
                color: #343434;
            }

                .seach-list p span i img {
                    padding-right: 0.37%;
                }

            .seach-list p span strong {
                display: flex;
                flex-direction: column;
                line-height: 2.2;
                padding-left: 1.05%;
            }

            .seach-list p span sub {
                display: flex;
                padding-top: 27px;
            }

            .seach-list p span:nth-child(1) sub img {
                padding-top: 8px;
                padding-bottom: 10px;
            }

            .seach-list p span strong b {
                flex: 1;
                color: #666;
                font-size: 16px;
                font-weight: normal;
            }
