﻿@{
    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
}
@model HlktechSite.Entity.EndProducts
<style asp-location="true">
    
</style>
<link rel="stylesheet" href="/static/plugins/perfect-scrollbar.min.css">
<link rel="stylesheet" href="~/static/admin/css/admin1.css">
<link rel="stylesheet" href="/static/plugins/perfect-scrollbar.min.css">
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>成品管理</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
        
                <li><a href="@Url.Action("UpdateGoods",new { Id = Model.Id })"><span>编辑成品</span></a></li>
                <li><a href="@Url.Action("ModifyAPicture",new { Id=Model.Id})"><span>编辑图片</span></a></li>
                <li><a href="@Url.Action("SearchCategories",new { Id=Model.Id})"  class="current"   ><span>选择分类</span></a></li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>
    <div class="wrapper_search">
        <div class="wp_sort">
            <div id="dataLoading" class="wp_data_loading">
                <div class="data_loading">加载中...</div>
            </div>
            <div class="sort_selector">
                <div class="sort_title">
                    您常用的成品分类：        <div class="text" id="commSelect">
                        <div>请选择</div>
                        <div class="select_list" id="commListArea">
                            <ul>
                                <li id="select_list_no" style="display: none;"><span class="title">您还没有添加过常用的分类</span></li>
                                @foreach (var item in ViewBag.CommonlyUsedOptions as List<HlktechSite.Entity.EndProductClassStaple>)
                                {
                                    <li data-param="{stapleid:@item.Id}">
                                        <span dstype="staple_name">@item.EndProductClass1?.Name &gt;@item.EndProductClass2?.Name &gt;@item.EndProductClass3?.Name</span>
                                        <a href="JavaScript:void(0);" dstype="del-comm-cate" title="ds_delete">X</a>
                                    </li>
                                   
                                }
                            </ul>
                        </div>
                    </div>
                    <i class="iconfont">&#xe689;</i>
                </div>
            </div>
            <div id="class_div" class="wp_sort_block">
                <div class="sort_list">
                    <div class="wp_category_list">
                        <div id="class_div_1" class="category_list">
                            <ul>
                                @foreach (var item in ViewBag.list1 as IEnumerable<HlktechSite.Entity.EndProductClass>)
                                {
                                    <li class="" dstype="selClass" data-param="{gcid:@item.Id,deep:@item.Level+1,tid:1}"> <a class="@(item.Id == Model.Cid1?"classDivClick":"")" href="javascript:void(0)"><i class="iconfont"></i>@item.Name</a></li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="sort_list">
                    <div class="wp_category_list blank">
                        <div id="class_div_2" class="category_list">
                            <ul>
                                @foreach (var item in ViewBag.list2 as IEnumerable<HlktechSite.Entity.EndProductClass>)
                                {
                                    <li class="" dstype="selClass" data-param="{gcid:@item.Id,deep:@item.Level+1,tid:1}"> <a class="@(item.Id == Model.Cid2?"classDivClick":"")" href="javascript:void(0)"><i class="iconfont"></i>@item.Name</a></li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="sort_list sort_list_last">
                    <div class="wp_category_list blank">
                        <div id="class_div_3" class="category_list">
                            <ul>
                                @foreach (var item in ViewBag.list3 as IEnumerable<HlktechSite.Entity.EndProductClass>)
                                {
                                    <li class="" dstype="selClass" data-param="{gcid:@item.Id,deep:@item.Level+1,tid:1}"> <a class="@(item.Id == Model.Cid3?"classDivClick":"")" href="javascript:void(0)"><i class="iconfont"></i>@item.Name</a></li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="alert">
            <dl class="hover_tips_cont">
                <dt id="commodityspan"><span style="color:#F00;">请选择成品类别</span></dt>
                <dt id="commoditydt" style="display: none;" class="current_sort">您当前选择的成品类别是：</dt>
                <dd id="commoditydd"></dd>
            </dl>
        </div>
        <div class="wp_confirm">
            <form method="get" action="@Url.Action("SearchCategoriess")">
                <input type="hidden" name="Id" value="@Model.Id" />
                <input type="hidden" name="class_id" id="class_id" value="" />
                <input type="hidden" name="t_id" id="t_id" value="" />
                <div class="bottom tc">
                    <input disabled="disabled" dstype="buttonNextStep" value="下一步，填写成品信息" type="submit" class="btn" style=" width: 200px;" />
                </div>
            </form>
        </div>
    </div>
    <script src="/static/plugins/mlselection.js"></script>
    <script src="/static/plugins/jquery.mousewheel.js"></script>
    <script src="/static/home/<USER>/sellergoods_add_step1.js"></script>
    <script src="/static/plugins/perfect-scrollbar.min.js"></script>
    <script  asp-location="Head">
        SEARCHKEY = '请输入成品名称或分类属性名称';
    </script>
</div>
<script type="text/javascript" asp-location="Footer">
        var HOMESITEURL = "@Url.Action("PitchOn", "Goods")";
    var ADMINSITEURL = "/@adminarea";
    var HOMESITEROOT = "/@adminarea";
    var BASESITEURL = "/@adminarea";
    var ADMINSITEROOT = "/static/admin";
    var Querytheson = "@Url.Action("Querytheson", "EndProductClass")";
    var createData = "@Url.Action("CreateGoods")";
    var CreateImg = "@Url.Action("UploadImg")";
</script>
