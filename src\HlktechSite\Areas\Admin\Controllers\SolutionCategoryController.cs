﻿using DG.Cube.BaseControllers;
using DG.Cube.Models;

using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechSite.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Log;

using Pek;
using Pek.Helpers;
using Pek.Iot;
using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechSite.Areas.Admin.Controllers;

/// <summary>解决方案分类</summary>
[DisplayName("解决方案分类")]
[Description("用于解决方案分类的管理")]
[AdminArea]
[DHMenu(47,ParentMenuName = "Site", CurrentMenuUrl = "~/{area}/SolutionCategory", CurrentMenuName = "SolutionCategoryList", CurrentIcon = "&#xe652;", LastUpdate = "20240125")]
public class SolutionCategoryController : DGBaseAdminControllerX
{
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 47;
    /// <summary>
    /// 解决方案分类列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("解决方案分类列表")]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var list = SolutionCategory.FindAllByLevel(0);
        foreach (var item in list)
        {
            var Model = SolutionCategory.FindAllByParentId(item.Id);
            if (Model.Count != 0)
            {
                item.subset = true;
            }
        }
        viewModel.list = list;
        return View(viewModel);
    }
    /// <summary>
    /// 获取解决方案分类表下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取解决方案分类表下级数据")]
    public IActionResult GetSubordinateData(string Id)
    {
        var zList = new List<Hierarchy>();
        if (Id.IsNullOrWhiteSpace())
        {
            return Json(new { });
        }
        var list = SolutionCategory.FindAllByParentId(Id.ToInt());
        foreach (var item in list)
        {
            var model = new Hierarchy();
            model.gc_name = item.Name.SafeString().Trim();
            model.gc_id = item.Id;
            model.gc_parent_id = item.ParentId;
            var exc = SolutionCategory.FindAllByParentId(item.Id);
            if (exc.Count > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.gc_sort = item.DisplayOrder;
            model.deep = item.Level;
            zList.Add(model);
        }
        return Json(new { zList });
    }
    /// <summary>
    /// 打开修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("打开修改页面")]
    public IActionResult EditSolutionCategory(string Id)
    {
        dynamic viewModel = new ExpandoObject();
        var Model = SolutionCategory.FindById(Id.ToInt());
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        var List = new List<SolutionCategory>();
        var live1 = SolutionCategory.FindAllByLevel(0);
        GetCategoryList(live1, List);
        viewModel.Plist = List;
        viewModel.Model = Model;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(viewModel);
    }



    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IList<SolutionCategory> levelList, IList<SolutionCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = SolutionCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }




    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取是否存在该名称")]
    public IActionResult GetByName(string gc_name, int gc_id)
    {
        var Model = SolutionCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }
    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    public IActionResult GetByNames(string gc_name, int gc_id)
    {
        var Model = SolutionCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }
    /// <summary>
    /// 修改分类接口
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="gc_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改分类接口")]
    public IActionResult EditSolutionCategory(string gc_name, int gc_parent_id, int gc_id, int DisplayOrder)
    {
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("文章名称不能为空") });
        }
        var Model = SolutionCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("该下载分类名称已存在") });
        }
        if (gc_parent_id == gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("父级栏目不能为自身") });
        }
        using (var tran1 = SolutionCategory.Meta.CreateTrans())
        {

            var models = SolutionCategory.FindById(gc_id);
            if (models == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
            }
            models.Name = gc_name.SafeString().Trim();
            models.ParentId = gc_parent_id.ToInt();
            models.DisplayOrder = DisplayOrder.ToShort();
            var pmodel = SolutionCategory.FindById(gc_parent_id);
            if (pmodel == null)
            {
                models.ParentIdList = gc_id.ToString();
                models.Level = 0;
            }
            else
            {
                models.ParentIdList = pmodel.ParentIdList + "," + gc_id.ToString();
                models.Level = pmodel.Level + 1;
            }
            if (models.Level > 2)
            {
                return Prompt(new PromptModel { Message = GetResource("分类不能超过三层") });
            }
            models.Update();
            SolutionCategory.Meta.Cache.Clear("");//清楚缓存
                                                  //循环修改子集的父级Id集合
            var zList = SolutionCategory.FindAllByParentId(gc_id);
            foreach (var item in zList)
            {
                item.ParentIdList = models.ParentIdList + "," + item.Id;
                var slist = SolutionCategory.FindAllByParentId(item.Id);
                foreach (var row in slist)
                {
                    item.ParentIdList = item.ParentIdList + "," + row.Id;
                }
                slist.Save();
            }
            zList.Save();

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable)
            {
                XTrace.WriteLine("即将获取多语言列表");
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
                XTrace.WriteLine("即将获取多语言列表数量=="+ Languagelist.Count());
                var lanlist = SolutionCategoryLan.FindAllByCId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new SolutionCategoryLan();
                    }
                    aaaa.Name = GetRequest($"[{item.Id}].gc_name").SafeString().Trim();
                    aaaa.CId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Save();
                }
            }
            tran1.Commit();
        }
        SolutionCategoryLan.Meta.Cache.Clear("");//清除缓存
        SolutionCategory.Meta.Cache.Clear("");//清除缓存

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }
    /// <summary>
    /// 新增页面打开
    /// </summary>
    /// <param name="parent_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("解决方案分类新增页面")]
    public IActionResult AddSolutionCategory(int parent_id = 0)
    {
        XTrace.WriteLine("获取进入到新增页面");
        dynamic viewModel = new ExpandoObject();
        var List = new List<SolutionCategory>();
        viewModel.ParentId = parent_id;
        var live1 = SolutionCategory.FindAllByLevel(0);//一级数据
          GetCategoryList(live1, List);

        viewModel.Plist = List;
        var DisplayOrder = 0;
        if (parent_id != 0)
        {
            DisplayOrder = (Int32)SolutionCategory.FindMax("DisplayOrder", SolutionCategory._.ParentId == parent_id);
        }
        else
        {
            DisplayOrder = (Int32)SolutionCategory.FindMax("DisplayOrder");
        }

        ViewBag.DisplayOrder = DisplayOrder + 1;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言 
        return View(viewModel);
    }
    /// <summary>
    /// 解决方案分类新增
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("解决方案分类新增")]
    public IActionResult AddSolutionCategory(string gc_name, int gc_parent_id, int DisplayOrder)
    {
        XTrace.WriteLine("获取到的gc_parent_id==" + gc_parent_id);
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("文章名称不能为空") });
        }

        var model = SolutionCategory.FindByName(gc_name.SafeString().Trim());
        if (model != null)
        {
            return Prompt(new PromptModel { Message = GetResource("文章分类名称已存在") });
        }
        var Pmodel = SolutionCategory.FindById(gc_parent_id);
        if (Pmodel != null)
        {
            if (Pmodel.Level >= 2)
            {
                return Prompt(new PromptModel { Message = GetResource("文章分类最多存在三级,创建失败！") });
            }
        }
        using (var tran1 = SolutionCategory.Meta.CreateTrans())
        {

            var Model = new SolutionCategory();
            Model.Name = gc_name.SafeString().Trim();
            Model.ParentId = gc_parent_id;
            Model.DisplayOrder = DisplayOrder.ToShort();
            Model.Insert();

            if (Pmodel == null)
            {
                Model.Level = 0;
                Model.ParentIdList = Model.Id.ToString();
            }
            else
            {
                Model.Level = Pmodel.Level + 1;
                Model.ParentIdList = Pmodel.ParentIdList + "," + Model.Id;
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言               
                var lanlist = SolutionCategoryLan.FindAllByCId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new SolutionCategoryLan();
                    }

                    aaaa.Name = GetRequest($"[{item.Id}].gc_name").SafeString().Trim();
                    aaaa.CId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Save();
                }
            }
            tran1.Commit();
        }

        SolutionCategoryLan.Meta.Cache.Clear("");//清除缓存
        SolutionCategory.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }


    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();

        var list = SolutionCategory.FindByIds(Ids);
        var dellist = new List<SolutionCategory>();

        res = DeleteSolutionCategory(res, dellist, list);

        if (!res.msg.IsNullOrWhiteSpace())
        {
            return Json(res);
        }

        foreach (var item in dellist)
        {
            SolutionCategoryLan.Delete(SolutionCategoryLan._.CId == item.Id);
        }
        if (dellist.Delete(true) > 0)
            SolutionCategoryLan.Meta.Cache.Clear("");
            SolutionCategory.Meta.Cache.Clear("");

        res.msg = GetResource("删除成功");
        return Json(res);
    }


    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private DResult DeleteSolutionCategory(DResult res, IList<SolutionCategory> dellist, IList<SolutionCategory> list)
    {
        if (list.Count > 0)
        {
            foreach (var item in list)
            {
                var listKnowledge = Case.FindAllByCId(item.Id);
                if (listKnowledge.Count > 0)
                {
                    res.msg = String.Format(GetResource("选中的{0}有关联文章数据 不允许被删除"), item.Name);
                    return res;
                }
                else
                {
                    dellist.Add(item);
                    var childlist = SolutionCategory.FindAllByParentId(item.Id);
                    res = DeleteSolutionCategory(res, dellist, childlist);
                }
            }
        }
        return res;
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = SolutionCategory.FindById(Id);

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = SolutionCategory.FindByName(value);
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.DisplayOrder = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }

}
