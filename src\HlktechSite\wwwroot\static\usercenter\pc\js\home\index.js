// 控制台首页脚本
$(function () {
	// home-user-money jump
	$(".home-user-money").click(function () {
		window.location.href = $(this).data("url");
	});
	// incomeChart
	var chartLegendArr = ["消费记录", "收入记录"];
	var chartDataArr = [
		{
			value: nyData.out_money,
			name: "消费记录"
		},
		{
			value: nyData.in_money,
			name: "收入记录"
		}
	];
	// 初始化图表
	var userExpenseChart = echarts.init($("#userExpenseGraph")[0]);
	var seriesConfig = {
		name: '消费趋势',
		type: 'pie',
		x: 0,
		width: 136,
		center: [100, 100],
		radius: [48, 68],
		avoidLabelOverlap: false,
		hoverAnimation: true,
		animation: true,
		animationType: "expansion",
		legendHoverLink: false,
		label: {
			normal: {
				show: true,
				position: 'center',
				textStyle: {
					fontSize: '14',
					fontWeight: 'normal',
					color: "#262829"
				},
				formatter: ""
			},
			emphasis: {
				show: true,
				textStyle: {
					fontSize: '14',
					fontWeight: 'normal'
				},
				formatter: "￥{c}"
			}
		},
		itemStyle: { //图形样式
			normal: { //normal 是图形在默认状态下的样式；emphasis 是图形在高亮状态下的样式，比如在鼠标悬浮或者图例联动高亮时。
				label: { //饼图图形上的文本标签
					show: false //平常不显示
				},
				labelLine: { //标签的视觉引导线样式
					show: false //平常不显示
				}
			},
			emphasis: { //normal 是图形在默认状态下的样式；emphasis 是图形在高亮状态下的样式，比如在鼠标悬浮或者图例联动高亮时。
				label: { //饼图图形上的文本标签
					show: false,
					position: 'center',
					textStyle: {
						fontSize: '24',
						fontWeight: 'bold'
					}
				}
			}
		},
		labelLine: {
			normal: {
				show: false
			}
		},
		emphasis: {
			label: {
				show: true
			}
		},
		data: chartDataArr
	};
	var chartOption1 = {
		title: {
			text: '本月统计',
			x: 70,
			y: 90,
			show: false,
			textStyle: {
				fontSize: '14',
				fontWeight: 'normal'
			}
		},
		tooltip: {
			trigger: 'item',
			formatter: "{a} <br/>{b} : {c}元 ({d}%)"
		},
		color: ["#ffad33", "#00aaff"],
		legend: { //图例组件
			//right:100,  //图例组件离右边的距离
			orient: 'horizontal', //布局  纵向布局 图例标记居文字的左边 vertical则反之
			width: 100, //图行例组件的宽度,默认自适应
			itemGap: 60,
			x: '60%', //图例显示在右边
			y: 32, //图例在垂直方向上面显示居中
			itemWidth: 12, //图例标记的图形宽度
			itemHeight: 12, //图例标记的图形高度
			data: chartLegendArr,
			textStyle: { //图例文字的样式
				color: '#a0a2a3', //文字颜色
				fontSize: 12 //文字大小
			},
		},
		series: [seriesConfig]
	};
	var chartOptionNoMoney = {
		title: {
			text: '无记录',
			x: 75,
			y: 90,
			textStyle: {
				fontSize: '14',
				fontWeight: 'normal',
				color: "#a0a2a3"
			}
		},
		tooltip: {
			trigger: 'item',
			formatter: ""
		},
		color: ["#eee"],
		legend: { //图例组件
			//right:100,  //图例组件离右边的距离
			orient: 'horizontal', //布局  纵向布局 图例标记居文字的左边 vertical则反之
			width: 100, //图行例组件的宽度,默认自适应
			itemGap: 60,
			x: '60%', //图例显示在右边
			y: 32, //图例在垂直方向上面显示居中
			itemWidth: 12, //图例标记的图形宽度
			itemHeight: 12, //图例标记的图形高度
			data: chartLegendArr,
			textStyle: { //图例文字的样式
				color: '#a0a2a3', //文字颜色
				fontSize: 12 //文字大小
			},
			selectedMode:false,
		},
		series: [{
			name: '消费趋势',
			type: 'pie',
			x: 0,
			width: 136,
			center: [100, 100],
			radius: [48, 68],
			avoidLabelOverlap: false,
			hoverAnimation: false,
			label: {
				normal: {
					show: false,
					position: 'center',
					textStyle: {
						fontSize: '14',
						fontWeight: 'normal',
						color: "#262829"
					},
					formatter: ""
				},
				emphasis: {
					show: false,
					textStyle: {
						fontSize: '14',
						fontWeight: 'normal'
					},
					formatter: "￥{c}"
				}
			},
			itemStyle: { //图形样式
				normal: { //normal 是图形在默认状态下的样式；emphasis 是图形在高亮状态下的样式，比如在鼠标悬浮或者图例联动高亮时。
					label: { //饼图图形上的文本标签
						show: false //平常不显示
					},
					labelLine: { //标签的视觉引导线样式
						show: false //平常不显示
					}
				},
				emphasis: { //normal 是图形在默认状态下的样式；emphasis 是图形在高亮状态下的样式，比如在鼠标悬浮或者图例联动高亮时。
					label: { //饼图图形上的文本标签
						show: false,
						position: 'center',
						textStyle: {
							fontSize: '24',
							fontWeight: 'bold'
						}
					},
					color: "#eee"
				}
			},
			labelLine: {
				normal: {
					show: false
				}
			},
			data: chartDataArr
		}]
	};
	// 收入支出均为0的时候使用无记录option
	if (parseFloat(nyData.out_money) == 0 && parseFloat(nyData.in_money) == 0) {
		userExpenseChart.setOption(chartOptionNoMoney);
		$(".user-expense-title").hide();
	}
	else {
		userExpenseChart.setOption(chartOption1);
	}
	// The methods below used to forbid all legend canceled
	var triggerAction = function(action, selected) {
		legend = [];

		for ( name in selected) {
			if (selected.hasOwnProperty(name)) {
				legend.push({name: name});
			}
		}

		userExpenseChart.dispatchAction({
			type: action,
			batch: legend
		});
	};

	var isFirstUnSelect = function(selected) {

		var unSelectedCount = 0;
		for ( name in selected) {
			if (!selected.hasOwnProperty(name)) {
				continue;
			}

			if (selected[name] == false) {
				++unSelectedCount;
			}
		}
		return unSelectedCount==1;
	};

	var isAllUnSelected = function(selected) {
		var selectedCount = 0;
		for ( name in selected) {
			if (!selected.hasOwnProperty(name)) {
				continue;
			}

			// 所有 selected Object 里面 true 代表 selected， false 代表 unselected
			if (selected[name] == true) {
				++selectedCount;
			}
		}
		return selectedCount==0;
	};

	userExpenseChart.on('legendselectchanged', function(obj) {
		var selected = obj.selected;
		var legend = obj.name;

		// 使用 legendToggleSelect Action 会重新触发 legendselectchanged Event，导致本函数重复运行
		// 使得 无 selected 对象
		if (selected != undefined) {

			/*if (isFirstUnSelect(selected)) {
				triggerAction('legendToggleSelect', selected);
			} else if (isAllUnSelected(selected)) {
				triggerAction('legendSelect', selected);

			}*/
			if (isAllUnSelected(selected)) {
				triggerAction('legendToggleSelect', selected);
				//triggerAction('legendSelect', selected);
			}
		}

	});

	window.onresize = userExpenseChart.resize;

	// custom mouseover event

	if (parseFloat(nyData.out_money) > 0 || parseFloat(nyData.in_money) > 0) {
		userExpenseChart.on('mouseover', function (obj) {
			$(".user-expense-title").hide()

		})
		userExpenseChart.on('mouseout', function () {
			$(".user-expense-title").show()
		});

		userExpenseChart.on("legend");
	}


});