//// Configure bundling and minification for the project.
//// More info at https://go.microsoft.com/fwlink/?LinkId=808241
//[
//  {
//    "outputFileName": "wwwroot/build/admin/css/site.min.css",
//    // An array of relative input file paths. Globbing patterns supported
//    "inputFiles": [
//      //"wwwroot/themes/layui/css/layui.css",
//      "wwwroot/themes/css/toastr.css",
//      "wwwroot/themes/css/nprogress.css",
//      "wwwroot/themes/css/app.css"
//    ]
//  },
//  {
//    "outputFileName": "wwwroot/build/admin/css/login.min.css",
//    // An array of relative input file paths. Globbing patterns supported
//    "inputFiles": [
//      //"wwwroot/themes/layui/css/layui.css",
//      "wwwroot/themes/css/toastr.css",
//      "wwwroot/themes/css/login.css"
//    ]
//  },
//  {
//    "outputFileName": "wwwroot/build/admin/js/login.min.js",
//    "inputFiles": [
//      "wwwroot/themes/js/jsencrypt.js",
//      "wwwroot/themes/js/login.js"
//    ],
//    // Optionally specify minification options
//    "minify": {
//      "enabled": true,
//      "renameLocals": true
//    },
//    // Optionally generate .map file
//    "sourceMap": false
//  }
//]
