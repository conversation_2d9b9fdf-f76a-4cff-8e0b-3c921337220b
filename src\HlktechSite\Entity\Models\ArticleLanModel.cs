﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechSite.Entity;

/// <summary>文章翻译</summary>
public partial class ArticleLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>文章Id</summary>
    public Int32 AId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>文章标题</summary>
    public String? Name { get; set; }

    /// <summary>内容</summary>
    public String? Content { get; set; }

    /// <summary>简介</summary>
    public String? Summary { get; set; }

    /// <summary>文章主图</summary>
    public String? Pic { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IArticleLan model)
    {
        Id = model.Id;
        AId = model.AId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
        Summary = model.Summary;
        Pic = model.Pic;
    }
    #endregion
}
