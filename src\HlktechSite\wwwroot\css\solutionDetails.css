﻿.title1 {
    color: #333 !important;
    background-color: #fff;
}

.title1:hover {
    background-color: #eee !important;
}

.title3 {
    color: #fff !important;
    background-color: #337ab7;
}

    .title3:hover {
        background-color: #337ab7 !important;
    }


.solution-top > img {
    width: 100%;
}

.solution-top {
    position: relative;
}

    .solution-top > div {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        bottom: 0px;
        color: #FFFFFF;
    }

        .solution-top > div > h2 {
            margin-top: 109px;
            text-align: center;
            font-size: 56px;
        }

        .solution-top > div > p {
            width: 844px;
            margin: 0 auto;
            margin-top: 35px;
            text-align: center;
            font-size: 20px;
        }

.solution-middle {
    position: relative;
}

.solution-con {
    width: 1102px;
    padding-left: 49px;
    padding-right: 49px;
    margin: 0 auto;
    margin-top: -65px;
    margin-bottom: 105px;
    padding-top: 23px;
    box-shadow: 0px 2px 26px 1px rgba(154,154,154,0.52);
    background: rgba(255,255,255,1);
}

    .solution-con > h2, .solution-con > p {
        margin: 0px;
        padding: 0px;
        display: flex;
        justify-content: space-between;
        color: #333333;
        font-weight: bold;
        font-size: 24px;
    }

    .solution-con > h2 >span{
      flex:1;
    }

        .solution-con > h2 > span:last-child {
            color: #727272;
            text-align: right;
            font-weight: 400;
        }


.bdsharebuttonbox {
    border-bottom: 2px solid #D7D7D7;
    padding-bottom: 31px;
}
.solution-image-text{
    padding-top:30px;
    padding-bottom:60px;
/*    height:3079px;*/
}


.solution-con > p {
    padding-bottom: 43px;
    font-size: 16px;
    color: #333333;
}

    .solution-con > p > span {
        font-weight: 400;
    }

        .solution-con > p > span > a {
            color: #7B7B7B;
            overflow: hidden;
            display: inline-block;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            vertical-align: top;
        }


        .solution-con > p > span > a:hover {
            text-decoration:none;
        }


.solution-con > p>span:last-child {
   text-align:right;
}