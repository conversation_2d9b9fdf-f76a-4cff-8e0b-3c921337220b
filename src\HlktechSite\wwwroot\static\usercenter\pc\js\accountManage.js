$(function () {
	// 验证方式切换，更改表单提交地址
	var changeVerifyMethod = function (tabsContainer, form) {
		var $_tabs = $(tabsContainer).find("a");
		var $_form = $(form);
		if ($_tabs.length == 1) {
			$(".choose-verify-method").hide();
		}
		// 默认将表单action设置为第一个tab的data-action
		$_form.attr("action", $_tabs.first().data("action"));
		$_tabs.click(function () {
			$_form.attr("action", $(this).data("action"));
		});

		// 触发第一个按钮的click事件
		$_tabs.first().trigger("click");
	};

	// 创建NY表单验证实例
	var clearValidateError = NY.validater.clearValidateError;
	var validateShowError = NY.validater.validateShowError;
	// 非空验证
	var fieldEmptyValidate = function (form, remindWords) {
		var $_form = $(form);
		var $_visibleInputs = $_form.find(".validate-control input:visible");
		var isFormFilled = true;
		var words = remindWords || "该项不能为空";
		// 清空错误提示
		clearValidateError($_form);
		$_visibleInputs.each(function () {
			var $_input = $(this);
			if (!$_input.val()) {
				validateShowError($_input, words, $_form);
				isFormFilled = false;
				return false;
			}
		});

		return isFormFilled;
	};

	// 基本信息页面
	var $_savingInfoButton = $("#savingInfo");
	var $_industrySelectTabs = $("#industrySelectTabs");
	if ($_savingInfoButton.length > 0) {
		// 行业信息选择
		$_industrySelectTabs.on("click", "a", function () {
			var $_self = $(this);
			$_industrySelectTabs.find("a").removeClass("ny-tab-selected");
			$_self.addClass("ny-tab-selected");
			$("input[name=crmIndustry]").val($_self.data("method"));
		});

		// 初始化行业信息下拉
		$("[data-method='" + nyData.basicInfo.crmIndustry + "']").trigger("click");
	}

	// 基本信息页面，保存基本信息
	$_savingInfoButton.click(function () {
		var $_infoForm = $("#basicInfoForm");
		var actionUrl = $_infoForm.attr("action");

		if (!fieldEmptyValidate($_infoForm)) {
			return false;
		}

		NY.post({
			beforeSend: function () {
				NY.waiting("正在保存，请稍后...", true);
			},
			url: actionUrl,
			data: $_infoForm.serialize(),
			complete: function(){
				NY.hideWaiting();
			}
		});
	});

	//判断密码强度
	function passCheck(value) {
		var strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
		var mediumRegex = new RegExp("^(?=.{7,})(((?=.*[A-Z])(?=.*[a-z]))|((?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[0-9]))).*$", "g");
		var weakRegex = new RegExp("(?=.{6,}).*", "g");
		var $pwdLevelBar = $(".pwd-strength li");
		var $pwdLevelTxt = $(".show-pwd-level");
		if (value) {
			if (false == weakRegex.test(value)) {
				$pwdLevelBar.removeClass();
				$pwdLevelTxt.addClass("hide");
				$(".pwd-short").removeClass("hide");
			} else if (strongRegex.test(value)) {
				$pwdLevelBar.removeClass();
				$pwdLevelBar.eq(0).addClass("levelbar-strong");
				$pwdLevelBar.eq(1).addClass("levelbar-strong");
				$pwdLevelBar.eq(2).addClass("levelbar-strong");
				$pwdLevelTxt.addClass("hide");
				$(".pwd-strong").removeClass("hide");
			} else if (mediumRegex.test(value)) {
				$pwdLevelBar.removeClass();
				$pwdLevelBar.eq(0).addClass("levelbar-medium");
				$pwdLevelBar.eq(1).addClass("levelbar-medium");
				$pwdLevelTxt.addClass("hide");
				$(".pwd-medium").removeClass("hide");
			} else {
				$pwdLevelBar.removeClass();
				$pwdLevelBar.eq(0).addClass("levelbar-weak");
				$pwdLevelTxt.addClass("hide");
				$(".pwd-weak").removeClass("hide");
			}
			return true;
		} else {
			$pwdLevelBar.removeClass();
			$pwdLevelTxt.addClass("hide");
			$(".pwd-null").removeClass("hide");
		}
	}
	var $_newPwdInput = $("#newPwdInput");
	$_newPwdInput.keyup(function (e) {
		passCheck($(this).val());
	});


	// 保存密码
	$("#savePwd").click(function () {
		var $_pwdForm = $("#changePassword");
		var $_pwdInputs = $(".password-input");
		var $_reminder = $(".error-reminder");
		var $_oldPwdInput = $("#oldPwdInput");
		var $_rePwdInput = $("#rePwdInput");
		var errorClassName = "error-input";
		var reminderClassName = ".error-reminder";
		var inputNameMap = {
			"oldPassword": $_oldPwdInput,
			"password": $_newPwdInput,
			"password2": $_rePwdInput
		};
		var actionUrl = $_pwdForm.attr("action");
		var frontValidFailCall = function ($_input, remindWords) {
			$_input.addClass(errorClassName);
			$_input.parent().find(reminderClassName).html(remindWords);
			$_input.focus();
			isFrontValidateOK = false;
		};
		// 前端验证通过标识
		var isFrontValidateOK = true;
		// 清空警告
		$_pwdInputs.removeClass(errorClassName);
		$_reminder.empty();
		// 表单验证
		if(!$_oldPwdInput.val()) {
			frontValidFailCall($_oldPwdInput, "旧密码不能为空");
			return false;
		}else {
			$.post({
				url: '/user/password/checkOldPwd.html',
				dataType: 'json',
				data: {oldPassword: $_oldPwdInput.val()},
				success: function(data) {
					if(data.result) {
						// 清空警告
						$_oldPwdInput.removeClass(errorClassName);
						$_reminder.empty();

						if(!$_newPwdInput.val()) {
							frontValidFailCall($_newPwdInput, "新密码不能为空");
							return false;
						}
						if(! new RegExp('^(?![^a-zA-Z]+$).(?!\D+$).{7,29}$').test($_newPwdInput.val()) ) {
							frontValidFailCall($_newPwdInput, "密码必须由8-30位的数字、字母或特殊符号组成");
							return false;
						}
						if ($_newPwdInput.val() != $_rePwdInput.val()) {
							frontValidFailCall($_rePwdInput, "两次密码填写不一致");
							return false;
						}
						// 未通过前端验证则不提交表单
						if (!isFrontValidateOK) {
							return;
						}
						$_pwdForm.find(".protection-enabled").trigger("click");
					}else {
						frontValidFailCall($_oldPwdInput, data.text || "旧密码错误");
						return false;
					}
				}
			});
		}
	});

	/**
	 * 修改绑定手机
	 */
	// step 1
	var $_verifyMethodTabs = $("#verifyMethodToggle a");
	var $_modifyMobileForm = $("#modifyMobileForm");
	changeVerifyMethod("#verifyMethodToggle", $_modifyMobileForm);
	// 提交验证结果
	$("#modifyMobileVerify").click(function () {
		if (!fieldEmptyValidate($_modifyMobileForm)) {
			return false;
		}

		NY.post({
			url: $_modifyMobileForm.attr("action"),
			data: $_modifyMobileForm.serialize()
		});
	});

	// step 2
	// 发送短信验证码
	var $_setNewMobileForm = $("#setNewMobileForm");
	var $_sendCodeWithCaptcha = $("#sendCodeWithCaptcha");
	var setMobileSMSLeftTime = $("#setLeftTime").html();
	if (setMobileSMSLeftTime > 0) {
		NY.dom.createCountDown($_sendCodeWithCaptcha, {
			time: setMobileSMSLeftTime
		});
	}

	$_sendCodeWithCaptcha.click(function () {
		clearValidateError($_setNewMobileForm);
		var modifyMobileImgCaptchaValue = $("#modifyMobileImgCaptcha").val();
		var newMobileValue = $("#newMobile").val();

		if (newMobileValue && modifyMobileImgCaptchaValue) {
			NY.post({
				data: {
					mobile: newMobileValue,
					captcha: modifyMobileImgCaptchaValue
				},
				url: "/user/mobile/sendCode.html",
				success: function (responseJSON) {
					NY.dom.createCountDown($_sendCodeWithCaptcha, {
						time: 60
					});
				}
			});
		} else {
			// 对发送验证码所必须的项目验证
			$(".send-code-needed").each(function () {
				var $_self = $(this);
				if (!$_self.val()) {
					validateShowError($_self, "该项不能为空", $_setNewMobileForm);
					return false;
				}
			});
		}
	});

	$("#bindNewMobile").click(function () {
		clearValidateError($_setNewMobileForm);
		var isNeededInputFilled = true;
		// 对提交表单必须的项目验证
		$(".submit-needed").each(function () {
			var $_self = $(this);
			if (!$_self.val()) {
				validateShowError($_self, "该项不能为空", $_setNewMobileForm);
				isNeededInputFilled = false;
				return false;
			}
		});

		if (isNeededInputFilled) {
			NY.post({
				data: $_setNewMobileForm.serialize(),
				url: $_setNewMobileForm.attr("action"),
				successResultFalse: function (responseJSON) {
					validateShowError(responseJSON.name, responseJSON.text, $_setNewMobileForm);
				}
			});
		}
	});

	/**
	 * 绑定邮箱
	 */
	var $_bindEmailForm = $("#bindEmailForm");
	// 绑定邮箱发送验证邮件
	var $_sendBindEmailCaptcha = $("#sendBindEmailCaptcha");
	$_sendBindEmailCaptcha.click(function () {


		var url = $_sendBindEmailCaptcha.attr('url');
		var defaultUrl = "/user/email/sendCode.html";
		url = url ? url : defaultUrl;

		if (!$(".captcha-needed").val()) {
			validateShowError("email", "该项不能为空", $_bindEmailForm);
		} else {
			var $_securityCodePopWrapper = $("#securityCodePopWrapper");
			var captchaVal;
			$.dialog({
				title: "请输入验证码",
				content: $_securityCodePopWrapper[0],
				cancel: true,
				cancelVal: "取消",
				padding: "30px 45px 26px 40px",
				init:function(){
					$_securityCodePopWrapper.find(".captcha-input").val("");
					$_securityCodePopWrapper.find(".show-captcha").trigger("click");
				},
				ok:function(){
					captchaVal=$_securityCodePopWrapper.find(".captcha-input").val();
					if(captchaVal){
						$_sendBindEmailCaptcha.attr("disabled", true);
						NY.post({
							data: {
								email: $("#bindEmailCaptcha").val(),
								captcha:captchaVal
							},
							// url: "/user/email/sendCode.html",
							url: url,
							successResultFalse: function (responseJSON) {
								validateShowError(responseJSON.name, responseJSON.text, $_bindEmailForm);
							},
							beforeSend:function(){
								NY.waiting("邮件发送中......", true);
							},
							complete: function (responseJSON) {
								NY.hideWaiting();
								$_sendBindEmailCaptcha.attr("disabled", false);
							}
						});
					}else {
						NY.warn("请填写验证码")
						return false;
					}
					
				}
			});
			
			
			
			
			
			
			
			
			
			
			
		}

	});
	// 绑定保存
	$("#bindEmail").click(function () {
		clearValidateError($_bindEmailForm);

		var isNeededInputFilled = true;
		$(".validate-control input").each(function() {
			var $_self = $(this);
			if (!$_self.val()) {
				validateShowError($_self, "该项不能为空", $_bindEmailForm);
				isNeededInputFilled = false;
				return false;
			}
		});

		if (isNeededInputFilled) {
			NY.post({
				data: $_bindEmailForm.serialize(),
				url: $_bindEmailForm.attr("action"),
				successResultFalse: function (responseJSON) {
					validateShowError(responseJSON.name, responseJSON.text, $_bindEmailForm);
				}
			});
		}
	});

	/**
	 * 设置邮箱验证
	 */
	// step 1
	var $_sendEmailCode = $("#sendEmailCaptcha");
	var $_modifyEmailForm = $("#modifyEmailForm");
	$_modifyEmailForm.attr("action", $_verifyMethodTabs.eq(0).data("action"));
	// 验证方式切换，更改表单提交地址
	changeVerifyMethod("#verifyMethodToggle", $_modifyEmailForm);
	// 提交验证结果
	$("#modifyEmailVerify").click(function () {
		if (!fieldEmptyValidate($_modifyEmailForm)) {
			return false;
		}

		NY.post({
			url: $_modifyEmailForm.attr("action"),
			data: $_modifyEmailForm.serialize()
		});
	});

	// step 2
	// 发送新邮箱验证码
	var $_setEmailForm = $("#setNewEmailForm");
	var $_newEmailInput = $("#newEmailInput");
	$("#sendEmailWithCaptcha").click(function () {
		clearValidateError($_setEmailForm);
		var $_self = $(this);
		var $_newEmailValue = $_newEmailInput.val();
		if ($_newEmailValue) {
			var $_securityCodePopWrapper = $("#securityCodePopWrapper");
			var captchaVal;
			$.dialog({
				title: "请输入验证码",
				content: $_securityCodePopWrapper[0],
				cancel: true,
				cancelVal: "取消",
				padding: "30px 45px 26px 40px",
				init:function(){
					$_securityCodePopWrapper.find(".captcha-input").val("");
					$_securityCodePopWrapper.find(".show-captcha").trigger("click");
				},
				ok:function(){
					captchaVal=$_securityCodePopWrapper.find(".captcha-input").val();
					if(captchaVal){
						$_self.attr("disabled", true);
						$_self.val("发送中...");
						NY.post({
							isCoverSuccess: true,
							data: {
								email: $_newEmailValue,
								captcha:captchaVal
							},
							// url: "/user/email/sendCode.html",
							url: "/user/newemail/sendCode.html",
							success: function (responseJSON) {
								if (responseJSON.result) {
									NY.success(responseJSON.text, 3, function () {
										$_self.attr("disabled", false);
										$_self.val("发送验证码");
									});
			
								}
								else {
									NY.warn(responseJSON.text, 3, function () {
										$_self.attr("disabled", false);
										$_self.val("点击再次发送");
									});
								}
			
							}
						});
					}else {
						NY.warn("请填写验证码")
						return false;
					}
					
				}
			});
		} else {
			validateShowError($_newEmailInput, "该项不能为空", $_setEmailForm);
		}
	});
	$("#bindNewEmail").click(function () {
		clearValidateError($_setEmailForm);
		var isSetEmailNeededFilled = true;
		$(".validate-control input").each(function() {
			var $_self = $(this);
			if (!$_self.val()) {
				validateShowError($_self, "该项不能为空", $_setEmailForm);
				isSetEmailNeededFilled = false;
				return false;
			}
		});

		if (isSetEmailNeededFilled) {
			NY.post({
				data: $_setEmailForm.serialize(),
				url: $_setEmailForm.attr("action"),
				successResultFalse: function (responseJSON) {
					validateShowError(responseJSON.name, responseJSON.text, $_setEmailForm);
				}
			});
		}
	});

	/**
	 * 修改密保
	 */
	// 未设置用户密保
	var $_noprotectionsetForm = $("#noprotectionsetForm");
	$("#addProtect").click(function () {
		// $_noprotectionsetForm.find(".protection-enabled").trigger("click");
		NY.post({
            url: $_noprotectionsetForm.attr("action"),
            data: $_noprotectionsetForm.serialize()
		});
    });

	// step1 安全验证
	var $_modifyProtectForm = $("#modifyProtectForm");
	$_modifyProtectForm.attr("action", $_verifyMethodTabs.eq(0).data("action"));
	// 验证方式切换，更改表单提交地址
	changeVerifyMethod("#verifyMethodToggle", $_modifyProtectForm);
	// 提交验证结果
	$("#modifyProtectVerify").click(function () {
		if (!fieldEmptyValidate($_modifyProtectForm)) {
			return false;
		}

		NY.post({
			url: $_modifyProtectForm.attr("action"),
			data: $_modifyProtectForm.serialize()
		});
	});

	// step2 保存新密保
	var $_setNewProtectForm = $("#setNewProtectForm");
	var $_newAnswerInput = $("#newAnswerInput");
	$("#saveNewProtect").click(function () {
		clearValidateError($_setNewProtectForm);
		var isAnswersFilled = true;
		$(".new-protect-answer").each(function () {
			var $_self = $(this);

			if (!$_self.val()) {
				validateShowError($_self, "该项不能为空", $_setNewProtectForm);
				isAnswersFilled = false;
				return false;
			}
		});
		if (isAnswersFilled) {
			$.dialog({
				title: "重设密保问题",
				content: $("#checkProtectionTpl")[0],
				cancel: true,
				okVal: "确定修改",
				init:function(){
					$(".pro-question-1").html($("[name=question1] option:checked").text());
					$(".pro-question-2").html($("[name=question2] option:checked").text());
					$(".pro-question-3").html($("[name=question3] option:checked").text());

					$(".pro-answer-1").html($("[name=answer1]").val());
					$(".pro-answer-2").html($("[name=answer2]").val());
					$(".pro-answer-3").html($("[name=answer3]").val());
				},
				ok: function () {
					NY.post({
						data: $_setNewProtectForm.serialize(),
						url: $_setNewProtectForm.attr("action"),
						successResultFalse: function (responseJSON) {
							validateShowError(responseJSON.name, responseJSON.text, $_setEmailForm);
						}
					});
					return false;
				}
			});
		}
	});
});